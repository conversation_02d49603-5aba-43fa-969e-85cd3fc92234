package switcher;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;

public class SwitcherTests extends BaseTest {
    @Test
    @Tags({@Tag("web")})
    public void updateSwitcherBalance() {
        Reporter.log("Starting the loginWithValidAdminAccount Test");

        //Visit Page and enter phone number
        Reporter.log("Going to Login Page", 3, true);
        webLoginPage.get().goToLoginPage();
        Reporter.log("Entering the phone number", 3, true);
        webLoginPage.get().enterPhoneNumber(defaultTestData.get().getAdminUser().getPhoneNumber());
        Reporter.log("Pressing the submit button", 3, true);
        webLoginPage.get().pressSubmitBtn();

        //Bypass the Google Login step
        webLoginPage.get().byPassGoogleLogin(defaultTestData.get().getAdminUser().getPhoneNumber(),
                defaultTestData.get().getAdminUser().getBypassScriptPassword());

        //Validate that the more page is displayed
        Reporter.log("Login is successful and checking if more button is displayed");
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        Reporter.log("More Button is displayed");

        //Navigate to switcher page and assert successful navigation
        Reporter.log("Navigating to switcher page");
        webSwitcherPage.get().goToSwitcherPage();
        Assert.assertTrue(webSwitcherPage.get().isPageDisplayed());

        //Searching for phone number and select user
        webSwitcherPage.get().openSelectUserDropdown();
        webSwitcherPage.get().searchForPhoneNumberAndSelectUser("01011842324");
        Assert.assertTrue(webSwitcherPage.get().isUpdateBalanceBtnDisplayed());

        //Update user balance
        Assert.assertTrue(webSwitcherPage.get().updateUserBalance("80"));
    }
}
