package fleetApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Collections;

public class CashCollectTests extends BaseTest{

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping")})
    public void checkCashCollectWillRemoveOrderFromTrip() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(),
                defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(),
                Collections.singletonList((defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                )));

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertTrue(defaultTestData.get().getTripTasks().getAssignedTrips().getLast().getAction().isEnabled());

        fleetAppApiClient.get().tripActions(defaultTestData.get().getDaUser(), "START_TRIP",
                defaultTestData.get().getAssignedTrip().getTripId(), configs.get().getTestLatitude(),
                configs.get().getTestLongitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(defaultTestData.get().getTripTasks().getCurrentTrip().getStatusId(), 2);

        fleetAppApiClient.get().taskActions(defaultTestData.get().getDaUser(), "COMPLETE_PICKUP_TASK",
                defaultTestData.get().getTripTasks().getCurrentTrip().getPickups().getFirst().getId(),
                configs.get().getTestLatitude(), configs.get().getTestLongitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(Collections.emptyList(), defaultTestData.get().getTripTasks().getCurrentTrip().getPickups());

        Assert.assertNotEquals(Collections.emptyList(), defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries());

        Assert.assertEquals(defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getDetails().getOrderNumber(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());

        fleetAppApiClient.get().taskActions(defaultTestData.get().getDaUser(), "START_DELIVERY_TASK",
                defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getId(),
                configs.get().getTestLatitude(), configs.get().getTestLongitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(2, defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getStatusId());

        fleetAppApiClient.get().taskActions(defaultTestData.get().getDaUser(), "CONFIRM_DELIVERY_TASK",
                defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getId(),
                configs.get().getTestLatitude(), configs.get().getTestLongitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(3, defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getStatusId());

        fleetAppApiClient.get().cashCollect(defaultTestData.get().getDaUser(), "COMPLETE_DELIVERY_TASK",
                defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getId(),
                configs.get().getTestLatitude(), configs.get().getTestLongitude(),
                defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getDetails().getCashCollection().getAmountToCollect());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals("COMPLETE_TRIP", defaultTestData.get().getTripTasks().getCurrentTrip().getAction().getAction());

        Assert.assertTrue( defaultTestData.get().getTripTasks().getCurrentTrip().getAction().isEnabled());

        Assert.assertEquals(Collections.emptyList(), defaultTestData.get().getTripTasks().getCurrentTrip().getPickups());

        Assert.assertEquals(Collections.emptyList(), defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries());

        fleetAppApiClient.get().tripActions(defaultTestData.get().getDaUser(), "COMPLETE_TRIP",
                Integer.valueOf(defaultTestData.get().getTripTasks().getCurrentTrip().getTripId()), configs.get().getTestLatitude(),
                configs.get().getTestLongitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertNull( defaultTestData.get().getTripTasks().getCurrentTrip());

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping")})
    public void checkCashCollectionBelowOrderAmount() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(),
                defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(),
                Collections.singletonList((defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                )));

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertTrue(defaultTestData.get().getTripTasks().getAssignedTrips().getLast().getAction().isEnabled());

        fleetAppApiClient.get().tripActions(defaultTestData.get().getDaUser(), "START_TRIP",
                defaultTestData.get().getAssignedTrip().getTripId(), configs.get().getTestLatitude(),
                configs.get().getTestLongitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(defaultTestData.get().getTripTasks().getCurrentTrip().getStatusId(), 2);

        fleetAppApiClient.get().taskActions(defaultTestData.get().getDaUser(), "COMPLETE_PICKUP_TASK",
                defaultTestData.get().getTripTasks().getCurrentTrip().getPickups().getFirst().getId(),
                configs.get().getTestLatitude(), configs.get().getTestLongitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(Collections.emptyList(), defaultTestData.get().getTripTasks().getCurrentTrip().getPickups());

        Assert.assertNotEquals(Collections.emptyList(), defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries());

        Assert.assertEquals(defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getDetails().getOrderNumber(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());

        fleetAppApiClient.get().taskActions(defaultTestData.get().getDaUser(), "START_DELIVERY_TASK",
                defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getId(),
                configs.get().getTestLatitude(), configs.get().getTestLongitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(2, defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getStatusId());

        fleetAppApiClient.get().taskActions(defaultTestData.get().getDaUser(), "CONFIRM_DELIVERY_TASK",
                defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getId(),
                configs.get().getTestLatitude(), configs.get().getTestLongitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(3, defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getStatusId());

        fleetAppApiClient.get().cashCollect(defaultTestData.get().getDaUser(), "COMPLETE_DELIVERY_TASK",
                defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getId(),
                configs.get().getTestLatitude(), configs.get().getTestLongitude(),
                defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries().getFirst().getDetails().getCashCollection().getAmountToCollect() - 100);

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals("COMPLETE_TRIP", defaultTestData.get().getTripTasks().getCurrentTrip().getAction().getAction());

        Assert.assertTrue( defaultTestData.get().getTripTasks().getCurrentTrip().getAction().isEnabled());

        Assert.assertEquals(Collections.emptyList(), defaultTestData.get().getTripTasks().getCurrentTrip().getPickups());

        Assert.assertEquals(Collections.emptyList(), defaultTestData.get().getTripTasks().getCurrentTrip().getDeliveries());

        fleetAppApiClient.get().tripActions(defaultTestData.get().getDaUser(), "COMPLETE_TRIP",
                Integer.valueOf(defaultTestData.get().getTripTasks().getCurrentTrip().getTripId()), configs.get().getTestLatitude(),
                configs.get().getTestLongitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertNull( defaultTestData.get().getTripTasks().getCurrentTrip());

    }
}
