package fleetApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Collections;

public class PickupTasksTests extends BaseTest {

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping")})
    public void checkStartingTripIsDisabledWhenOrderIsNotPackaged() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(),
                defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(),
                Collections.singletonList((defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                )));
        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertFalse(
                defaultTestData.get().getTripTasks()
                        .getAssignedTrips()
                        .stream()
                        .filter(trip -> trip.getTripId() == defaultTestData.get().getAssignedTrip().getTripId())
                        .findFirst()
                        .orElseThrow(() -> new AssertionError("TripId not found in assigned trips"))
                        .getAction()
                        .isEnabled(),
                "The action is enabled for the expected tripId."
        );
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping")})
    public void checkStartingTripIsEnabledWhenOrderIsPackaged() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(),
                defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(),
                Collections.singletonList((defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                )));

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertTrue(defaultTestData.get().getTripTasks().getAssignedTrips().getLast().getAction().isEnabled());

        Assert.assertTrue(
                defaultTestData.get().getTripTasks()
                        .getAssignedTrips()
                        .stream()
                        .filter(trip -> trip.getTripId() == defaultTestData.get().getAssignedTrip().getTripId())
                        .findFirst()
                        .orElseThrow(() -> new AssertionError("TripId not found in assigned trips"))
                        .getAction()
                        .isEnabled(),
                "The action is not enabled for the expected tripId."
        );

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping")})
    public void checkStartingTripWillChangeStatus() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(),
                defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(),
                Collections.singletonList((defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                )));

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertTrue(defaultTestData.get().getTripTasks().getAssignedTrips().getLast().getAction().isEnabled());

        Assert.assertTrue(
                defaultTestData.get().getTripTasks()
                        .getAssignedTrips()
                        .stream()
                        .filter(trip -> trip.getTripId() == defaultTestData.get().getAssignedTrip().getTripId())
                        .findFirst()
                        .orElseThrow(() -> new AssertionError("TripId not found in assigned trips"))
                        .getAction()
                        .isEnabled(),
                "The action is not enabled for the expected tripId."
        );

        fleetAppApiClient.get().tripActions(defaultTestData.get().getDaUser(), "START_TRIP",
                defaultTestData.get().getAssignedTrip().getTripId(), configs.get().getTestLatitude(), configs.get().getTestLatitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(defaultTestData.get().getTripTasks().getCurrentTrip().getStatusId(), 2);

        fleetAppApiClient.get().login(defaultTestData.get().getMidMileUser(),
                defaultTestData.get().getMidMileUser().getLocalPhoneNumber(), defaultTestData.get().getMidMileUser().getBypassScriptPassword());

        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getMidMileUser(),
                Collections.singletonList((defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                )));

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping")})
    public void checkCompletingPickupTask() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(),
                defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(),
                Collections.singletonList((defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                )));

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertTrue(defaultTestData.get().getTripTasks().getAssignedTrips().getLast().getAction().isEnabled());

        Assert.assertTrue(
                defaultTestData.get().getTripTasks()
                        .getAssignedTrips()
                        .stream()
                        .filter(trip -> trip.getTripId() == defaultTestData.get().getAssignedTrip().getTripId())
                        .findFirst()
                        .orElseThrow(() -> new AssertionError("TripId not found in assigned trips"))
                        .getAction()
                        .isEnabled(),
                "The action is not enabled for the expected tripId."
        );

        fleetAppApiClient.get().tripActions(defaultTestData.get().getDaUser(), "START_TRIP",
                defaultTestData.get().getAssignedTrip().getTripId(), configs.get().getTestLatitude(),
                configs.get().getTestLatitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(defaultTestData.get().getTripTasks().getCurrentTrip().getStatusId(), 2);

        fleetAppApiClient.get().taskActions(defaultTestData.get().getDaUser(), "COMPLETE_PICKUP_TASK",
                defaultTestData.get().getTripTasks().getCurrentTrip().getPickups().getFirst().getId(),
                configs.get().getTestLatitude(), configs.get().getTestLatitude());

        defaultTestData.get().setTripTasks(fleetAppApiClient.get().getTripTasks(defaultTestData.get().getDaUser()));

        Assert.assertEquals(Collections.emptyList(), defaultTestData.get().getTripTasks().getCurrentTrip().getPickups());

        fleetAppApiClient.get().login(defaultTestData.get().getMidMileUser(),
                defaultTestData.get().getMidMileUser().getLocalPhoneNumber(), defaultTestData.get().getMidMileUser().getBypassScriptPassword());

        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getMidMileUser(),
                Collections.singletonList((defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                )));
    }

}
