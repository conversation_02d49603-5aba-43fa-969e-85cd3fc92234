package fleetApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Optional;
import java.util.Random;

public class SideMenuTests extends BaseTest {

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckDaFullName() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        Assert.assertEquals(fleetAppApiClient.get().getDaData(defaultTestData.get().getDaUser())
                .get("statusCode"), 200);
        Assert.assertEquals(defaultTestData.get().getDaUser().getFullName(), "محمد احمد عبد المقصود");

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckDaHRID() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        Assert.assertEquals(fleetAppApiClient.get().getDaData(defaultTestData.get().getDaUser())
                .get("statusCode"), 200);
        Assert.assertEquals(defaultTestData.get().getDaUser().getHrId(), 6564777);

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckDaRatingScore() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        Assert.assertEquals(fleetAppApiClient.get().getDaData(defaultTestData.get().getDaUser())
                .get("statusCode"), 200);
        Assert.assertEquals(defaultTestData.get().getDaUser().getDaScore(), "0.00");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckfpNameAssigned() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        Assert.assertEquals(fleetAppApiClient.get().getDaData(defaultTestData.get().getDaUser())
                .get("statusCode"), 200);
        Assert.assertEquals(defaultTestData.get().getDaUser().getDaFpNameAssigned(), defaultTestData.get().getTestWarehouse().getName());
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckChangingFpAssigned() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");

        deliveryCapacityManagementApiClient.get().unAssignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "5e2c373d6b874d02cdc83203");

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        Assert.assertEquals(fleetAppApiClient.get().getDaData(defaultTestData.get().getDaUser())
                .get("statusCode"), 200);
        Assert.assertEquals(defaultTestData.get().getDaUser().getDaFpNameAssigned(), "Maadi FP #1");

        deliveryCapacityManagementApiClient.get().unAssignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "5e2c373d6b874d02cdc83203");

        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckBalanceisDisplayed() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        Assert.assertEquals(fleetAppApiClient.get().getDaData(defaultTestData.get().getDaUser())
                .get("statusCode"), 200);
        Assert.assertEquals(defaultTestData.get().getDaUser().getDaBalance(), 1389);
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void ChangeDaStatusToAvailable() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        Assert.assertEquals(fleetAppApiClient.get().changeDaStatus(defaultTestData.get().getDaUser(), "available")
                .get("statusCode"), "200");
        Assert.assertEquals(defaultTestData.get().getDaUser().getDaStatus(), "available");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckChangingDaStatusToUnavialbleWithAssignedTrips() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        Assert.assertEquals(fleetAppApiClient.get().changeDaStatus(defaultTestData.get().getDaUser(), "unavailable")
                .get("errors"), "[You have active trips please complete them before change your status to unavailable]");

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckChangingStatusToUnavailableWithoutTripsAssigned() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getMidMileUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");

        fleetAppApiClient.get().login(defaultTestData.get().getMidMileUser(), defaultTestData.get().getMidMileUser().getLocalPhoneNumber(), defaultTestData.get().getMidMileUser().getBypassScriptPassword());

        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getMidMileUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

        Assert.assertEquals(fleetAppApiClient.get().changeDaStatus(defaultTestData.get().getMidMileUser(), "unavailable")
                .get("statusCode"), "200");
        Assert.assertEquals(defaultTestData.get().getMidMileUser().getDaStatus(), "unavailable");

    }

}
