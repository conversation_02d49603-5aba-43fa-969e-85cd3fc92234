package fleetApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import models.FleetTrip;
import models.Order;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Collections;
import java.util.Random;
import java.util.stream.Collectors;

public class ControlRoomIntegrationTests extends BaseTest {
    
    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckCreatingTripWithOrderUnassigned() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(),
                defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());

        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));

        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned( defaultTestData.get().getDaUser()));

        Assert.assertTrue(defaultTestData.get().getFleetTrip().getUpComing()
                        .stream()
                        .map(FleetTrip.FleetTripDetails::getTripId)
                        .toList()
                        .contains(defaultTestData.get().getAssignedTrip().getTripId()),
                "Expected tripId not found in upComing trips");

        Assert.assertTrue(defaultTestData.get().getFleetTrip().getUpComing()
                        .get(defaultTestData.get().getFleetTrip().getUpComing().size() - 1)
                        .getOrders()
                        .stream()
                        .map(Order::getOrderId)
                        .toList()
                        .contains(defaultTestData.get().getOrdersList().getFirst().getOrderId()),
                "Expected orderId not found in the last upComing trip's orders");

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckAssignOrderToExistingTrip() {
        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(),
                defaultTestData.get().getDaUser().getLocalPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());

        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));

        fleetAppApiClient.get().assignOrderToExistingTrip(
                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().get(2)),
                defaultTestData.get().getAssignedTrip().getTripId());

        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned( defaultTestData.get().getDaUser()));

        Assert.assertTrue(defaultTestData.get().getFleetTrip().getUpComing()
                                .get(defaultTestData.get().getFleetTrip().getUpComing().size() - 1)
                                .getOrders()
                                .stream()
                                .map(Order::getOrderId)
                                .toList()
                                .contains(defaultTestData.get().getOrdersList().getFirst().getOrderId()),
                "Expected orderId not found in the last upComing trip's orders");
    }
    
}
