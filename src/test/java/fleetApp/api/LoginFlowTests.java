package fleetApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;

import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Random;

@Test
public class LoginFlowTests extends BaseTest {

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkUnregisteredPhoneNumber() {

        Assert.assertEquals(fleetAppApiClient.get().verifyPhoneNumber("01234567890")
                .get("statusCode"), 403);
        Assert.assertEquals(((String[]) fleetAppApiClient.get().verifyPhoneNumber("01234567890")
                .get("errors"))[0], "هذا الرقم غير مصرح له بالدخول");

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkWrongNumberFormat() {

        Assert.assertEquals(fleetAppApiClient.get().verifyPhoneNumber("22" + new Random().nextInt(100000))
                .get("statusCode"), 400);
        Assert.assertEquals(((String[]) fleetAppApiClient.get().verifyPhoneNumber("22" + new Random().nextInt(100000))
                .get("errors"))[0], "برجاء إدخال رقم هاتف مكون من 11 رقم");

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkEmptyPhoneNumber() {

        Assert.assertEquals(fleetAppApiClient.get().verifyPhoneNumber("")
                .get("statusCode"), 400);
        Assert.assertEquals(((String[]) fleetAppApiClient.get().verifyPhoneNumber("")
                .get("errors"))[0], "يجب إدخال رقم الهاتف المسجل على بريدفاست");

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkUnassignedDaToFp() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");
        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

        deliveryCapacityManagementApiClient.get().unAssignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

        Assert.assertEquals(fleetAppApiClient.get().verifyPhoneNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber())
                .get("statusCode"), 400);
        Assert.assertEquals(((String[]) fleetAppApiClient.get().verifyPhoneNumber("0" + defaultTestData.get().getDaUser().getLocalPhoneNumber())
                .get("errors"))[0], "هذا الرقم غير تابع لأي نقطة توزيع, يجب أن تكون تابع لنقطة توزيع لتستطيع الدخول");

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkAssignedDaToFp() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");
        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

        Assert.assertEquals(fleetAppApiClient.get().verifyPhoneNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber())
                .get("statusCode"), 200);
        Assert.assertEquals(fleetAppApiClient.get().verifyPhoneNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber())
                .get("hasAccess"), true);
        Assert.assertEquals(fleetAppApiClient.get().verifyPhoneNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber())
                .get("userId"), 847643);

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkIncorrectRole() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "fp_manager");
        Assert.assertEquals(fleetAppApiClient.get().verifyPhoneNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber())
                .get("statusCode"), 403);
        Assert.assertEquals(((String[]) fleetAppApiClient.get().verifyPhoneNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber())
                .get("errors"))[0], "هذا الرقم غير مصرح له بالدخول");
        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkWrongPassword() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");
        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());
        Assert.assertEquals(((String[]) fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(), "12314345")
                .get("errors"))[0], "كلمة المرور غير صحيحة");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkValidPasswordLoginSuccessfully() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");
        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());
        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(), "gfutya8o");
        Assert.assertEquals(defaultTestData.get().getDaUser().getId(), "847643");
    }

    @Test
    @Tags({@Tag("web"), @Tag("fleetApp")})
    public void checkUpdateStatus() {

        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), configs.get().getdAPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        fleetAppApiClient.get().updateDaStatus(
                defaultTestData.get().getDaUser(), defaultTestData.get().getTestWarehouse().getId(), "available",
                configs.get().getTestLatitude(), configs.get().getTestLongitude());
        Assert.assertTrue(fleetAppApiClient.get().getDaData(defaultTestData.get().getDaUser()).get("status").equals("available"),
                "DA status should be available after update");
    }
}
