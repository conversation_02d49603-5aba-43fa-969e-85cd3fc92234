package fleetApp.android;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;
public class BfShopsPickupDetailsTests extends BaseTest {
    @Test (groups = "B10-39792")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void CheckPickupActionIsDimmedIfOrderIsPrePackaging() {
//        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        //login
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
////        create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //click on order dropdown
        androidTripDetailsScreen.get().clickONOrderDropDown();
        //check That pickup button dimmed
        androidTripDetailsScreen.get().isPickupButtonDisabled();}
    // _______________________________________________________________________________________________//
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void CheckPickupActionIsEnabledIfOrderIsPackaging () {
        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        //login
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        //change packing date from DB
//        jsonPath.set(JsonPath.given(databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
//                , "UPDATE `picker_testing`.`picker_orders` SET `preparation_deadline` = '2025-03-18 11:30:00' WHERE `id` ="
//                        + defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getStratDelivryElemnt(),
                "left");
        //click on order dropdown
        androidTripDetailsScreen.get().clickONOrderDropDown();
        //check That pickup button enabled
        androidTripDetailsScreen.get().isPickupButtonEnabled();
    }
    //------------------------------------------------------------------------------------------//
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void CheckPickupActionIsTurnedToDisplayAfterClickingOnIt() {
//        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        //login
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getStratDelivryElemnt(),
                "left");
        //click on order dropdown
        androidTripDetailsScreen.get().clickONOrderDropDown();
        //check That pickup button enabled
        androidTripDetailsScreen.get().isPickupButtonEnabled();
        //click on pickup button
      //  androidTripDetailsScreen.get().clickOnPickupButton();
        //click on modal pickup button
        androidTripDetailsScreen.get().clickOnPickupBtnInModule();
        //check if the module dismissed and display button is appeared
        androidTripDetailsScreen.get().isDisplayBtnAppeared();}
    //_________________________________________________________________________________________________//
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void CheckByClickingOnPickupButtonPickupModuleAppears () {
//        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        //login
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getStratDelivryElemnt(),
                "left");
        //click on order dropdown
        androidTripDetailsScreen.get().clickONOrderDropDown();
        //check That pickup button enabled
        androidTripDetailsScreen.get().isPickupButtonEnabled();
        //click on pickup button
       // androidTripDetailsScreen.get().clickOnPickupButton();
        //check if pickup module appeared
        androidTripDetailsScreen.get().isPickupModuleAppeared();}
    //____________________________________________________________________________________________________//
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void CheckClickOnCancelIconPickupModuleWillClose () {
        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getStratDelivryElemnt(),
                "left");
        //click on order dropdown
        androidTripDetailsScreen.get().clickONOrderDropDown();
        //check That pickup button enabled
        androidTripDetailsScreen.get().isPickupButtonEnabled();
        //click on pickup button
       // androidTripDetailsScreen.get().clickOnPickupButton();
        //click on modal pickup button
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getPickupBtnInModule(),
                "left");
        //click on close Btn
        androidTripDetailsScreen.get().clickOnCloseIcon();
        //check if the module dismissed and display button is appeared
        androidTripDetailsScreen.get().isDisplayBtnAppeared();}
    //_______________________________________________________________________//
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void CheckGoToDeliverySectionButtonWillAppear  () {
        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getStratDelivryElemnt(),
                "left");
        //click on order dropdown
        androidTripDetailsScreen.get().clickONOrderDropDown();
        //check That pickup button enabled
        androidTripDetailsScreen.get().isPickupButtonEnabled();
        //click on pickup button
      //  androidTripDetailsScreen.get().clickOnPickupButton();
        //click on modal pickup button
        //click on modal pickup button
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getPickupBtnInModule(),
                "left");
        //check that delivery button displayed
        androidTripDetailsScreen.get().isStartDeliveryBtnDisplayed();
}
//_________________________________________________________________________________________________//
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void CheckClickingOnGoToDeliverySection() {
        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getStratDelivryElemnt(),
                "left");
        //click on order dropdown
        androidTripDetailsScreen.get().clickONOrderDropDown();
        //check That pickup button enabled
        androidTripDetailsScreen.get().isPickupButtonEnabled();
        //click on pickup button
     //   androidTripDetailsScreen.get().clickOnPickupButton();
        //click on modal pickup button
        //click on modal pickup button
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getPickupBtnInModule(),
                "left");
        //check that delivery button displayed
        androidTripDetailsScreen.get().isStartDeliveryBtnDisplayed();
        //click on goto delivery section
        androidTripDetailsScreen.get().clickOnStartDeliveryBtn();
}
//_____________________________________________________________________________________________________//
@Test (groups = "B10-39791")
@Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
public void CheckDeliveryScreenTitle() {
    //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
    androidSplashScreen.get().pressLoginBtn();
    androidSplashScreen.get().setTempMobileNumber("mobileNumber");
    androidSplashScreen.get().pressConfirmBtn();
    androidSplashScreen.get().setPassword("lihj50rq");
    androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
    // click on trip card
    androidFleetHomeScreen.get().clickOnTripCard();
    //start the trip
    androidTestsExecutionHelper.get().swipeElementInDirection(
            androidDriver.get(),
            androidTripDetailsScreen.get().getStratDelivryElemnt(),
            "left");
    //click on order dropdown
    androidTripDetailsScreen.get().clickONOrderDropDown();
    //check That pickup button enabled
    androidTripDetailsScreen.get().isPickupButtonEnabled();
    //click on pickup button
  //  androidTripDetailsScreen.get().clickOnPickupButton();
    //click on modal pickup button
    //click on modal pickup button
    androidTestsExecutionHelper.get().swipeElementInDirection(
            androidDriver.get(),
            androidTripDetailsScreen.get().getPickupBtnInModule(),
            "left");
    //check that delivery button displayed
    androidTripDetailsScreen.get().isStartDeliveryBtnDisplayed();
    //click on goto delivery section
    androidTripDetailsScreen.get().clickOnStartDeliveryBtn();
    //check screen title
    androidOrderDeliveryScreen.get().isDeliverTxtAppeared();
}
//_____________________________________________________________________________________________//
@Test (groups = "B10-39791")
@Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
public void CheckClickingOnBackBtnFromDeliveryScreen() {
    //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
    androidSplashScreen.get().pressLoginBtn();
    androidSplashScreen.get().setTempMobileNumber("mobileNumber");
    androidSplashScreen.get().pressConfirmBtn();
    androidSplashScreen.get().setPassword("lihj50rq");
    androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
    // click on trip card
    androidFleetHomeScreen.get().clickOnTripCard();
    //start the trip
    androidTestsExecutionHelper.get().swipeElementInDirection(
            androidDriver.get(),
            androidTripDetailsScreen.get().getStratDelivryElemnt(),
            "left");
    //click on order dropdown
    androidTripDetailsScreen.get().clickONOrderDropDown();
    //check That pickup button enabled
    androidTripDetailsScreen.get().isPickupButtonEnabled();
    //click on pickup button
   // androidTripDetailsScreen.get().clickOnPickupButton();
    //click on modal pickup button
    androidTestsExecutionHelper.get().swipeElementInDirection(
            androidDriver.get(),
            androidTripDetailsScreen.get().getPickupBtnInModule(),
            "left");
    //check that delivery button displayed
    androidTripDetailsScreen.get().isStartDeliveryBtnDisplayed();
    //click on goto delivery section
    androidTripDetailsScreen.get().clickOnStartDeliveryBtn();
    //check screen title
    androidOrderDeliveryScreen.get().isDeliverTxtAppeared();
    //click on back button
    androidOrderDeliveryScreen.get().clickOnBackBtn();
    //check the redirection
    androidFleetHomeScreen.get().isCurrentTripAppeared();
}
//_______________________________________________________________________________________//
@Test (groups = "B10-39791")
@Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
public void CheckThatDisplayBtnIsCliCKable() {
    //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
    androidSplashScreen.get().pressLoginBtn();
    androidSplashScreen.get().setTempMobileNumber("mobileNumber");
    androidSplashScreen.get().pressConfirmBtn();
    androidSplashScreen.get().setPassword("lihj50rq");
    androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
    // click on trip card
    androidFleetHomeScreen.get().clickOnTripCard();
    //start the trip
    androidTestsExecutionHelper.get().swipeElementInDirection(
            androidDriver.get(),
            androidTripDetailsScreen.get().getStratDelivryElemnt(),
            "left");
    //click on order dropdown
    androidTripDetailsScreen.get().clickONOrderDropDown();
    //check That pickup button enabled
    androidTripDetailsScreen.get().isPickupButtonEnabled();
    //click on pickup button
   // androidTripDetailsScreen.get().clickOnPickupButton();
    //click on modal pickup button
    androidTestsExecutionHelper.get().swipeElementInDirection(
            androidDriver.get(),
            androidTripDetailsScreen.get().getPickupBtnInModule(),
            "left");
    //check that delivery button displayed
    androidTripDetailsScreen.get().isStartDeliveryBtnDisplayed();
    //click on Display Btn
    androidOrderDeliveryScreen.get().clickOnDisplayBtn();
    //start the deleivry
    androidTestsExecutionHelper.get().swipeElementInDirection(
            androidDriver.get(),
            androidTripDetailsScreen.get().getStratDelivryElemnt(),
            "left");
}
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void checkEnteringAmountIsMoreThanTheReceiptValueBy100199AndConfirmIt() {
        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getStratDelivryElemnt(),
//                "left");
        //click on order dropdown
//        androidTripDetailsScreen.get().clickONOrderDropDown();
//        //check That pickup button enabled
//        androidTripDetailsScreen.get().isPickupButtonEnabled();
//        //click on pickup button
//        androidTripDetailsScreen.get().clickOnPickupButton();
//        //click on modal pickup button
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getPickupBtnInModule(),
//                "left");
//        //check that delivery button displayed
//        androidTripDetailsScreen.get().isStartDeliveryBtnDisplayed();
//        //start the deleivry
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getStratDelivryElemnt(),
//                "left");
        //click on Display Btn
        androidOrderDeliveryScreen.get().clickOnDisplayBtn();
        //click on order card in map view
        androidMapViewScreen.get().clickOnOrderCard();
        //click on order details button
        androidMapViewScreen.get().clickOnOrderDetailsBtn();
        //SLIDE start delivery
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidFleetOrderDetailsScreen.get().getStartDeliverySlider(),
//                "left");
//        //Slide confirm delivery
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidFleetOrderDetailsScreen.get().getConfirmDeliverySlider(),
//                "left");
//        // slide complete delivery
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidFleetOrderDetailsScreen.get().getCompleteDeliverySlide(),
//                "left");
        // slide cash collection
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getCompleteDeliverySlide(),
                "left");
        // enter amount + 150
        int orderValue = androidCashCollectionScreen.get().extractCashOrderValue();
        androidCashCollectionScreen.get().enterAmountPlus150();
        //click on confirm button
        androidCashCollectionScreen.get().clickOnConfirmBtn();
        //reenter amount again
        androidCashCollectionScreen.get().inputAmountBasedOnOrderValue(orderValue);
        //click on confirm Btn
        androidCashCollectionScreen.get().clickOnConfirmBtn();
        //slide cash collection
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getCompleteDeliverySlide(),
                "left");
        //click on ok btn
        androidCashCollectionScreen.get().clickOnOkBtn();
}
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void checkEnteringAmountIsMoreThanTheReceiptValueBy200AndMore() {
        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getStratDelivryElemnt(),
//                "left");
        //click on order dropdown
//        androidTripDetailsScreen.get().clickONOrderDropDown();
//        //check That pickup button enabled
//        androidTripDetailsScreen.get().isPickupButtonEnabled();
//        //click on pickup button
//        androidTripDetailsScreen.get().clickOnPickupButton();
//        //click on modal pickup button
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getPickupBtnInModule(),
//                "left");
//        //check that delivery button displayed
//        androidTripDetailsScreen.get().isStartDeliveryBtnDisplayed();
//        //start the deleivry
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getStratDelivryElemnt(),
//                "left");
        //click on Display Btn
        androidOrderDeliveryScreen.get().clickOnDisplayBtn();
        //click on order card in map view
        androidMapViewScreen.get().clickOnOrderCard();
        //click on order details button
        androidMapViewScreen.get().clickOnOrderDetailsBtn();
        //SLIDE start delivery
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidFleetOrderDetailsScreen.get().getStartDeliverySlider(),
//                "left");
//        //Slide confirm delivery
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidFleetOrderDetailsScreen.get().getConfirmDeliverySlider(),
//                "left");
//        // slide complete delivery
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidFleetOrderDetailsScreen.get().getCompleteDeliverySlide(),
//                "left");
        // slide cash collection
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getCompleteDeliverySlide(),
                "left");
        //enter ammount plus 200
        androidCashCollectionScreen.get().enterAmountPlus300();
        //click on confirm button
        androidCashCollectionScreen.get().clickOnConfirmBtn();
        // check cashcollection block module
        androidCashCollectionScreen.get().isCollectionBlockModuleAppeared();
    }
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void checkEnteringAmountIslessThanTheReceiptValue() {
        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getStratDelivryElemnt(),
//                "left");
        //click on order dropdown
        androidTripDetailsScreen.get().clickONOrderDropDown();
//        //check That pickup button enabled
        androidTripDetailsScreen.get().isPickupButtonEnabled();
//        //click on pickup button
       // androidTripDetailsScreen.get().clickOnPickupButton();
//        //click on modal pickup button
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getPickupBtnInModule(),
                "left");
//        //check that delivery button displayed
//        androidTripDetailsScreen.get().isStartDeliveryBtnDisplayed();
//        //start the deleivry
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getStratDelivryElemnt(),
//                "left");
        //click on Display Btn
        androidOrderDeliveryScreen.get().clickOnDisplayBtn();
        //click on order card in map view
        androidMapViewScreen.get().clickOnOrderCard();
        //click on order details button
        androidMapViewScreen.get().clickOnOrderDetailsBtn();
        //SLIDE start delivery
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getStartDeliverySlider(),
                "left");
//        //Slide confirm delivery
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getConfirmDeliverySlider(),
                "left");
//        // slide complete delivery
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getCompleteDeliverySlide(),
                "left");
        // slide cash collection
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getCompleteDeliverySlide(),
                "left");
        //enter amoumt less than order value
        androidCashCollectionScreen.get().enterAmountLessThanTotal();
        //click on confirm button
        androidCashCollectionScreen.get().clickOnConfirmBtn();
        //check cash collection block module
        androidCashCollectionScreen.get().isCollectionBlockModuleAppeared();
    }
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void checkCCFlow() {
        //create an order
//        defaultTestData.set(testExecutionHelper.get().registerUsingApi(defaultTestData.get()));
//        defaultTestData.set(testExecutionHelper.get().createAddressUsingApi(defaultTestData.get()));
//        defaultTestData.get().getRandomTestUser().setTestOrder(
//                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
//                        , "20"
//                        , "0"
//                        , "cod"
//                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
//                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
//                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
//                        , false, false, false, false));
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
//        //create trip and assign order
//        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
//                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getOrdersList().getFirst().getOrderId())));
//        defaultTestData.get().setFleetTrip(fleetAppApiClient.get().getDaTripsAssigned(defaultTestData.get().getDaUser()));
//        // mark order to be packaging
//        orderApiClient.get().markOrderAsPacked(defaultTestData.get().getAdminUser()
//                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), 3);
//        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
//                defaultTestData.get().getRandomTestUser()));
        // click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getStratDelivryElemnt(),
//                "left");
        //click on order dropdown
//        androidTripDetailsScreen.get().clickONOrderDropDown();
//        //check That pickup button enabled
//        androidTripDetailsScreen.get().isPickupButtonEnabled();
//        //click on pickup button
//        androidTripDetailsScreen.get().clickOnPickupButton();
//        //click on modal pickup button
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getPickupBtnInModule(),
//                "left");
//        //check that delivery button displayed
//        androidTripDetailsScreen.get().isStartDeliveryBtnDisplayed();
//        //start the deleivry
//        androidTestsExecutionHelper.get().swipeElementInDirection(
//                androidDriver.get(),
//                androidTripDetailsScreen.get().getStratDelivryElemnt(),
//                "left");
        //click on Display Btn
        androidOrderDeliveryScreen.get().clickOnDisplayBtn();
        //click on order card in map view
        androidMapViewScreen.get().clickOnOrderCard();
        //click on order details button
        androidMapViewScreen.get().clickOnOrderDetailsBtn();
        //SLIDE start delivery
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getStartDeliverySlider(),
                "left");
//        //Slide confirm delivery
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getConfirmDeliverySlider(),
                "left");
//        //slide complete delivery
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getCompleteDeliverySlide(),
                "left");
        // slide cash collection
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidFleetOrderDetailsScreen.get().getCompleteDeliverySlide(),
                "left");
        //click on ok button
        androidCashCollectionScreen.get().clickOnOkBtn();
    }
    @Test (groups = "B10-39792")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void updateDAAvailability() {
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
                fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), configs.get().getdAPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        fleetAppApiClient.get().updateDaStatus(
                defaultTestData.get().getDaUser(), defaultTestData.get().getTestWarehouse().getId(), "available",
                configs.get().getTestLatitude(), configs.get().getTestLongitude());
        Assert.assertTrue(fleetAppApiClient.get().getDaData(defaultTestData.get().getDaUser()).get("status").equals("available"),
                "DA status should be available after update");
    }
    @Test (groups = "B10-39791")
    @Tags({@Tag("android"), @Tag("fleetApp"),@Tag("web"), @Tag("mobile-shopping")})
    public void CheckHandShakeWithPickerFlow() {
        //login
        androidSplashScreen.get().pressLoginBtn();
        androidSplashScreen.get().setTempMobileNumber("mobileNumber");
        androidSplashScreen.get().pressConfirmBtn();
        androidSplashScreen.get().setPassword("lihj50rq");
        androidSplashScreen.get().pressPasswordLoginBtn();
        //start shift
        fleetAppApiClient.get().login(defaultTestData.get().getDaUser(), configs.get().getdAPhoneNumber(), defaultTestData.get().getDaUser().getBypassScriptPassword());
        fleetAppApiClient.get().updateDaStatus(
                defaultTestData.get().getDaUser(), defaultTestData.get().getTestWarehouse().getId(), "available",
                configs.get().getTestLatitude(), configs.get().getTestLongitude());
        Assert.assertTrue(fleetAppApiClient.get().getDaData(defaultTestData.get().getDaUser()).get("status").equals("available"),
                "DA status should be available after update");
        //click on trip card
        androidFleetHomeScreen.get().clickOnTripCard();
        //start the trip
       androidTestsExecutionHelper.get().swipeElementInDirection(
               androidDriver.get(),
              androidTripDetailsScreen.get().getStratTripElemnt(),
              "left");
       //click on order drop dwon
        androidTripDetailsScreen.get().clickONOrderDropDown();
       //click on scan AWB btn
        androidTripDetailsScreen.get().clickOnScanAWBBtn();
        //get AWB value
        androidTripDetailsScreen.get().getAWBValue();
        //click on can't scan AWB Btn
        androidTripDetailsScreen.get().clickOnCantScanCodeBtn();
        //click on AWBtxt field
        //androidTripDetailsScreen.get().clickOnAWBTxtField();
        //enterAWB
        androidTripDetailsScreen.get().enterAWB();
        //click on confirm button
        androidTripDetailsScreen.get().clickOnConfirmBtn();
        //confirm pickup
        androidTestsExecutionHelper.get().swipeElementInDirection(
                androidDriver.get(),
                androidTripDetailsScreen.get().getConfirmPickupBtn(),
                "left");
    }
}
