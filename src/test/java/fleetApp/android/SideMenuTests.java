package fleetApp.android;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class SideMenuTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void CheckSideMenuEntrypoint() {

        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());

        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();

        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),defaultTestData.get().getDaUser().getLocalPhoneNumber());
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());

        androidSplashScreen.get().setPassword(defaultTestData.get().getDaUser().getBypassScriptPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
        androidSideMenuScreen.get().openSideMenu();
        Assert.assertTrue(androidSideMenuScreen.get().isSideMenuLoaded());
        Assert.assertTrue(androidSideMenuScreen.get().arePlaceHolderButtonsDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void CheckCloseButton() {

        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());

        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();

        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),defaultTestData.get().getDaUser().getLocalPhoneNumber());
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());

        androidSplashScreen.get().setPassword(defaultTestData.get().getDaUser().getBypassScriptPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
        androidSideMenuScreen.get().openSideMenu();
        androidSideMenuScreen.get().getDaName();
        androidSideMenuScreen.get().closeSideMenu();
        Assert.assertEquals(androidSplashScreen.get().getEmptyScreenTxt(),"ليس لديك شحنات لتوصيلها");

    }

    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void CheckDaName() {

        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());

        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();

        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),defaultTestData.get().getDaUser().getLocalPhoneNumber());
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());

        androidSplashScreen.get().setPassword(defaultTestData.get().getDaUser().getBypassScriptPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
        androidSideMenuScreen.get().openSideMenu();
        Assert.assertEquals(androidSideMenuScreen.get().getDaName(), "محمد احمد عبد المقصود");

    }

    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void CheckDaHrId() {

        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());

        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();

        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),defaultTestData.get().getDaUser().getLocalPhoneNumber());
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());

        androidSplashScreen.get().setPassword(defaultTestData.get().getDaUser().getBypassScriptPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
        androidSideMenuScreen.get().openSideMenu();
        Assert.assertEquals(androidSideMenuScreen.get().getDaHrId(), "HRID:6564777");

    }

    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void CheckDaScore() {

        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());

        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();

        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),defaultTestData.get().getDaUser().getLocalPhoneNumber());
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());

        androidSplashScreen.get().setPassword(defaultTestData.get().getDaUser().getBypassScriptPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
        androidSideMenuScreen.get().openSideMenu();
        Assert.assertEquals(androidSideMenuScreen.get().getDaScore(), "0.00");

    }

    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void CheckFpNameAssignedToDa() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");

        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());

        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();

        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),defaultTestData.get().getDaUser().getLocalPhoneNumber());
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());

        androidSplashScreen.get().setPassword(defaultTestData.get().getDaUser().getBypassScriptPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
        androidSideMenuScreen.get().openSideMenu();
        Assert.assertEquals(androidSideMenuScreen.get().getfpNameAssigned(), defaultTestData.get().getTestWarehouse().getName());

    }

    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void CheckChangeFpAssignedReflect() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");

        deliveryCapacityManagementApiClient.get().unAssignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "5e2c373d6b874d02cdc83203");

        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());

        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();

        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),defaultTestData.get().getDaUser().getLocalPhoneNumber());
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());

        androidSplashScreen.get().setPassword(defaultTestData.get().getDaUser().getBypassScriptPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
        androidSideMenuScreen.get().openSideMenu();
        Assert.assertEquals(androidSideMenuScreen.get().getfpNameAssigned(), "Maadi FP #1");

        deliveryCapacityManagementApiClient.get().unAssignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "5e2c373d6b874d02cdc83203");

        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

    }

    @Test
    @Tags({@Tag("android"), @Tag("fleetApp")})
    public void CheckBalance() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");

        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

        Assert.assertEquals(androidSplashScreen.get().getLandingScreenText(), "وصل مع بريدفاست");
        Assert.assertTrue(androidSplashScreen.get().isLoginButtonDisplayed());
        androidSplashScreen.get().pressLoginBtn();
        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidSplashScreen.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidSplashScreen.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isConfirmBtnDisabled());

        androidSplashScreen.get().setMobileNumber(defaultTestData.get().getDaUser().getLocalPhoneNumber());
        androidSplashScreen.get().pressConfirmBtn();

        Assert.assertTrue(androidSplashScreen.get().isBackButtonDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidSplashScreen.get().getMobileNumber(),defaultTestData.get().getDaUser().getLocalPhoneNumber());
        Assert.assertTrue(androidSplashScreen.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidSplashScreen.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidSplashScreen.get().isUseAnotherNumberBtnEnabled());

        androidSplashScreen.get().setPassword(defaultTestData.get().getDaUser().getBypassScriptPassword());
        androidSplashScreen.get().pressPasswordLoginBtn();
        androidSideMenuScreen.get().openSideMenu();
        Assert.assertEquals(androidSideMenuScreen.get().getBalance(), "1389");

    }

}
