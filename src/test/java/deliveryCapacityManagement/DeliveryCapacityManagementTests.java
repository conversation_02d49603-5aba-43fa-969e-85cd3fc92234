package deliveryCapacityManagement;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import models.Warehouse;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Collections;
import java.util.stream.Collectors;

@Test
public class DeliveryCapacityManagementTests extends BaseTest {

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void checkMultiFpsAccess() {
        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "fp_manager");

        deliveryCapacityManagementApiClient.get().getUserMongoId(defaultTestData.get().getAdminUser());

        deliveryCapacityManagementApiClient.get().assignSupportManagerToFps(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getWarehousesList().stream()
                        .map(Warehouse::getId)
                        .limit(6)
                        .collect(Collectors.toList()),
                deliveryCapacityManagementApiClient.get().getUserMongoId(defaultTestData.get().getAdminUser()));

        deliveryCapacityManagementApiClient.get().assignedFpstoUser(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getWarehousesList().stream()
                        .map(Warehouse::getId)
                        .limit(6)
                        .collect(Collectors.toList()));

        Assert.assertTrue(
                deliveryCapacityManagementApiClient.get().assignedFpstoUser(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getWarehousesList().stream()
                                .map(Warehouse::getId)
                                .limit(6)
                                .collect(Collectors.toList())));

        webLoginPage.get().goToLoginPage();

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().getFpNames();

        Assert.assertTrue(
                deliveryCapacityManagementApiClient.get().getFpNamesAssigned(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getWarehousesList().stream()
                                .map(Warehouse::getId)
                                .limit(6)
                                .collect(Collectors.toList())).containsAll(
                        webDeliveryCapacityManagementPage.get().getFpNames()));
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping"), @Tag("database")})
    public void checkInstantSlotCapacityAfterPlaceOrder() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "1000");

        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 1000);

        Assert.assertTrue(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId()).getFirst().getInstantFlag());

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false, false));

        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 999);
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "cancelled", false);
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping"), @Tag("database")})
    public void checkScheduledSlotCapacityAfterPlaceOrder() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24Hours(), "1000");

        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 1000);

        Assert.assertFalse(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId()).getFirst().getInstantFlag());

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false, false));

        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 999);
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "cancelled", false);
    }

    @Test(groups = {"checkBusyStatusWhenAllCapacityZero"})
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void checkBusyStatusWhenAllCapacityZero() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId()).getFirst().getWarehouseStatus()
                , "BUSY");

        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "1000");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping"), @Tag("database")})
    public void checkCancelledOrderReturnsCapacity() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "1000");

        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 1000);

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false, false));

        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 999);

        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , "cancelled", false);

        Assert.assertEquals(
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getRandomTestUser().getTestOrder().getWarehouse().getId()).getFirst().
                        getCurrentTimeslotCapacity(), 1000);
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void checkEditingCapacityReflectsInApi() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "1000");

        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 1000);

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void checkExtendedSlotsWhenBusy() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues("00:00-01:00", "1000");

        Assert.assertEquals(
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId()).getFirst().getCurrentTimeslot(), "12:00 AM - 01:00 AM");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void checkAssignDaApiReflectsDcm() {

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");

        deliveryCapacityManagementApiClient.get().assignDaToFp(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), defaultTestData.get().getTestWarehouse().getId());

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        webDeliveryCapacityManagementPage.get().openDaTab();
        Assert.assertTrue(webDeliveryCapacityManagementPage.get().isDaAssigned(
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()).getFullName()));

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void checkAssignDaDashboard() {

        switcherApiClient.get().searchForUser("01000749075", defaultTestData.get().getAdminUser());

        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "driver");

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        webDeliveryCapacityManagementPage.get().openDaTab();
        webDeliveryCapacityManagementPage.get().assignDafromList(switcherApiClient.get()
                .searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()).getFullName());

        Assert.assertTrue(webDeliveryCapacityManagementPage.get().isDaAssigned(switcherApiClient.get()
                .searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()).getFullName()));
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void check24hrToggleEnabled() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enable24hrtoggle(), "24 Hours");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId()).getFirst().getWarehouseStatus()
                , "BUSY");

        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enable24hrtoggle(), "24 Hours");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "1000");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void check24hrToggleDisabled() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertNotEquals(webDeliveryCapacityManagementPage.get().disable24hrtoggle(), "24 Hours");
        Assert.assertTrue(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId()).getFirst().getWarehouseStatus().equals("BUSY")
                || deliveryCapacityManagementApiClient.get().getAllSlotsData(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId()).getFirst().getWarehouseStatus().equals("SLEEP"));

        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enable24hrtoggle(), "24 Hours");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "1000");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void checkExtendedToggleEnabled() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId()).getFirst().getWarehouseStatus()
                , "BUSY");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "1000");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void checkExtendedToggleDisabled() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().disableExtendedHrsToggle(), "DISABLED");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId()).getFirst().getWarehouseStatus()
                , "BUSY");
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "1000");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("mobile-shopping"), @Tag("database")})
    public void checkPlaceOrderFailureZeroCapacity() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId()).getFirst().getWarehouseStatus()
                , "BUSY");
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false, false));
        Assert.assertTrue(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId() == null);
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void checkSingleFpsAccess() {
        switcherApiClient.get().changeUserRole(defaultTestData.get().getAdminUser(),
                switcherApiClient.get().searchForUser(defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "fp_manager");

        deliveryCapacityManagementApiClient.get().assignSupportManagerToFps(defaultTestData.get().getAdminUser(),
                Collections.singletonList(defaultTestData.get().getTestWarehouse().getId()),
                deliveryCapacityManagementApiClient.get().getUserMongoId(defaultTestData.get().getAdminUser()));

        Assert.assertTrue(
                deliveryCapacityManagementApiClient.get().assignedFpstoUser(defaultTestData.get().getAdminUser(),
                        Collections.singletonList(defaultTestData.get().getTestWarehouse().getId())));

        webLoginPage.get().goToLoginPage();

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        webDeliveryCapacityManagementPage.get().goToPage();
        Assert.assertEquals(defaultTestData.get().getTestWarehouse().getName()
                , webDeliveryCapacityManagementPage.get().getSelectedFpName());
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void PlaceOrderFPAvailabilityAfter4H() {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        webDeliveryCapacityManagementPage.get().goToPage();

        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getTestWarehouse().getName());

        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");

        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(5), "100");

        webDeliveryCapacityManagementPage.get().setCapacityValues(
                webDeliveryCapacityManagementPage.get().getScheduledTimeSlot24HoursCalculated(6), "100");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void validateFpETA() {
        jsonPath.set(new JsonPath(deliveryCapacityManagementApiClient.get().postFpEta(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(), 60)
                .then()
                .statusCode(201)
                .extract().asString()
        ));
        Assert.assertTrue(jsonPath.get().getString("success").equals("true"));
        Assert.assertTrue(jsonPath.get().getString("message").equals("Success"));

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void validateEtaSync() {
        jsonPath.set(new JsonPath(deliveryCapacityManagementApiClient.get().postEtaManualSync(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(), "2560", 40)
                .then()
                .statusCode(201)
                .extract().asString()
        ));
        Assert.assertTrue(jsonPath.get().getString("success").equals("true"));
        Assert.assertTrue(jsonPath.get().getString("message").equals("Success"));
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void validateEtaInAvailability() throws InterruptedException {
        deliveryCapacityManagementApiClient.get().postEtaManualSync(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId(), "2560", 40);
        // adding wait time of 3 mins as sync api takes time to reflect in availability
        Thread.sleep(180000);
        // assert that ETA reflects in availability
        jsonPath.set(new JsonPath(deliveryCapacityManagementApiClient.get().postAvailabilityEndpoint(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(), "2560")
                .then()
                .statusCode(201)
                .extract().asString()
        ));
        Assert.assertEquals(jsonPath.get().getInt("data.etaInMinutes"), 40);
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void validateEtaLessThan15InAvailability() throws InterruptedException {
        deliveryCapacityManagementApiClient.get().postEtaManualSync(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId(), "2560", 10);
        // adding wait time of 3 mins as sync api takes time to reflect in availability
        Thread.sleep(180000);

        jsonPath.set(new JsonPath(deliveryCapacityManagementApiClient.get().postAvailabilityEndpoint(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(), "2560")
                .then()
                .statusCode(201)
                .extract().asString()
        ));
        Assert.assertEquals(jsonPath.get().getInt("data.etaInMinutes"), 15);
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management"), @Tag("database")})
    public void validateEtaMoreThan60InAvailability() throws InterruptedException {
        deliveryCapacityManagementApiClient.get().postEtaManualSync(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId(), "2560", 70);
        // adding wait time of 3 mins as sync api takes time to reflect in availability
        Thread.sleep(180000);

        jsonPath.set(new JsonPath(deliveryCapacityManagementApiClient.get().postAvailabilityEndpoint(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(), "2560")
                .then()
                .statusCode(201)
                .extract().asString()
        ));
        Assert.assertEquals(jsonPath.get().getInt("data.etaInMinutes"), 60);
    }
}
