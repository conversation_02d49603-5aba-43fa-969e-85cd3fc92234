package Transit;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.List;

public class TransitApiTests extends BaseTest {
    @Test(groups = {"inventory"})
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateGetTransferResponse() {
        jsonPath.set(new JsonPath(transitApiClient.get().getTransfersResponse(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(), 0, new String[]{"1", "2", "3"})
                .then()
                .statusCode(200)
                .extract().asString())
        );
        Assert.assertEquals(jsonPath.get().getString("status"), "success");
    }

    @Test
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateGetTransferDetailsResponse() {
        transitApiClient.get().getTransferDetailsResponse(defaultTestData.get().getAdminUser(),
                transitApiClient.get().getAllTransfers(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId()).getFirst());
        Assert.assertFalse(
                transitApiClient.get().getAllTransfers(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId()).isEmpty()
                , "The transfer list should not be empty.");
    }

    @Test
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateGetTransfersCountResponse() {
        jsonPath.set(new JsonPath(transitApiClient.get().getTransfersCountResponse(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse())
                .then()
                .statusCode(200)
                .extract().asString())
        );
        Assert.assertEquals(jsonPath.get().getString("status"), "success");
        Assert.assertNotNull(jsonPath.get().getString("payload.notSent"));
        Assert.assertNotNull(jsonPath.get().getString("payload.sent"));
    }

    @Test
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateTransferCountEqualNumberOfTransfers() {
        defaultTestData.get().setNotSentTransfersCount(transitApiClient.get().getNotSentTransfersCount(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse()));
        defaultTestData.get().setSentTransfersCount(transitApiClient.get().getSentTransfersCount(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse()));
        Assert.assertEquals(transitApiClient.get().getSumNumberOfTransfers(defaultTestData.get().getAdminUser(), defaultTestData.get().getTestWarehouse()),
                defaultTestData.get().getNotSentTransfersCount() + defaultTestData.get().getSentTransfersCount());
    }

    @Test(enabled = false)
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateDeleteOneProductLineFromTransfer() {
        defaultTestData.get().getTransfer().
                setId(transitApiClient.get().getNumberOfProductsEqualOne(
                        defaultTestData.get().getAdminUser(), defaultTestData.get().getTestWarehouse().getId()).getId());

        defaultTestData.get().getTransfer().setProductLineID(transitApiClient.get().
                getTransferDetailsResponse(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTransfer()).getProductLineID());

        transitApiClient.get().deleteTransferProductLine(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTransfer());
        Assert.assertEquals(transitApiClient.get().deleteTransferProductLine(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTransfer()).jsonPath().getString("message"), "Can't perform operation on transfer of id " + defaultTestData.get().getTransfer().getId() + " with status Deleted");
    }

    @Test(enabled = false)
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateDeleteMoreThanOneProductLineFromTransfer() {
        defaultTestData.get().getTransfer().
                setId(transitApiClient.get().getNumberOfProductsMoreThanOne(
                        defaultTestData.get().getAdminUser(), defaultTestData.get().getTestWarehouse().getId()).getId());

        defaultTestData.get().getTransfer().setProductLineID(transitApiClient.get().
                getTransferDetailsResponse(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTransfer()).getProductLineID());

        transitApiClient.get().deleteTransferHaveMoreThanOneProduct(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTransfer());

        Assert.assertNotNull(transitApiClient.get().
                getTransferDetailsResponse(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTransfer()));
    }

    @Test(enabled = false)
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateSendTransfer() {
        jsonPath.set(new JsonPath(transitApiClient.get().sendTransfer(String.valueOf(
                                transitApiClient.get().getAllNotSentTransfers(
                                                defaultTestData.get().getAdminUser(),
                                                defaultTestData.get().getTestWarehouse().getId()
                                        )
                                        .getFirst()
                                        .getId()
                        ),
                        defaultTestData.get().getAdminUser()
                )
                .then()
                .statusCode(201)
                .extract().asString()
        ));
        Assert.assertNotNull(jsonPath.get().get("payload.isSplitted"), "'isSplitted' should not be null");
        if (jsonPath.get().getBoolean("payload.isSplitted")) {
            System.out.println("The transfer is split");
            Assert.assertTrue(jsonPath.get().getBoolean("payload.isSplitted"), "should be true");
        } else {
            Assert.assertFalse(jsonPath.get().getBoolean("payload.isSplitted"), " should be false");
        }
    }

    @Test(enabled = false)
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateChangeDestination() {
        defaultTestData.get().setSentTransfersCount(
                transitApiClient.get().getSentTransfersCount(defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getTestWarehouse()));

        jsonPath.set(new JsonPath(transitApiClient.get().changeDestination(defaultTestData.get().getAdminUser(),
                        transitApiClient.get().getAllSentTransfers(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getTestWarehouse().getId()).getFirst()
                        , defaultTestData.get().getTestWarehouse()
                        , transitApiClient.get().destinationLocationsList(
                                defaultTestData.get().getAdminUser()).getFirst())
                .then()
                .extract().body().asString()));

        Assert.assertEquals(defaultTestData.get().getSentTransfersCount(),
                transitApiClient.get().getSentTransfersCount(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse()));
    }

    @Test(enabled = false)
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateChangeDestinationOfNotSentTransfer(){
        defaultTestData.get().getTransfer().
                setId(transitApiClient.get().getNumberOfProductsEqualOne(
                        defaultTestData.get().getAdminUser(),defaultTestData.get().getTestWarehouse().getId()).getId());

        defaultTestData.get().getTransfer().setProductLineID(transitApiClient.get().
                getTransferDetailsResponse(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTransfer()).getProductLineID());

        jsonPath.set(new JsonPath( transitApiClient.get().changeDestinationOfNotSentTransfer(defaultTestData.get().getAdminUser(),
               defaultTestData.get().getTestWarehouse(),
                defaultTestData.get().getTransfer(),
                transitApiClient.get().destinationLocationsList(defaultTestData.get().getAdminUser()).getFirst())
                .then()
                .extract().body().asString()));

        Assert.assertEquals(jsonPath.get().getString("status"), "success");
        Assert.assertEquals(jsonPath.get().getString("payload.isDeleted"),"true");
        Assert.assertEquals(jsonPath.get().getString("payload.originalTransferId"),String.valueOf(defaultTestData.get().getTransfer().getId()));
    }

    @Test
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateSearchingForValidTransfer(){
        jsonPath.set(new JsonPath( transitApiClient.get().searchForTransfer(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId(),transitApiClient.get().getAllTransfers(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId()).getFirst())
                .then()
                .extract().asString()));
        Assert.assertEquals(jsonPath.get().getString("status"), "success");
        Assert.assertNotNull(jsonPath.get().getString("payload.transfers"));
    }

    @Test(enabled = false)
    @Tags({@Tag("api"), @Tag("transit")})
    public void validateSearchingForDeletedTransfer() {
        defaultTestData.get().getTransfer().
                setId(transitApiClient.get().getNumberOfProductsEqualOne(
                        defaultTestData.get().getAdminUser(), defaultTestData.get().getTestWarehouse().getId()).getId());

        defaultTestData.get().getTransfer().setProductLineID(transitApiClient.get().
                getTransferDetailsResponse(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTransfer()).getProductLineID());

        transitApiClient.get().deleteTransferProductLine(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTransfer());

        jsonPath.set(new JsonPath( transitApiClient.get().searchForTransfer(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestWarehouse().getId(),defaultTestData.get().getTransfer())
                .then()
                .extract().asString()));

        Assert.assertEquals(jsonPath.get().getString("status"), "success");
        List<Object> transfers = jsonPath.get().getList("payload.transfers");
        Assert.assertTrue(transfers.isEmpty(), "Transfers list should be empty");
    }
}
