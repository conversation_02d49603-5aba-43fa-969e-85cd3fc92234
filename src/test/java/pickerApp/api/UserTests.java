package pickerApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Collections;
import java.util.Random;

@Test
public class UserTests extends BaseTest {

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkLoginWithValidCredentials() {

        Assert.assertEquals(pickerAppApiClient.get().login(defaultTestData.get().getPickerUser(), defaultTestData.get().getPickerUser().getLocalPhoneNumber(),
                        defaultTestData.get().getPickerUser().getBypassScriptPassword())
                .get("statusCode"), 200);
        Assert.assertNotNull(defaultTestData.get().getPickerUser().getAuthorizationToken());

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkLoginWithWrongPhone() {

        Assert.assertEquals(pickerAppApiClient.get().login(defaultTestData.get().getPickerUser(), "01234447890",
                        defaultTestData.get().getPickerUser().getBypassScriptPassword())
                .get("message"), "يجب إدخال رقم الهاتف المسجل على بريدفاست");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkLoginWithWrongPassword() {

        Assert.assertEquals(pickerAppApiClient.get().login(defaultTestData.get().getPickerUser(), defaultTestData.get().getPickerUser().getLocalPhoneNumber(),
                        "asdfasdfasdf")
                .get("message"), "خطأ في كلمة المرور");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkWrongRole() {

        Assert.assertEquals(pickerAppApiClient.get().login(defaultTestData.get().getPickerUser(), defaultTestData.get().getMidMileUser().getLocalPhoneNumber(),
                        defaultTestData.get().getMidMileUser().getBypassScriptPassword())
                .get("message"), "عفوًا، ليس لديك التصريح اللازم لتسجيل الدخول");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckUnassignedPickerToFp() {

        Assert.assertEquals(pickerAppApiClient.get().login(defaultTestData.get().getDaUser(), defaultTestData.get().getDaUser().getLocalPhoneNumber(),
                        defaultTestData.get().getDaUser().getBypassScriptPassword())
                .get("statusCode"), 200);

        Assert.assertEquals(pickerAppApiClient.get().changePickerStatus(defaultTestData.get().getDaUser(), defaultTestData.get().getTestDeviceLongitude(),
                defaultTestData.get().getTestDeviceLatitude(),
                "en", "busy").get("message"), "عفوًا، يجب إضافتك لأحد فرق عمل نقاط التوزيع أولًا");
    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkAssigningOrderWhenAvailable() {

        Assert.assertEquals(pickerAppApiClient.get().login(defaultTestData.get().getPickerUser(), defaultTestData.get().getPickerUser().getLocalPhoneNumber(),
                        defaultTestData.get().getPickerUser().getBypassScriptPassword())
                .get("statusCode"), 200);

        Assert.assertEquals(pickerAppApiClient.get().changePickerStatus(defaultTestData.get().getPickerUser(), defaultTestData.get().getTestDeviceLongitude(),
                defaultTestData.get().getTestDeviceLatitude(), "en", "busy").get("data"), "busy");

        Assert.assertEquals(pickerAppApiClient.get().changePickerStatus(defaultTestData.get().getPickerUser(), defaultTestData.get().getTestDeviceLongitude(),
                defaultTestData.get().getTestDeviceLatitude(), "en", "available").get("data"), "available");

        Assert.assertEquals(pickerAppApiClient.get().assignOrderToPicker(defaultTestData.get().getAdminUser(),
                        switcherApiClient.get().searchForUser(defaultTestData.get().getAdminUser().getLocalPhoneNumber(),
                                defaultTestData.get().getAdminUser()).getId(),

                        switcherApiClient.get().searchForUser(defaultTestData.get().getPickerUser().getLocalPhoneNumber(),
                                defaultTestData.get().getAdminUser()).getId(), Collections.singletonList(defaultTestData.get().getOrdersList().get(3)))
                .get("statusCode"), 201);

    }

    @Test
    @Tags({@Tag("web"), @Tag("delivery-capacity-management")})
    public void checkAssigningOrderWhenBusy() {

        Assert.assertEquals(pickerAppApiClient.get().login(defaultTestData.get().getPickerUser(), defaultTestData.get().getPickerUser().getLocalPhoneNumber(),
                        defaultTestData.get().getPickerUser().getBypassScriptPassword())
                .get("statusCode"), 200);

        Assert.assertEquals(pickerAppApiClient.get().changePickerStatus(defaultTestData.get().getPickerUser(), defaultTestData.get().getTestDeviceLongitude(),
                defaultTestData.get().getTestDeviceLatitude(), "en", "busy").get("data"), "busy");

        Assert.assertEquals(pickerAppApiClient.get().assignOrderToPicker(defaultTestData.get().getAdminUser(),
                        switcherApiClient.get().searchForUser(defaultTestData.get().getAdminUser().getLocalPhoneNumber(),
                                defaultTestData.get().getAdminUser()).getId(),

                        switcherApiClient.get().searchForUser(defaultTestData.get().getPickerUser().getLocalPhoneNumber(),
                                defaultTestData.get().getAdminUser()).getId(),  Collections.singletonList(defaultTestData.get().getOrdersList().get(3)))
                .get("message"), "Picker status is not available");

        Assert.assertEquals(pickerAppApiClient.get().changePickerStatus(defaultTestData.get().getPickerUser(), defaultTestData.get().getTestDeviceLongitude(),
                defaultTestData.get().getTestDeviceLatitude(), "en", "available").get("data"), "available");
    }

}
