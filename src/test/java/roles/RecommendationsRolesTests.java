package roles;

import base.BaseTest;
import helpers.dataProviders.RolesDataProviderSource;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class RecommendationsRolesTests extends BaseTest {
    @Test(dataProvider = "recommendationsRolesMap", dataProviderClass = RolesDataProviderSource.class)
    public void validateRecommendationsPageRoleFunctionAccess(String roleName, String pageAccess){
        defaultTestData.set(testExecutionHelper.get().buildBurnerUserData(defaultTestData.get(), roleName,
                dataHelper.get(), webLoginPage.get(), webHomePage.get(), webUsersListPage.get(),
                rolesList.get(), setUpHelper.get()));

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getBurnerUser().getPhoneNumber()
                , defaultTestData.get().getBurnerUser().getBypassScriptPassword());

        //Validate that home page is opened
        webHomePage.get().isMoreBtnDisplayed();

        //Go to target page
        webRecommendationsAdminPage.get().goToPage();
        webRecommendationsAdminPage.get().isPageDisplayed();

        boolean isPageAccess = pageAccess.equalsIgnoreCase("true");

        validationResults.set(
                recommendationsRolesValidators.get().validateRecommendationsPageAccessPerRole(validationResults.get(),
                        webRecommendationsAdminPage.get(), roleName, isPageAccess));

        Assert.assertTrue(validationResults.get().isResult()
                , "Validation failed for role: " + roleName +
                        "\nYou can find the detailed validations below\n"
                        + validationResults.get().getValidationResults().toString());
    }
}
