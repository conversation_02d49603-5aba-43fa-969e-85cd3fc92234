package roles;

import base.BaseTest;
import helpers.dataProviders.RolesDataProviderSource;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class BannersRolesTests extends BaseTest {
    @Test(dataProvider = "bannersRolesMap", dataProviderClass = RolesDataProviderSource.class)
    public void validateBannersRoleFunctionAccess(String roleName, String pageAccess,
                                                  String sortBanners, String createBanners, String editBanners,
                                                  String deleteBanners){
        defaultTestData.set(testExecutionHelper.get().buildBurnerUserData(defaultTestData.get(), roleName,
                dataHelper.get(), webLoginPage.get(), webHomePage.get(), webUsersListPage.get(),
                rolesList.get(), setUpHelper.get()));

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getBurnerUser().getPhoneNumber()
                , defaultTestData.get().getBurnerUser().getBypassScriptPassword());

        //Validate that home page is opened
        webHomePage.get().isMoreBtnDisplayed();

        //Go to target page
        webBannersAdminPage.get().goToPage();
        webBannersAdminPage.get().isPageDisplayed();

        boolean isPageAccess = pageAccess.equalsIgnoreCase("true");
        boolean isSortAccess = sortBanners.equalsIgnoreCase("true");
        boolean isCreateAccess = createBanners.equalsIgnoreCase("true");
        boolean isEditAccess = editBanners.equalsIgnoreCase("true");
        boolean isDeleteAccess = deleteBanners.equalsIgnoreCase("true");

        validationResults.set(
                bannersRolesValidators.get()
                        .validateBannersPageAccessPerRole(validationResults.get(),
                                webBannersAdminPage.get(), roleName, isPageAccess, isCreateAccess, isSortAccess,
                                isEditAccess, isDeleteAccess));

        Assert.assertTrue(validationResults.get().isResult()
                , "Validation failed for role: " + roleName +
                        "\nYou can find the detailed validations below\n"
                        + validationResults.get().getValidationResults().toString());
    }
}
