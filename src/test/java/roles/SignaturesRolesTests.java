package roles;

import base.BaseTest;
import helpers.dataProviders.RolesDataProviderSource;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class SignaturesRolesTests extends BaseTest {
    @Test(dataProvider = "signatureRolesMap", dataProviderClass = RolesDataProviderSource.class)
    public void validateSignaturesRoleFunctionAccess(String roleName, String pageAccess){
        defaultTestData.set(testExecutionHelper.get().buildBurnerUserData(defaultTestData.get(), roleName,
                dataHelper.get(), webLoginPage.get(), webHomePage.get(), webUsersListPage.get(),
                rolesList.get(), setUpHelper.get()));

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getBurnerUser().getPhoneNumber()
                , defaultTestData.get().getBurnerUser().getBypassScriptPassword());

        //Validate that home page is opened
        webHomePage.get().isMoreBtnDisplayed();

        //Go to target page
        webSignatureAdminPage.get().goToPage();
        webSignatureAdminPage.get().isPageDisplayed();

        boolean isPageAccess = pageAccess.equalsIgnoreCase("true");

        validationResults.set(
                signaturesRolesValidators.get().validateSignatureAccessPerRole(validationResults.get(),
                        webSignatureAdminPage.get(), roleName, isPageAccess));

        Assert.assertTrue(validationResults.get().isResult()
                , "Validation failed for role: " + roleName +
                        "\nYou can find the detailed validations below\n"
                        + validationResults.get().getValidationResults().toString());
    }
}
