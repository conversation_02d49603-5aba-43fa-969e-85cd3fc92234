package roles;

import base.BaseTest;
import helpers.dataProviders.RolesDataProviderSource;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class PostsRolesTests extends BaseTest {
    @Test(dataProvider = "postsRolesMap", dataProviderClass = RolesDataProviderSource.class)
    public void validatePostsRoleFunctionAccess(String roleName, String pageAccess){
        defaultTestData.set(testExecutionHelper.get().buildBurnerUserData(defaultTestData.get(), roleName,
                dataHelper.get(), webLoginPage.get(), webHomePage.get(), webUsersListPage.get(),
                rolesList.get(), setUpHelper.get()));

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getBurnerUser().getPhoneNumber()
                , defaultTestData.get().getBurnerUser().getBypassScriptPassword());

        //Validate that home page is opened
        webHomePage.get().isMoreBtnDisplayed();

        //Go to target page
        webPostsAdminPage.get().goToPage();
        webPostsAdminPage.get().isPageDisplayed();

        boolean isPageAccess = pageAccess.equalsIgnoreCase("true");

        validationResults.set(
                postsRolesValidators.get().validatePostsAccessPerRole(validationResults.get(),
                        webPostsAdminPage.get(), roleName, isPageAccess));

        Assert.assertTrue(validationResults.get().isResult()
                , "Validation failed for role: " + roleName +
                        "\nYou can find the detailed validations below\n"
                        + validationResults.get().getValidationResults().toString());
    }
}
