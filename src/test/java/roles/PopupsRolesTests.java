package roles;

import base.BaseTest;
import helpers.dataProviders.RolesDataProviderSource;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class PopupsRolesTests extends BaseTest {
    @Test(dataProvider = "popupsRolesMap", dataProviderClass = RolesDataProviderSource.class)
    public void validatePopupsRoleFunctionAccess(String roleName, String pageAccess, String createPopups,
                                                  String editPopups, String deletePopups){
        defaultTestData.set(testExecutionHelper.get().buildBurnerUserData(defaultTestData.get(), roleName,
                dataHelper.get(), webLoginPage.get(), webHomePage.get(), webUsersListPage.get(),
                rolesList.get(), setUpHelper.get()));

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getBurnerUser().getPhoneNumber()
                , defaultTestData.get().getBurnerUser().getBypassScriptPassword());

        //Validate that home page is opened
        webHomePage.get().isMoreBtnDisplayed();

        //Go to target page
        webPopUpsAdminPage.get().goToPage();
        webPopUpsAdminPage.get().isPageDisplayed();

        boolean isPageAccess = pageAccess.equalsIgnoreCase("true");
        boolean isCreateAccess = createPopups.equalsIgnoreCase("true");
        boolean isEditAccess = editPopups.equalsIgnoreCase("true");
        boolean isDeleteAccess = deletePopups.equalsIgnoreCase("true");

        validationResults.set(
                popupsRolesValidators.get()
                        .validatePopupsPageAccessPerRole(validationResults.get(),
                                webPopUpsAdminPage.get(), roleName, isPageAccess, isCreateAccess, isEditAccess,
                                isDeleteAccess));

        Assert.assertTrue(validationResults.get().isResult()
                , "Validation failed for role: " + roleName +
                        "\nYou can find the detailed validations below\n"
                        + validationResults.get().getValidationResults().toString());
    }
}
