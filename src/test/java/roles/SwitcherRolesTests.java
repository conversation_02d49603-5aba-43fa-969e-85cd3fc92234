package roles;

import base.BaseTest;
import helpers.dataProviders.RolesDataProviderSource;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class SwitcherRolesTests extends BaseTest {
    @Test(dataProvider = "switcherRolesMap", dataProviderClass = RolesDataProviderSource.class)
    public void validateSwitcherPageRoleFunctionAccess(String roleName, String pageAccess, String updateBalance,
                                                      String changePassword, String blockUser, String deleteUser,
                                                      String updateRoles, String updateUserData,
                                                      String updateAddress, String viewUserInfo, String addNewUser){

        defaultTestData.set(testExecutionHelper.get().buildBurnerUserData(defaultTestData.get(), roleName,
                dataHelper.get(), webLoginPage.get(), webHomePage.get(), webUsersListPage.get(),
                rolesList.get(), setUpHelper.get()));

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getBurnerUser().getPhoneNumber()
                , defaultTestData.get().getBurnerUser().getBypassScriptPassword());

        //Validate that home page is opened
        webHomePage.get().isMoreBtnDisplayed();

        //Go To target page
        webSwitcherPage.get().goToSwitcherPage();
        webSwitcherPage.get().isPageDisplayed();

        //Search for the burner admin credentials
        webSwitcherPage.get().openSelectUserDropdown();
        webSwitcherPage.get().searchForPhoneNumberAndSelectUser(defaultTestData.get().getBurnerUser().getPhoneNumber());

        boolean isPageAccess = pageAccess.equalsIgnoreCase("true");
        boolean isUpdateBalance = updateBalance.equalsIgnoreCase("true");
        boolean isChangePassword = changePassword.equalsIgnoreCase("true");
        boolean isBlockUser = blockUser.equalsIgnoreCase("true");
        boolean isDeleteUser = deleteUser.equalsIgnoreCase("true");
        boolean isUpdateRoles = updateRoles.equalsIgnoreCase("true");
        boolean isUpdateUserData = updateUserData.equalsIgnoreCase("true");
        boolean isUpdateAddress = updateAddress.equalsIgnoreCase("true");
        boolean isViewUserInfo = viewUserInfo.equalsIgnoreCase("true");
        boolean isAddNewUser = addNewUser.equalsIgnoreCase("true");

        validationResults.set(
                switcherRolesValidators.get().validateSwitcherPageAccessPerRole(validationResults.get(),
                webSwitcherPage.get(), roleName, isPageAccess, isUpdateBalance, isChangePassword, isBlockUser,
                isDeleteUser, isUpdateRoles, isUpdateUserData, isUpdateAddress, isViewUserInfo, isAddNewUser));

        Assert.assertTrue(validationResults.get().isResult()
                , "Validation failed for role: " + roleName +
                        "\nYou can find the detailed validations below\n"
                        + validationResults.get().getValidationResults().toString());
    }
}
