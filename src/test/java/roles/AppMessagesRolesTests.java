package roles;

import base.BaseTest;
import helpers.dataProviders.RolesDataProviderSource;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class AppMessagesRolesTests extends BaseTest {
    @Test(dataProvider = "appMessagesRolesMap", dataProviderClass = RolesDataProviderSource.class)
    public void validateAppMessagesRoleFunctionAccess(String roleName, String pageAccess){
        defaultTestData.set(testExecutionHelper.get().buildBurnerUserData(defaultTestData.get(), roleName,
                dataHelper.get(), webLoginPage.get(), webHomePage.get(), webUsersListPage.get(),
                rolesList.get(), setUpHelper.get()));

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getBurnerUser().getPhoneNumber()
                , defaultTestData.get().getBurnerUser().getBypassScriptPassword());

        //Validate that home page is opened
        webHomePage.get().isMoreBtnDisplayed();

        //Go to target page
        webAppMessagesAdminPage.get().goToPage();
        webAppMessagesAdminPage.get().isPageDisplayed();

        boolean isPageAccess = pageAccess.equalsIgnoreCase("true");

        validationResults.set(
                appMessagesRolesValidators.get().validateAppMessagesAccessPerRole(validationResults.get(),
                        webAppMessagesAdminPage.get(), roleName, isPageAccess));

        Assert.assertTrue(validationResults.get().isResult()
                , "Validation failed for role: " + roleName +
                        "\nYou can find the detailed validations below\n"
                        + validationResults.get().getValidationResults().toString());
    }
}
