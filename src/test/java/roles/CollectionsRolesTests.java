package roles;

import base.BaseTest;
import helpers.dataProviders.RolesDataProviderSource;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class CollectionsRolesTests extends BaseTest {
    @Test(dataProvider = "collectionsRolesMap", dataProviderClass = RolesDataProviderSource.class)
    public void validateCollectionsRoleFunctionAccess(String roleName, String pageAccess,
                                                      String createCollections, String editCollections,
                                                      String sortCollections, String deleteCollections){
        defaultTestData.set(testExecutionHelper.get().buildBurnerUserData(defaultTestData.get(), roleName,
                dataHelper.get(), webLoginPage.get(), webHomePage.get(), webUsersListPage.get(),
                rolesList.get(), setUpHelper.get()));

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getBurnerUser().getPhoneNumber()
                , defaultTestData.get().getBurnerUser().getBypassScriptPassword());

        //Validate that home page is opened
        webHomePage.get().isMoreBtnDisplayed();

        //Go to target page
        webCollectionsAdminPage.get().goToPage();
        webCollectionsAdminPage.get().isPageDisplayed();

        boolean isPageAccess = pageAccess.equalsIgnoreCase("true");
        boolean isSortAccess = sortCollections.equalsIgnoreCase("true");
        boolean isCreateAccess = createCollections.equalsIgnoreCase("true");
        boolean isEditAccess = editCollections.equalsIgnoreCase("true");
        boolean isDeleteAccess = deleteCollections.equalsIgnoreCase("true");

        validationResults.set(
                collectionsRolesValidators.get()
                        .validateCollectionsPageAccessPerRole(validationResults.get(),
                                webCollectionsAdminPage.get(), roleName, isPageAccess, isCreateAccess, isSortAccess,
                                isEditAccess, isDeleteAccess));

        Assert.assertTrue(validationResults.get().isResult()
                , "Validation failed for role: " + roleName +
                        "\nYou can find the detailed validations below\n"
                        + validationResults.get().getValidationResults().toString());
    }
}
