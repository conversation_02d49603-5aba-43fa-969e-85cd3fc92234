package roles;

import base.BaseTest;
import helpers.dataProviders.RolesDataProviderSource;
import org.testng.Assert;
import org.testng.annotations.Test;

public class CCDiscountRolesTests extends BaseTest {
    @Test(dataProvider = "ccDiscountRolesMap", dataProviderClass = RolesDataProviderSource.class)
    public void validateCCDiscountRoleFunctionAccess(String roleName, String pageAccess){
        defaultTestData.set(testExecutionHelper.get().buildBurnerUserData(defaultTestData.get(), roleName,
                dataHelper.get(), webLoginPage.get(), webHomePage.get(), webUsersListPage.get(),
                rolesList.get(), setUpHelper.get()));

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getBurnerUser().getPhoneNumber()
                , defaultTestData.get().getBurnerUser().getBypassScriptPassword());

        //Validate that home page is opened
        webHomePage.get().isMoreBtnDisplayed();

        //Go to target page
        webCCDiscountAdminPage.get().goToPage();
        webCCDiscountAdminPage.get().isPageDisplayed();

        boolean isPageAccess = pageAccess.equalsIgnoreCase("true");

        validationResults.set(
                ccDiscountRolesValidators.get().validateCCDiscountAccessPerRole(validationResults.get(),
                        webCCDiscountAdminPage.get(), roleName, isPageAccess));

        Assert.assertTrue(validationResults.get().isResult()
                , "Validation failed for role: " + roleName +
                        "\nYou can find the detailed validations below\n"
                        + validationResults.get().getValidationResults().toString());
    }
}
