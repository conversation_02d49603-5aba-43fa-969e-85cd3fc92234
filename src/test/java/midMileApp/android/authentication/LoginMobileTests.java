package midMileApp.android.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import org.testng.Assert;
import org.testng.annotations.Test;

public class LoginMobileTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void validateLoggingInWithValidCredentialsIsWorking() {
        //Open the midMile app
        Assert.assertEquals(androidLoginPage.get().getLandingScreenText(), "بريدفاست للموزعين");
        Assert.assertTrue(androidLoginPage.get().isLoginButtonDisplayed());
        androidLoginPage.get().pressLoginBtn();
        //Navigate to the login page
        Assert.assertTrue(androidLoginPage.get().isBackButtonDisplayed());
        Assert.assertEquals(androidLoginPage.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidLoginPage.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidLoginPage.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidLoginPage.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidLoginPage.get().isConfirmBtnDisabled());
        //Enter the valid mobile number
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        //Navigate to the password page
        Assert.assertTrue(androidLoginPage.get().isBackButtonDisplayed());
        Assert.assertEquals(androidLoginPage.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidLoginPage.get().getMobileNumber(),defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        Assert.assertTrue(androidLoginPage.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidLoginPage.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidLoginPage.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidLoginPage.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidLoginPage.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidLoginPage.get().isUseAnotherNumberBtnEnabled());
        //Enter the valid password
        androidLoginPage.get().setPassword(defaultTestData.get().getMidMileUser().getBypassScriptPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void loginWithWrongNumber() {
        //Open the midMile app
        Assert.assertEquals(androidLoginPage.get().getLandingScreenText(), "بريدفاست للموزعين");
        Assert.assertTrue(androidLoginPage.get().isLoginButtonDisplayed());
        androidLoginPage.get().pressLoginBtn();
        //Navigate to the login page
        Assert.assertTrue(androidLoginPage.get().isBackButtonDisplayed());
        Assert.assertEquals(androidLoginPage.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidLoginPage.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidLoginPage.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidLoginPage.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidLoginPage.get().isConfirmBtnDisabled());
        //Enter wrong number format
        androidLoginPage.get().setMobileNumber("12345678912");
        androidLoginPage.get().pressConfirmBtn();
        Assert.assertEquals(androidLoginPage.get().getWrongNumberTxt(),"يجب إدخال رقم صحيح");
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void loginWithWrongPassword() {
        //Open the midMile app
        Assert.assertEquals(androidLoginPage.get().getLandingScreenText(), "بريدفاست للموزعين");
        Assert.assertTrue(androidLoginPage.get().isLoginButtonDisplayed());
        androidLoginPage.get().pressLoginBtn();
        //Navigate to the login page
        Assert.assertTrue(androidLoginPage.get().isBackButtonDisplayed());
        Assert.assertEquals(androidLoginPage.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidLoginPage.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidLoginPage.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidLoginPage.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidLoginPage.get().isConfirmBtnDisabled());
        //Enter the valid mobile number
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        //Navigate to the password page
        Assert.assertTrue(androidLoginPage.get().isBackButtonDisplayed());
        Assert.assertEquals(androidLoginPage.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidLoginPage.get().getMobileNumber(),defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        Assert.assertTrue(androidLoginPage.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidLoginPage.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidLoginPage.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidLoginPage.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidLoginPage.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidLoginPage.get().isUseAnotherNumberBtnEnabled());
        //Enter the invalid password
        androidLoginPage.get().setPassword(defaultTestData.get().getRandomTestUser().getEmailPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
        Assert.assertEquals(androidLoginPage.get().getWrongPasswordTxt(),"كلمة المرور غير صحيحة");
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void CheckEmptyStateScreen() {
        //Open the midMile app
        Assert.assertEquals(androidLoginPage.get().getLandingScreenText(), "بريدفاست للموزعين");
        Assert.assertTrue(androidLoginPage.get().isLoginButtonDisplayed());
        androidLoginPage.get().pressLoginBtn();
        //Navigate to the login page
        Assert.assertTrue(androidLoginPage.get().isBackButtonDisplayed());
        Assert.assertEquals(androidLoginPage.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidLoginPage.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidLoginPage.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidLoginPage.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidLoginPage.get().isConfirmBtnDisabled());
        //Enter the valid mobile number
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        //Navigate to the password page
        Assert.assertTrue(androidLoginPage.get().isBackButtonDisplayed());
        Assert.assertEquals(androidLoginPage.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidLoginPage.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidLoginPage.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidLoginPage.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidLoginPage.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidLoginPage.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidLoginPage.get().isUseAnotherNumberBtnEnabled());
        //Enter the valid password
        androidLoginPage.get().setPassword(defaultTestData.get().getMidMileUser().getBypassScriptPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
        //Check empty state screen
        Assert.assertEquals(androidLoginPage.get().getEmptyScreenTxt(),"ليس لديك شحنات لتوصيلها");
        Assert.assertEquals(androidLoginPage.get().getEmptySecondScreenTxt(),"الشحنات المطلوب منك توصيلها سوف تظهر هنا");
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void validateLoggingOutIsWorking() {
        //Open the midMile app
        Assert.assertEquals(androidLoginPage.get().getLandingScreenText(), "بريدفاست للموزعين");
        Assert.assertTrue(androidLoginPage.get().isLoginButtonDisplayed());
        androidLoginPage.get().pressLoginBtn();
        //Navigate to the login page
        Assert.assertTrue(androidLoginPage.get().isBackButtonDisplayed());
        Assert.assertEquals(androidLoginPage.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidLoginPage.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidLoginPage.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidLoginPage.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidLoginPage.get().isConfirmBtnDisabled());
        //Enter the valid mobile number
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        //Navigate to the password page
        Assert.assertTrue(androidLoginPage.get().isBackButtonDisplayed());
        Assert.assertEquals(androidLoginPage.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertEquals(androidLoginPage.get().getMobileNumber(),defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        Assert.assertTrue(androidLoginPage.get().isPasswordFieldDisplayed());
        Assert.assertEquals(androidLoginPage.get().getPassword(),"كلمة المرور");
        Assert.assertTrue(androidLoginPage.get().isPasswordLoginBtnDisplayed());
        Assert.assertTrue(androidLoginPage.get().isPasswordLoginBtnDisabled());
        Assert.assertTrue(androidLoginPage.get().isUseAnotherNumberBtnDisplayed());
        Assert.assertTrue(androidLoginPage.get().isUseAnotherNumberBtnEnabled());
        //Enter the valid password
        androidLoginPage.get().setPassword(defaultTestData.get().getMidMileUser().getBypassScriptPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
        //Click on menu tab
        androidLoginPage.get().pressMenuTab();
        Assert.assertEquals(androidLoginPage.get().getDispatcherName(), midMileAppApiClient.get().getMidMileUserObject(defaultTestData.get().getMidMileUser()).getFullName());
        Assert.assertTrue(androidLoginPage.get().isHrIdDisplayed());
        //Check logout flow
        androidLoginPage.get().pressLogoutBtn();
        Assert.assertEquals(androidLoginPage.get().getLandingScreenText(), "بريدفاست للموزعين");
        Assert.assertTrue(androidLoginPage.get().isLoginButtonDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void loggingInWithUnauthorizedNumber() {
        //Open the midMile app
        Assert.assertEquals(androidLoginPage.get().getLandingScreenText(), "بريدفاست للموزعين");
        Assert.assertTrue(androidLoginPage.get().isLoginButtonDisplayed());
        androidLoginPage.get().pressLoginBtn();
        //Navigate to the login page
        Assert.assertTrue(androidLoginPage.get().isBackButtonDisplayed());
        Assert.assertEquals(androidLoginPage.get().getLoginTitle(), "تسجيل الدخول");
        Assert.assertTrue(androidLoginPage.get().isMobileNumberFieldDisplayed());
        Assert.assertEquals(androidLoginPage.get().getMobileNumber(),"رقم الهاتف المسجل");
        Assert.assertTrue(androidLoginPage.get().isConfirmButtonDisplayed());
        Assert.assertTrue(androidLoginPage.get().isConfirmBtnDisabled());
        //Enter unauthorized mobile number
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getRandomTestUser().getPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        //Assert that the mobile number is unauthorized to log in
        Assert.assertEquals(androidLoginPage.get().getUnauthorizedNumberTxt(),"هذا الرقم غير مصرح له بالدخول");
    }
}
