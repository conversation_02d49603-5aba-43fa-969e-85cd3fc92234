package midMileApp.android.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class ActionTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void checkFirstActionAtOrdersToReceiveSection() {
        //Login with valid user
        androidLoginPage.get().pressLoginBtn();
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        androidLoginPage.get().setPassword(defaultTestData.get().getMidMileUser().getBypassScriptPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
        //Check 2 grouping sections
        Assert.assertEquals(androidActions.get().getOrdersToReceiveText(),"تحميل الشاحنة");
        Assert.assertEquals(androidActions.get().getOrdersToDeliverText(),"توصيل الطلبات");
        //Expand the orders to receive section
        Assert.assertTrue(androidActions.get().isConfirmArrivalActionBtnDisplayed());
        Assert.assertTrue(androidActions.get().isSourceLocationDisplayed());
        Assert.assertTrue(androidActions.get().isOrderNumberDisplayed());
        Assert.assertTrue(androidActions.get().isOutRefNumberDisplayed());
        Assert.assertTrue(androidActions.get().isDestinationLocationDisplayed());
        Assert.assertTrue(androidActions.get().isInternalCategoryDisplayed());
        //Take the first action
        androidActions.get().pressConfirmArrivalReceiveBtn();
        //Check that the second action displayed
        Assert.assertTrue(androidActions.get().isStartLoadingActionBtnDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void checkSecondActionAtOrdersToReceiveSection() {
        //Login with valid user
        androidLoginPage.get().pressLoginBtn();
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        androidLoginPage.get().setPassword(defaultTestData.get().getMidMileUser().getBypassScriptPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
        //Check 2 grouping sections
        Assert.assertEquals(androidActions.get().getOrdersToReceiveText(),"تحميل الشاحنة");
        Assert.assertEquals(androidActions.get().getOrdersToDeliverText(),"توصيل الطلبات");
        //Expand the orders to receive section
        Assert.assertTrue(androidActions.get().isSourceLocationDisplayed());
        Assert.assertTrue(androidActions.get().isStartLoadingActionBtnDisplayed());
        Assert.assertTrue(androidActions.get().isOrderNumberDisplayed());
        Assert.assertTrue(androidActions.get().isOutRefNumberDisplayed());
        Assert.assertTrue(androidActions.get().isDestinationLocationDisplayed());
        Assert.assertTrue(androidActions.get().isInternalCategoryDisplayed());
        //Take the second action
        androidActions.get().pressStartLoadingReceiveBtn();
        //Check that the third action displayed
        Assert.assertTrue(androidActions.get().isEndLoadingActionBtnDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void checkLastActionAtOrdersToReceiveSection() {
        //Login with valid user
        androidLoginPage.get().pressLoginBtn();
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        androidLoginPage.get().setPassword(defaultTestData.get().getMidMileUser().getBypassScriptPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
        //Check 2 grouping sections
        Assert.assertEquals(androidActions.get().getOrdersToReceiveText(),"تحميل الشاحنة");
        Assert.assertEquals(androidActions.get().getOrdersToDeliverText(),"توصيل الطلبات");
        //Check the order card
        Assert.assertTrue(androidActions.get().isSourceLocationDisplayed());
        Assert.assertTrue(androidActions.get().isEndLoadingActionBtnDisplayed());
        Assert.assertTrue(androidActions.get().isOrderNumberDisplayed());
        Assert.assertTrue(androidActions.get().isOutRefNumberDisplayed());
        Assert.assertTrue(androidActions.get().isDestinationLocationDisplayed());
        Assert.assertTrue(androidActions.get().isInternalCategoryDisplayed());
        //Take the last action
        androidActions.get().pressEndLoadingActionBtn();
        //Check that the success message displayed
        Assert.assertTrue(androidActions.get().isDoneLoadingSuccessMssgDisplayed());
        Assert.assertTrue(androidActions.get().isDoneLoadingSuccessMssgBtnDisplayed());
        Assert.assertNotSame(androidActions.get().getSuccessMssgLoadingText(),"تم تنزيل طلب"+androidActions.get().getOrderNumber()+"بنجاح");
        androidActions.get().pressDoneLoadingSuccessMssgBtn();
        //Check that the order at orders to deliver section
        Assert.assertTrue(androidActions.get().isDestinationLocationDeliveryDisplayed());
        Assert.assertEquals(androidActions.get().getOrdersNumberToDeliver(),"1");
        Assert.assertTrue(androidActions.get().isOrderNumberDisplayed());
        Assert.assertTrue(androidActions.get().isInRefNumberDisplayed());
        Assert.assertTrue(androidActions.get().isInternalCategoryDisplayed());
        //Check the first action at orders to deliver section
        Assert.assertTrue(androidActions.get().isStartDeliveryActionBtnDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void checkFirstActionAtOrdersToDeliverSection() {
        //Login with valid user
        androidLoginPage.get().pressLoginBtn();
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        androidLoginPage.get().setPassword(defaultTestData.get().getMidMileUser().getBypassScriptPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
        //Check 2 grouping sections
        Assert.assertEquals(androidActions.get().getOrdersToReceiveText(),"تحميل الشاحنة");
        Assert.assertEquals(androidActions.get().getOrdersToDeliverText(),"توصيل الطلبات");
        //Check orders to receive section order counter
        Assert.assertEquals(androidActions.get().getOrdersNumberToReceive(),"0");
        //Check the order at orders to deliver section
        Assert.assertTrue(androidActions.get().isStartDeliveryActionBtnDisplayed());
        Assert.assertTrue(androidActions.get().isDestinationLocationDeliveryDisplayed());
        Assert.assertEquals(androidActions.get().getOrdersNumberToDeliver(),"1");
        Assert.assertTrue(androidActions.get().isOrderNumberDisplayed());
        Assert.assertTrue(androidActions.get().isInRefNumberDisplayed());
        Assert.assertTrue(androidActions.get().isInternalCategoryDisplayed());
        //Take the first action at orders to deliver section
        androidActions.get().pressStartDeliveryBtn();
        //Check the second action displayed at the order to deliver section
        Assert.assertTrue(androidActions.get().isConfirmArrivalDeliveryBtnDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void checkSecondActionAtOrdersToDeliverSection() {
        //Login with valid user
        androidLoginPage.get().pressLoginBtn();
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        androidLoginPage.get().setPassword(defaultTestData.get().getMidMileUser().getBypassScriptPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
        //Check 2 grouping sections
        Assert.assertEquals(androidActions.get().getOrdersToReceiveText(),"تحميل الشاحنة");
        Assert.assertEquals(androidActions.get().getOrdersToDeliverText(),"توصيل الطلبات");
        //Check orders to receive section order counter
        Assert.assertEquals(androidActions.get().getOrdersNumberToReceive(),"0");
        //Check the order at orders to deliver section
        Assert.assertTrue(androidActions.get().isConfirmArrivalDeliveryBtnDisplayed());
        Assert.assertTrue(androidActions.get().isDestinationLocationDeliveryDisplayed());
        Assert.assertEquals(androidActions.get().getOrdersNumberToDeliver(),"1");
        Assert.assertTrue(androidActions.get().isOrderNumberDisplayed());
        Assert.assertTrue(androidActions.get().isInRefNumberDisplayed());
        Assert.assertTrue(androidActions.get().isInternalCategoryDisplayed());
        //Take the second action at orders to deliver section
        androidActions.get().pressConfirmArrivalDeliveryBtn();
        //Check the third action displayed at the order to deliver section
        Assert.assertTrue(androidActions.get().isStartDeloadingBtnDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void checkThirdActionAtOrdersToDeliverSection() {
        //Login with valid user
        androidLoginPage.get().pressLoginBtn();
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        androidLoginPage.get().setPassword(defaultTestData.get().getMidMileUser().getBypassScriptPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
        //Check 2 grouping sections
        Assert.assertEquals(androidActions.get().getOrdersToReceiveText(),"تحميل الشاحنة");
        Assert.assertEquals(androidActions.get().getOrdersToDeliverText(),"توصيل الطلبات");
        //Check orders to receive section order counter
        Assert.assertEquals(androidActions.get().getOrdersNumberToReceive(),"0");
        //Check the order at orders to deliver section
        Assert.assertTrue(androidActions.get().isStartDeloadingBtnDisplayed());
        Assert.assertTrue(androidActions.get().isDestinationLocationDeliveryDisplayed());
        Assert.assertEquals(androidActions.get().getOrdersNumberToDeliver(),"1");
        Assert.assertTrue(androidActions.get().isOrderNumberDisplayed());
        Assert.assertTrue(androidActions.get().isInRefNumberDisplayed());
        Assert.assertTrue(androidActions.get().isInternalCategoryDisplayed());
        //Take the third action at orders to deliver section
        androidActions.get().pressStartDeloadingBtn();
        //Check the last action displayed at the order to deliver section
        Assert.assertTrue(androidActions.get().isEndDeloadingBtnDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("midMile")})
    public void checkLastActionAtOrdersToDeliverSection() {
        //Login with valid user
        androidLoginPage.get().pressLoginBtn();
        androidLoginPage.get().setMobileNumber(defaultTestData.get().getMidMileUser().getLocalPhoneNumber());
        androidLoginPage.get().pressConfirmBtn();
        androidLoginPage.get().setPassword(defaultTestData.get().getMidMileUser().getBypassScriptPassword());
        androidLoginPage.get().pressPasswordLoginBtn();
        //Check 2 grouping sections
        Assert.assertEquals(androidActions.get().getOrdersToReceiveText(),"تحميل الشاحنة");
        Assert.assertEquals(androidActions.get().getOrdersToDeliverText(),"توصيل الطلبات");
        //Check orders to receive section order counter
        Assert.assertEquals(androidActions.get().getOrdersNumberToReceive(),"0");
        //Check the order at orders to deliver section
        Assert.assertTrue(androidActions.get().isEndDeloadingBtnDisplayed());
        Assert.assertTrue(androidActions.get().isDestinationLocationDeliveryDisplayed());
        Assert.assertEquals(androidActions.get().getOrdersNumberToDeliver(),"1");
        Assert.assertTrue(androidActions.get().isOrderNumberDisplayed());
        Assert.assertTrue(androidActions.get().isInRefNumberDisplayed());
        Assert.assertTrue(androidActions.get().isInternalCategoryDisplayed());
        //Take the last action at orders to deliver section
        androidActions.get().pressEndDeloadingBtn();
        //Check the success mssg displayed
        Assert.assertTrue(androidActions.get().isDoneDeloadingSuccessMssgDisplayed());
        Assert.assertTrue(androidActions.get().isDoneDeloadingSuccessMssgBtnDisplayed());
        Assert.assertNotSame(androidActions.get().getSuccessMssgDeloadingText(),"تم تنزيل طلب"+androidActions.get().getOrderNumberDeloading()+"بنجاح");
        androidActions.get().pressDoneDeloadingSuccessMssgBtn();
        //Check the empty state screen
        Assert.assertTrue(androidActions.get().isEmptyScreenFirstTxtDisplayed());
        Assert.assertTrue(androidActions.get().isEmptyScreenSecondTxtDisplayed());
    }
}
