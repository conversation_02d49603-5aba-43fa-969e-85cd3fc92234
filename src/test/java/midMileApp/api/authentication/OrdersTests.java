package midMileApp.api.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import org.testng.Assert;
import org.testng.annotations.Test;

public class OrdersTests extends BaseTest {
    @Test
    @Tags({@Tag("api"), @Tag("midMile")})
    public void validateListingOrdersFromMobileSide() {
        jsonPath.set(JsonPath.given(midMileAppApiClient.get().getMidMileUserOrders(midMileAppApiClient.get().getMidMileUserObject(defaultTestData.get().getMidMileUser()))
                .then()
                .statusCode(200)
                .extract().asString()));

        Assert.assertFalse(jsonPath.get().getString("data.receivals").isEmpty());
        Assert.assertFalse(jsonPath.get().getString("data.deliveries").isEmpty());
    }
    @Test
    @Tags({@Tag("api"), @Tag("midMile")})
    public void validateListingOrdersFromDashboard() {
          testExecutionHelper.get().getOrderByCertainCriteria(midMileOrdersApiClient.get().listMidMileDashboardOrdersWithFilters(defaultTestData.get().getAdminUser(),"2024-03-04"),"dispatcherValue",null);
    }
    @Test
    @Tags({@Tag("api"), @Tag("midMile")})
    public void validateAssigningOrderToDispatcher() {
        jsonPath.set(JsonPath.given( midMileOrdersApiClient.get().assignOrderToDispatcher(midMileAppApiClient.get().getMidMileUserObject(defaultTestData.get().getMidMileUser()),
                testExecutionHelper.get().getOrderByCertainCriteria(midMileOrdersApiClient.get().listMidMileDashboardOrdersWithFilters(defaultTestData.get().getAdminUser(),"2024-03-03"),"dispatcherValue",null))
                .then()
                .statusCode(200)
                .extract().asString()));
        Assert.assertEquals(jsonPath.get().getString("success"), "true");
        Assert.assertEquals(jsonPath.get().getString("message"), "order/s assigned successfully");
    }
}
