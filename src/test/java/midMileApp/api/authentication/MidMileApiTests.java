package midMileApp.api.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import org.testng.Assert;
import org.testng.annotations.Test;

public class MidMileApiTests extends BaseTest {

    @Test
    @Tags({@Tag("api"), @Tag("midMile")})
    public void validateLoggingInWithValidCredentialsIsWorkingCorrectly() {

         jsonPath.set(new JsonPath(midMileAppApiClient.get().getLoginEndpointResponse(
                        configs.get().getTestMobileNumber().replaceAll("\\D+", "")
                                + defaultTestData.get().getMidMileUser().getLocalPhoneNumber()
                        , defaultTestData.get().getMidMileUser().getBypassScriptPassword())
                .then()
                 .log().ifValidationFails()
                 .statusCode(200)
                .extract().asString())
        );
        Assert.assertNotNull(jsonPath.get().get("data.token"));
        Assert.assertFalse(jsonPath.get().getString("data.token").isEmpty());
        Assert.assertTrue(jsonPath.get().getString("data.roles").contains("dispatcher"));
    }

    @Test (priority = 1)
    @Tags({@Tag("api"), @Tag("midMile")})
    public void postStopConfirmArrivalActionReceivals() {

        jsonPath.set(new JsonPath(midMileAppApiClient.get().postStopActionAPI( defaultTestData.get().getMidMileUser(),
                        String.valueOf(configs.get().getTestLatitude()), String.valueOf(configs.get().getTestLongitude())
                        , midMileAppApiClient.get().getAllDispatcherTrips(
                                defaultTestData.get().getMidMileUser(), "receivals").getFirst())
                .then()
                .statusCode(200)
                .extract().asString())
        );
        System.out.println(jsonPath.get());
        Assert.assertTrue(jsonPath.get().getString("reload").equals("true"));
        Assert.assertNull(jsonPath.get().getString("new_action"));
    }

    @Test (priority = 2)
    @Tags({@Tag("api"), @Tag("midMile")})
    public void postTaskStartLoadingAction(){

        jsonPath.set(new JsonPath(midMileAppApiClient.get().postTaskActionAPI( defaultTestData.get().getMidMileUser(),
                        String.valueOf(configs.get().getTestLatitude()), String.valueOf(configs.get().getTestLongitude())
                        , midMileAppApiClient.get().getAllDispatcherTrips(
                                defaultTestData.get().getMidMileUser(), "receivals").getFirst())
                .then()
                .statusCode(200)
                .extract().asString())
        );
        Assert.assertTrue(jsonPath.get().getString("reload").equals("false"));
        Assert.assertEquals(jsonPath.get().getString("new_action.key"), "FINISH_LOADING");
    }

    @Test (priority = 3)
    @Tags({@Tag("api"), @Tag("midMile")})
    public void postTaskFinishLoadingAction(){

        jsonPath.set(new JsonPath(midMileAppApiClient.get().postTaskActionAPI( defaultTestData.get().getMidMileUser(),
                        String.valueOf(configs.get().getTestLatitude()), String.valueOf(configs.get().getTestLongitude())
                        , midMileAppApiClient.get().getAllDispatcherTrips(
                                defaultTestData.get().getMidMileUser(), "receivals").getFirst())

                .then()
                .statusCode(200)
                .extract().asString())
        );
        Assert.assertTrue(jsonPath.get().getString("reload").equals("true"));
        Assert.assertNull(jsonPath.get().getString("new_action"));
    }

    @Test (priority = 4)
    @Tags({@Tag("api"), @Tag("midMile")})
    public void postStopStartHeadingAction() {

        jsonPath.set(new JsonPath(midMileAppApiClient.get().postStopActionAPI( defaultTestData.get().getMidMileUser(),
                        String.valueOf(configs.get().getTestLatitude()), String.valueOf(configs.get().getTestLongitude())
                        , midMileAppApiClient.get().getAllDispatcherTrips(
                                defaultTestData.get().getMidMileUser(), "deliveries").getFirst())
                .then()
                .statusCode(200)
                .extract().asString())
        );
        System.out.println(jsonPath.get());
        Assert.assertTrue(jsonPath.get().getString("reload").equals("true"));
        Assert.assertNull(jsonPath.get().getString("new_action"));
    }
    @Test (priority = 5)
    @Tags({@Tag("api"), @Tag("midMile")})
    public void postStopConfirmArrivalActionDeliveries() {

        jsonPath.set(new JsonPath(midMileAppApiClient.get().postStopActionAPI( defaultTestData.get().getMidMileUser(),
                        String.valueOf(configs.get().getTestLatitude()), String.valueOf(configs.get().getTestLongitude())
                        , midMileAppApiClient.get().getAllDispatcherTrips(
                                defaultTestData.get().getMidMileUser(), "deliveries").getFirst())
                .then()
                .statusCode(200)
                .extract().asString())
        );
        System.out.println(jsonPath.get());
        Assert.assertTrue(jsonPath.get().getString("reload").equals("true"));
        Assert.assertNull(jsonPath.get().getString("new_action"));
    }

    @Test (priority = 6)
    @Tags({@Tag("api"), @Tag("midMile")})
    public void postTaskStartOffLoadingAction(){

        jsonPath.set(new JsonPath(midMileAppApiClient.get().postTaskActionAPI( defaultTestData.get().getMidMileUser(),
                        String.valueOf(configs.get().getTestLatitude()), String.valueOf(configs.get().getTestLongitude())
                        , midMileAppApiClient.get().getAllDispatcherTrips(
                                defaultTestData.get().getMidMileUser(), "deliveries").getFirst())
                .then()
                .statusCode(200)
                .extract().asString())
        );
        Assert.assertTrue(jsonPath.get().getString("reload").equals("false"));
        Assert.assertEquals(jsonPath.get().getString("new_action.key"), "FINISH_OFFLOADING");
    }

    @Test (priority = 7)
    @Tags({@Tag("api"), @Tag("midMile")})
    public void postTaskFinishOffLoadingAction(){

        jsonPath.set(new JsonPath(midMileAppApiClient.get().postTaskActionAPI( defaultTestData.get().getMidMileUser(),
                        String.valueOf(configs.get().getTestLatitude()), String.valueOf(configs.get().getTestLongitude())
                        , midMileAppApiClient.get().getAllDispatcherTrips(
                                defaultTestData.get().getMidMileUser(), "deliveries").getFirst())
                .then()
                .statusCode(200)
                .extract().asString())
        );
        Assert.assertTrue(jsonPath.get().getString("reload").equals("true"));
        Assert.assertNull(jsonPath.get().getString("new_action"));
    }
}
