package mainAdminPortal.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;

@Test
public class LoginTests extends BaseTest {
    @Test
    @Tags({@Tag("web")})
    public void loginWithGoogleAdminAccount(){
        Reporter.log("Starting the loginWithValidAdminAccount Test");
        //Visit Page and enter phone number
        Reporter.log("Going to Login Page", 3, true);
        webLoginPage.get().goToLoginPage();
        Reporter.log("Entering the phone number", 3, true);
        webLoginPage.get().enterPhoneNumber(defaultTestData.get().getAdminUser().getLocalPhoneNumber());
        Assert.assertTrue(webLoginPage.get().isPhoneNumberEnteredCorrectly(
                defaultTestData.get().getAdminUser().getLocalPhoneNumber()));
        Reporter.log("Pressing the submit button", 3, true);
        webLoginPage.get().pressSubmitBtn();

        //Assert that google login button is displayed
        Reporter.log("Checking if google Login button is displayed", 3, true);
        Assert.assertTrue(webLoginPage.get().isGoogleLoginBtnDisplayed());

        //Login with the google account
        Reporter.log("Logging in with Google Account", 3, true);
        webLoginPage.get().pressGoogleLoginBtn();
        webGoogleLoginPage.get().loginWithGmail(defaultTestData.get());

        //Validate that the more page is displayed
        Reporter.log("Login is successful and checking if more button is displayed");
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        Reporter.log("More Button is displayed");
    }

    @Test(groups = {"smoke"})
    @Tags({@Tag("web")})
    public void LoginWithByPassingGoogleLogin(){
        Reporter.log("Starting the loginWithValidAdminAccount Test");
        //Visit Page and enter phone number
        Reporter.log("Going to Login Page", 3, true);
        webLoginPage.get().goToLoginPage();
        Reporter.log("Entering the phone number", 3, true);
        webLoginPage.get().enterPhoneNumber(defaultTestData.get().getAdminUser().getPhoneNumber());
        Reporter.log("Pressing the submit button", 3, true);
        webLoginPage.get().pressSubmitBtn();

        //Bypass the Google Login step
        webLoginPage.get().byPassGoogleLogin(defaultTestData.get().getAdminUser().getPhoneNumber(),
                defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webLoginPage.get().isGoogleLoginBtnNotDisplayed(), "Google Login button is still displayed");

        //Validate that the more page is displayed
        Reporter.log("Login is completed and checking if more button is displayed and current url is: "
                + webDriver.get().getCurrentUrl(), true);
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        Reporter.log("More Button is displayed");
    }
}
