package mainAdminPortal.orders;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;

@Test
public class EditOrderTests extends BaseTest {
    @Test
    @Tags({@Tag("web")})
    public void validateEditButtonExists(){
        Reporter.log("Starting the loginWithValidAdminAccount Test");
        //Visit Page and enter phone number
        Reporter.log("Going to Login Page", 3, true);
        webLoginPage.get().goToLoginPage();
        Reporter.log("Entering the phone number", 3, true);
        webLoginPage.get().enterPhoneNumber(defaultTestData.get().getAdminUser().getPhoneNumber());
        Reporter.log("Pressing the submit button", 3, true);
        webLoginPage.get().pressSubmitBtn();

        //Bypass the Google Login step
        webLoginPage.get().byPassGoogleLogin(defaultTestData.get().getAdminUser().getPhoneNumber(),
                defaultTestData.get().getAdminUser().getPhoneNumber());

        //Validate that the more page is displayed
        Reporter.log("Login is successful and checking if more button is displayed");
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        Reporter.log("More Button is displayed");

        //Go to order page
        webOrdersAdminPage.get().goToPage();
        Assert.assertTrue(webOrdersAdminPage.get().isPageDisplayed(), "Order page is not opened");
    }
}
