package stockTake.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import org.testng.Assert;
import org.testng.annotations.Test;

public class StockTakeApiTests extends BaseTest {
    @Test(groups = {"logistics"})
    @Tags({@Tag("api"), @Tag("stockTake")})
    public void validateListingAllCategories() {
        stockTakeApiClient.get().getAllCategoriesResponse(defaultTestData.get().getStockTakerUser());
        Assert.assertFalse(
                stockTakeApiClient.get().getAllCategoriesResponse(defaultTestData.get().getStockTakerUser())
                        .isEmpty()
                , "The categories list should not be empty.");
    }

    @Test
    @Tags({@Tag("api"), @Tag("stockTake")})
    public void validateListingActiveProductsAtCategoryAndSubCategory() {
        Assert.assertTrue(!stockTakeApiClient.get()
                .getAllActiveProductsOnCategories(defaultTestData.get().getStockTakerUser()).isEmpty());
    }
    
    @Test
    @Tags({@Tag("api"), @Tag("stockTake")})
    public void validateListingAllProductsAtCategory() {
        Assert.assertTrue(!stockTakeApiClient.get()
                .getAllProductsOnCategories(defaultTestData.get().getStockTakerUser()).isEmpty());
    }

    @Test
    @Tags({@Tag("api"), @Tag("stockTake")})
    public void validateScanningProductNotOnTheSystem() {
        jsonPath.set(new JsonPath(stockTakeApiClient.get().scanningProductNotOnTheSystem(
                        defaultTestData.get().getStockTakerUser(),
                        defaultTestData.get().getStockTakerUser().getFpId(),
                        defaultTestData.get().getRandomTestUser().getPhoneCountry())
                .then()
                .statusCode(400)
                .extract().asString())
        );
        Assert.assertEquals(jsonPath.get().getString("status"), "fail");
        Assert.assertEquals(jsonPath.get().getString("errors.code"), "[error.notFound]");
        Assert.assertEquals(jsonPath.get().getString("errors.message"), "[هذا المنتج غير موجود]");
    }

    @Test
    @Tags({@Tag("api"), @Tag("stockTake")})
    public void validateScanningInActiveProductOnTheSystem() {
        jsonPath.set(new JsonPath(stockTakeApiClient.get().scanningInActiveProductOnTheSystem(
                defaultTestData.get().getStockTakerUser(),
                        defaultTestData.get().getStockTakerUser().getFpId(),
                        stockTakeApiClient.get()
                                .getInActiveProductBarcodeOnCategories(defaultTestData.get().getStockTakerUser()))
                .then()
                .statusCode(200)
                .extract().asString())
        );
        Assert.assertEquals(jsonPath.get().getString("status"), "success");
        Assert.assertTrue(!jsonPath.get().getString("payload.productMongoId").isEmpty());
        Assert.assertFalse(jsonPath.get().getBoolean("payload.isActive"));
    }
}
