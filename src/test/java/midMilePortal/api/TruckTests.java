package midMilePortal.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class TruckTests extends BaseTest {

    @Test
    @Tags({@Tag("api")})
    public void validateListingAllTrucks() {
        Assert.assertFalse(
                midMileTrucksApiClient.get().listMidMileDashboardTrucks(defaultTestData.get().getAdminUser())
                        .isEmpty()
                , "The Trucks list should not be empty.");
    }
}
