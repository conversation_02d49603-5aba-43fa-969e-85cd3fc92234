package midMilePortal.dashboard;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;

public class OrderTests extends BaseTest {
    @Test
    @Tags({@Tag("web")})
    public void validateApplyFilterByOrderNumber() throws InterruptedException {
        Reporter.log("Going to Login Page", 3, true);
        webLoginPage.get().goToLoginPage();
        Reporter.log("Logging in with the admin user", 3, true);
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        // Validate that the more page is displayed
        Reporter.log("Login is successful and checking isf more button is displayed");
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        Reporter.log("More Button is displayed");
        midMileAdminPage.get().goToPage();
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isApplyFilterDisplay());
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        midMileAdminPage.get().searchForOrder("BKRY/OUT/034704");
        midMileAdminPage.get().clickApply();
        Assert.assertTrue(midMileAdminPage.get().isDataDisplayed());
    }
    @Test
    @Tags({@Tag("web")})
    public void checkAssignTruckButtonDisplayedAtTheList(){
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        midMileAdminPage.get().goToPage();
        Assert.assertEquals(midMileAdminPage.get().getSourceLocationModuleTitle(),"Select source location");
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().chooseTargetDate(configs.get().getTestMidMileOrdersDate(), "dd-MM-yyyy");
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        Assert.assertEquals(midMileAdminPage.get().getOrdersPageTitle(),"Orders");
        Assert.assertTrue(midMileAdminPage.get().isAssignTruckBtnDisplayed());
        Assert.assertEquals(midMileAdminPage.get().getAssignTruckBtnTxt(),"Assign Truck");
    }
    @Test
    @Tags({@Tag("web")})
    public void checkEditTruckButtonDisplayedAtTheList(){
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        midMileAdminPage.get().goToPage();
        Assert.assertEquals(midMileAdminPage.get().getSourceLocationModuleTitle(),"Select source location");
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().chooseTargetDate(configs.get().getTestMidMileOrdersDate(), "dd-MM-yyyy");
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        Assert.assertEquals(midMileAdminPage.get().getOrdersPageTitle(),"Orders");
        Assert.assertTrue(midMileAdminPage.get().isEditTruckBtnDisplayed());
    }
    @Test
    @Tags({@Tag("web")})
    public void checkAssignTruckModalComponents(){
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        midMileAdminPage.get().goToPage();
        Assert.assertEquals(midMileAdminPage.get().getSourceLocationModuleTitle(),"Select source location");
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().chooseTargetDate(configs.get().getTestMidMileOrdersDate(), "dd-MM-yyyy");
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        Assert.assertEquals(midMileAdminPage.get().getOrdersPageTitle(),"Orders");
        Assert.assertTrue(midMileAdminPage.get().isAssignTruckBtnDisplayed());
        midMileAdminPage.get().clickAssignTruck();
        Assert.assertTrue(midMileAdminPage.get().isAssignTruckModalTitleDisplayed());
        Assert.assertEquals(midMileAdminPage.get().getAssignTruckModalTitle(),"Assign Truck");
        Assert.assertTrue(midMileAdminPage.get().isSearchFieldDisplayed());
        Assert.assertTrue(midMileAdminPage.get().isTruckDetailsColumnDisplayed());
        Assert.assertEquals(midMileAdminPage.get().getTruckDetailsLabelAtTruckAssignationModal(),"Truck details");
        Assert.assertTrue(midMileAdminPage.get().isAssignedOrdersColumnDisplayed());
        Assert.assertEquals(midMileAdminPage.get().getAssignOrdersLabelAtTruckAssignationModal(),"# assigned orders");
    }
    @Test
    @Tags({@Tag("web")})
    public void checkAssignTruckButtonOfAssignationModalNotDisplayedWithoutSelection(){
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        midMileAdminPage.get().goToPage();
        Assert.assertEquals(midMileAdminPage.get().getSourceLocationModuleTitle(),"Select source location");
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().chooseTargetDate(configs.get().getTestMidMileOrdersDate(), "dd-MM-yyyy");
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        Assert.assertEquals(midMileAdminPage.get().getOrdersPageTitle(),"Orders");
        Assert.assertTrue(midMileAdminPage.get().isAssignTruckBtnDisplayed());
        midMileAdminPage.get().clickAssignTruck();
        Assert.assertTrue(midMileAdminPage.get().isAssignTruckModalTitleDisplayed());
        Assert.assertEquals(midMileAdminPage.get().getAssignTruckModalTitle(),"Assign Truck");
        Assert.assertTrue(midMileAdminPage.get().isAssignBtnDisplayed(), "Assign Button is displayed");
    }
    @Test
    @Tags({@Tag("web")})
    public void checkSuccessfulTruckAssignment(){
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        midMileAdminPage.get().goToPage();
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().chooseTargetDate(configs.get().getTestMidMileOrdersDate(), "dd-MM-yyyy");
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        Assert.assertTrue(midMileAdminPage.get().isAssignTruckBtnDisplayed());
        midMileAdminPage.get().clickAssignTruck();
        Assert.assertTrue(midMileAdminPage.get().isAssignTruckModalTitleDisplayed());
        midMileAdminPage.get().selectFirstTruck();
        midMileAdminPage.get().clickAssignOfTruckModal();
        Assert.assertEquals(midMileAdminPage.get().getSuccessMssg(),"Truck assigned successfully");
    }
    @Test
    @Tags({@Tag("web")})
    public void checkEditTruckButtonWithCanceledStatus() throws InterruptedException {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        midMileAdminPage.get().goToPage();
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().chooseTargetDate(configs.get().getTestMidMileOrdersDate(), "dd-MM-yyyy");
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        midMileAdminPage.get().filterByStatus("Canceled");
        midMileAdminPage.get().clickApply();
        Assert.assertTrue(midMileAdminPage.get().isCanceledStatusDisplayed());
        Assert.assertTrue(midMileAdminPage.get().isEditTruckBtnDisabled());
    }
    @Test
    @Tags({@Tag("web")})
    public void checkEmptyTruckAssignmentAtAssignDispatcherModal() throws InterruptedException {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        midMileAdminPage.get().goToPage();
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().chooseTargetDate(configs.get().getTestMidMileOrdersDate(), "dd-MM-yyyy");
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        midMileAdminPage.get().checkAndClickAssignDispatcher();
        Assert.assertEquals(midMileAdminPage.get().getAssignedTruckTitle(),"Assigned Truck");
        Assert.assertEquals(midMileAdminPage.get().getEmptyAssignedTruckText(),"No truck assigned yet");
        Assert.assertEquals(midMileAdminPage.get().getAssignDispatcherModalTitle(),"Assign dispatcher");
    }
    @Test
    @Tags({@Tag("web")})
    public void checkEditTruckButtonWithArrivedStatus() throws InterruptedException {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        midMileAdminPage.get().goToPage();
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().chooseTargetDate(configs.get().getTestMidMileOrdersDate(), "dd-MM-yyyy");
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        midMileAdminPage.get().filterByStatus("Arrived");
        midMileAdminPage.get().clickApply();
        Assert.assertTrue(midMileAdminPage.get().isArrivedStatusDisplayed());
        Assert.assertTrue(midMileAdminPage.get().isEditTruckBtnDisabled());
    }
    @Test
    @Tags({@Tag("web")})
    public void checkEditTruckButtonWithCompletedStatus() throws InterruptedException {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        midMileAdminPage.get().goToPage();
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().chooseTargetDate(configs.get().getTestMidMileOrdersDate(), "dd-MM-yyyy");
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        midMileAdminPage.get().filterByStatus("Completed");
        midMileAdminPage.get().clickApply();
        Assert.assertTrue(midMileAdminPage.get().isCompletedStatusDisplayed());
        Assert.assertTrue(midMileAdminPage.get().isEditTruckBtnDisabled());
    }
    @Test
    @Tags({@Tag("web")})
    public void checkSuccessfulTruckEditing() throws InterruptedException {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        midMileAdminPage.get().goToPage();
        Assert.assertEquals(midMileAdminPage.get().getSourceLocationModuleTitle(),"Select source location");
        midMileAdminPage.get().clickFirstOption();
        midMileAdminPage.get().chooseTargetDate(configs.get().getTestMidMileOrdersDate(), "dd-MM-yyyy");
        midMileAdminPage.get().clickShowButton();
        Assert.assertTrue(midMileAdminPage.get().isOrderModuleOpen());
        midMileAdminPage.get().filterByStatus("Placed");
        midMileAdminPage.get().clickApply();
        Assert.assertTrue(midMileAdminPage.get().isPlacedStatusDisplayed());
        Assert.assertTrue(midMileAdminPage.get().isEditTruckBtnDisplayed());
        midMileAdminPage.get().clickEditTruckBtn();
        Assert.assertEquals(midMileAdminPage.get().getAssignTruckModalTitle(),"Assign Truck");
        Assert.assertTrue(midMileAdminPage.get().isSearchFieldDisplayed());
        Assert.assertTrue(midMileAdminPage.get().isTruckDetailsColumnDisplayed());
        Assert.assertEquals(midMileAdminPage.get().getTruckDetailsLabelAtTruckAssignationModal(),"Truck details");
        Assert.assertTrue(midMileAdminPage.get().isAssignedOrdersColumnDisplayed());
        Assert.assertEquals(midMileAdminPage.get().getAssignOrdersLabelAtTruckAssignationModal(),"# assigned orders");
        midMileAdminPage.get().selectAnotherTruck();
        midMileAdminPage.get().clickAssignOfTruckModal();
        Assert.assertEquals(midMileAdminPage.get().getSuccessMssg(),"Truck assigned successfully");
    }
}
