package kitchen;

import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import org.testng.Assert;
import org.testng.annotations.Test;
import base.BaseTest;

import java.util.Collections;

import static org.hamcrest.Matchers.*;

public class KitchenApisTests extends BaseTest {
    @Test
    @Tags({@Tag("api"), @Tag("kitchen")})
    public void validatePostLoginResponse() {
        jsonPath.set(new JsonPath(kitchenApiClient.get().postLoginsEndpointResponse(configs.get()
                        .getChefCountryCode() + configs.get().getChefPhoneNumber(), configs.get().getChefPassword())
                .then()
                .statusCode(201)
                .extract().asString())
        );
        Assert.assertTrue(jsonPath.get().getString("status").equals("200"));
        Assert.assertFalse(jsonPath.get().getString("data").isEmpty());
        Assert.assertEquals(jsonPath.get().getString("code"), "login_success");
    }

    @Test
    @Tags({@Tag("api"), @Tag("kitchen")})
    public void loginWrongPhone() {
        jsonPath.set(new JsonPath(kitchenApiClient.get().postLoginsEndpointResponse("0127558799777",
                        configs.get().getChefPassword())
                .then()
                .statusCode(201)
                .extract().asString())
        );
        Assert.assertTrue(jsonPath.get().getString("status").equals("403"));
        Assert.assertTrue(jsonPath.get().getString("data.message").equals("هذا الرقم غير مصرح له بالدخول"));
        Assert.assertEquals(jsonPath.get().getString("code"), "invalid_phone");
    }

    @Test
    @Tags({@Tag("api"), @Tag("kitchen")})
    public void loginWrongPassword() {
        jsonPath.set(new JsonPath(kitchenApiClient.get().postLoginsEndpointResponse(configs.get().getChefCountryCode()
                        + configs.get().getChefPhoneNumber(), defaultTestData.get().getRandomTestUser().getEmailPassword())
                .then()
                .statusCode(201)
                .extract().asString())
        );
        Assert.assertTrue(jsonPath.get().getString("status").equals("403"));
        Assert.assertTrue(jsonPath.get().getString("data.message").equals("كلمة المرور غير صحيحة"));
        Assert.assertEquals(jsonPath.get().getString("code"), "invalid_password");
    }

    @Test
    @Tags({@Tag("api"), @Tag("kitchen")})
    public void getChefDataResponse() {
        jsonPath.set(new JsonPath(kitchenApiClient.get().getChefData(kitchenApiClient.get()
                        .getChefUserObject(defaultTestData.get().getChefUser()))
                .then()
                .statusCode(200)
                .extract().asString())
        );
        Assert.assertTrue(jsonPath.get().getString("data.user.roles").contains("chef"));
    }

    @Test
    @Tags({@Tag("api"), @Tag("kitchen")})
    public void loginUnassignedToFP() {
        kitchenApiClient.get().unAssignSupportManagerToFps(defaultTestData.get().getAdminUser(),
                kitchenApiClient.get().getChefAssignedWarehouseId(kitchenApiClient.get()
                        .getChefUserObject(defaultTestData.get().getChefUser())), deliveryCapacityManagementApiClient.get()
                        .getUserMongoId(defaultTestData.get().getAdminUser()));

        jsonPath.set(new JsonPath(kitchenApiClient.get().postLoginsEndpointResponse(configs.get()
                        .getChefCountryCode() + configs.get().getChefPhoneNumber(), configs.get().getChefPassword())
                .then()
                .statusCode(201)
                .extract().asString()
        ));
        Assert.assertTrue(jsonPath.get().getString("code").equals("user_fp_assigned_code"));
        Assert.assertTrue(jsonPath.get().getString("status").equals("400"));
        Assert.assertTrue(jsonPath.get().getString("data.has_access").equals("false"));
        Assert.assertTrue(jsonPath.get().getString("data.message").equals("عفوًا، يجب إضافتك لأحد فرق عمل نقاط التوزيع أولًا"));
    }

    @Test
    @Tags({@Tag("api"), @Tag("kitchen")})
    public void loginDifferentRole() {
        switcherApiClient.get().changeUserRole(kitchenApiClient.get().getChefUserObject(defaultTestData.get().getAdminUser()),
                switcherApiClient.get().searchForUser(configs.get().getChefCountryCode() + configs.get().getChefPhoneNumber(),
                        defaultTestData.get().getAdminUser()), "dispatcher");

        jsonPath.set(new JsonPath(kitchenApiClient.get().postLoginsEndpointResponse(configs.get()
                        .getChefCountryCode() + configs.get().getChefPhoneNumber(), configs.get().getChefPassword())
                .then()
                .statusCode(201)
                .extract().asString()
        ));
        Assert.assertTrue(jsonPath.get().getString("code").equals("invalid_phone"));
        Assert.assertTrue(jsonPath.get().getString("status").equals("403"));
        Assert.assertTrue(jsonPath.get().getString("data.has_access").equals("false"));
        Assert.assertTrue(jsonPath.get().getString("data.message").equals("هذا الرقم غير مصرح له بالدخول"));
    }

    @Test
    @Tags({@Tag("api"), @Tag("kitchen")})
    public void updateChefStatus() {
        jsonPath.set(new JsonPath(kitchenApiClient.get().updateChefStatus(kitchenApiClient.get()
                        .getChefUserObject(defaultTestData.get().getChefUser()), "available")
                .then()
                .statusCode(200)
                .extract()
                .asString()));
        Assert.assertTrue(jsonPath.get().getString("success").equals("true"));
        Assert.assertEquals(jsonPath.get().getString("message"), "Success");
        Assert.assertTrue((jsonPath.get().getString("data.user.status").equals("available")));
    }

    @Test
    @Tags({@Tag("api"), @Tag("kitchen")})
    public void validateChefReceivesOrderAssignedToPicker() {
        Assert.assertEquals(pickerAppApiClient.get().assignOrderToPicker(defaultTestData.get().getAdminUser(),
                        switcherApiClient.get().searchForUser(defaultTestData.get().getAdminUser().getLocalPhoneNumber(),
                                defaultTestData.get().getAdminUser()).getId(),
                        switcherApiClient.get().searchForUser(defaultTestData.get().getPickerUser().getLocalPhoneNumber()
                                , defaultTestData.get().getAdminUser()).getId()
                        , Collections.singletonList(defaultTestData.get().getOrdersList().getFirst()))
                .get("statusCode"), 201);

        kitchenApiClient.get()
                .loginChef(configs.get().getChefCountryCode() + configs.get().getChefPhoneNumber()
                        , configs.get().getChefPassword())
                .then()
                .statusCode(201)
                .body("data", not(empty()))
                .body("code", equalTo("login_success"));

        jsonPath.set(new JsonPath(
                kitchenApiClient.get()
                        .getChefAssignedOrders(kitchenApiClient.get().getChefUserObject(defaultTestData.get().getChefUser()))
                        .then()
                        .statusCode(200)  // Expecting status code 200 for success
                        .extract()
                        .asString()
        ));

        Assert.assertFalse(
                jsonPath.get().getList("data.newOrders").isEmpty(),
                "'newOrders' list should not be empty."
        );
    }
}
