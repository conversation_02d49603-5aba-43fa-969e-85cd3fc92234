package cardService.adminPanel;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class CardAdminPanelTests extends BaseTest {

    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void loginWithValidAdminAccount() {
        // Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        // Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        // Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        // Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");
    }

    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void searchByCardUserMobileNumber() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        //Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        //Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        //Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        //Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        //Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        //Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByRegisteredCardUserMobileNumber
                (defaultTestData.get().getRandomTestUser().getPhoneNumber());

        //Validate that search results are displayed
        Assert.assertTrue(webCardPanelSearchUsersPage.get().moreDetailsBtnIsDisplayed(),
                "More Details button is displayed");

        //Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        //Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void searchByCardUserBcid() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Initiate set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");
        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                ,defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        //Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        //Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        //Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        //Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        //Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        //Click on more options button
        webCardPanelSearchUsersPage.get().clickOnMoreOptionsBtn();

        //Search by card user bcid number
        webCardPanelSearchUsersPage.get().searchByCardUserBcidNumber(defaultTestData.get().getCardService().getBcid());

        //Validate that search results data
        Assert.assertTrue(webCardPanelSearchUsersPage.get().moreDetailsBtnIsDisplayed(),
                "More Details button is displayed");

        //Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        //Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void searchByCardUserNationalId() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        //Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        //Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        //Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        //Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        //Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        //Click on more options button
        webCardPanelSearchUsersPage.get().clickOnMoreOptionsBtn();

        //Search by card user national ID
        webCardPanelSearchUsersPage.get().searchByCardUserNationalId(
                defaultTestData.get().getRandomTestUser().getNationalId());

        //Validate that search results data
        Assert.assertTrue(webCardPanelSearchUsersPage.get().moreDetailsBtnIsDisplayed(),
                "More Details button is displayed");

        //Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        //Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void searchByCardUserLastFourDigits() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Initiate set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                ,defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        //Get card last 4 digits from the response of card details
        Assert.assertEquals(cardServiceApiClient.get().getCardDetails(
                defaultTestData.get().getCardService()), "Card Details");

        //Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        //Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        //Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        //Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        //Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        //Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        //Click on more options button
        webCardPanelSearchUsersPage.get().clickOnMoreOptionsBtn();

        //Search by card user last four digits
        webCardPanelSearchUsersPage.get().searchByCardUserLastFourDigits(defaultTestData.get().
                getCardService().getCardLastFourDigits());

        //Validate that search results data
        Assert.assertTrue(webCardPanelSearchUsersPage.get().moreDetailsBtnIsDisplayed(),
                "More Details button is displayed");

        //Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        //Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
 @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void replaceActiveCardUser()  {
     //Register a new user with random mobile number
     defaultTestData.get().setRandomTestUser(
             testExecutionHelper.get().registerUsingApi(
                     defaultTestData.get(),
                     defaultTestData.get().getRandomTestUser()
             )
     );

     //API to login to Pay SDK
     defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
             configs.get().getCardMobileLoginSchemeUserName()
             , configs.get().getCardMobileLoginSchemePassword()
             , defaultTestData.get().getCardService()));

     //Initiate create card user API
     cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
             defaultTestData.get().getRandomTestUser().getId(),
             defaultTestData.get().getRandomTestUser().getEmailAddress(),
             "بثينة", "مصطفى",
             defaultTestData.get().getRandomTestUser().getNationalId(),
             configs.get().getCardUserNationalIdExpiryDate(),
             defaultTestData.get().getCardService());

     //Validate that card status is changed to pending
     Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
             defaultTestData.get().getCardService()), "Pending");

     //Initiate set passcode API
     cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
             defaultTestData.get().getCardService());

     //Validate that card status is changed to registered
     Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
             defaultTestData.get().getCardService()), "Registered");

     //Add customer details API
     cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
             databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                     , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                             + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
             "بثينة", "مصطفي",
             defaultTestData.get().getRandomTestUser().getNationalId(),
             configs.get().getCardUserNationalIdExpiryDate(),
             defaultTestData.get().getRandomTestUser().getAddress(),
             "female", "Cairo",
             defaultTestData.get().getRandomTestUser().getDateOfBirth());

     //Initiate cards pool API and get the BCID
     cardServiceApiClient.get().getCardsPool(
             configs.get().getCardServiceContractNumber()
             , configs.get().getCardServiceTypeId()
             , configs.get().getCardServiceProductNumber()
             , defaultTestData.get().getCardService());

     //Change card status from registered to received
     cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
             databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                     ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                             + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
             configs.get().getPickupLocationId());

     //Validate that card status is changed to Received
     Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
             defaultTestData.get().getCardService()), "Received");

     //Login user by passcode to get user's token
     cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
             ,defaultTestData.get().getCardService());

     //Initiate linking API
     cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

     //Validate that card status is changed to linked
     Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
             defaultTestData.get().getCardService()), "Linked");

     //Initiate set-pin API and navigate to activation link to set user's pin
     webDriver.get().navigate().to(cardServiceApiClient.get()
             .setPin(defaultTestData.get().getCardService()));

     //Run web test to create card pin
     Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
     setCardPinPage.get().enterCardPin();
     Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
     setCardPinPage.get().confirmCardPin();
     Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

     //Initiate activate API
     cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

     //Validate that card status is changed to active
     Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
             defaultTestData.get().getCardService()), "Active");

     //Navigation to Card Panel login page
     webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
     webCardPanelLoginPage.get().goToPage();

     //Validate that page is open and header is displayed
     Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
     Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
             , "Login form header isn't displayed");

     //Login with admin credentials
     webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
             , configs.get().getCardAdminPanelAdminPassword());

     //Validate AdminPanel is displayed correctly
     Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
             ,"Dashboard isn't displayed after login");
     Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

     //Click on Card Users list from side menu
     Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
     webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

     //Validate that search card users page is displayed
     webCardPanelDashboardPage.get().clickOnSearchCardsTab();
     Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
             "Page isn't displayed correctly.");
     Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
             "Find the customers you are looking for");

     //Search by BCard user mobile number
     webCardPanelSearchUsersPage.get().searchByRegisteredCardUserMobileNumber
             (defaultTestData.get().getRandomTestUser().getPhoneNumber());

     //Click on more details button
     webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

     //Validate that card users details page is displayed
     Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
             "Page isn't displayed correctly.");

     //Click on replace button
     webCardPanelViewUsersDetailsPage.get().clickOnThreeDotsBtn();
     webCardPanelViewUsersDetailsPage.get().clickOnReplaceCardBtn();
     Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().replaceCardModalIsDisplayed()
             ,"modal isn't displayed correctly ");

     //Confirm replace
     webCardPanelViewUsersDetailsPage.get().clickOnConfirmReplaceCardBtn();

     //Check for the toaster message
     Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().replacementRequestToasterIsDisplayed());
     Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().getReplacementRequestToasterText().
             contains("The replacement request has been successfully submitted."));

     //Assert that card collected button is displayed after replacement is done
     Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardCollectedBtnIsDisplayed());

     //Delete DB record from actions_logger
     databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
             "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                     + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                     + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

     //Delete DB record from wallet_user_sessions
     databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
             "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                     " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                     + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

     //Delete from cards
     databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
             "DELETE FROM card_hades_db.cards WHERE walletUserId =" +
                     " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                     + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

     //Delete from external_user_balances
     databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
             "DELETE FROM card_hades_db.external_user_balances WHERE user_id = "
                     + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                     + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

     //Delete DB record from wallet_users
     databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
             "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                     + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void replaceLinkedCardUser()  {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Initiate set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                ,defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        //Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        //Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        //Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        //Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        //Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        //Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByRegisteredCardUserMobileNumber
                (defaultTestData.get().getRandomTestUser().getPhoneNumber());

        //Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        //Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        //Click on replace button
        webCardPanelViewUsersDetailsPage.get().clickOnThreeDotsBtn();
        webCardPanelViewUsersDetailsPage.get().clickOnReplaceCardBtn();
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().replaceCardModalIsDisplayed()
                ,"modal isn't displayed correctly ");

        //Confirm replace
        webCardPanelViewUsersDetailsPage.get().clickOnConfirmReplaceCardBtn();

        //Check for the toaster message
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().replacementRequestToasterIsDisplayed());
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().getReplacementRequestToasterText().
                contains("The replacement request has been successfully submitted."));

        //Assert that card collected button is displayed after replacement is done
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardCollectedBtnIsDisplayed());

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete from cards
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.cards WHERE walletUserId =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete from external_user_balances
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.external_user_balances WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void replaceReceivedCardUser()  {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Initiate set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        //Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        //Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        //Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        //Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        //Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        //Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByRegisteredCardUserMobileNumber
                (defaultTestData.get().getRandomTestUser().getPhoneNumber());

        //Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        //Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        //Click on replace button
        webCardPanelViewUsersDetailsPage.get().clickOnThreeDotsBtn();
        webCardPanelViewUsersDetailsPage.get().clickOnReplaceCardBtn();
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().replaceCardModalIsDisplayed()
                ,"modal isn't displayed correctly ");

        //Confirm replace
        webCardPanelViewUsersDetailsPage.get().clickOnConfirmReplaceCardBtn();

        //Check for the toaster message
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().replacementRequestToasterIsDisplayed());
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().getReplacementRequestToasterText().
                contains("The replacement request has been successfully submitted."));

        //Assert that card collected button is displayed after replacement is done
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardCollectedBtnIsDisplayed());

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete from cards
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.cards WHERE walletUserId =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete from external_user_balances
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.external_user_balances WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = " +
                        "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '" +
                        defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void resetUserPinCountTest()  {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Initiate set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                ,defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        //Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        //Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        //Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        //Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        //Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        //Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        //Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByRegisteredCardUserMobileNumber
                (defaultTestData.get().getRandomTestUser().getPhoneNumber());

        //Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        //Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        //Click on reset button
        webCardPanelViewUsersDetailsPage.get().clickOnThreeDotsBtn();
        webCardPanelViewUsersDetailsPage.get().clickOnResetPinCountBtn();
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().resetPinCountModalIsDisplayed()
                ,"modal isn't displayed correctly ");

        //Confirm reset pin count
       webCardPanelViewUsersDetailsPage.get().clickOnConfirmResetPinCountBtn();

        //Check the toaster message
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().customerPinUnblockedToasterIsDisplayed());
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().getResetPinCountToasterText().
                contains("Customer PIN unblocked successfully"));

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void closeCardTest()  {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Initiate set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                ,defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        //Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        //Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        //Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        //Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        //Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        //Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        //Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByRegisteredCardUserMobileNumber
                (defaultTestData.get().getRandomTestUser().getPhoneNumber());

        //Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        //Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        //Click on close button
        webCardPanelViewUsersDetailsPage.get().clickOnThreeDotsBtn();
        webCardPanelViewUsersDetailsPage.get().clickOnCloseBtn();
        webCardPanelViewUsersDetailsPage.get().clickOnConfirmCloseBtn();

        //Validate the card status is changed to closed
        Assert.assertEquals(webCardPanelViewUsersDetailsPage.get().getClosedStatusText(),"Closed");

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete from cards
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.cards WHERE walletUserId =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete from external_user_balances
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.external_user_balances WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void unblockCardUserSuspensionForWrongPasscodeInsertion() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Validate that response code is 200 when calling set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //update passcode tb be invalid passcode
        defaultTestData.get().getCardService().setPassCode("444444");

        //login with invalid passcode for 4 times to make a user suspended and assert that suspension message is displayed in the forth trail
        for (int i = 1; i <= 4; i++) {
            jsonPath.set(new JsonPath(cardServiceApiClient.get().
                    loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                            ,defaultTestData.get().getCardService()).then().statusCode(400).extract().asString()));
            if (i <= 3) {
                Assert.assertEquals(jsonPath.get().getString("message"),
                        "Invalid passcode. Please try again.");
            } else {
                Assert.assertEquals(jsonPath.get().getString("message"),
                        "account is currently not active your current status is  'Suspended'.");
            }
        }
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Suspended");

        //Validate that card status is changed to Suspended
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Suspended");

        //Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        //Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        //Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        //Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        //Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        //Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        //Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByRegisteredCardUserMobileNumber
                (defaultTestData.get().getRandomTestUser().getPhoneNumber());

        //Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        //Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        //Click on unblock passcode button
        webCardPanelViewUsersDetailsPage.get().clickOnThreeDotsBtn();
        webCardPanelViewUsersDetailsPage.get().clickOnUnblockPasscodeBtn();
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().unblockPasscodeModalIsDisplayed()
                ,"Modal isn't displayed correctly ");

        //Click on ok,unblock button
        webCardPanelViewUsersDetailsPage.get().clickOnConfirmUnblockPasscodeBtn();

        //Validate the success message
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().unblockPasscodeSuccessMessageIsDisplayed());
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().getUnblockPasscodeSuccessMessageText().
                contains("Wallet user unblocked successfully"));
        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void uploadDoc() throws InterruptedException {
        // Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        // Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        // Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        // Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        // Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        // Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        // Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByCardUserMobileNumber(configs.get().getCardUserMobileNumber());

        // Validate that search results are displayed
        Assert.assertTrue(webCardPanelSearchUsersPage.get().moreDetailsBtnIsDisplayed(),
                "More Details button is displayed");

        // Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        // Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        //Click on  Documents tab
        webCardPanelViewUsersDetailsPage.get().clickOnDocTab();
        //Assert there is no doc
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().noFileUploaded(),"There are no doc");
        webCardPanelViewUsersDetailsPage.get().clickOnUploadDocs();
        webCardPanelViewUsersDetailsPage.get().uploadDocImage(configs.get().getImagePath());
        Thread.sleep(5500);
        //Assert the image is uploaded
        Assert.assertFalse(webCardPanelViewUsersDetailsPage.get().noFileUploaded(),"The file is uploaded");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void scheduledDoc() throws InterruptedException {
        // Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        // Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        // Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        // Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        // Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        // Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        // Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByCardUserMobileNumber(configs.get().getCardUserMobileNumber());

        // Validate that search results are displayed
        Assert.assertTrue(webCardPanelSearchUsersPage.get().moreDetailsBtnIsDisplayed(),
                "More Details button is displayed");

        // Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        // Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        //Click on  Documents tab
        webCardPanelViewUsersDetailsPage.get().clickOnDocTab();
        //Assert there is no doc
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().noFileUploaded(),"There are no doc");
        webCardPanelViewUsersDetailsPage.get().clickOnUploadDocs();
        webCardPanelViewUsersDetailsPage.get().uploadDocImage(configs.get().getImagePath());
        Thread.sleep(5000);
        //Assert the image is uploaded
        Assert.assertFalse(webCardPanelViewUsersDetailsPage.get().noFileUploaded(),"The file is uploaded");
        //Click on Three dots of doc
        webCardPanelViewUsersDetailsPage.get().clickOnThreeDotBtn();
        //Click on Schedule to SFTP
        webCardPanelViewUsersDetailsPage.get().clickOnScheduleToSFTPBtn();
        //Click on Scheduled btn
        webCardPanelViewUsersDetailsPage.get().clickOnScheduleBtn();
        //Assert Doc is scheduled
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().displayedOfScheduledStatus(),
                "The file become scheduled");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void deleteUploadedDocument() throws InterruptedException {
        // Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        // Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        // Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        // Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        // Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        // Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        // Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByCardUserMobileNumber(configs.get().getCardUserMobileNumber());

        // Validate that search results are displayed
        Assert.assertTrue(webCardPanelSearchUsersPage.get().moreDetailsBtnIsDisplayed(),
                "More Details button is displayed");

        // Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        // Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        // Click on  Documents tab
        webCardPanelViewUsersDetailsPage.get().clickOnDocTab();

        // Assert there is no document
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().noFileUploaded(),"There are no doc");
        webCardPanelViewUsersDetailsPage.get().clickOnUploadDocs();
        webCardPanelViewUsersDetailsPage.get().uploadDocImage(configs.get().getImagePath());
        Thread.sleep(5000);

        // Assert the image is uploaded
        Assert.assertFalse(webCardPanelViewUsersDetailsPage.get().noFileUploaded(),"The file is uploaded");

        // Click on delete document button
        webCardPanelViewUsersDetailsPage.get().clickOnThreeDotBtn();
        webCardPanelViewUsersDetailsPage.get().clickOnDeleteDocBtn();
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().deleteDocModalIsDisplayed()
                ,"Modal isn't displayed correctly ");

        // Click on confirm delete button
        webCardPanelViewUsersDetailsPage.get().clickOnConfirmDeleteDocBtn();

        // Validate the deletion success message
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().deleteDocSuccessMessageIsDisplayed());
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().getDeleteDocSuccessMessageText().
                contains("The document has been successfully deleted"));

        // Assert there is no document displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().noFileUploaded(),"There are no doc");
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void cancelScheduledDocument() throws InterruptedException {
        // Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        // Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        // Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        // Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        // Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        // Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        // Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByCardUserMobileNumber(configs.get().getCardUserMobileNumber());

        // Validate that search results are displayed
        Assert.assertTrue(webCardPanelSearchUsersPage.get().moreDetailsBtnIsDisplayed(),
                "More Details button is displayed");

        // Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        // Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        // Click on  Documents tab
        webCardPanelViewUsersDetailsPage.get().clickOnDocTab();

        // Assert there is no document
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().noFileUploaded(),"There are no doc");
        webCardPanelViewUsersDetailsPage.get().clickOnUploadDocs();
        webCardPanelViewUsersDetailsPage.get().uploadDocImage(configs.get().getImagePath());
        Thread.sleep(5000);

        // Assert the image is uploaded
        Assert.assertFalse(webCardPanelViewUsersDetailsPage.get().noFileUploaded(),"The file is uploaded");

        // Click on schedule document button
        webCardPanelViewUsersDetailsPage.get().clickOnThreeDotBtn();
        webCardPanelViewUsersDetailsPage.get().clickOnScheduleToSFTPBtn();
        webCardPanelViewUsersDetailsPage.get().clickOnScheduleBtn();

        // Assert the document scheduled successfully
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().displayedOfScheduledStatus(),
                "The file is scheduled");
        Thread.sleep(5000);

        // Click on cancel document button
        webCardPanelViewUsersDetailsPage.get().clickOnCancelDocBtn();
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cancelDocModalIsDisplayed()
                ,"Modal isn't displayed correctly ");

        // Click on confirm cancel button
        webCardPanelViewUsersDetailsPage.get().clickOnConfirmCancelDocBtn();

        // Validate cancel success message
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cancelDocSuccessMessageIsDisplayed());
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().getCancelDocSuccessMessageText().
                contains("The document scheduling has been successfully canceled"));
    }
    @Test
    @Tags({@Tag("web"), @Tag("cardservice")})
    public void editCustomerDetails() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Navigation to Card Panel login page
        webCardPanelLoginPage.get().goToUrl(configs.get().getCardServicesAdminPanelBaseURL());
        webCardPanelLoginPage.get().goToPage();

        //Validate that page is open and header is displayed
        Assert.assertTrue(webCardPanelLoginPage.get().isPageDisplayed(),"Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelLoginPage.get().getPageTitleText(),"Login"
                , "Login form header isn't displayed");

        //Login with admin credentials
        webCardPanelLoginPage.get().fillLoginFormAndSubmit(configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword());

        //Validate AdminPanel is displayed correctly
        Assert.assertTrue(webCardPanelDashboardPage.get().dashboardTitleIsDisplayed()
                ,"Dashboard isn't displayed after login");
        Assert.assertEquals(webCardPanelDashboardPage.get().getPageTitleText(),"’’ Welcome to BPay Card ,,");

        //Click on Card Users list from side menu
        Assert.assertTrue(webCardPanelDashboardPage.get().cardUsersListIsDisplayed(),"Card Users");
        webCardPanelDashboardPage.get().clickOnCardUsersDropDownList();

        //Validate that search card users page is displayed
        webCardPanelDashboardPage.get().clickOnSearchCardsTab();
        Assert.assertTrue(webCardPanelSearchUsersPage.get().searchCardPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        Assert.assertEquals(webCardPanelSearchUsersPage.get().getSearchCardsPageTitleText(),
                "Find the customers you are looking for");

        //Search by BCard user mobile number
        webCardPanelSearchUsersPage.get().searchByRegisteredCardUserMobileNumber
                (defaultTestData.get().getRandomTestUser().getPhoneNumber());

        //Click on more details button
        webCardPanelSearchUsersPage.get().clickOnMoreDetailsBtn();

        //Validate that card users details page is displayed
        Assert.assertTrue(webCardPanelViewUsersDetailsPage.get().cardUsersDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");

        //Click on edit customer details button
        webCardPanelViewUsersDetailsPage.get().clickOnEditCustomerDetailsBtn();

        //Validate that edit customer details page is displayed
        Assert.assertTrue(webCardPanelEditCustomerDetailsPage.get().editCustomerDetailsPageTitleIsDisplayed(),
                "Page isn't displayed correctly.");
        //Enter Customer address
        webCardPanelEditCustomerDetailsPage.get().enterCustomerAddress("Breadfast Campus, Kattamya");

        //Select gender Female
        webCardPanelEditCustomerDetailsPage.get().selectCustomerGender();

        //Select Cairo city from the dropdown list
        webCardPanelEditCustomerDetailsPage.get().selectCustomerCity();

        // Click on Confirm button
        webCardPanelEditCustomerDetailsPage.get().clickOnConfirmBtn();

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
}

