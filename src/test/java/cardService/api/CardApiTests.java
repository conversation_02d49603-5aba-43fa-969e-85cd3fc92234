package cardService.api;

import base.BaseTest;
import helpers.dataProviders.TransferReasonDataProvider;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class CardApiTests extends BaseTest {

    @Tags({@Tag("api"), @Tag("card-service"), @Tag("web"), @Tag("database")})
    public void activateCard() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
         cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                 configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Validate that response code is 200 when calling set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                , defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }

    @Test
    @Tags({@Tag("api"), @Tag("card-service"), @Tag("web"), @Tag("database")})
    public void resetCardPin() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Validate that response code is 200 when calling set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                , defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        //Initiate reset-pin API and navigate to activation link to reset user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .resetPin(defaultTestData.get().getCardService()));

        //Run web test to reset card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Delete DB record from card pin reset tries
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                , "DELETE cprt FROM card_hades_db.card_pin_reset_tries cprt\n" +
                        "left join card_hades_db.wallet_users wu on wallet_user_id = wu.id\n" +
                        "WHERE wu.mobile_number = "
                        + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\"");

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }

    @Test
    @Tags({@Tag("api"), @Tag("card-service"), @Tag("web"), @Tag("database")})
    public void getBalance() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());
        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Validate that response code is 200 when calling set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                , defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        //Validate that response code is 200 when calling Get Balance
        Assert.assertEquals(cardServiceApiClient.get().getBalanceEndPointResponse
                (defaultTestData.get().getCardService()), 0.0);
        //Delete DB record
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                , "DELETE cprt FROM card_hades_db.card_pin_reset_tries cprt\n" +
                        "left join card_hades_db.wallet_users wu on wallet_user_id = wu.id\n" +
                        "WHERE wu.mobile_number = "
                        + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\"");

        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                , "DELETE wus FROM card_hades_db.wallet_user_sessions wus\n" +
                        "left join card_hades_db.wallet_users wu on wallet_user_id = wu.id\n" +
                        "WHERE wu.mobile_number = "
                        + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\"");

        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                , "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = "
                        + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\"");
    }

    @Test(dataProvider = "transferReasons", dataProviderClass = TransferReasonDataProvider.class)
    @Tags({@Tag("api"), @Tag("card-service"), @Tag("web"), @Tag("database"), @Tag("customer-app")})
    public void sendMoney(String transferReason) {

        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Validate that response code is 200 when calling set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                , defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        //Adjust balance API
        cardServiceApiClient.get().singleAdjustmentEndPoint(defaultTestData.get().getCardService(),
                configs.get().getCardAdminPanelAdminUserName());

        //Initiate get user details API and get the receiver details
        Assert.assertEquals(cardServiceApiClient.get().receiverDetails(configs.get().getReceiverMobileNumber(),
                defaultTestData.get().getCardService()), "Wallet User Details");

        //Initiate check sender API and get the requestRefNum response
        Assert.assertEquals(cardServiceApiClient.get().checkSender(configs.get().getReceiverMobileNumber(),100,
                defaultTestData.get().getCardService(),transferReason,"Share car ride"),
                (transferReason == null || transferReason.trim().isEmpty())
                ? "LIVING_EXPENSES"
                : transferReason);

        //Validate the success transaction of sending money API
        Assert.assertEquals(cardServiceApiClient.get().completeTransaction
                (defaultTestData.get().getCardService()), "Successful Transaction of 100.0000 EGP");

        // Delete DB dependent records in balance_history table
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.balance_history WHERE user_id = (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }

    @Test
    @Tags({@Tag("api"), @Tag("card-service"), @Tag("web"), @Tag("database")})
    public void getTrxHistory() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
         cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                 configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Validate that response code is 200 when calling set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                , defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        //Validate that response code is 200 and the message is Transactions History
        Assert.assertEquals(cardServiceApiClient.get().getTransactionHistoryEndPoint
                (defaultTestData.get().getCardService()), "Transactions History");

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }

    @Test
    @Tags({@Tag("api"), @Tag("card-service"), @Tag("web"), @Tag("database")})
    public void replaceCard() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to log in to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
         cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                 defaultTestData.get().getRandomTestUser().getNationalId(),
                 configs.get().getCardUserNationalIdExpiryDate(),
                 defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Validate that response code is 200 when calling set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                , defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        Assert.assertEquals(cardServiceApiClient.get().sendOtpForReplaceCard(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                "ReplaceCard",defaultTestData.get().getCardService()),"sms code sent");
        //Initiate replace card API
        Assert.assertEquals(cardServiceApiClient.get().validateReplacedCardOtp(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                JsonPath.given(databaseConnectionFactory.get().executeQuery(defaultTestData.get().
                 getDbConnection(), "select code from card_hades_db.verification v where v.verification_mobile ='"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber()
                        + "'")).getString("[0].code"),"ReplaceCard",
                         defaultTestData.get().getCardService()),"ok");

   Assert.assertTrue(cardServiceApiClient.get().verifyPasscodeForReplacingCard(defaultTestData.get().getRandomTestUser().getId(),
           defaultTestData.get().getCardService()));

        Assert.assertEquals(cardServiceApiClient.get().replaceCard(defaultTestData.get().getCardService(),
                JsonPath.given(databaseConnectionFactory.get().executeQuery(defaultTestData.get().
                getDbConnection(),
                                "select code from card_hades_db.verification v where v.verification_mobile ='"
                                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'"))
                                        .getString("[0].code")),"Your card has been replaced");

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        // Delete DB dependent records in wallet_user_sessions table
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id ="
                        + " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        // Delete DB the user record
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
    @Test
    @Tags({@Tag("api"), @Tag("card-service"), @Tag("web"), @Tag("database"), @Tag("customer-app")})
    public void getCardDetails() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
         cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                 configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Validate that response code is 200 when calling set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                , defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        // Validate card user's details
        Assert.assertEquals(cardServiceApiClient.get().getCardDetails(
                defaultTestData.get().getCardService()), "Card Details");

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }

    @Test
    @Tags({@Tag("api"), @Tag("card-service"), @Tag("web"), @Tag("database")})
    public void forgetPasscode() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Validate that response code is 200 when calling set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                , defaultTestData.get().getCardService());

        //send otp to reset passcode
        Assert.assertEquals(cardServiceApiClient.get().resetPasscode(defaultTestData.get().getRandomTestUser().getId()),
                "sms code sent");

        //retrieve to OTP sent from database and validate it
        Assert.assertEquals(cardServiceApiClient.get().validateCode(defaultTestData.get().getRandomTestUser().getId(),
                        JsonPath.given(databaseConnectionFactory.get().executeQuery(defaultTestData.get().
                                                getDbConnection()
        , "select code from card_hades_db.verification v where v.verification_mobile ='" +
                                        defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'")).getString("[0].code"),
                defaultTestData.get().getCardService()),"Valid code");

        //validate the national ID
        Assert.assertEquals(cardServiceApiClient.get().validateNationalID(defaultTestData.
                get().getRandomTestUser().getId(), defaultTestData.get().getCardService()),"ok");

        //update the passcode
        Assert.assertEquals(cardServiceApiClient.get().updatePasscode(defaultTestData.get().getCardService(),
                JsonPath.given(databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                                , "select code from card_hades_db.verification v where v.verification_mobile ='"
                                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'"))
                .getString("[0].code")),"Updated successfully");

        //Delete DB dependent records in wallet_user_sessions table
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id ="
                        + " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB the user record
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");

    }

    @Tags({@Tag("api"), @Tag("card-service"), @Tag("web"), @Tag("database")})
    public void editCustomerDetails() {
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Initiate set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        // Delete DB dependent records in wallet_user_sessions table
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id ="
                        + " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        // Delete DB the user record
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }
}
