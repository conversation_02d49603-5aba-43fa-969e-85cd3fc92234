package cardService.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class CardActivationTests extends BaseTest {
    @Test(groups = {"payment-api-smoke", "payment"})
    @Tags({@Tag("api"), @Tag("card-service"), @Tag("web"), @Tag("database")})
    public void validateCardCreationFlow(){
        //Register a new user with random mobile number
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //API to login to Pay SDK
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Initiate create card user API
        cardServiceApiClient.get().createCardUser(defaultTestData.get().getRandomTestUser().getPhoneNumber(),
                defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                "بثينة", "مصطفى",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to pending
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Pending");

        //Initiate set passcode API
        cardServiceApiClient.get().setPasscode(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService());

        //Validate that card status is changed to registered
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Registered");

        //Add customer details API
        cardServiceApiClient.get().editCustomerDetails(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        , "SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = \""
                                + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                "بثينة", "مصطفي",
                defaultTestData.get().getRandomTestUser().getNationalId(),
                configs.get().getCardUserNationalIdExpiryDate(),
                defaultTestData.get().getRandomTestUser().getAddress(),
                "female", "Cairo",
                defaultTestData.get().getRandomTestUser().getDateOfBirth());

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());

        //Change card status from registered to received
        cardServiceApiClient.get().collectCard(defaultTestData.get().getCardService(),
                databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                        ,"SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = "
                                + "\"" + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "\""),
                configs.get().getPickupLocationId());

        //Validate that card status is changed to Received
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Received");

        //Login user by passcode to get user's token
        cardServiceApiClient.get().loginByPasscodeAndGetUserToken(defaultTestData.get().getRandomTestUser().getId()
                ,defaultTestData.get().getCardService());

        //Initiate linking API
        cardServiceApiClient.get().linkCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to linked
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Linked");

        //Initiate set-pin API and navigate to activation link to set user's pin
        webDriver.get().navigate().to(cardServiceApiClient.get()
                .setPin(defaultTestData.get().getCardService()));

        //Run web test to create card pin
        Assert.assertTrue(setCardPinPage.get().isSetPinPageDisplayed());
        setCardPinPage.get().enterCardPin();
        Assert.assertTrue(setCardPinPage.get().isConfirmPinPageDisplayed());
        setCardPinPage.get().confirmCardPin();
        Assert.assertTrue(setCardPinPage.get().isSuccessPageDisplayed());

        //Initiate activate API
        cardServiceApiClient.get().activateCard(defaultTestData.get().getCardService());

        //Validate that card status is changed to active
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "Active");

        //Delete DB record from actions_logger
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.actions_logger WHERE user_id = "
                        + "(SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_user_sessions
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_user_sessions WHERE wallet_user_id =" +
                        " (SELECT id FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "')");

        //Delete DB record from wallet_users
        databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection(),
                "DELETE FROM card_hades_db.wallet_users WHERE mobile_number = '"
                        + defaultTestData.get().getRandomTestUser().getPhoneNumber() + "'");
    }

    @Test(groups = "card-Hades-Backend-BF-smoke")
    @Tags({@Tag("api"), @Tag("card-service")})
    public void validateCardStatusApi(){
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardMobileLoginSchemeUserName()
                , configs.get().getCardMobileLoginSchemePassword()
                , defaultTestData.get().getCardService()));

        //Validate that response code is 200
        Assert.assertEquals(cardServiceApiClient.get().getCardStatus(defaultTestData.get().getRandomTestUser().getId(),
                defaultTestData.get().getCardService()), "NA");
    }

    @Test(groups = "card")
    @Tags({@Tag("api"), @Tag("card-service")})
    public void validateCardsPoolApi(){
        //Login with admin username and password to get the token
        defaultTestData.get().setCardService(cardServiceApiClient.get().getCardServiceToken(
                configs.get().getCardAdminPanelAdminUserName()
                , configs.get().getCardAdminPanelAdminPassword()
                , defaultTestData.get().getCardService()));

        //Initiate cards pool API and get the BCID
        cardServiceApiClient.get().getCardsPool(
                configs.get().getCardServiceContractNumber()
                , configs.get().getCardServiceTypeId()
                , configs.get().getCardServiceProductNumber()
                , defaultTestData.get().getCardService());
    }
}
