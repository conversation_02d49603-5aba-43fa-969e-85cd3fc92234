package base;

import helpers.*;
import helpers.apiClients.YeloApiClient;
import helpers.apiClients.mobileApiClients.*;
import helpers.apiClients.mobileApiClients.foodAggregatorApiClients.FoodAggregatorRestaurantsApiClient;
import helpers.apiClients.webApiClients.*;
import helpers.apiValidator.CreateOrderApiValidator;
import helpers.apiValidator.GetOrderApiValidator;
import helpers.factories.*;
import helpers.factories.dataFactories.UserDataFactory;
import helpers.mobileTestsExecutionHelpers.android.AndroidNativeAggregatorTestsExecutionHelper;
import helpers.mobileTestsExecutionHelpers.ios.IosNativeAggregatorTestsExecutionHelper;
import helpers.rolesValidators.*;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.AndroidStartScreenRecordingOptions;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.ios.IOSStartScreenRecordingOptions;
import io.qameta.allure.Allure;
import io.restassured.path.json.JsonPath;
import modals.Payment.ApsCcWebPage;
import modals.cardsAdminPanel.*;
import modals.chatbotSdk.ChatbotSdkHostPage;
import modals.chatbotSdk.WebChatbotSdk;
import modals.customerApp.android.*;
import modals.customerApp.android.androidAccountSettingsPage.AndroidAccountSettingsPage;
import modals.customerApp.android.androidAccountSettingsPage.MyAddresses.AndroidDisabledLocationAccessModal;
import modals.customerApp.android.androidAccountSettingsPage.MyAddresses.AndroidMyAddressesScreen;
import modals.customerApp.android.androidAccountSettingsPage.UpdatePersonalInfo.AndroidUpdateAccountSettingScreen;
import modals.customerApp.android.androidAccountSettingsPage.UpdatePersonalInfo.AndroidUpdatePhoneNumberOTPVerificationScreen;
import modals.customerApp.android.androidAccountSettingsPage.androidDeleteAccount.AndroidDeleteAccountOTPVerificationScreen;
import modals.customerApp.android.androidAccountSettingsPage.androidDeleteAccount.AndroidDeleteAccountScreen;
import modals.customerApp.android.androidAccountSettingsPage.androidDeleteAccount.AndroidDeleteRequestScreen;
import modals.customerApp.android.androidAccountSettingsPage.androidDeleteAccount.AndroidVerifyDeleteAccountScreen;
import modals.customerApp.android.androidCheckoutScreens.AndroidCartScreen;
import modals.customerApp.android.androidCheckoutScreens.AndroidCheckoutScreen;
import modals.customerApp.android.androidCheckoutScreens.AndroidOrderSuccessScreen;
import modals.customerApp.android.androidCreateAddressScreens.AndroidAddressCreateSuccessModal;
import modals.customerApp.android.androidCreateAddressScreens.AndroidCreateAddressScreen;
import modals.customerApp.android.androidHomePage.*;
import modals.customerApp.android.androidMoreScreen.*;
import modals.customerApp.android.androidMoreScreen.androidActivityHistory.AndroidActivityHistoryScreen;
import modals.customerApp.android.androidMoreScreen.androidActivityHistory.AndroidBillsTabScreen;
import modals.customerApp.android.androidPayPage.*;
import modals.customerApp.android.androidPermissionAlerts.AndroidLocationPermissionAlert;
import modals.customerApp.androidNative.*;
import modals.customerApp.androidNative.AndroidNativeMoreScreen.*;
import modals.customerApp.androidNative.AndroidNativeMoreScreen.AndroidNativeAddressManagementScreens.AndroidNativeAddressDetailsScreen;
import modals.customerApp.androidNative.AndroidNativeMoreScreen.AndroidNativeAddressManagementScreens.AndroidNativeSavedAddressScreen;
import modals.customerApp.androidNative.AndroidNativeMoreScreen.AndroidNativeDeleteAccountScreens.AndroidNativeDeleteAccountOTPVerificationScreen;
import modals.customerApp.androidNative.AndroidNativeMoreScreen.AndroidNativeDeleteAccountScreens.AndroidNativeDeleteAccountScreen;
import modals.customerApp.androidNative.androidNativeCheckoutPage.AndroidNativeCheckoutScreen;
import modals.customerApp.androidNative.androidNativeHomePage.AndroidNativeCarouselScreen;
import modals.customerApp.androidNative.androidNativeHomePage.AndroidNativeHomeScreen;
import modals.customerApp.androidNative.androidNativeHomePage.AndroidNativeRateOrderPopup;
import modals.customerApp.androidNative.foodAggregator.AndroidNativeFoodAggregatorHomeScreen;
import modals.customerApp.androidNative.foodAggregator.AndroidNativeFoodAggregatorRestaurantDetailsScreen;
import modals.customerApp.ios.*;
import modals.customerApp.ios.iosAlerts.IosLocationPermissionAlert;
import modals.customerApp.ios.iosAlerts.IosNotificationsPermissionsAlert;
import modals.customerApp.ios.iosAlerts.IosTrackingPermissionAlert;
import modals.customerApp.ios.iosCheckoutScreen.IosCartScreen;
import modals.customerApp.ios.iosCheckoutScreen.IosCheckoutScreen;
import modals.customerApp.ios.iosCheckoutScreen.IosOrderSuccessScreen;
import modals.customerApp.ios.iosHomePage.*;
import modals.customerApp.ios.iosMoreScreen.*;
import modals.customerApp.ios.iosMoreScreen.UpdatePersonalInfo.IosUpdateAccountSettingScreen;
import modals.customerApp.ios.iosMoreScreen.UpdatePersonalInfo.IosUpdatePhoneNumberOTPVerficationScreen;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.IosAccountSettingsScreen;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosCreateAddressScreens.IosAddressCreateSuccessModal;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosCreateAddressScreens.IosCreateAddressScreen;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosDeleteAccountScreen.IosDeleteAccountOTPVerificationScreen;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosDeleteAccountScreen.IosDeleteAccountScreen;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosDeleteAccountScreen.IosDeleteRequestScreen;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosDeleteAccountScreen.IosVerifyDeleteAccountScreen;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosManageAddressesScreens.IosDeleteAddressConfirmationModal;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosManageAddressesScreens.IosDeleteAddressErrorModal;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosManageAddressesScreens.IosDisabledLocationModal;
import modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosManageAddressesScreens.IosMyAddressesScreen;
import modals.customerApp.ios.iosMoreScreen.iosActivityHistoryScreens.IosActivityHistoryScreen;
import modals.customerApp.ios.iosMoreScreen.iosActivityHistoryScreens.IosBillsTabScreen;
import modals.customerApp.ios.iosPayPage.*;
import modals.customerApp.iosNative.*;
import modals.customerApp.iosNative.foodAggregator.IosNativeFoodAggregatorHomeScreen;
import modals.customerApp.iosNative.foodAggregator.IosNativeFoodAggregatorRestaurantDetailsScreen;
import modals.customerApp.iosNative.iosNativeCheckoutPage.IosNativeCheckoutScreen;
import modals.customerApp.iosNative.iosNativeCheckoutPage.IosNativeOrderSuccessScreen;
import modals.customerApp.iosNative.iosNativeHomePage.IosNativeCollectionDetailsScreen;
import modals.customerApp.iosNative.iosNativeHomePage.IosNativeHomeScreen;
import modals.customerApp.iosNative.iosNativeMorePage.*;
import modals.customerApp.iosNative.iosNativeMorePage.iosNativeDeleteAccountScreens.IosNativeDeleteAccountOTPVerificationScreen;
import modals.customerApp.iosNative.iosNativeMorePage.iosNativeDeleteAccountScreens.IosNativeDeleteAccountScreen;
import modals.customerApp.iosNative.iosNativeMorePage.iosNativeDeleteAccountScreens.IosNativeDeleteSuccessScreen;
import modals.fleetApp.android.AndroidSideMenuScreen;
import modals.fleetApp.android.AndroidSplashScreen;
import modals.fleetApp.android.bfShops.*;
import modals.mainAdminPortal.*;
import modals.midMileApp.android.AndroidActions;
import modals.midMileApp.android.AndroidLoginPage;
import modals.midMilePage.OrdersPage;
import modals.wordpressAdmin.*;
import models.*;
import org.json.JSONObject;
import org.openqa.selenium.WebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.testng.ITestResult;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;

import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

public class BaseTest {
    private static final ThreadLocal<Logger> logger = new ThreadLocal<>();
    private static final Logger log = LoggerFactory.getLogger(BaseTest.class);
    protected static ThreadLocal<DataHelper> dataHelper = new ThreadLocal<>();
    protected static ThreadLocal<Configs> configs = new ThreadLocal<>();
    protected static ThreadLocal<ConfigurationsManagementHelper> configurationsManagementHelper = new ThreadLocal<>();
    protected static ThreadLocal<MobileDriversFactory> mobileDriversFactory = new ThreadLocal<>();
    protected static ThreadLocal<AppiumServer> androidAppiumServerController = new ThreadLocal<>();
    protected static ThreadLocal<AppiumServer> iosAppiumServerController = new ThreadLocal<>();
    protected static ThreadLocal<ServerFactory> serverFactory = new ThreadLocal<>();
    protected static ThreadLocal<SetUpHelper> setUpHelper = new ThreadLocal<>();
    protected static ThreadLocal<WebDriver> webDriver = new ThreadLocal<>();
    protected static ThreadLocal<AndroidDriver> androidDriver = new ThreadLocal<>();
    protected static ThreadLocal<AndroidDevice> androidDevice = new ThreadLocal<>();
    protected static ThreadLocal<IOSDriver> iosDriver = new ThreadLocal<>();
    protected static ThreadLocal<IosDevice> iosDevice = new ThreadLocal<>();
    protected static ThreadLocal<TestExecutionHelper> testExecutionHelper = new ThreadLocal<>();
    protected static ThreadLocal<EncryptionHelper> cardServiceEncryptionHelper = new ThreadLocal<>();
    protected static ThreadLocal<TestData> defaultTestData = new ThreadLocal<>();
    protected static ThreadLocal<List<Role>> rolesList = new ThreadLocal<>();
    protected static ThreadLocal<ValidationResults> validationResults = new ThreadLocal<>();
    protected static ThreadLocal<SwitcherRolesValidators> switcherRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<PlanningCenterRolesValidators> planningCenterRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<PagesAdminPageRolesValidators> pagesAdminPageRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<AppFaqsRolesValidators> appFaqsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<CancellationReasonsRolesValidators> cancellationReasonsRolesValidators =
            new ThreadLocal<>();
    protected static ThreadLocal<RecommendationsRolesValidators> recommendationsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<BulkDiscountsRolesValidators> bulkDiscountsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<AttendanceRolesValidators> attendanceRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<SignaturesRolesValidators> signaturesRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<PostsRolesValidators> postsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<FlyersRolesValidators> flyersRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<ReferralsRolesValidators> referralsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<AppMessagesRolesValidators> appMessagesRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<ProductsSortingRolesValidators> productsSortingRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<InternalCategoriesRolesValidators> internalCategoriesRolesValidators =
            new ThreadLocal<>();
    protected static ThreadLocal<ScheduledOrdersRolesValidators> scheduledOrdersRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<CategoriesRolesValidators> categoriesRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<IngredientsRolesValidators> ingredientsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<ProductsRolesValidators> productsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<BrandsRolesValidators> brandsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<CouponsRolesValidators> couponsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<OrdersRolesValidators> ordersRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<AreasRolesValidators> areasRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<EventsRolesValidators> eventsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<CareersRolesValidators> careersRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<RepeatedOrdersRolesValidators> repeatedOrdersRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<BannersRolesValidators> bannersRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<PopupsRolesValidators> popupsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<CollectionsRolesValidators> collectionsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<GeneralSettingsRolesValidators> generalSettingsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<CXSettingsRolesValidators> cxSettingsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<OrderSmsRolesValidators> orderSmsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<IntercomSettingsRolesValidators> intercomSettingsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<CssSettingsRolesValidators> cssSettingsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<SlackSettingsRolesValidators> slackSettingsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<VodafoneCouponRolesValidators> vodafoneCouponRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<CCDiscountRolesValidators> ccDiscountRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<CareersSettingsRolesValidators> careersSettingsRolesValidators = new ThreadLocal<>();
    protected static ThreadLocal<RecommendationSettingsRolesValidators> recommendationSettingsRolesValidators =
            new ThreadLocal<>();
    protected static ThreadLocal<IosTestsExecutionHelper> iosTestsExecutionHelper = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeTestsExecutionHelper> iosNativeTestsExecutionHelper = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeAggregatorTestsExecutionHelper> iosNativeAggregatorTestsExecutionHelper
            = new ThreadLocal<>();
    protected static ThreadLocal<AndroidTestsExecutionHelper> androidTestsExecutionHelper = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeTestsExecutionHelper> androidNativeTestsExecutionHelper
            = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeAggregatorTestsExecutionHelper> androidNativeAggregatorTestsExecutionHelper
            = new ThreadLocal<>();
    protected static ThreadLocal<WebDriversFactory> webDriversFactory = new ThreadLocal<>();
    protected static ThreadLocal<DatabaseConnectionFactory> databaseConnectionFactory = new ThreadLocal<>();
    protected static ThreadLocal<Process> webRecordingProcess = new ThreadLocal<>();
    protected static ThreadLocal<WebVideoRecorderFactory> webVideoRecorderFactory = new ThreadLocal<>();
    protected static ThreadLocal<TunnelServerConnection> tunnelServerConnection = new ThreadLocal<>();
    protected static ThreadLocal<AndroidLandingScreen> androidLandingScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidFreshChatScreen> androidFreshChatScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidReferralScreen> androidReferralScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidRecommendationScreen> androidRecommendationScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidPhoneNumberScreen> androidPhoneNumberScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidCountriesListScreen> androidCountriesListScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidOTPVerificationScreen> androidOtpVerificationScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidProductScreen> androidProductScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidCreateAccountScreen> androidCreateAccountInfoScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidRegisterSuccessScreen> androidRegisterSuccessScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<IosOpenedBannerModalScreen> iosOpenedBannerModalScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidLocationPermissionAlert> androidLocationPermissionAlert = new ThreadLocal<>();
    protected static ThreadLocal<AndroidHomeScreen> androidHomeScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidMoreScreen> androidMoreScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidDisabledLocationAccessModal> androidDisabledLocationAccess = new ThreadLocal<>();
    protected static ThreadLocal<AndroidTalkToUsScreen> androidTalkToUsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidSetAddressScreen> androidSetAddressScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidCreateAddressScreen> androidCreateAddressScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidAccountSettingsPage> androidAccountSettingsPage = new ThreadLocal<>();
    protected static ThreadLocal<AndroidDeleteAccountScreen> androidDeleteAccountScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidVerifyDeleteAccountScreen> androidVerifyDeleteAccountScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<AndroidDeleteAccountOTPVerificationScreen> androidDeleteAccountOTPVerificationScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<AndroidDeleteRequestScreen> androidDeleteRequestScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidPayScreen> androidPayScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidBillingCategoryScreen> androidBillingCategoryScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidCountriesSelectionScreen> androidCountriesSelectionScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidProviderScreen> androidProviderScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<AndroidInvoiceSummaryScreen> androidInvoiceSummaryScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<AndroidPaymentSuccessScreen> androidPaymentSuccessScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<AndroidOrderDetailsScreen> androidOrderDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNowAndTomorrowModal> androidNowAndTomorrowModal = new ThreadLocal<>();
    protected static ThreadLocal<AndroidCategoryScreen> androidCategoryScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidAddressSelectionScreen> androidAddressSelectionScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidAddressCreateSuccessModal> androidAddressCreateSuccessModal = new ThreadLocal<>();
    protected static ThreadLocal<AndroidCartScreen> androidCartScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidOrderSuccessScreen> androidOrderSuccessScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidCheckoutScreen> androidCheckoutScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidUpdateAccountSettingScreen> androidUpdateAccountSettingScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<AndroidUpdatePhoneNumberOTPVerificationScreen> androidUpdatePhoneNumberOTPVerificationScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<AndroidAccountSettingsScreen> androidAccountSettingsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidMyAddressesScreen> androidMyAddressesScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidActivityHistoryScreen> androidActivityHistoryScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidBillsTabScreen> androidBillsTabScreen = new ThreadLocal<>();

    protected static ThreadLocal<AndroidBreadfastRewardsScreen> androidBreadfastRewardsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidChooseCountryModal> androidChooseCountryModal = new ThreadLocal<>();
    protected static ThreadLocal<AndroidChooseLanguageModal> androidChooseLanguageModal = new ThreadLocal<>();
    protected static ThreadLocal<AndroidFavoritesScreen> androidFavoritesScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidFreeCreditScreen> androidFreeCreditScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidHelpScreen> androidHelpScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidSavedCardScreen> androidSavedCardScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidAddCardInfoScreen> androidAddCardInfoScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidSearchScreen> androidSearchScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidSearchResultScreen> androidSearchResultScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidCardSelectionModal> androidCardSelectionModal = new ThreadLocal<>();
    protected static ThreadLocal<AndroidChatbotScreen> androidChatbotScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidBusyModalScreen> androidBusyModalScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidOpenedBannerModalScreen> androidOpenedBannerModalScreen = new ThreadLocal<>();

    protected static ThreadLocal<AndroidNativeCountriesSelectionScreen> androidNativeCountriesSelectionScreen
            = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeLandingScreen> androidNativeLandingScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativePhoneNumberScreen> androidNativePhoneNumberScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativePhoneCountrySelectionDropdownScreen>
            androidNativePhoneCountrySelectionDropdownScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeOtpVerificationScreen> androidNativeOtpVerificationScreen
            = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeCreateAccountScreen> androidNativeCreateAccountScreen
            = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeRegisterSuccessScreen> androidNativeRegisterSuccessScreen
            = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeHomeScreen> androidNativeHomeScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeRateOrderPopup> androidNativeRateOrderPopup = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeMoreScreen> androidNativeMoreScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeFavouriteScreen> androidNativeFavouriteScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeAccountSettingsScreen> androidNativeAccountSettingsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativePersonalInfoScreen> androidNativePersonalInfoScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeSavedAddressScreen> androidNativeSavedAddressScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeAddressDetailsScreen> androidNativeAddressDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeEmailAddressScreen> androidNativeEmailAddressScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeChangeCountryModalScreen> androidNativeChangeCountryModalScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeChatBotScreen> androidNativeChatBotScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeProductScreen> androidNativeProductScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeCategoriesDetailsScreen> androidNativeCategoriesDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeCartScreen> androidNativeCartScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeInternalCustomizedProductScreen> androidNativeInternalCustomizedProductScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeDeleteAccountScreen> androidNativeDeleteAccountScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeDeleteAccountOTPVerificationScreen> androidNativeDeleteAccountVerifyNumberScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeCheckoutScreen> androidNativeCheckoutScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeCollectionDetailsScreen> androidNativeCollectionDetailsScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeOrderDetailsScreen> androidNativeOrderDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeCarouselScreen> androidNativeCarouselScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeFoodAggregatorHomeScreen> androidNativeFoodAggregatorHomeScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativeFoodAggregatorRestaurantDetailsScreen> androidNativeFoodAggregatorResturantDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<GoogleLoginPage> webGoogleLoginPage = new ThreadLocal<>();
    protected static ThreadLocal<HomePage> webHomePage = new ThreadLocal<>();
    protected static ThreadLocal<LoginPage> webLoginPage = new ThreadLocal<>();
    protected static ThreadLocal<SwitcherPage> webSwitcherPage = new ThreadLocal<>();
    protected static ThreadLocal<UsersListPage> webUsersListPage = new ThreadLocal<>();
    protected static ThreadLocal<AppFaqsAdminPage> webAppFaqsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<AppMessagesAdminPage> webAppMessagesAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<AreasAdminPage> webAreasAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<BrandsAdminPage> webBrandsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<CareersAdminPage> webCareersAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<CouponsAdminPage> webCouponsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<EventsAdminPage> webEventsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<FlyersAdminPage> webFlyersAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<IngredientsAdminPage> webIngredientsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<InternalCategoriesAdminPage> webInternalCategoriesAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<OrdersAdminPage> webOrdersAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<PagesAdminPage> webPagesAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<PostsAdminPage> webPostsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<ProductCategoriesAdminPage> webProductsCategoriesAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<ProductsAdminPage> webProductsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<ReferralsAdminPage> webReferralsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<RepeatedOrdersAdminPage> webRepeatedOrdersAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<RecommendationSettingsAdminPage> webRecommendationSettingsAdminPage =
            new ThreadLocal<>();
    protected static ThreadLocal<CareersSettingsAdminPage> webCareersSettingsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<CCDiscountAdminPage> webCCDiscountAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<VodafoneCouponAdminPage> webVodafoneCouponAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<SlackSettingsAdminPage> webSlackSettingsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<CssSettingsAdminPage> webCSSSettingsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<IntercomSettingsAdminPage> webIntercomSettingsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<OrderSmsAdminPage> webOrderSmsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<CXSettingsAdminPage> webCXSettingsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<GeneralSettingsAdminPage> webGeneralSettingsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<AttendanceAdminPage> webAttendanceAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<BulkDiscountsAdminPage> webBulkDiscountsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<CancellationReasonsAdminPage> webCancellationReasonsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<CategoriesSortingAdminPage> webCategoriesSortingAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<PlanningCenterAdminPage> webPlanningCenterAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<RecommendationsAdminPage> webRecommendationsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<ScheduledOrdersAdminPage> webScheduledOrdersAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<SignatureAdminPage> webSignatureAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<BannersAdminPage> webBannersAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<PopUpsAdminPage> webPopUpsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<CollectionsAdminPage> webCollectionsAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<ControlRoomV2AdminPage> webControlRoomV2AdminPage = new ThreadLocal<>();
    protected static ThreadLocal<DevlieryCapacityManagementAdminPage> webDeliveryCapacityManagementPage = new ThreadLocal<>();
    protected static ThreadLocal<CardAdminPanelLoginPage> webCardPanelLoginPage = new ThreadLocal<>();
    protected static ThreadLocal<ChatbotSdkHostPage> webChatbotSdkHostPage = new ThreadLocal<>();
    protected static ThreadLocal<CardPanelDashboard> webCardPanelDashboardPage = new ThreadLocal<>();
    protected static ThreadLocal<SearchCardsUsers> webCardPanelSearchUsersPage = new ThreadLocal<>();
    protected static ThreadLocal<ViewCardUsersDetails> webCardPanelViewUsersDetailsPage = new ThreadLocal<>();
    protected static ThreadLocal<EditCustomerDetailsPage> webCardPanelEditCustomerDetailsPage= new ThreadLocal<>();
    protected static ThreadLocal<WebChatbotSdk> webChatbotSdk = new ThreadLocal<>();

    protected static ThreadLocal<IosLandingScreen> iosLandingScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNotificationsPermissionsAlert> iosNotificationsPermissionsAlert
            = new ThreadLocal<>();
    protected static ThreadLocal<IosPhoneNumberScreen> iosPhoneNumberScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosCountriesListScreen> iosCountriesListScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosOTPVerificationScreen> iosOTPVerificationScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosCreateAccountScreen> iosCreateAccountScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosRegisterSuccessScreen> iosRegisterSuccessScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosTrackingPermissionAlert> iosTrackingPermissionAlert = new ThreadLocal<>();
    protected static ThreadLocal<IosHomeScreen> iosHomeScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosMoreScreen> iosMoreScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosAccountSettingsScreen> iosAccountSettingsScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosDeleteAccountScreen> iosDeleteAccountScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosVerifyDeleteAccountScreen> iosVerifyDeleteAccountScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosDeleteAccountOTPVerificationScreen> iosDeleteAccountOTPVerificationScreen
            = new ThreadLocal<>();
    protected static ThreadLocal<IosDeleteRequestScreen> iosDeleteRequestScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosUpdateAccountSettingScreen> iosUpdateAccountSettingScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosUpdatePhoneNumberOTPVerficationScreen> iosUpdatePhoneNumberOTPVerficationScreen
            = new ThreadLocal<>();
    protected static ThreadLocal<IosLocationPermissionAlert> iosLocationPermissionAlert = new ThreadLocal<>();
    protected static ThreadLocal<IosSetAddressScreen> iosSetAddressScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosCheckoutScreen> iosCheckoutScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosCreateAddressScreen> iosCreateAddressScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosAddressCreateSuccessModal> iosAddressCreateSuccessModal = new ThreadLocal<>();
    protected static ThreadLocal<IosReferralScreen> iosReferralScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosFreshChatScreen> iosFreshChatScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosCountriesSelectionScreen> iosCountriesSelectionScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosRecommendationsScreen> iosRecommendationsScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosAddressSelectionScreen> iosAddressSelectionScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosPayScreen> iosPayScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosBillingCategoryScreen> iosBillingCategoryScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosProviderScreen> iosProviderScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosInvoiceSummaryScreen> iosInvoiceSummaryScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosInvoiceSuccessScreen> iosInvoiceSuccessScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosAddCardInfoScreen> iosAddCardInfoScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosCategoryScreen> iosCategoryScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosProductDetailsScreen> iosProductDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNowAndTomorrowModal> iosNowAndTomorrowModal = new ThreadLocal<>();
    protected static ThreadLocal<IosActivityHistoryScreen> iosActivityHistoryScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosBreadfastRewardsScreen> iosBreadfastRewardsScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosChooseCountryModal> iosChooseCountryModal = new ThreadLocal<>();
    protected static ThreadLocal<IosChooseLanguageModal> iosChooseLanguageModal = new ThreadLocal<>();
    protected static ThreadLocal<IosFavoritesScreen> iosFavoritesScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosFreeCreditScreen> iosFreeCreditScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosHelpScreen> iosHelpScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosCartScreen> iosCartScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosCardSelectionModal> iosCardSelectionModal = new ThreadLocal<>();
    protected static ThreadLocal<IosOrderSuccessScreen> iosOrderSuccessScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosSavedCardsScreen> iosSavedCardsScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosBillsTabScreen> iosBillsTabScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosMyAddressesScreen> iosMyAddressesScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosDeleteAddressConfirmationModal> iosDeleteAddressConfirmationModal
            = new ThreadLocal<>();
    protected static ThreadLocal<IosDeleteAddressErrorModal> iosDeleteAddressErrorModal = new ThreadLocal<>();
    protected static ThreadLocal<IosDisabledLocationModal> iosDisabledLocationModal = new ThreadLocal<>();
    protected static ThreadLocal<IosSearchScreen> iosSearchScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosSearchResultScreen> iosSearchResultScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosChatbotScreen> iosChatbotScreen = new ThreadLocal<>();

    protected static ThreadLocal<IosNativeCountriesSelectionScreen> iosNativeCountriesSelectionScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<IosNativeLandingScreen> iosNativeLandingScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativePhoneNumberScreen> iosNativePhoneNumberScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativePhoneCountrySelectionDropdownScreen>
            iosNativePhoneCountrySelectionDropdownScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeOtpVerificationScreen> iosNativeOtpVerificationScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeCreateAccountScreen> iosNativeCreateAccountScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeRegisterSuccessScreen> iosNativeRegisterSuccessScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeHomeScreen> iosNativeHomeScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeMoreScreen> iosNativeMoreScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeCartScreen> iosNativeCartScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeUpdateCustomizedProductScreen> iosNativeUpdateCustomizedProductScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<IosNativeRemoveCustomizedItemsScreen> iosNativeRemoveCustomizedItemsScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<IosNativeInternalCustomizedProductScreen> iosNativeInternalCustomizedProductScreen =
            new ThreadLocal<>();
    protected static ThreadLocal<IosNativeCheckoutScreen> iosNativeCheckoutScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeOrderSuccessScreen> iosNativeOrderSuccessScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeCategoryDetailsScreen> iosNativeCategoryDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeAccountSettingsScreen> iOSNativeAccountSettingsScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeAddressDetailsScreen> iosNativeAddressDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeSavedAddressScreen> iosNativeSavedAddressScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativePersonalInfoScreen> iosNativePersonalInfoScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeFavouriteScreen> iosNativeFavouriteScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeEmailAddressScreen> iosNativeEmailAddressScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeChatBotScreen> iosNativeChatBotScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeProductScreen> iosNativeProductScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeChangeCountryModalScreen> iosNativeChangeCountryModalScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeDeleteAccountScreen> iosNativeDeleteAccountScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeDeleteAccountOTPVerificationScreen> iosNativeDeleteAccountOTPVerificationScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeDeleteSuccessScreen> iosNativeDeleteSuccessScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeCollectionDetailsScreen> iosNativeCollectionDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeFoodAggregatorHomeScreen> iosNativeFoodAggregatorHomeScreen = new ThreadLocal<>();
    protected static ThreadLocal<IosNativeFoodAggregatorRestaurantDetailsScreen> iosNativeFoodAggregatorResturantDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<TransitApiClient> transitApiClient = new ThreadLocal<>();
    protected static ThreadLocal<InventoryApiClient> inventoryApiClient = new ThreadLocal<>();
    protected static ThreadLocal<KitchenApiClient> kitchenApiClient = new ThreadLocal<>();
    protected static ThreadLocal<AdminAuthorizationApiClient> adminAuthorizationApiClient = new ThreadLocal<>();
    protected static ThreadLocal<SwitcherApiClient> switcherApiClient = new ThreadLocal<>();
    protected static ThreadLocal<ControlRoomV2ApiClient> controlRoomV2ApiClient = new ThreadLocal<>();
    protected static ThreadLocal<MidMileOrdersApiClient> midMileOrdersApiClient = new ThreadLocal<>();
    protected static ThreadLocal<MidMileAppApiClient> midMileAppApiClient = new ThreadLocal<MidMileAppApiClient>();
    protected static ThreadLocal<CouponsApiClient> couponsApiClient = new ThreadLocal<>();
    protected static ThreadLocal<AddressApiClient> addressApiClient = new ThreadLocal<>();
    protected static ThreadLocal<OrderApiClient> orderApiClient = new ThreadLocal<>();
    protected static ThreadLocal<OrderPaymentApiClient> orderPaymentApiClient = new ThreadLocal<>();
    protected static ThreadLocal<DeliveryCapacityManagementApiClient> deliveryCapacityManagementApiClient = new ThreadLocal<>();
    protected static ThreadLocal<FleetAppApiClient> fleetAppApiClient = new ThreadLocal<>();
    protected static ThreadLocal<MobileWarehousesApiClient> mobileWarehousesApiClient = new ThreadLocal<>();
    protected static ThreadLocal<MobileAuthorizationApiClient> mobileAuthorizationApiClient = new ThreadLocal<>();
    protected static ThreadLocal<MobilePayServicesApiClient> mobilePayServicesApiClient = new ThreadLocal<>();
    protected static ThreadLocal<CardServiceApiClient> cardServiceApiClient = new ThreadLocal<>();
    protected static ThreadLocal<StockTakeApiClient> stockTakeApiClient = new ThreadLocal<>();
    protected static ThreadLocal<PaymentPanelApiClient> paymentPanelApiClient = new ThreadLocal<>();
    protected static ThreadLocal<ChatbotApiClient> chatbotApiClient = new ThreadLocal<>();
    protected static ThreadLocal<PlanningCenterApiClient> planningCenterApiClient = new ThreadLocal<>();
    protected static ThreadLocal<PickerAppApiClient> pickerAppApiClient = new ThreadLocal<>();
    protected static ThreadLocal<FreshChatApiClient> freshChatApiClient = new ThreadLocal<>();
    protected static ThreadLocal<CheckoutApiClient> checkoutApiClient = new ThreadLocal<>();
    protected static ThreadLocal<FoodAggregatorRestaurantsApiClient> foodAggregatorRestaurantsApiClient =new ThreadLocal<>();
    protected static ThreadLocal<YeloApiClient> yeloApiClient =new ThreadLocal<>();
    protected static ThreadLocal<OptionSetsApiClient> optionSetsApiClient = new ThreadLocal<>();
    protected static ThreadLocal<JsonPath> jsonPath = new ThreadLocal<>();
    protected static ThreadLocal<AndroidLoginPage> androidLoginPage = new ThreadLocal<>();
    protected static ThreadLocal<IosOrderDetailsScreen> iosOrderDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidActions> androidActions = new ThreadLocal<>();
    protected static ThreadLocal<AndroidSplashScreen> androidSplashScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidSideMenuScreen> androidSideMenuScreen = new ThreadLocal<>();
    protected static ThreadLocal<OrdersPage> midMileAdminPage = new ThreadLocal<>();
    protected static ThreadLocal<SetCardPinPage> setCardPinPage = new ThreadLocal<>();
    protected static ThreadLocal<CreateOrderApiValidator> createOrderApiValidator = new ThreadLocal<>();
    protected static ThreadLocal<GetOrderApiValidator> getOrderApiValidator = new ThreadLocal<>();
    protected static ThreadLocal<DynamicProductCarouselApiClient> dynamicProductCarouselApiClient = new ThreadLocal<>();
    protected static ThreadLocal<ApsCcWebPage> apsCcWebPage = new ThreadLocal<>();
    protected static ThreadLocal<AndroidFleetHomeScreen> androidFleetHomeScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidOrderDeliveryScreen> androidOrderDeliveryScreen=new ThreadLocal<>();
    protected static ThreadLocal<AndroidTripDetailsScreen> androidTripDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidTimeSlotListingScreen> androidTimeSlotListingScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidCashCollectionScreen> androidCashCollectionScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidFleetOrderDetailsScreen> androidFleetOrderDetailsScreen = new ThreadLocal<>();
    protected static ThreadLocal<AndroidMapViewScreen> androidMapViewScreen = new ThreadLocal<>();

    protected static ThreadLocal<MidMileTrucksApiClient> midMileTrucksApiClient = new ThreadLocal<>();
    protected static ThreadLocal<AndroidNativePromosScreen> androidNativePromosScreen = new ThreadLocal<>();

    @BeforeMethod (alwaysRun = true)
    public void setup(ITestResult result, Method method) {
        MDC.put("testCase", method.getName());
        MDC.put("threadId", String.valueOf(Thread.currentThread().threadId()));
        logger.set(LoggerFactory.getLogger("BaseTest.class"));

        //Reading configs
        dataHelper.set(new DataHelper());
        configs.set(new Configs());
        configs.set(dataHelper.get().readConfigs(configs.get()));

        //Configurations management and cooping it to the test level
        logger.get().info("Initiating the configurations management process to align it with test tag");
        configurationsManagementHelper.set(new ConfigurationsManagementHelper(configs.get()));
        configs.set(configurationsManagementHelper.get().updateConfigsToMatchTestTag(method));

        //Building default testData object
        logger.get().info("Creating test data...");
        testExecutionHelper.set(new TestExecutionHelper(configs.get()));
        validationResults.set(new ValidationResults());
        createOrderApiValidator.set(new CreateOrderApiValidator());
        getOrderApiValidator.set(new GetOrderApiValidator());

        //Initializing API clients
        adminAuthorizationApiClient.set(new AdminAuthorizationApiClient(configs.get()));
        switcherApiClient.set(new SwitcherApiClient(configs.get()));
        transitApiClient.set(new TransitApiClient(configs.get()));
        inventoryApiClient.set(new InventoryApiClient(configs.get()));
        controlRoomV2ApiClient.set(new ControlRoomV2ApiClient(configs.get()));
        couponsApiClient.set(new CouponsApiClient(configs.get()));
        addressApiClient.set(new AddressApiClient(configs.get()));
        orderApiClient.set(new OrderApiClient(configs.get()));
        deliveryCapacityManagementApiClient.set(new DeliveryCapacityManagementApiClient(configs.get()));
        mobileWarehousesApiClient.set(new MobileWarehousesApiClient(configs.get()));
        mobileAuthorizationApiClient.set(new MobileAuthorizationApiClient(configs.get()));
        midMileAppApiClient.set(new MidMileAppApiClient(configs.get()));
        kitchenApiClient.set(new KitchenApiClient(configs.get()));
        fleetAppApiClient.set(new FleetAppApiClient(configs.get()));
        midMileOrdersApiClient.set(new MidMileOrdersApiClient(configs.get()));
        mobilePayServicesApiClient.set(new MobilePayServicesApiClient(configs.get()));
        cardServiceApiClient.set(new CardServiceApiClient(configs.get()));
        addressApiClient.set(new AddressApiClient(configs.get()));
        orderApiClient.set(new OrderApiClient(configs.get()));
        orderPaymentApiClient.set(new OrderPaymentApiClient(configs.get()));
        stockTakeApiClient.set(new StockTakeApiClient(configs.get()));
        paymentPanelApiClient.set(new PaymentPanelApiClient(configs.get()));
        pickerAppApiClient.set(new PickerAppApiClient(configs.get()));
        chatbotApiClient.set(new ChatbotApiClient(configs.get()));
        planningCenterApiClient.set(new PlanningCenterApiClient(configs.get()));
        freshChatApiClient.set(new FreshChatApiClient(configs.get()));
        checkoutApiClient.set(new CheckoutApiClient(configs.get()));
        optionSetsApiClient.set(new OptionSetsApiClient(configs.get()));
        dynamicProductCarouselApiClient.set(new DynamicProductCarouselApiClient(configs.get()));
        midMileTrucksApiClient.set(new MidMileTrucksApiClient(configs.get()));
        foodAggregatorRestaurantsApiClient.set(new FoodAggregatorRestaurantsApiClient(configs.get()));
        yeloApiClient.set(new YeloApiClient(configs.get()));

        //Build Test data
        defaultTestData.set(new TestData());
        defaultTestData.set(testExecutionHelper.get().buildTestData(defaultTestData.get()));

        if (configs.get().isUseForeignCountryData()) {
            defaultTestData.get().setRandomTestUser(new UserDataFactory(configs.get())
                    .buildForeignCountryData(defaultTestData.get().getRandomTestUser()));
            defaultTestData.get().setSecondaryTestUser(new UserDataFactory(configs.get())
                    .buildForeignCountryData(defaultTestData.get().getSecondaryTestUser()));
        }
        if (configs.get().isBuildMobileCategoriesAndProductsTestData())
            defaultTestData.set(testExecutionHelper.get().buildCustomerAppWarehousesTestData(defaultTestData.get()));
        if (configs.get().isBuildMobileShopsTestData())
            defaultTestData.set(testExecutionHelper.get().buildCustomerAppShopsWarehouseTestData(defaultTestData.get()));
        if (configs.get().isBuildWebControlRoomWarehousesAndOrders())
            defaultTestData.set(testExecutionHelper.get().buildControlRoomTestData(defaultTestData.get()));
        if (configs.get().isConnectToDB()) {
            databaseConnectionFactory.set(new DatabaseConnectionFactory(configs.get()));
            defaultTestData.get().setDbConnection(databaseConnectionFactory.get().setupSshConnection());
        }
        if (configs.get().isRegisterUserUsingApi())
            defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        if (configs.get().isBuildMobileBillingTestData())
            defaultTestData.set(testExecutionHelper.get()
                    .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));
        if (configs.get().isCardServicesTestsEnabled()) {
            cardServiceEncryptionHelper.set(new EncryptionHelper(configs.get()));
            defaultTestData.set(testExecutionHelper.get().buildCardServiceTestData(defaultTestData.get()));
        }
        if (configs.get().isBuildStockTakerTestData())
            defaultTestData.set(testExecutionHelper.get().buildStockTakeTestSession(defaultTestData.get()));
        if (configs.get().isBuildFoodAggregatorTestData()){
            defaultTestData.set(testExecutionHelper.get().buildFoodAggregatorTestData(defaultTestData.get()));
        }

        logger.get().info("Initiating Test Setup...");
        //Starting web driver
        if (configs.get().getWebBuildEnabled()) {
            webDriversFactory.set(new WebDriversFactory(configs.get()));
            webDriver.set(webDriversFactory.get().build());
            setUpHelper.set(new SetUpHelper(webDriver.get(), configs.get()));
            setUpHelper.get().openBaseURL();
            webDriver.get().manage().window().setSize(new org.openqa.selenium.Dimension(1920, 1080));
            logger.get().info("Initiating webDriver completed and current opened URL is: {} " +
                            "and window size is (width: {}, height: {})",
                    webDriver.get().getCurrentUrl(),
                    webDriver.get().manage().window().getSize().getWidth(),
                    webDriver.get().manage().window().getSize().getHeight());

            if (configs.get().isTestExecutionVideoRecordingEnabled()) {
                try {
                    Files.createDirectories(Paths.get("resources/screenRecordings/web/"));
                } catch (Exception e) {
                    logger.get().error("Creating the web tests recording directory failed", e);
                }
                webVideoRecorderFactory.set(new WebVideoRecorderFactory());
                webRecordingProcess.set(webVideoRecorderFactory.get().startRecording(webRecordingProcess.get()
                        , "resources/screenRecordings/web/"
                                + method.getName()
                                + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber() + ".mp4"));
            }

            //Initiating Web Page Objects
            logger.get().info("Initiating Web Page Object models...");
            webHomePage.set(new HomePage(webDriver.get()));
            webLoginPage.set(new LoginPage(webDriver.get()));
            webGoogleLoginPage.set(new GoogleLoginPage(webDriver.get()));
            webOrdersAdminPage.set(new OrdersAdminPage(webDriver.get()));
            webSwitcherPage.set(new SwitcherPage(webDriver.get()));
            webUsersListPage.set(new UsersListPage(webDriver.get()));
            //Initializing wpAdmin pages
            webAppFaqsAdminPage.set(new AppFaqsAdminPage(webDriver.get()));
            webAppMessagesAdminPage.set(new AppMessagesAdminPage(webDriver.get()));
            webAreasAdminPage.set(new AreasAdminPage(webDriver.get()));
            webBrandsAdminPage.set(new BrandsAdminPage(webDriver.get()));
            webCareersAdminPage.set(new CareersAdminPage(webDriver.get()));
            webCouponsAdminPage.set(new CouponsAdminPage(webDriver.get()));
            webEventsAdminPage.set(new EventsAdminPage(webDriver.get()));
            webFlyersAdminPage.set(new FlyersAdminPage(webDriver.get()));
            webIngredientsAdminPage.set(new IngredientsAdminPage(webDriver.get()));
            webInternalCategoriesAdminPage.set(new InternalCategoriesAdminPage(webDriver.get()));
            webOrdersAdminPage.set(new OrdersAdminPage(webDriver.get()));
            webPagesAdminPage.set(new PagesAdminPage(webDriver.get()));
            webPostsAdminPage.set(new PostsAdminPage(webDriver.get()));
            webProductsCategoriesAdminPage.set(new ProductCategoriesAdminPage(webDriver.get()));
            webProductsAdminPage.set(new ProductsAdminPage(webDriver.get()));
            webReferralsAdminPage.set(new ReferralsAdminPage(webDriver.get()));
            webRepeatedOrdersAdminPage.set(new RepeatedOrdersAdminPage(webDriver.get()));
            webGeneralSettingsAdminPage.set(new GeneralSettingsAdminPage(webDriver.get()));
            webCXSettingsAdminPage.set(new CXSettingsAdminPage(webDriver.get()));
            webOrderSmsAdminPage.set(new OrderSmsAdminPage(webDriver.get()));
            webIntercomSettingsAdminPage.set(new IntercomSettingsAdminPage(webDriver.get()));
            webCSSSettingsAdminPage.set(new CssSettingsAdminPage(webDriver.get()));
            webSlackSettingsAdminPage.set(new SlackSettingsAdminPage(webDriver.get()));
            webVodafoneCouponAdminPage.set(new VodafoneCouponAdminPage(webDriver.get()));
            webCCDiscountAdminPage.set(new CCDiscountAdminPage(webDriver.get()));
            webCareersSettingsAdminPage.set(new CareersSettingsAdminPage(webDriver.get()));
            webRecommendationSettingsAdminPage.set(new RecommendationSettingsAdminPage(webDriver.get()));
            //Initializing dashboard pages
            webAttendanceAdminPage.set(new AttendanceAdminPage(webDriver.get()));
            webBulkDiscountsAdminPage.set(new BulkDiscountsAdminPage(webDriver.get()));
            webCancellationReasonsAdminPage.set(new CancellationReasonsAdminPage(webDriver.get()));
            webCategoriesSortingAdminPage.set(new CategoriesSortingAdminPage(webDriver.get()));
            webPlanningCenterAdminPage.set(new PlanningCenterAdminPage(webDriver.get()));
            webRecommendationsAdminPage.set(new RecommendationsAdminPage(webDriver.get()));
            webScheduledOrdersAdminPage.set(new ScheduledOrdersAdminPage(webDriver.get()));
            webSignatureAdminPage.set(new SignatureAdminPage(webDriver.get()));
            webBannersAdminPage.set(new BannersAdminPage(webDriver.get()));
            webPopUpsAdminPage.set(new PopUpsAdminPage(webDriver.get()));
            webCollectionsAdminPage.set(new CollectionsAdminPage(webDriver.get()));
            webControlRoomV2AdminPage.set(new ControlRoomV2AdminPage(webDriver.get()));
            webDeliveryCapacityManagementPage.set(new DevlieryCapacityManagementAdminPage(webDriver.get()));
            webCardPanelLoginPage.set(new CardAdminPanelLoginPage(webDriver.get()));
            webCardPanelDashboardPage.set(new CardPanelDashboard(webDriver.get()));
            webCardPanelSearchUsersPage.set(new SearchCardsUsers(webDriver.get()));
            webCardPanelViewUsersDetailsPage.set(new ViewCardUsersDetails(webDriver.get()));
            webCardPanelEditCustomerDetailsPage.set(new EditCustomerDetailsPage(webDriver.get()));
            midMileAdminPage.set(new OrdersPage(webDriver.get()));
            setCardPinPage.set(new SetCardPinPage(webDriver.get()));
            //Initializing the webChatbot integration
            webChatbotSdkHostPage.set(new ChatbotSdkHostPage(webDriver.get()));
            webChatbotSdk.set(new WebChatbotSdk(webDriver.get()));
            apsCcWebPage.set(new ApsCcWebPage(webDriver.get()));
            logger.get().info("Completed Web Page Object models initiation.");

            //Handle the roles testing functions
            if (configs.get().isRolesTestsEnabled()) {
                rolesList.set(new ArrayList<>());
                rolesList.set(dataHelper.get().readRolesFromCSV());
                switcherRolesValidators.set(new SwitcherRolesValidators());
                planningCenterRolesValidators.set(new PlanningCenterRolesValidators());
                pagesAdminPageRolesValidators.set(new PagesAdminPageRolesValidators());
                appFaqsRolesValidators.set(new AppFaqsRolesValidators());
                cancellationReasonsRolesValidators.set(new CancellationReasonsRolesValidators());
                recommendationsRolesValidators.set(new RecommendationsRolesValidators());
                bulkDiscountsRolesValidators.set(new BulkDiscountsRolesValidators());
                attendanceRolesValidators.set(new AttendanceRolesValidators());
                signaturesRolesValidators.set(new SignaturesRolesValidators());
                postsRolesValidators.set(new PostsRolesValidators());
                flyersRolesValidators.set(new FlyersRolesValidators());
                referralsRolesValidators.set(new ReferralsRolesValidators());
                appMessagesRolesValidators.set(new AppMessagesRolesValidators());
                productsSortingRolesValidators.set(new ProductsSortingRolesValidators());
                internalCategoriesRolesValidators.set(new InternalCategoriesRolesValidators());
                scheduledOrdersRolesValidators.set(new ScheduledOrdersRolesValidators());
                categoriesRolesValidators.set(new CategoriesRolesValidators());
                ingredientsRolesValidators.set(new IngredientsRolesValidators());
                productsRolesValidators.set(new ProductsRolesValidators());
                brandsRolesValidators.set(new BrandsRolesValidators());
                couponsRolesValidators.set(new CouponsRolesValidators());
                ordersRolesValidators.set(new OrdersRolesValidators());
                areasRolesValidators.set(new AreasRolesValidators());
                eventsRolesValidators.set(new EventsRolesValidators());
                careersRolesValidators.set(new CareersRolesValidators());
                repeatedOrdersRolesValidators.set(new RepeatedOrdersRolesValidators());
                bannersRolesValidators.set(new BannersRolesValidators());
                popupsRolesValidators.set(new PopupsRolesValidators());
                collectionsRolesValidators.set(new CollectionsRolesValidators());
                generalSettingsRolesValidators.set(new GeneralSettingsRolesValidators());
                cxSettingsRolesValidators.set(new CXSettingsRolesValidators());
                orderSmsRolesValidators.set(new OrderSmsRolesValidators());
                intercomSettingsRolesValidators.set(new IntercomSettingsRolesValidators());
                cssSettingsRolesValidators.set(new CssSettingsRolesValidators());
                slackSettingsRolesValidators.set(new SlackSettingsRolesValidators());
                vodafoneCouponRolesValidators.set(new VodafoneCouponRolesValidators());
                ccDiscountRolesValidators.set(new CCDiscountRolesValidators());
                careersSettingsRolesValidators.set(new CareersSettingsRolesValidators());
                recommendationSettingsRolesValidators.set(new RecommendationSettingsRolesValidators());
            }
        }

        //Ngrok tunnel and local Spring server setup
        if (configs.get().getWebhookBuildEnabled()) {
            serverFactory.set(new ServerFactory());

            tunnelServerConnection.set(new TunnelServerConnection());
            tunnelServerConnection.set(serverFactory.get().createTunnel(
                    defaultTestData.get().getRandomTestUser().getEmailAddress()
                    .replaceAll("\\D", ""), tunnelServerConnection.get()));
            tunnelServerConnection.set(serverFactory.get().createWebhooksListener(tunnelServerConnection.get()));
        }

        //Appium Server Setup
        if (configs.get().getMobileBuildEnabled()) {
            mobileDriversFactory.set(new MobileDriversFactory(configs.get()));
            androidAppiumServerController.set(new AppiumServer());
            iosAppiumServerController.set(new AppiumServer());

            // Build Appium Server reserved for android device/emulator
            androidAppiumServerController.set(mobileDriversFactory
                    .get().buildLocalAppiumServer(androidAppiumServerController.get()));
            // Start Appium Server
            androidAppiumServerController.set(mobileDriversFactory
                    .get().startAppiumServer(androidAppiumServerController.get()));

            // Build Appium Server reserved for iOS device/simulator
            iosAppiumServerController.set(mobileDriversFactory
                    .get().buildLocalAppiumServer(iosAppiumServerController.get()));
            //Start Appium Server
            iosAppiumServerController.set(mobileDriversFactory
                    .get().startAppiumServer(iosAppiumServerController.get()));
        }

        //Android Driver Setup
        if (configs.get().getMobileBuildEnabled() && configs.get().getAndroidBuildEnabled()) {
            androidDevice.set(new AndroidDevice());
            // Generate a random port number for the automation driver to rely on
            androidAppiumServerController.set(mobileDriversFactory.get()
                    .generateRandomDriverPortNumber(androidAppiumServerController.get()));
            androidAppiumServerController.set(mobileDriversFactory.get()
                    .generateListenerPortNumber(androidAppiumServerController.get()));
            androidDevice.set(testExecutionHelper.get().buildAndroidDeviceData(androidDevice.get()));
            androidDriver.set(mobileDriversFactory.get().buildAndroidDriver(androidAppiumServerController.get()
                    , androidDevice.get()));

            androidTestsExecutionHelper.set(new AndroidTestsExecutionHelper());
            androidNativeTestsExecutionHelper.set(new AndroidNativeTestsExecutionHelper());
            androidNativeAggregatorTestsExecutionHelper.set(new AndroidNativeAggregatorTestsExecutionHelper());

            //Grant the Fine location permission to the app
            androidTestsExecutionHelper.get().grantFineLocationPermissionToCurrentApp(androidDriver.get());

            //Change Location of the device
            androidTestsExecutionHelper.get().setAndroidEmulatorLocation(defaultTestData.get().getTestDeviceLatitude(),
                    defaultTestData.get().getTestDeviceLongitude(), androidDriver.get());

            //androidDriver start recording
            if (configs.get().isTestExecutionVideoRecordingEnabled()
                    && !configs.get().getMobileRunMode().equalsIgnoreCase("remote")) {
                androidDriver.get().startRecordingScreen(new AndroidStartScreenRecordingOptions()
                        .withTimeLimit(Duration.ofMinutes(30))
                        .enableBugReport());
            }

            //Initiating UI objects
            androidLandingScreen.set(new AndroidLandingScreen(androidDriver.get()));
            androidPhoneNumberScreen.set(new AndroidPhoneNumberScreen(androidDriver.get()));
            androidOrderDetailsScreen.set(new AndroidOrderDetailsScreen(androidDriver.get()));
            androidCountriesListScreen.set(new AndroidCountriesListScreen(androidDriver.get()));
            androidOtpVerificationScreen.set(new AndroidOTPVerificationScreen(androidDriver.get()));
            androidCreateAccountInfoScreen.set(new AndroidCreateAccountScreen(androidDriver.get()));
            androidRegisterSuccessScreen.set(new AndroidRegisterSuccessScreen(androidDriver.get()));
            androidHomeScreen.set(new AndroidHomeScreen(androidDriver.get()));
            androidProductScreen.set(new AndroidProductScreen(androidDriver.get()));
            androidLocationPermissionAlert.set(new AndroidLocationPermissionAlert(androidDriver.get()));
            androidMoreScreen.set(new AndroidMoreScreen(androidDriver.get()));
            androidSetAddressScreen.set(new AndroidSetAddressScreen(androidDriver.get()));
            androidCreateAddressScreen.set(new AndroidCreateAddressScreen(androidDriver.get()));
            androidFreshChatScreen.set(new AndroidFreshChatScreen(androidDriver.get()));
            androidReferralScreen.set(new AndroidReferralScreen(androidDriver.get()));
            androidRecommendationScreen.set(new AndroidRecommendationScreen(androidDriver.get()));
            androidAccountSettingsPage.set(new AndroidAccountSettingsPage(androidDriver.get()));
            androidDeleteAccountScreen.set(new AndroidDeleteAccountScreen(androidDriver.get()));
            androidVerifyDeleteAccountScreen.set(new AndroidVerifyDeleteAccountScreen(androidDriver.get()));
            androidDeleteAccountOTPVerificationScreen.set(new AndroidDeleteAccountOTPVerificationScreen(androidDriver.get()));
            androidDeleteRequestScreen.set(new AndroidDeleteRequestScreen(androidDriver.get()));
            androidPayScreen.set(new AndroidPayScreen(androidDriver.get()));
            androidBillingCategoryScreen.set(new AndroidBillingCategoryScreen(androidDriver.get()));
            androidProviderScreen.set(new AndroidProviderScreen(androidDriver.get()));
            androidInvoiceSummaryScreen.set(new AndroidInvoiceSummaryScreen(androidDriver.get()));
            androidPaymentSuccessScreen.set(new AndroidPaymentSuccessScreen(androidDriver.get()));
            androidCountriesSelectionScreen.set(new AndroidCountriesSelectionScreen(androidDriver.get()));
            androidNowAndTomorrowModal.set(new AndroidNowAndTomorrowModal(androidDriver.get()));
            androidCategoryScreen.set(new AndroidCategoryScreen(androidDriver.get()));
            androidAddressSelectionScreen.set(new AndroidAddressSelectionScreen(androidDriver.get()));
            androidAddressCreateSuccessModal.set(new AndroidAddressCreateSuccessModal(androidDriver.get()));
            androidAccountSettingsScreen.set(new AndroidAccountSettingsScreen(androidDriver.get()));
            androidActivityHistoryScreen.set(new AndroidActivityHistoryScreen(androidDriver.get()));
            androidBillsTabScreen.set(new AndroidBillsTabScreen(androidDriver.get()));
            androidBreadfastRewardsScreen.set(new AndroidBreadfastRewardsScreen(androidDriver.get()));
            androidChooseCountryModal.set(new AndroidChooseCountryModal(androidDriver.get()));
            androidChooseLanguageModal.set(new AndroidChooseLanguageModal(androidDriver.get()));
            androidFavoritesScreen.set(new AndroidFavoritesScreen(androidDriver.get()));
            androidFreeCreditScreen.set(new AndroidFreeCreditScreen(androidDriver.get()));
            androidTalkToUsScreen.set(new AndroidTalkToUsScreen(androidDriver.get()));
            androidHelpScreen.set(new AndroidHelpScreen(androidDriver.get()));
            androidCartScreen.set(new AndroidCartScreen(androidDriver.get()));
            androidCheckoutScreen.set(new AndroidCheckoutScreen(androidDriver.get()));
            androidOrderSuccessScreen.set((new AndroidOrderSuccessScreen(androidDriver.get())));
            androidCreateAddressScreen.set(new AndroidCreateAddressScreen(androidDriver.get()));
            androidAddressCreateSuccessModal.set(new AndroidAddressCreateSuccessModal(androidDriver.get()));
            androidOrderSuccessScreen.set(new AndroidOrderSuccessScreen(androidDriver.get()));
            androidUpdateAccountSettingScreen.set(new AndroidUpdateAccountSettingScreen(androidDriver.get()));
            androidUpdatePhoneNumberOTPVerificationScreen.set(new AndroidUpdatePhoneNumberOTPVerificationScreen(androidDriver.get()));
            androidSavedCardScreen.set(new AndroidSavedCardScreen(androidDriver.get()));
            androidAddCardInfoScreen.set(new AndroidAddCardInfoScreen(androidDriver.get()));
            androidSearchScreen.set(new AndroidSearchScreen(androidDriver.get()));
            androidSearchResultScreen.set(new AndroidSearchResultScreen(androidDriver.get()));
            androidCardSelectionModal.set(new AndroidCardSelectionModal(androidDriver.get()));
            androidMyAddressesScreen.set(new AndroidMyAddressesScreen(androidDriver.get()));
            androidDisabledLocationAccess.set(new AndroidDisabledLocationAccessModal(androidDriver.get()));
            androidLoginPage.set(new AndroidLoginPage(androidDriver.get()));
            androidActions.set(new AndroidActions(androidDriver.get()));
            androidSplashScreen.set(new AndroidSplashScreen(androidDriver.get()));
            androidSideMenuScreen.set(new AndroidSideMenuScreen(androidDriver.get()));
            androidChatbotScreen.set(new AndroidChatbotScreen(androidDriver.get()));
            androidBusyModalScreen.set(new AndroidBusyModalScreen(androidDriver.get()));
            androidOpenedBannerModalScreen.set(new AndroidOpenedBannerModalScreen(androidDriver.get()));
            androidFleetHomeScreen.set((new AndroidFleetHomeScreen(androidDriver.get())));
            androidTripDetailsScreen.set(new  AndroidTripDetailsScreen(androidDriver.get()));
            androidCashCollectionScreen.set(new AndroidCashCollectionScreen(androidDriver.get()));
            androidFleetOrderDetailsScreen.set(new  AndroidFleetOrderDetailsScreen(androidDriver.get()));
            androidMapViewScreen.set(new  AndroidMapViewScreen(androidDriver.get()));
            androidOrderDeliveryScreen.set(new AndroidOrderDeliveryScreen(androidDriver.get()));
            androidNativeCountriesSelectionScreen.set(new AndroidNativeCountriesSelectionScreen(androidDriver.get()));
            androidNativeLandingScreen.set(new AndroidNativeLandingScreen(androidDriver.get()));
            androidNativePhoneNumberScreen.set(new AndroidNativePhoneNumberScreen(androidDriver.get()));
            androidNativePhoneCountrySelectionDropdownScreen.set(
                    new AndroidNativePhoneCountrySelectionDropdownScreen(androidDriver.get()));
            androidNativeOtpVerificationScreen.set(new AndroidNativeOtpVerificationScreen(androidDriver.get()));
            androidNativeCreateAccountScreen.set(new AndroidNativeCreateAccountScreen(androidDriver.get()));
            androidNativeRegisterSuccessScreen.set(new AndroidNativeRegisterSuccessScreen(androidDriver.get()));
            androidNativeHomeScreen.set(new AndroidNativeHomeScreen(androidDriver.get()));
            androidNativeRateOrderPopup.set(new AndroidNativeRateOrderPopup(androidDriver.get()));
            androidNativeMoreScreen.set(new AndroidNativeMoreScreen(androidDriver.get()));
            androidNativeFavouriteScreen.set(new AndroidNativeFavouriteScreen(androidDriver.get()));
            androidNativeAccountSettingsScreen.set(new AndroidNativeAccountSettingsScreen(androidDriver.get()));
            androidNativePersonalInfoScreen.set(new AndroidNativePersonalInfoScreen(androidDriver.get()));
            androidNativeSavedAddressScreen.set(new AndroidNativeSavedAddressScreen(androidDriver.get()));
            androidNativeAddressDetailsScreen.set(new AndroidNativeAddressDetailsScreen(androidDriver.get()));
            androidNativeEmailAddressScreen.set(new AndroidNativeEmailAddressScreen(androidDriver.get()));
            androidNativeChangeCountryModalScreen.set(new AndroidNativeChangeCountryModalScreen(androidDriver.get()));
            androidNativeChatBotScreen.set(new AndroidNativeChatBotScreen(androidDriver.get()));
            androidNativeProductScreen.set(new AndroidNativeProductScreen(androidDriver.get()));
            androidNativeCategoriesDetailsScreen.set(new AndroidNativeCategoriesDetailsScreen(androidDriver.get()));
            androidNativeCartScreen.set(new AndroidNativeCartScreen(androidDriver.get()));
            androidNativeInternalCustomizedProductScreen.set(new AndroidNativeInternalCustomizedProductScreen(androidDriver.get()));
            androidNativeDeleteAccountScreen.set(new AndroidNativeDeleteAccountScreen(androidDriver.get()));
            androidNativeDeleteAccountVerifyNumberScreen.set(new AndroidNativeDeleteAccountOTPVerificationScreen(androidDriver.get()));
            androidNativeCheckoutScreen.set(new AndroidNativeCheckoutScreen(androidDriver.get()));
            androidNativeCollectionDetailsScreen.set(new AndroidNativeCollectionDetailsScreen(androidDriver.get()));
            androidNativeOrderDetailsScreen.set(new AndroidNativeOrderDetailsScreen(androidDriver.get()));
            androidNativeCarouselScreen.set(new AndroidNativeCarouselScreen(androidDriver.get()));
            androidNativePromosScreen.set(new AndroidNativePromosScreen(androidDriver.get()));
//Food aggregator Screens
            androidNativeFoodAggregatorHomeScreen.set(new AndroidNativeFoodAggregatorHomeScreen(androidDriver.get()));
            androidNativeFoodAggregatorResturantDetailsScreen.set(new AndroidNativeFoodAggregatorRestaurantDetailsScreen(androidDriver.get()));
        }

        //iOS Driver Setup
        if (configs.get().getMobileBuildEnabled() && configs.get().getiOSBuildEnabled()) {
            iosDevice.set(new IosDevice());
            iosDevice.set(testExecutionHelper.get().buildIosDeviceData(iosDevice.get()));

            if (!configs.get().getMobileRunMode().equalsIgnoreCase("remote")){
                //Create an iOS simulator and assign for the test session using the generated email identifier
                iosDevice.set(mobileDriversFactory.get()
                        .createStandAloneSimulatorImage(iosDevice.get(), defaultTestData.get()));
            }

            //Generate a random port for the automation driver to rely on
            iosAppiumServerController.set(mobileDriversFactory.get()
                    .generateRandomDriverPortNumber(iosAppiumServerController.get()));
            iosAppiumServerController.set(mobileDriversFactory.get()
                    .generateListenerPortNumber(iosAppiumServerController.get()));
            iosDriver.set(mobileDriversFactory.get().buildIosDriver(iosAppiumServerController.get(), iosDevice.get()));

            //Set the location of the device
            iosTestsExecutionHelper.set(new IosTestsExecutionHelper());
            iosNativeTestsExecutionHelper.set(new IosNativeTestsExecutionHelper());
            iosTestsExecutionHelper.get().setIosEmulatorLocation(defaultTestData.get().getTestDeviceLatitude(),
                    defaultTestData.get().getTestDeviceLongitude(), iosDriver.get());
            iosNativeAggregatorTestsExecutionHelper.set(new IosNativeAggregatorTestsExecutionHelper());

            if (configs.get().isTestExecutionVideoRecordingEnabled() && !configs.get().getMobileRunMode().equalsIgnoreCase("remote")) {
                iosDriver.get().startRecordingScreen(
                        new IOSStartScreenRecordingOptions()
                                .withTimeLimit(Duration.ofMinutes(30))
                                .withFps(5)
                                .withVideoQuality(IOSStartScreenRecordingOptions.VideoQuality.LOW));
            }

            //Initiating UI objects
            iosLandingScreen.set(new IosLandingScreen(iosDriver.get()));
            iosPhoneNumberScreen.set(new IosPhoneNumberScreen(iosDriver.get()));
            iosCountriesListScreen.set(new IosCountriesListScreen(iosDriver.get()));
            iosNotificationsPermissionsAlert.set(new IosNotificationsPermissionsAlert(iosDriver.get()));
            iosOTPVerificationScreen.set(new IosOTPVerificationScreen(iosDriver.get()));
            iosCreateAccountScreen.set(new IosCreateAccountScreen(iosDriver.get()));
            iosRegisterSuccessScreen.set(new IosRegisterSuccessScreen(iosDriver.get()));
            iosTrackingPermissionAlert.set(new IosTrackingPermissionAlert(iosDriver.get()));
            iosHomeScreen.set(new IosHomeScreen(iosDriver.get()));
            iosMoreScreen.set(new IosMoreScreen(iosDriver.get()));
            iosLocationPermissionAlert.set(new IosLocationPermissionAlert(iosDriver.get()));
            iosSetAddressScreen.set(new IosSetAddressScreen(iosDriver.get()));
            iosReferralScreen.set(new IosReferralScreen(iosDriver.get()));
            iosFreshChatScreen.set(new IosFreshChatScreen(iosDriver.get()));
            iosCountriesSelectionScreen.set(new IosCountriesSelectionScreen(iosDriver.get()));
            iosRecommendationsScreen.set(new IosRecommendationsScreen(iosDriver.get()));
            iosAddressSelectionScreen.set(new IosAddressSelectionScreen(iosDriver.get()));
            iosPayScreen.set(new IosPayScreen(iosDriver.get()));
            iosBillingCategoryScreen.set(new IosBillingCategoryScreen(iosDriver.get()));
            iosProviderScreen.set(new IosProviderScreen(iosDriver.get()));
            iosInvoiceSummaryScreen.set(new IosInvoiceSummaryScreen(iosDriver.get()));
            iosInvoiceSuccessScreen.set(new IosInvoiceSuccessScreen(iosDriver.get()));
            iosAddCardInfoScreen.set(new IosAddCardInfoScreen(iosDriver.get()));
            iosCategoryScreen.set(new IosCategoryScreen(iosDriver.get()));
            iosNowAndTomorrowModal.set(new IosNowAndTomorrowModal(iosDriver.get()));
            iosAccountSettingsScreen.set(new IosAccountSettingsScreen(iosDriver.get()));
            iosActivityHistoryScreen.set(new IosActivityHistoryScreen(iosDriver.get()));
            iosBreadfastRewardsScreen.set(new IosBreadfastRewardsScreen(iosDriver.get()));
            iosChooseCountryModal.set(new IosChooseCountryModal(iosDriver.get()));
            iosChooseLanguageModal.set(new IosChooseLanguageModal(iosDriver.get()));
            iosFavoritesScreen.set(new IosFavoritesScreen(iosDriver.get()));
            iosFreeCreditScreen.set(new IosFreeCreditScreen(iosDriver.get()));
            iosHelpScreen.set(new IosHelpScreen(iosDriver.get()));
            iosCartScreen.set(new IosCartScreen(iosDriver.get()));
            iosDeleteAccountScreen.set(new IosDeleteAccountScreen(iosDriver.get()));
            iosVerifyDeleteAccountScreen.set(new IosVerifyDeleteAccountScreen(iosDriver.get()));
            iosUpdateAccountSettingScreen.set(new IosUpdateAccountSettingScreen(iosDriver.get()));
            iosDeleteAccountOTPVerificationScreen.set(new IosDeleteAccountOTPVerificationScreen(iosDriver.get()));
            iosDeleteRequestScreen.set(new IosDeleteRequestScreen(iosDriver.get()));
            iosUpdatePhoneNumberOTPVerficationScreen.set(new IosUpdatePhoneNumberOTPVerficationScreen(iosDriver.get()));
            iosProductDetailsScreen.set(new IosProductDetailsScreen(iosDriver.get()));
            iosCheckoutScreen.set(new IosCheckoutScreen(iosDriver.get()));
            iosCreateAddressScreen.set(new IosCreateAddressScreen(iosDriver.get()));
            iosAddressCreateSuccessModal.set(new IosAddressCreateSuccessModal(iosDriver.get()));
            iosCardSelectionModal.set(new IosCardSelectionModal(iosDriver.get()));
            iosOrderSuccessScreen.set(new IosOrderSuccessScreen(iosDriver.get()));
            iosSavedCardsScreen.set(new IosSavedCardsScreen(iosDriver.get()));
            iosBillsTabScreen.set(new IosBillsTabScreen(iosDriver.get()));
            iosMyAddressesScreen.set(new IosMyAddressesScreen(iosDriver.get()));
            iosDeleteAddressConfirmationModal.set(new IosDeleteAddressConfirmationModal(iosDriver.get()));
            iosDeleteAddressErrorModal.set(new IosDeleteAddressErrorModal(iosDriver.get()));
            iosDisabledLocationModal.set(new IosDisabledLocationModal(iosDriver.get()));
            iosSearchScreen.set(new IosSearchScreen(iosDriver.get()));
            iosSearchResultScreen.set(new IosSearchResultScreen(iosDriver.get()));
            iosOrderDetailsScreen.set(new IosOrderDetailsScreen(iosDriver.get()));
            iosChatbotScreen.set(new IosChatbotScreen(iosDriver.get()));
            iosOpenedBannerModalScreen.set(new IosOpenedBannerModalScreen(iosDriver.get()));

            // Initializing iOS native UI objects
            iosNativeCountriesSelectionScreen.set(new IosNativeCountriesSelectionScreen(iosDriver.get()));
            iosNativeLandingScreen.set(new IosNativeLandingScreen(iosDriver.get()));
            iosNativePhoneNumberScreen.set(new IosNativePhoneNumberScreen(iosDriver.get()));
            iosNativePhoneCountrySelectionDropdownScreen.set(
                    new IosNativePhoneCountrySelectionDropdownScreen(iosDriver.get()));
            iosNativeOtpVerificationScreen.set(new IosNativeOtpVerificationScreen(iosDriver.get()));
            iosNativeCreateAccountScreen.set(new IosNativeCreateAccountScreen(iosDriver.get()));
            iosNativeRegisterSuccessScreen.set(new IosNativeRegisterSuccessScreen(iosDriver.get()));
            iosNativeHomeScreen.set(new IosNativeHomeScreen(iosDriver.get()));
            iosNativeCategoryDetailsScreen.set(new IosNativeCategoryDetailsScreen(iosDriver.get()));
            iosNativeMoreScreen.set(new IosNativeMoreScreen(iosDriver.get()));
            iosNativeCartScreen.set(new IosNativeCartScreen(iosDriver.get()));
            iosNativeCheckoutScreen.set(new IosNativeCheckoutScreen(iosDriver.get()));
            iosNativeInternalCustomizedProductScreen.set(new IosNativeInternalCustomizedProductScreen(iosDriver.get()));
            iosNativeRemoveCustomizedItemsScreen.set(new IosNativeRemoveCustomizedItemsScreen(iosDriver.get()));
            iosNativeUpdateCustomizedProductScreen.set(new IosNativeUpdateCustomizedProductScreen(iosDriver.get()));
            iosNativeOrderSuccessScreen.set(new IosNativeOrderSuccessScreen(iosDriver.get()));
            iOSNativeAccountSettingsScreen.set(new IosNativeAccountSettingsScreen(iosDriver.get()));
            iosNativeAddressDetailsScreen.set(new IosNativeAddressDetailsScreen(iosDriver.get()));
            iosNativeSavedAddressScreen.set(new IosNativeSavedAddressScreen(iosDriver.get()));
            iosNativePersonalInfoScreen.set(new IosNativePersonalInfoScreen(iosDriver.get()));
            iosNativeFavouriteScreen.set(new IosNativeFavouriteScreen(iosDriver.get()));
            iosNativeEmailAddressScreen.set(new IosNativeEmailAddressScreen(iosDriver.get()));
            iosNativeChatBotScreen.set(new IosNativeChatBotScreen(iosDriver.get()));
            iosNativeProductScreen.set(new IosNativeProductScreen(iosDriver.get()));
            iosNativeChangeCountryModalScreen.set(new IosNativeChangeCountryModalScreen(iosDriver.get()));
            iosNativeDeleteAccountScreen.set(new IosNativeDeleteAccountScreen(iosDriver.get()));
            iosNativeDeleteAccountOTPVerificationScreen.set(new IosNativeDeleteAccountOTPVerificationScreen(iosDriver.get()));
            iosNativeDeleteSuccessScreen.set(new IosNativeDeleteSuccessScreen(iosDriver.get()));
            iosNativeCollectionDetailsScreen.set(new IosNativeCollectionDetailsScreen(iosDriver.get()));
            iosNativeFoodAggregatorHomeScreen.set(new IosNativeFoodAggregatorHomeScreen(iosDriver.get()));
            iosNativeFoodAggregatorResturantDetailsScreen.set(new IosNativeFoodAggregatorRestaurantDetailsScreen(iosDriver.get()));
        }
    }

    @AfterMethod (alwaysRun = true)
    public void tearDown(ITestResult testResult) {
        logger.get().info("Initiating TearDown process");
        MDC.clear();
        try {
            if (configs.get().isTestExecutionVideoRecordingEnabled()) {
                logger.get().info("Starting to collect the video recordings");
                if (configs.get().getAndroidBuildEnabled()) {
                    Files.createDirectories(Paths.get("resources/screenRecordings/android/"));
                    Files.write(Paths.get("resources/screenRecordings/android/"
                                    + testResult.getMethod().getMethodName()
                                    + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber() + ".mp4")
                            , Base64.getDecoder().decode(androidDriver.get().stopRecordingScreen()));
                }
                if (configs.get().getiOSBuildEnabled()) {
                    Files.createDirectories(Paths.get("resources/screenRecordings/ios/"));
                    Files.write(Paths.get("resources/screenRecordings/ios/"
                                    + testResult.getMethod().getMethodName()
                                    + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber() + ".mp4")
                            , Base64.getDecoder().decode(iosDriver.get().stopRecordingScreen()));
                }
                if (configs.get().getWebBuildEnabled()) {
                    webVideoRecorderFactory.get().stopRecording(webRecordingProcess.get());
                }
                logger.get().info("Finished collecting the video recordings.");
            } else {
                logger.get().info("Ignoring video recording as it's disabled.");
            }
        } catch (Exception e) {
            logger.get().error("Collecting video recordings failed with exception", e);
        }
        try {
            //Close the browser
            if (configs.get().getWebBuildEnabled()) {
                if (testResult.getStatus() == ITestResult.FAILURE && webDriver.get() != null) {
                    logger.get().info("Taking a screenshot for the failed test case: {}",
                            testResult.getMethod().getMethodName());
                    setUpHelper.get().captureScreenshot(webDriver.get(),testResult.getMethod().getMethodName());
                    logger.get().info("Attaching the screenshot to the allure report...");
                    try {
                        Allure.addAttachment("Last screenshot before failure",
                                "image/png",
                                new java.io.ByteArrayInputStream(((org.openqa.selenium.TakesScreenshot) webDriver.get())
                                        .getScreenshotAs(org.openqa.selenium.OutputType.BYTES)),
                                "png");
                    } catch (Exception e){
                        logger.get().error("Attaching the screenshot to the allure report failed.", e);
                    }
                }
                logger.get().info("closing the web browser.");
                try {
                    webDriver.get().quit();
                    logger.get().info("Closed the web browser successfully.");
                } catch (Exception e) {
                    logger.get().error("Closing the web browser didn't happen correctly.", e);
                }
            }

            //Terminate the remote driver to avoid long idle timeout between sessions
            if (configs.get().getMobileRunMode().equalsIgnoreCase("remote")){
                logger.get().info("Terminating drivers on remote provider {}...", configs.get().getRemoteProviderName());

                switch (configs.get().getRemoteProviderName().toLowerCase()){
                    case "browserstack" -> {
                        try {
                            if (configs.get().getAndroidBuildEnabled()){
                                logger.get().info("Terminating browserStack Android driver for session: {} and test name is {}"
                                        , androidDriver.get().getSessionId().toString()
                                        , testResult.getMethod().getMethodName());

                                Allure.addAttachment("BrowserStack session URL"
                                        ,  "text/uri-list"
                                        , configs.get().getBrowserStackBaseURL()
                                                + "/sessions/"
                                                + androidDriver.get().getSessionId().toString());

                                androidDriver.get().executeScript("browserstack_executor: " +
                                        "{\"action\": \"setSessionName\", " +
                                        "\"arguments\": {\"name\": \"" + testResult.getMethod().getMethodName() + "\"}}");

                                if(testResult.getStatus() != 1){
                                    androidDriver.get().executeScript("browserstack_executor: " +
                                            "{\"action\": \"setSessionStatus\", " +
                                            "\"arguments\": {\"status\":\"failed\", " +
                                            "\"reason\":" + JSONObject.quote(testResult.getThrowable().getMessage()) + "}}");
                                } else {
                                    androidDriver.get().executeScript("browserstack_executor: " +
                                            "{\"action\": \"setSessionStatus\", " +
                                            "\"arguments\": {\"status\":\"passed\", " +
                                            "\"reason\": \"Test passed successfully\"}}");
                                }
                                androidDriver.get().quit();
                            }

                            if (configs.get().getiOSBuildEnabled()){
                                logger.get().info("Terminating browserStack Android driver for session: {} and test name is {}"
                                        , iosDriver.get().getSessionId().toString()
                                        , testResult.getMethod().getMethodName());

                                Allure.addAttachment("BrowserStack session URL"
                                        ,  "text/uri-list"
                                        , configs.get().getBrowserStackBaseURL()
                                                + "/sessions/"
                                                + iosDriver.get().getSessionId().toString());

                                iosDriver.get().executeScript("browserstack_executor: " +
                                        "{\"action\": \"setSessionName\", " +
                                        "\"arguments\": {\"name\": \"" + testResult.getMethod().getMethodName() + "\"}}");

                                if(testResult.getStatus() != 1){
                                    iosDriver.get().executeScript("browserstack_executor: " +
                                            "{\"action\": \"setSessionStatus\", " +
                                            "\"arguments\": {\"status\":\"failed\", " +
                                            "\"reason\":" + JSONObject.quote(testResult.getThrowable().getMessage()) + "}}");
                                } else {
                                    iosDriver.get().executeScript("browserstack_executor: " +
                                            "{\"action\": \"setSessionStatus\", " +
                                            "\"arguments\": {\"status\":\"passed\", " +
                                            "\"reason\": \"Test passed successfully\"}}");
                                }
                                iosDriver.get().quit();
                            }
                            logger.get().info("Terminating the driver on browserstack completed successfully.");
                        } catch (Exception e){
                            logger.get().error("Terminating the driver failed with error {}", e.getMessage());
                        }
                    }
                    case "lambdatest" -> {
                        // ToDo Implement LambdaTest driver termination
                    }
                }

            }

            //Terminate the android emulator
            if (configs.get().getAndroidBuildEnabled()) {
                logger.get().info("Terminating android appium server session...");
                try {
                    androidAppiumServerController.get().getAppiumLocalService().stop();
                } catch (Exception e) {
                    logger.get().error("Terminating the android appium server session didn't work correctly.", e);
                }
                if (!androidAppiumServerController.get().getAppiumLocalService().isRunning()) {
                    logger.get().info("Appium server on URL has been stopped: \n"
                            + androidAppiumServerController.get().getAppiumLocalService().getUrl());
                }
                logger.get().info("Killing the android emulator...");
                try {
                    if (!configs.get().getMobileRunMode().equalsIgnoreCase("remote")) {
                        mobileDriversFactory.get().killAndroidEmulator(androidDevice.get());
                        logger.get().info("Android emulator has been terminated.");
                    } else {
                        logger.get().info("Skipping android emulator termination as remote execution is enabled.");
                    }
                } catch (Exception e) {
                    logger.get().info("Couldn't terminate the android emulator.", e);
                }
            }

            //Terminate iOS simulator
            if (configs.get().getiOSBuildEnabled()) {
                logger.get().info("Terminating the iOS appium server session...");
                try {
                    iosAppiumServerController.get().getAppiumLocalService().stop();
                } catch (Exception e) {
                    logger.get().error("Terminating the iOS appium server session didn't work correctly.", e);
                }
                if (!iosAppiumServerController.get().getAppiumLocalService().isRunning()) {
                    logger.get().info("Appium server on the below URL has been stopped: \n{}"
                            , iosAppiumServerController.get().getAppiumLocalService().getUrl());
                }

                //Clean up the created iOS simulator if any was created for the test
                if (!configs.get().getMobileRunMode().equalsIgnoreCase("remote")) {
                    mobileDriversFactory.get().killIosSimulator(iosDevice.get());
                    mobileDriversFactory.get().deleteIosSimulator(iosDevice.get());
                }
            }

            //Terminate tunnel and webhooks listener
            if (configs.get().getWebhookBuildEnabled()) {
                serverFactory.get().stopWebhookListener(tunnelServerConnection.get());
                serverFactory.get().tearDownNgrokTunnel(tunnelServerConnection.get());
            }
        } catch (Exception e) {
            logger.get().warn("Tear down operation failed.", e);
        }

        try {
            if (configs.get().isConnectToDB() && defaultTestData.get().getDbConnection() != null) {
                logger.get().info("Starting the DB connection termination process...");
                databaseConnectionFactory.get().closeConnection(defaultTestData.get().getDbConnection());
                logger.get().info("Closed the DB connection successfully.");
            } else {
                logger.get().info("DB connection flag is false or DB is not connected. " +
                        "Skipping the DB connection termination process.");
            }
        } catch (Exception e) {
            logger.get().warn("Something went wrong while closing the DB connection.", e);
        }
    }
}
