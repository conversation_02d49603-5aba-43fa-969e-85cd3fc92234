package Inventory;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import models.Batch;
import models.Product;
import models.StockBuckets;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

public class InventoryAPIsTests extends BaseTest {
    @Test(groups = {"inventory-smoke"})
    @Tags({@Tag("api"), @Tag("controlRoom")})
    public void validateDeductTriggersAdjustmentApprovals() {

        defaultTestData.get().setDeductResult(
                inventoryApiClient.get().deductStockAndTriggerApproval(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(), "Missing/Theft"));

        Assert.assertNotNull(defaultTestData.get().getDeductResult(),
                "Deduction failed after retrying all batches. Check logs for details.");

        // API success assertion
        jsonPath.set(new JsonPath(((Response)defaultTestData.get().getDeductResult().get("response")).then().extract().asString()));
        Assert.assertEquals(jsonPath.get().getString("success"), "true");

        // Stock bucket assertions
        Assert.assertEquals(
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockAfter")).getNotSellable(),
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockBefore")).getNotSellable() + Math.abs((int) defaultTestData.get().getDeductResult().get("delta")),
                0.01);
        Assert.assertEquals(
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockAfter")).getOnApp(),
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockBefore")).getOnApp() - Math.abs((int) defaultTestData.get().getDeductResult().get("delta")),
                0.01);

        // Manual approval check
        Assert.assertTrue(
                inventoryApiClient.get().hasMatchingAdjustment(
                        inventoryApiClient.get().getAllManualAdjustments(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(),
                                ((Product) defaultTestData.get().getDeductResult().get("product")).getMysqlId()), ((Batch) defaultTestData.get().getDeductResult().get("batch")).getId(),
                        (int) defaultTestData.get().getDeductResult().get("delta"), "Missing/Theft"),
                String.format("Expected manual adjustment not found. ProductID: %d, BatchID: %d, Delta: %d",
                        ((Product) defaultTestData.get().getDeductResult().get("product")).getMysqlId(), ((Batch) defaultTestData.get().getDeductResult().get("batch")).getId(), (int) defaultTestData.get().getDeductResult().get("delta"))
        );
    }

    @Test(groups = {"inventory-smoke"})
    @Tags({@Tag("api"), @Tag("controlRoom")})
    public void validateDeductWithoutAdjustmentApprovals() {

        defaultTestData.get().setDeductResult(
                inventoryApiClient.get().deductStockAndTriggerApproval(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(), "sales"));

        Assert.assertNotNull(defaultTestData.get().getDeductResult(),
                "Deduction failed after retrying all batches. Check logs for details.");

        // API success assertion
        jsonPath.set(new JsonPath(((Response)defaultTestData.get().getDeductResult().get("response")).then().extract().asString()));
        Assert.assertEquals(jsonPath.get().getString("success"), "true");

        // Stock bucket assertions
        Assert.assertEquals(
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockAfter")).getNotSellable(),
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockBefore")).getNotSellable(),
                "Not Sellable didn't remain unchanged");
        Assert.assertEquals(
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockAfter")).getFpStock(),
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockBefore")).getFpStock() - Math.abs((int) defaultTestData.get().getDeductResult().get("delta")),
                0.01,
                "FP Stock didn't decrease with delta amount");
        Assert.assertEquals(
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockAfter")).getOnApp(),
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockBefore")).getOnApp() - Math.abs((int) defaultTestData.get().getDeductResult().get("delta")),
                0.01,
                "On App didn't decrease with delta amount");
        Assert.assertEquals(
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockAfter")).getTotalLiability(),
                ((StockBuckets) defaultTestData.get().getDeductResult().get("stockBefore")).getTotalLiability() - Math.abs((int) defaultTestData.get().getDeductResult().get("delta")),
                0.01,
                "Total Liability didn't decrease with delta amount");

        // Manual approval check
        Assert.assertFalse(
                inventoryApiClient.get().hasMatchingAdjustment(
                        inventoryApiClient.get().getAllManualAdjustments(
                                defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getTestWarehouse().getId(),
                                ((Product) defaultTestData.get().getDeductResult().get("product")).getMysqlId()),
                        ((Batch) defaultTestData.get().getDeductResult().get("batch")).getId(),
                        (int) defaultTestData.get().getDeductResult().get("delta"), "sales"),
                String.format("Manual deduction with reason 'Sales' unexpectedly triggered an adjustment approval flow. ProductID: %d, BatchID: %d, Delta: %d",
                        ((Product) defaultTestData.get().getDeductResult().get("product")).getMysqlId(),
                        ((Batch) defaultTestData.get().getDeductResult().get("batch")).getId(),
                        (int) defaultTestData.get().getDeductResult().get("delta"))
        );
    }

    @Test(groups = {"inventory-smoke"})
    @Tags({@Tag("api"), @Tag("controlRoom")})
    public void validateManualAdditionAndOdooSync(){
        // 0) ensure additionResult map exists
        defaultTestData.get().setManualAdditionResult(new java.util.HashMap<>());

        // 1) BEFORE snapshot
        defaultTestData.get().getManualAdditionResult().put("before",
                inventoryApiClient.get().getParsedStockBuckets(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(),
                        inventoryApiClient.get().getAllProducts(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getTestWarehouse().getId()).getLast().getMysqlId()));

        // 2) Manual ADD
        defaultTestData.get().getManualAdditionResult().put("movementId", ((Number) (
                        inventoryApiClient.get().manualStockMovements(
                                defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getTestWarehouse().getId(),
                                inventoryApiClient.get().getAllProducts(defaultTestData.get().getAdminUser(),
                                        defaultTestData.get().getTestWarehouse().getId()).getLast(),
                                "11",
                                "addition").get("movementId"))).intValue());

        // 3) Sync with Odoo
        Assert.assertNotNull(
                inventoryApiClient.get().getManualAdjustmentMovement(
                        defaultTestData.get().getAdminUser(),
                        ((Number) defaultTestData.get().getManualAdditionResult().get("movementId")).intValue()),
                "No 'Manual Addition' batchId found for this movement.");

        Assert.assertEquals(inventoryApiClient.get().syncBatchesToOdoo(true, true,
                        inventoryApiClient.get().getAllProducts(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getTestWarehouse().getId()).getLast().getMysqlId(),
                        java.util.Collections.singletonList(
                                inventoryApiClient.get().getManualAdjustmentMovement(
                                        defaultTestData.get().getAdminUser(),
                                        ((Number) defaultTestData.get().getManualAdditionResult().get("movementId")).intValue()
                                ))).getStatusCode(),
                200,
                "Odoo sync did not return HTTP 200.");

        // 4) AFTER snapshot
        defaultTestData.get().getManualAdditionResult().put("after",
                inventoryApiClient.get().getParsedStockBuckets(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(),
                        inventoryApiClient.get().getAllProducts(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getTestWarehouse().getId()).getLast().getMysqlId()));

        // 4.1 On App increased by +1
        Assert.assertEquals(
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("after")).getOnApp(),
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("before")).getOnApp() + 1,
                "On App didn't increase by delta.");

        // 4.2 Reserved unchanged
        Assert.assertEquals(
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("after")).getReserved(),
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("before")).getReserved(),
                "Reserved changed after manual addition.");

        // 4.3 FP Stock increased by +1
        Assert.assertEquals(
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("after")).getFpStock(),
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("before")).getFpStock() + 1,
                "FP Stock didn't increase by delta.");

        // 4.4 Not Sellable unchanged
        Assert.assertEquals(
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("after")).getNotSellable(),
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("before")).getNotSellable(),
                "Not Sellable changed after manual addition.");

        // 4.5 Missing unchanged
        Assert.assertEquals(
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("after")).getMissing(),
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("before")).getMissing(),
                "Missing changed after manual addition.");

        // 4.6 Total Liability increased by +1
        Assert.assertEquals(
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("after")).getTotalLiability(),
                ((models.StockBuckets) defaultTestData.get().getManualAdditionResult().get("before")).getTotalLiability() + 1,
                "Total Liability didn't increase by delta.");
    }

    @Test(groups = {"inventory-smoke"})
    @Tags({@Tag("api"), @Tag("controlRoom"), @Tag("mobile-shopping")})
    public void validateStockMovementsForPlacedOrderSingleProduct() {

        // 1. Snapshot BEFORE for the chosen product (single product scope)
        defaultTestData.get().getOrderResult().put("before",
                inventoryApiClient.get().getParsedStockBuckets(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        // 2. Place order (single product)
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(
                        defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "cod",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),"",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false,false,false,false));

        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty(),
                "Order was not created");

        // 3. Assert movement status == reserved
        Assert.assertEquals(inventoryApiClient.get().getOrderMovementStatus(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()),
                "reserved",
                "Movement status after order creation is not 'reserved'");

        // 4. Store deltas by product for later assertions
        defaultTestData.get().getOrderResult().put("reservedDeltasByProduct",
                inventoryApiClient.get().sumDeltasByProductFromMovement(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        // 5. Snapshot AFTER reservation
        defaultTestData.get().getOrderResult().put("afterReservation",
                inventoryApiClient.get().getParsedStockBuckets(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        // 6. Assertions after reservation
        Assert.assertTrue(inventoryApiClient.get().validateAllBuckets(
                        (StockBuckets) defaultTestData.get().getOrderResult().get("before"),
                        (StockBuckets) defaultTestData.get().getOrderResult().get("afterReservation"),
                        Math.abs(
                                ((Map<Integer,Integer>) defaultTestData.get().getOrderResult().get("reservedDeltasByProduct"))
                                        .get(defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())),
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId(),
                        "RESERVATION").isResult(), validationResults.get().getValidationResults().toString());

        // 7. Move order to PACKAGED status
        controlRoomV2ApiClient.get().changeOrderStatus(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        defaultTestData.get().getRandomTestUser().setAllOrders(
                orderApiClient.get().listAllOrdersUser(defaultTestData.get().getRandomTestUser()));

        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst()
                        .getStatuses().optString("packaged").isEmpty(),
                "Order was not marked as packaged");

        // 8. Assert movement status == confirmed
        Assert.assertEquals(inventoryApiClient.get().getOrderMovementStatus(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()),
                "confirmed",
                "Movement status after packing is not 'confirmed'");

        // 9. Snapshot AFTER packed
        defaultTestData.get().getOrderResult().put("afterPacked",
                inventoryApiClient.get().getParsedStockBuckets(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        // 10. Assertions after packed
        Assert.assertTrue(inventoryApiClient.get().validateAllBuckets(
                        (StockBuckets) defaultTestData.get().getOrderResult().get("afterReservation"),
                        (StockBuckets) defaultTestData.get().getOrderResult().get("afterPacked"),
                        Math.abs(
                                ((Map<Integer,Integer>) defaultTestData.get().getOrderResult().get("reservedDeltasByProduct"))
                                        .get(defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())),
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId(),
                        "PACKAGED").isResult(), validationResults.get().getValidationResults().toString());
    }

    @Test(groups = {"inventory-smoke"})
    @Tags({@Tag("api"), @Tag("controlRoom"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateStockMovementsForPlacedOrderBundleProducts() {

        // 1. Resolve bundle by getting the single products Ids inside the bundle
        defaultTestData.get().getOrderResult().put("componentIds",
                inventoryApiClient.get().getBundleComponentProductIds(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(),
                        defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts().getFirst().getMysqlId(),
                        "now"));

        // 2. BEFORE buckets snapshot
        defaultTestData.get().getOrderResult().put("before",
                inventoryApiClient.get().getStockBucketsForProducts(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(),
                        new HashSet<>(
                                (List<Integer>) defaultTestData.get().getOrderResult().get("componentIds"))));

        // 3. Place order with first bundle product
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(
                        defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "cod",
                        defaultTestData.get().getCustomerAppTestSession().getBundleOnlyProducts().getFirst(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false, false, false, false));

        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty(),
                "Order was not created");

        // 4. Assert Movement status == reserved
        Assert.assertEquals(inventoryApiClient.get().getOrderMovementStatus(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()),
                "reserved",
                "Movement status after order creation is not 'reserved'");

        // 5. Get Deltas by product (sum across batches)
        defaultTestData.get().getOrderResult().put(
                "reservedDeltasByProduct",
                inventoryApiClient.get().sumDeltasByProductFromMovement(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        // 6. AFTER reservation buckets
        defaultTestData.get().getOrderResult().put("afterReservation",
                inventoryApiClient.get().getStockBucketsForProducts(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(),
                        new HashSet<>((List<Integer>) defaultTestData.get().getOrderResult().get("componentIds"))));

        // 7. Compare the buckets and do the needed assertion on each bucket separately for the reservation phase
        Assert.assertTrue(inventoryApiClient.get().compareBuckets(
                        (Map<Integer, StockBuckets>) defaultTestData.get().getOrderResult().get("before"),
                        (Map<Integer, StockBuckets>) defaultTestData.get().getOrderResult().get("afterReservation"),
                        (Map<Integer, Integer>) defaultTestData.get().getOrderResult().get("reservedDeltasByProduct"),
                        (List<Integer>) defaultTestData.get().getOrderResult().get("componentIds"), "RESERVATION")
                        .isResult(), validationResults.get().getValidationResults().toString());

        // 8. Move order to PACKAGED status
        controlRoomV2ApiClient.get().changeOrderStatus(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        defaultTestData.get().getRandomTestUser().setAllOrders(
                orderApiClient.get().listAllOrdersUser(defaultTestData.get().getRandomTestUser()));

        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst()
                        .getStatuses().optString("packaged").isEmpty(),
                "Order was not marked as packaged");

        // 9. Assert Movement status == confirmed
        Assert.assertEquals(inventoryApiClient.get().getOrderMovementStatus(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()),
                "confirmed",
                "Movement status after packing is not 'confirmed'");

        // 10. AFTER packed snapshot for both products
        defaultTestData.get().getOrderResult().put("afterPackaged",
                inventoryApiClient.get().getStockBucketsForProducts(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestWarehouse().getId(),
                        new HashSet<>((List<Integer>) defaultTestData.get().getOrderResult().get("componentIds"))));

        // 11. Compare the buckets and do the needed assertion on each bucket separately for the packaging phase
        Assert.assertTrue(inventoryApiClient.get().compareBuckets(
                        (java.util.Map<Integer, StockBuckets>) defaultTestData.get().getOrderResult().get("afterReservation"),
                        (java.util.Map<Integer, StockBuckets>) defaultTestData.get().getOrderResult().get("afterPackaged"),
                        (java.util.Map<Integer, Integer>) defaultTestData.get().getOrderResult().get("reservedDeltasByProduct"),
                        (java.util.List<Integer>) defaultTestData.get().getOrderResult().get("componentIds"), "PACKAGED")
                        .isResult(), validationResults.get().getValidationResults().toString());
    }
}
