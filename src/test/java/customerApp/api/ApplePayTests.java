package customerApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class ApplePayTests extends BaseTest {
    //Check Payment method is Apple Pay when paying with Apple Card
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentMethodIsApplePay() {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order using Create API and payment method is Apple Pay
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false,
                        false,false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,0.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );

        //Set the user order
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");
    }

    //Check order status when the Inai Callback trx status = AUTHORIZED
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentMethodIsApplePayWhnTrxIsAUTHORIZED() {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order using Create API and payment method is Apple Pay
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false,
                        false,false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,0.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"
        );
        //Set the user order
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");
    }

    //Check Apple Pay trx when cst Balance = 0 while balance toggle on
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentWithApplePayWhenBalanceIsON() {

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure the user balance =0
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("0.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        // Create Order using Create API , Use balance toggle = true
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false,false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,0.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        // Assert the order is processing and paid with Apple Pay
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");

        //Update User balance in test data after order is being authorized
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        // Assert user balance = 0 after the order is processing
        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance());
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getCurrentBalance(), "0.00");
    }
    //Check Apple Pay trx when cst Balance = 0 while balance toggle off
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentWithApplePayWhenBalanceIsOff() {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure the user balance=0
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("0.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        // Create Order using Create API , Use balance toggle = off
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        false,
                        false,false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,0.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        // Assert the order is processing and paid with Apple Pay
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");

        //Update User balance in test data after order is being authorized
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        // Assert user balance = 0 after the order is processing
        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance());
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getCurrentBalance(), "0.00");
    }
    //Check completing the order while there's no balance, trx amount should be captured
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckCompletingTheOrderWhileThereNoBalance() {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure the user balance =0
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("0.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        // Create Order using Create API, Use balance toggle = true
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false,false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,0.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        // Assert the order is processing and paid with Apple Pay
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");

        //Update User balance in test data after order is being authorized
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        // Assert user balance = 0 after the order is processing
        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance());
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getCurrentBalance(), "0.00");

        //Complete order cycle until being completed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered",
                0.0f, false);
        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");
        //Update the current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        // Assert balance is still = 0
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getCurrentBalance(), "0.00");
    }
    //Check completing the order while there's partial amount balance,
    // trx amount should be captured and balance will be deducted
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckCompletingOrderWithPartialBalanceAmount () {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure to Update user balance to 0
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        // Create Order using Create API, Use balance toggle = true
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false,false,false
                )
        );
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-1
                ,1.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        // Assert the order is processing and paid with Apple Pay
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");

        //Update User balance in test data after order is being authorized
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete order cycle
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered",
                0.0f, false);

        //Update the current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        // Assert balance is still = 0
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 0.00);
    }
    //Check completing the order while there's balance covers total order amount,
    // trx amount should not be captured and balance will be deducted
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkCompletingOrderWithBalanceCoversTotalOrderAmount () {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure to Update user balance to 5000
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        // Create Order using Create API, Use balance toggle = true
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "cod",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false,false,false
                )
        );
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,0.00
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        // Assert the order is processing and paid with Apple Pay
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "Cash On delivery");
        // Assert payment method is not apple pay
        Assert.assertNotEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");
        //Update User balance in test data after order is being authorized
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete order cycle
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered",
                0.0f, false);

        //Update the current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        // Assert balance is not equals to zero
        Assert.assertNotEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 0);
    }
    //Check completing the order while there's -ve balance,
    // trx amount should be captured and balance =0
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkCompletingOrderWithNegativeBalance () {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure to Update user balance to -100
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-100.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        // Create Order using Create API, Use balance toggle = true
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false,false,false
                )
        );
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()+100
                ,0.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        // Assert the order is processing and paid with Apple Pay
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");

        //Update User balance in test data after order is being authorized
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete order cycle
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(), false);

        //Update the current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        // Assert balance is now =0
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 0);
    }

    //Check that If the user cancels the order while there's no balance, the transaction order will be voided.
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentAfterOrderIsCancelled() {
        //Register and create address for a random user
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure the user balance =0
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("0.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        // Create Order using Create API and payment method is Apple Pay
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false, false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,0.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );

        //Set the user order
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");
        //Cancel the order after being processing //Order cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",true);

        //Update set user order after the order being cancelled
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        // Assert balance is now =0
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 0);
    }

    //Check that If the user cancels the order while there's partial balance,
    //the transaction order will be voided and balance returned back to the cst wallet
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentAfterOrderIsCancelledPartiallyPaidWithBalance() {
        //Register and create address for a random user
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure to Update user balance to pay partially
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        // Create Order using Create API and payment method is Apple Pay
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false, false,false
                )
        );
          // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-1
                ,1.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );

        //Set the user order
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");
        //Cancel the order after being processing //Order cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",true);

        //Update set user order after the order being cancelled
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");
        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        // Assert balance is not =0 , the balance is not deducted from the cst wallet
        Assert.assertNotEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 0);
        //Assert user balance is returned to the wallet after the order is cancelled
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 1.00);
    }
    //Check that If the user cancels the order while there's balance cover order total amount,
    // there's no trx to be voided and balance returned back to the cst wallet
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentAfterOrderIsCancelledFullyPaidWithBalance() {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure to Update user balance to pay partially
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        // Create Order using Create API and payment method is Apple Pay
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "cod",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false, false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,0.00
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"
        );

        //Set the user order
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "cod");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentTitle(), "Balance");
        //Cancel the order after being processing //Order cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",true);

        //Update set user order after the order being cancelled
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "cod");
        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        //Assert user balance is returned to the wallet after the order is cancelled
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 5000.00);
    }
//Check that If the user cancels the order while there's -ve balance,
// the order amount is voided and -ve balance should be as is
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentAfterOrderIsCancelledWhileHavingDueAmount() {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure to Update user balance to pay partially
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-10.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        // Create Order using Create API and payment method is Apple Pay
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false, false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()+10
                ,0.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );

        //Set the user order
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentTitle(), "Apple Pay");
        //Cancel the order after being processing //Order cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",true);

        //Update set user order after the order being cancelled
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "Apple Pay");
        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        //Assert user balance is returned to the wallet after the order is cancelled
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), -10);
    }

    //Check mark order as not received the order while there's no balance, the transaction order will be voided.
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentAfterOrderIsNotReceived() {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure to Update user balance to pay partially
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("0.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        // Create Order using Create API and payment method is Apple Pay
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false, false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,0.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );

        //Set the user order
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentTitle(), "Apple Pay");

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status after being not received
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "Apple Pay");
        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        //Assert user balance is returned to the wallet after the order is not-received
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 0.00);
    }

    //Check mark order as not received while there's partial balance,
    // the transaction order will be voided and balance returned back to the cst wallet
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentAfterOrderIsNotReceivedWithPartialAmount() {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure to Update user balance to pay partially
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        // Create Order using Create API and payment method is Apple Pay
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false, false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                ,Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"AUTHORIZED"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-1
                ,1.00
                ,"515d41be-fe81-4e54-ba75-dda3cda9fd99"
                ,"3835"
                ,"CREDIT"
                ,"c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                ,"SAR"
                ,"INAI_APPLE_PAY"
                ,"SUCCESS"
                ,"CHARGE"
                ,"WALLET"
                ,"f990cc50-9bb9-4d35-91f1-b3d02da9180"
        );

        //Set the user order
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentTitle(), "Apple Pay");

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status after being not received
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "Apple Pay");
        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        //Assert user balance is returned to the wallet after the order is not-received
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 1.00);
    }
    //Check mark order as not received  while there's balance cover order total amount,
    // there's no trx to be voided and balance returned back to the cst wallet
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkANotReceivedOrderWithBalanceCoversTotalOrderAmount () {
        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure to Update user balance to 5000
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        // Create Order using Create API, Use balance toggle = true
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "cod",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false, false,false
                )
        );
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                , Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                , "c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                , "AUTHORIZED"
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                , 0.00
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                , "515d41be-fe81-4e54-ba75-dda3cda9fd99"
                , "3835"
                , "CREDIT"
                , "c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                , "SAR"
                , "INAI_APPLE_PAY"
                , "SUCCESS"
                , "CHARGE"
                , "WALLET"
                , "f990cc50-9bb9-4d35-91f1-b3d02da9180"
        );
        //Set the user order
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "cod");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentTitle(), "Balance");

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status after being not received
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "cod");
        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        //Assert user balance is returned to the wallet after the order is not-received
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 5000.00);
    }
    //Check mark order as not received while there's -ve balance
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckPaymentAfterOrderIsNotReceivedWhileHavingDueAmount() {
        //Register and create address for a random user
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Make sure to Update user balance to pay partially
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-10.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        // Create Order using Create API and payment method is Apple Pay
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser(),
                        "20",
                        "0",
                        "apple_pay",
                        defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                        "",
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy"),
                        true,
                        false, false,false
                )
        );
        // Update order using Inai Callback
        orderApiClient.get().getInaiCallBackEndpointResponse(
                "MASTERCARD"
                , Integer.parseInt(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
                , "c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                , "AUTHORIZED"
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() + 10
                , 0.00
                , "515d41be-fe81-4e54-ba75-dda3cda9fd99"
                , "3835"
                , "CREDIT"
                , "c4366d3c-30ef-4dbe-8e48-3caf05b14f1f"
                , "SAR"
                , "INAI_APPLE_PAY"
                , "SUCCESS"
                , "CHARGE"
                , "WALLET"
                , "f990cc50-9bb9-4d35-91f1-b3d02da9180"

        );
        //Set the user order
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "apple_pay");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentTitle(), "Apple Pay");

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status after being not received
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert on order status and payment method
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received");
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getPaymentMethod(), "Apple Pay");
        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoOrBalanceById(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        //Assert user balance is returned to the wallet after the order is not-received
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 0.00);
    }

}

