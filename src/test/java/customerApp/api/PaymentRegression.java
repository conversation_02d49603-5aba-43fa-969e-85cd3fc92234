package customerApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.time.Duration;

public class PaymentRegression extends BaseTest {
    @Test
    @Tags({@Tag("api"), @Tag("web")})
    public void PayBillOrderUsingOnlyCC() throws InterruptedException {

        //Register a new user using api
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().getRandomTestUser().setTestOrder(defaultTestData.get().getTestOrder());

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,false
                ,"Billing-aps-integration");

        //Open Redirection page to complete payment
        webDriver.get().navigate().to(defaultTestData.get().getRandomTestUser().getTestOrder().getApsRedirectionURL());

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Pay order in APS
        apsCcWebPage.get().payOrderInAps(configs.get().getTestCreditCard(), configs.get().getTestExpDate(),
                configs.get().getTestCVC(), "Bothaina");

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment Successful");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
//        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
//                        defaultTestData.get().getPaymentPanelUser(),
//                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
//                ,"AUTHORIZED");

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "Billing-aps-integration");

        //Assert CC Transaction Status is Success
//        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
//                        defaultTestData.get().getPaymentPanelUser(),
//                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
//                ,"SUCCESS");
    }

    @Test
    @Tags({@Tag("api"), @Tag("web")})
    public void PayBillOrderUsingOnlyWallet() {

        //Register a new user using api
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("150.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        defaultTestData.get().getRandomTestUser().setTestOrder(defaultTestData.get().getTestOrder());

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true
                ,"Billing-aps-integration");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId())
                ,"AUTHORIZED");

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "Billing-aps-integration");

        //Assert wallet Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId())
                ,"SUCCESS");
    }

    @Test
    @Tags({@Tag("api"), @Tag("web")})
    public void PayBillOrderUsingCCAndWallet() throws InterruptedException{

        //Register a new user using api and update user balance
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("50.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        defaultTestData.get().getRandomTestUser().setTestOrder(defaultTestData.get().getTestOrder());

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true
                ,"Billing-aps-integration");

        //Open Redirection page to complete payment
        webDriver.get().navigate().to(defaultTestData.get().getRandomTestUser().getTestOrder().getApsRedirectionURL());

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Pay order in APS
        apsCcWebPage.get().payOrderInAps(configs.get().getTestCreditCard(), configs.get().getTestExpDate(),
                configs.get().getTestCVC(), "Bothaina");

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment Successful");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transactions Statuses are authorized
//        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
//                        defaultTestData.get().getPaymentPanelUser(),
//                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
//                ,"AUTHORIZED");
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId())
                ,"AUTHORIZED");

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "Billing-aps-integration");

        //Assert Transactions Statuses are success
//        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
//                        defaultTestData.get().getPaymentPanelUser(),
//                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
//                ,"SUCCESS");
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId())
                ,"SUCCESS");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void PayGroceryOrderUsingOnlyCC() throws  InterruptedException{

        //Register a new user using api
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //Create a new address using api
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using CC and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        ,"0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,false
                ,"order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder()
                , "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void PayGroceryOrderUsingOnlyWallet() {

        //Register a new user using api and update user balance
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("30.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create a new address using api
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using CC and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        ,"0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,true,false, false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true
                ,"order");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId())
                ,"AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Assert Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId())
                ,"SUCCESS");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web")})
    public void PayGroceryOrderUsingCCAndWallet() throws InterruptedException {

        //Register a new user using api and update user balance
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("10.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create a new address using api
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create an order using CC and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        ,"0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,true,false, false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true
                ,"order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder()
                , "order",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transactions Statuses are authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"AUTHORIZED");
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId())
                ,"AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Assert Transactions Statuses are success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId())
                ,"SUCCESS");
    }

    @Test
    @Tags({@Tag("api"), @Tag("web")})
    public void PayForTopUpOrder() throws InterruptedException {

        //Register a new user using api
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().getRandomTestUser().setTestOrder(defaultTestData.get().getTestOrder());

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,false
                ,"topup");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder()
                ,"topup",true));

        //Wait for webpage to redirect between different page
        Thread.sleep(Duration.ofSeconds(5));

        //Assert Payment successful
        Assert.assertEquals(webDriver.get().getTitle(), "Payment successful | inai");

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"AUTHORIZED");

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "topup");

        //Assert CC Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getCcTransactionId())
                ,"SUCCESS");
    }
}
