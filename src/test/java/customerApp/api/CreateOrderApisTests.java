package customerApp.api;

import base.BaseTest;
import helpers.dataProviders.ThreaderDataProviderSource;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import io.restassured.path.json.JsonPath;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;

import java.time.Duration;

public class CreateOrderApisTests extends BaseTest {

    @Test(groups = {"smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithOneProductFromApi() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId() == null ||
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());

        jsonPath.set(JsonPath.given(databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                , "Select * from orders where wp_order_id = "
                        + defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())));

        Assert.assertEquals(jsonPath.get().getString("[0].stock_status"), "CONFIRMED");
    }

    @Test(dataProvider = "threadCountsProvider", dataProviderClass = ThreaderDataProviderSource.class)
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithOneProductFromApiForStressTesting(int currentThread) {
        Reporter.log("Starting to execute thread #" + currentThread, true);
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId() == null ||
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());

        jsonPath.set(JsonPath.given(databaseConnectionFactory.get().executeQuery(defaultTestData.get().getDbConnection()
                , "Select * from orders where wp_order_id = "
                        + defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())));

        Assert.assertEquals(jsonPath.get().getString("[0].stock_status"), "CONFIRMED");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createAndCompleteOrderFromApi() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        //Mark order as packed , call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert packaged key in statuses in order object has a timestamp value , indicating order has been packaged successfully
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("packaged").isEmpty());

        //Mark order as picked up , call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert received key in statuses in order object has a timestamp value , indicating order has been picked up successfully
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("received").isEmpty());

        //Mark order as in-route , call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert started key in statuses in order object has a timestamp value , indicating order has been marked as in-route successfully
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("started").isEmpty());

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert arrived key in statuses in order object has a timestamp value , indicating order has been marked as delivering successfully
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("arrived").isEmpty());

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
    }
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void createOrderWithMultipleProductsFromApi() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false,""));

        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }

    @Test(groups = "balance-sync")
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping")})
    public void CreateCCOrderFullyPaidFromWalletAndGratuityAfterCheckout() throws InterruptedException {

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Assert Balance has been updated
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),500.0f);

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,true,false, false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(),true,"order");

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoById(defaultTestData.get().getRandomTestUser().getId()
                        ,defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total subtracted from initial balance
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())
                , 500.0f-defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(),"wallet");

        //Await product stock reflection in logs
        Thread.sleep(Duration.ofMinutes(2));

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total+gratuity(5) subtracted from initial balance
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())
                , 500.0f-defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()-5);
    }
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateOrderWithProductsThatHasNoDiscount(){

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create Order with products (both single and bundle) that is not discounted
        defaultTestData.get().getRandomTestUser().setTestOrder(
        orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                , "20"
                , "10"
                , "cod"
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsWithoutDiscount()
                , ""
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                , false, false, false,false,""));

        //Assert Order Has been created and user list of orders is not empty
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId() == null ||
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        //Assert there is no discount attached to the order
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getDiscount(),0);
    }
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateOrderWithProductsThatHasSalePrice(){

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create Order with products (both single and bundle) that has sale price
        defaultTestData.get().getRandomTestUser().setTestOrder(
        orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                , "20"
                , "10"
                , "cod"
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsWithSalePrice()
                , ""
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                , false, false, false,false,""));

        //Assert Order Has been created and user list of orders is not empty
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId() == null ||
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        //Assert there is a discount attached to the order
        Assert.assertNotEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getDiscount(), 0);
    }
    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateOrderWithSingleProductsThatHasExtraSalePrice(){

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create Order with products (both single and bundle) that has sale price
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleProductsWithExtraSalePrice().getFirst()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert Order Has been created and user list of orders is not empty
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId() == null ||
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        //Assert there is a discount attached to the order
        Assert.assertNotEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getDiscount(), 0);
    }
}
