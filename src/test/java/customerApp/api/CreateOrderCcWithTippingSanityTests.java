package customerApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.time.Duration;
import java.util.Collections;
import java.util.Random;

public class CreateOrderCcWithTippingSanityTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletTippingFromCheckoutThenVoidPayment() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "5" , "0");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Void Payment From Payment Panel
        paymentPanelApiClient.get().voidPayment(defaultTestData.get().getPaymentPanelUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());

        //Await payment tp get voided
        Thread.sleep(Duration.ofMinutes(1));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order transaction status is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionStatus(), "VOIDED");
        //Assert CC transaction in payment Panel is voided
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is Processing
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "processing");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getUserInfoById(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = 500 as it started
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500);
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletAndTippingDuringCheckoutAndPartialRefundOrderThroughSwitcherWithGratuityIssueReasonToCreditCard() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");
        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        // refund order with delivery issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                Float.valueOf(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()),
                false,
                "gratuity issue",
                0);

        Thread.sleep(Duration.ofSeconds(30));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        Thread.sleep(Duration.ofSeconds(30));

        //Assert Refund Type
        Assert.assertNotEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Credit Card");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()),
                0.01
        );
    }

    @Test(groups = {"paymentFailures","testWithCallingAssignDA"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletTippingFromCheckoutAndFullyRefundGratuityOrderThroughPaymentPanelToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Assign order to DA
        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);
        // TODO: Remove this wait after finalizing investigation in the reason of delaying sending the capture or void request to the payment services
        //Wait for 180 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(3));

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getRefundWalletPaymentTransactionAmount());

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Thread.sleep(Duration.ofSeconds(20));

        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500 - (Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount())),
                0.01
        );
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web") ,@Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletAndTippingDuringCheckoutAndPartialRefundOrderThroughSwitcherWithGratuityIssueReasonToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 5
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());
        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        // refund order with gratuity issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                Float.valueOf(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()),
                true,
                "gratuity issue",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getGratuityAmount()),
                0.01
        );
    }

    @Test(groups = {"testWithCallingAssignDA"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("web") , @Tag("database")})
    public void createOrderWithCCrWithCCFullyPaidFromWalletAndTippingDuringRatingAndPartialRefundOrderThroughSwitcherWithDeliveryIssueReasonToCreditCard() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Assign order to DA
        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true, "order");

        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Assert Transaction Status is authorized
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId()),
                "AUTHORIZED");

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        Thread.sleep(Duration.ofMinutes(1));

        //Assert wallet Transaction Status is Success
        Assert.assertEquals(paymentPanelApiClient.get().checkTransactionStatus(
                        defaultTestData.get().getPaymentPanelUser(),
                        defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId()),
                "SUCCESS");

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        // refund order with gratuity issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                13,
                true,
                "delivery issue",
                0);

        Thread.sleep(Duration.ofSeconds(30));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        Thread.sleep(Duration.ofSeconds(30));

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(), 0.01);
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromWalletTippingFromCheckoutAndRefundOrderWithLateDeliveryIssueToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        // refund order with delivery issue reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(),
                false,
                "delivery issue",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()
                ).getRefundType());

        Thread.sleep(Duration.ofSeconds(60));

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getDeliveryFees()+
                        defaultTestData.get().getRandomTestUser().getTestOrder().getServiceFees(), 0.01);
    }
    @Test(groups = {"paymentFailures"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndTippingDuringRatingAndFullyRefundOrderThroughPaymentPanelToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        // TODO: Remove this wait after finalizing investigation in the reason of delaying sending the capture or void request to the payment services
        //Wait for 180 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Payment Transaction Status updated to SUCCESS
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Thread.sleep(10000);

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getRefundWalletPaymentTransactionAmount());

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");
    }

    @Test(groups = {"paymentFailures"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndTippingDuringRatingAndFullyRefundOrderWithSelectCCThroughPaymentPanelToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        // TODO: Remove this wait after finalizing investigation in the reason of delaying sending the capture or void request to the payment services
        //Wait for 180 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Payment Transaction Status updated to SUCCESS
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total+gratuity(5) subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 5),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order Amount
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getRefundWalletPaymentTransactionAmount());

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndTippingDuringRatingAndPartialRefundOrderThroughPaymentPanelToWallet() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Payment Transaction Status updated to SUCCESS
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total+gratuity(5) subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 5),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Login to Payment Panel
        paymentPanelApiClient.get().loginToPaymentPanel(
                defaultTestData.get().getPaymentPanelUser().getEmailAddress(),
                defaultTestData.get().getPaymentPanelUser().getBypassScriptPassword(),
                defaultTestData.get().getPaymentPanelUser());

        // refund from payment panel
        paymentPanelApiClient.get().RefundPayment(
                defaultTestData.get().getPaymentPanelUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderPaymentId(),
                true,
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
        );

        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert order Amount
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getRefundWalletPaymentTransactionAmount());

        //Assert Order Transaction Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getRefundWalletPaymentTransactionType(), "REFUND");
    }

    @Test(groups = {"paymentFailures","testWithCallingAssignDA"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void CreateCCOrderFullyPaidFromWalletAndTippingDuringRatingAndFullyRefundOrderThroughSwitcherToWallet() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update User Balance to Cover Order Total
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst());

        //Create an order and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleOnlyProducts().getFirst()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false));

        //Assign order to DA
        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Update Random Test User balance after payment has been made-From user object
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Complete Order Cycle through the APIs
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        // TODO: Remove this wait after finalizing investigation in the reason of delaying sending the capture or void request to the payment services
        //Wait for 180 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Capture amount of order after completion
        orderPaymentApiClient.get().capturePayment(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder(), "order");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Payment Transaction Status updated to SUCCESS
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Update User balance in test data after order completion
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Create Gratuity Order
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update User balance in test data after paying for gratuity
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert Balance returning from user object is equal to order total+gratuity(5) subtracted from initial balance
        Assert.assertEquals(
                String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())),
                String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 5),
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        // refund order with order bad experience reason
        switcherApiClient.get().refundBalance(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                true,
                "order bad experience",
                0);

        Thread.sleep(Duration.ofSeconds(60));

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundType(switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getTestOrder(),
                defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundType());

        //Assert Refund Type
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundType(),
                "Wallet");

        // refund transaction
        defaultTestData.get().getRandomTestUser().getTestOrder().setRefundAmount(
                switcherApiClient.get().refundTransaction(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getTestOrder(),
                        defaultTestData.get().getTestOrder().getRefundTransactionId()).getRefundAmount());

        //Assert Refund amount
        Assert.assertEquals(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getTestOrder().getRefundAmount()),
                defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),
                0.01
        );
    }

}
