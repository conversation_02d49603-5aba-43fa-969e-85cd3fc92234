package customerApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class ReferralsTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database"), @Tag("web")})
    public void verifyReferralCodeWithFixedAmountAndDiscountPercentageAndFreeDelivery() throws InterruptedException {

        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Thread.sleep(5000);

        webReferralsAdminPage.get().goToPage();
        Thread.sleep(8000);
        webReferralsAdminPage.get().isPageDisplayed();

        Assert.assertTrue(webReferralsAdminPage.get().isPageDisplayed(), "Referrals page is not opened");

        //Referrer Fixed Amount
        webReferralsAdminPage.get().clickReferrerFixedAmountType();
        webReferralsAdminPage.get().enterReferrerAmount("50");
        webReferralsAdminPage.get().enterReferrerAmountOrdersNumber("1");

        //First Referred Reward Discount Percentage
        webReferralsAdminPage.get().clickFirstReferredRewardDiscountPercentageType();
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercent("50");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentCap("500");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentOrdersNumber("1");

        //Second Referred Reward Free Delivery
        webReferralsAdminPage.get().clickSecondReferredRewardFreeDeliveryType();
        webReferralsAdminPage.get().enterSecondReferredRewardFreeDeliveryOrdersNumber("1");

        //Number of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().selectNumberPlacedOrdersToBeAbleToUseReferral("1");

        //Total amount of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().enterTotalAmountPlacedOrdersToBeAbleToUseReferral("1");

        //Click Update Btn
        webReferralsAdminPage.get().clickUpdateBtn();

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .get(0)
                        .getStatus(),
                "completed",
                String.format("First Order ID: %s", defaultTestData.get().getRandomTestUser() .getAllOrders()
                        .get(0).getOrderId())
        );

        //Update Random Test Referral Code
        mobileAuthorizationApiClient.get().getUserReferralCode(defaultTestData.get().getRandomTestUser());
        defaultTestData.get().getSecondaryTestUser().setReferralCode(
                defaultTestData.get().getRandomTestUser().getReferralCode());

        // Register secondary Test User
        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get()
                        , defaultTestData.get().getSecondaryTestUser()));

        // Create address for secondary user
        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                    defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                    defaultTestData.get().getSecondaryTestUser()
                )
        );

        //Create order for Secondary user and set it in test data
        defaultTestData.get().getSecondaryTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getSecondaryTestUser()
                        , "10"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getTotal(),
                false);

        //Update test data with current order status
        defaultTestData.get().getSecondaryTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getSecondaryTestUser()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getSecondaryTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));

        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                .getSecondaryTestUser().getAllOrders().getFirst().getDeliveryFees(), 0.0f);

        //Assert Discount Percentage for referred
        Assert.assertEquals(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getReferralDiscountInFeesObject(),
                -(Float.parseFloat(String.valueOf(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5))),
                String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));

        Thread.sleep(5000);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getCurrentBalance(), "50");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database"), @Tag("web")})
    public void verifyReferralCodeWithFixedAmountAndDiscountPercentageAndCashbackPercentage() throws InterruptedException {
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Thread.sleep(5000);

        webReferralsAdminPage.get().goToPage();
        Thread.sleep(8000);
        webReferralsAdminPage.get().isPageDisplayed();

        //Referrer Fixed Amount
        webReferralsAdminPage.get().clickReferrerFixedAmountType();
        webReferralsAdminPage.get().enterReferrerAmount("50");
        webReferralsAdminPage.get().enterReferrerAmountOrdersNumber("1");

        //First Referred Reward Discount Percentage
        webReferralsAdminPage.get().clickFirstReferredRewardDiscountPercentageType();
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercent("50");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentCap("500");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentOrdersNumber("1");

        //Second Referred Reward Cashback Percentage
        webReferralsAdminPage.get().clickSecondReferredRewardCashbackPercentageType();
        webReferralsAdminPage.get().enterSecondReferredRewardCashbackPercent("50");
        webReferralsAdminPage.get().enterSecondReferredRewardCashbackCapAmount("300");
        webReferralsAdminPage.get().enterSecondReferredRewardCashbackPercentOrdersNumber("1");

        //Number of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().selectNumberPlacedOrdersToBeAbleToUseReferral("1");

        //Total amount of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().enterTotalAmountPlacedOrdersToBeAbleToUseReferral("1");

        //Click Update Btn
        webReferralsAdminPage.get().clickUpdateBtn();

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random referrer user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .get(0)
                        .getStatus(),
                "completed",
                String.format("First Order ID: %s", defaultTestData.get().getRandomTestUser() .getAllOrders()
                        .get(0).getOrderId())
        );

        //Update Random Test Referral Code
        mobileAuthorizationApiClient.get().getUserReferralCode(defaultTestData.get().getRandomTestUser());
        defaultTestData.get().getSecondaryTestUser().setReferralCode(
                defaultTestData.get().getRandomTestUser().getReferralCode());

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get()
                        , defaultTestData.get().getSecondaryTestUser()));

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                    defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                    defaultTestData.get().getSecondaryTestUser()
                )
        );

        //Create order for Secondary user and set it in test data
        defaultTestData.get().getSecondaryTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getSecondaryTestUser()
                        , "10"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getTotal(),
                false);

        //Update test data with current order status
        defaultTestData.get().getSecondaryTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getSecondaryTestUser()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getSecondaryTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));

        Assert.assertEquals(defaultTestData.get().getSecondaryTestUser().getTestOrder().getReferralDiscountInFeesObject(),
                -(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5));

        //Update test data with current user balance
        defaultTestData.get().getSecondaryTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getSecondaryTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Thread.sleep(5000);

        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getSecondaryTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));

        Thread.sleep(5000);

        //Update test data with referrer user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getCurrentBalance(), "50");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database"), @Tag("web")})
    public void verifyReferralCodeWithFixedAmountAndDiscountPercentageAndNoneSecondReward() throws InterruptedException {
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Thread.sleep(5000);

        webReferralsAdminPage.get().goToPage();
        Thread.sleep(8000);
        webReferralsAdminPage.get().isPageDisplayed();

        Assert.assertTrue(webReferralsAdminPage.get().isPageDisplayed(), "Referrals page is not opened");

        //Referrer Fixed Amount
        webReferralsAdminPage.get().clickReferrerFixedAmountType();
        webReferralsAdminPage.get().enterReferrerAmount("50");
        webReferralsAdminPage.get().enterReferrerAmountOrdersNumber("1");

        //First Referred Reward Discount Percentage
        webReferralsAdminPage.get().clickFirstReferredRewardDiscountPercentageType();
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercent("50");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentCap("500");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentOrdersNumber("1");

        //Second None Referred Reward
        webReferralsAdminPage.get().clickNoneSecondReferredRewardType();

        //Number of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().selectNumberPlacedOrdersToBeAbleToUseReferral("1");

        //Total amount of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().enterTotalAmountPlacedOrdersToBeAbleToUseReferral("1");

        //Click Update Btn
        webReferralsAdminPage.get().clickUpdateBtn();

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .get(0)
                        .getStatus(),
                "completed",
                String.format("First Order ID: %s", defaultTestData.get().getRandomTestUser() .getAllOrders()
                        .get(0).getOrderId())
        );

        //Update Random Test Referral Code
        mobileAuthorizationApiClient.get().getUserReferralCode(defaultTestData.get().getRandomTestUser());
        defaultTestData.get().getSecondaryTestUser().setReferralCode(
                defaultTestData.get().getRandomTestUser().getReferralCode());

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get()
                        , defaultTestData.get().getSecondaryTestUser()));

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                    defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                    defaultTestData.get().getSecondaryTestUser()
                )
        );

        //Create order for Secondary user and set it in test data
        defaultTestData.get().getSecondaryTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getSecondaryTestUser()
                        , "10"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getTotal(),
                false);

        //Update test data with current order status
        defaultTestData.get().getSecondaryTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getSecondaryTestUser()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getSecondaryTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));

        Assert.assertEquals(defaultTestData.get().getSecondaryTestUser().getTestOrder().getReferralDiscountInFeesObject(),
                -(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5));

        Thread.sleep(5000);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getCurrentBalance(), "50");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database"), @Tag("web")})
    public void verifyReferralCodeWithFixedAmountAndCashbackPercentageAndFreeDelivery() throws InterruptedException {
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Thread.sleep(5000);

        webReferralsAdminPage.get().goToPage();
        Thread.sleep(8000);
        webReferralsAdminPage.get().isPageDisplayed();

        //Referrer Fixed Amount
        webReferralsAdminPage.get().clickReferrerFixedAmountType();
        webReferralsAdminPage.get().enterReferrerAmount("50");
        webReferralsAdminPage.get().enterReferrerAmountOrdersNumber("1");

        //First Referred Reward Cashback Percentage
        webReferralsAdminPage.get().clickFirstReferredRewardCashbackPercentageType();
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercent("50");
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercentCap("300");
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercentOrdersNumber("1");

        //Second Referred Reward Free Delivery
        webReferralsAdminPage.get().clickSecondReferredRewardFreeDeliveryType();
        webReferralsAdminPage.get().enterSecondReferredRewardFreeDeliveryOrdersNumber("1");

        //Number of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().selectNumberPlacedOrdersToBeAbleToUseReferral("1");

        //Total amount of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().enterTotalAmountPlacedOrdersToBeAbleToUseReferral("1");

        //Click Update Btn
        webReferralsAdminPage.get().clickUpdateBtn();

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .getFirst()
                        .getStatus(),
                "completed",
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );

        //Update Random Test Referral Code
        mobileAuthorizationApiClient.get().getUserReferralCode(defaultTestData.get().getRandomTestUser());
        defaultTestData.get().getSecondaryTestUser().setReferralCode(
                defaultTestData.get().getRandomTestUser().getReferralCode());

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get()
                        , defaultTestData.get().getSecondaryTestUser()));

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                    defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                    defaultTestData.get().getSecondaryTestUser()
                )
        );

        //Create order for Secondary user and set it in test data
        defaultTestData.get().getSecondaryTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getSecondaryTestUser()
                        , "10"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getTotal(),
                false);

        //Update test data with current order status
        defaultTestData.get().getSecondaryTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getSecondaryTestUser()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getSecondaryTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));
        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                .getSecondaryTestUser().getAllOrders().getFirst().getDeliveryFees(), 0.0f);

        //Update test data with current user balance
        defaultTestData.get().getSecondaryTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getSecondaryTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getSecondaryTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));

        Thread.sleep(5000);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getCurrentBalance(), "50");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database"), @Tag("web")})
    public void verifyReferralCodeWithFixedAmountAndCashbackPercentageAndNoneSecondReward() throws InterruptedException {
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Thread.sleep(1000);

        webReferralsAdminPage.get().goToPage();
        Thread.sleep(8000);
        webReferralsAdminPage.get().isPageDisplayed();

        Assert.assertTrue(webReferralsAdminPage.get().isPageDisplayed(), "Referrals page is not opened");

        //Referrer Fixed Amount
        webReferralsAdminPage.get().clickReferrerFixedAmountType();
        webReferralsAdminPage.get().enterReferrerAmount("50");
        webReferralsAdminPage.get().enterReferrerAmountOrdersNumber("1");

        //First Referred Reward Cashback Percentage
        webReferralsAdminPage.get().clickFirstReferredRewardCashbackPercentageType();
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercent("50");
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercentCap("300");
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercentOrdersNumber("1");

        //Second None Referred Reward
        webReferralsAdminPage.get().clickNoneSecondReferredRewardType();

        //Number of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().selectNumberPlacedOrdersToBeAbleToUseReferral("1");

        //Total amount of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().enterTotalAmountPlacedOrdersToBeAbleToUseReferral("1");

        //Click Update Btn
        webReferralsAdminPage.get().clickUpdateBtn();

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .getFirst()
                        .getStatus(),
                "completed",
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );

        //Update Random Test Referral Code
        mobileAuthorizationApiClient.get().getUserReferralCode(defaultTestData.get().getRandomTestUser());
        defaultTestData.get().getSecondaryTestUser().setReferralCode(
                defaultTestData.get().getRandomTestUser().getReferralCode());

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get()
                        , defaultTestData.get().getSecondaryTestUser()));

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                    defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                    defaultTestData.get().getSecondaryTestUser()
                )
        );

        //Create order for Secondary user and set it in test data
        defaultTestData.get().getSecondaryTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getSecondaryTestUser()
                        , "10"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getTotal(),
                false);

        //Update test data with current order status
        defaultTestData.get().getSecondaryTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getSecondaryTestUser()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getSecondaryTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));

        //Update test data with current user balance
        defaultTestData.get().getSecondaryTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getSecondaryTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        //check cashback reward for referred user
        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getSecondaryTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));

        Thread.sleep(5000);

        //Update test data with current referrer user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Check reward for referrer user
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getCurrentBalance(), "50");
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database"), @Tag("web")})
    public void verifyReferralCodeWithCashbackAndDiscountPercentageAndFreeDelivery() throws InterruptedException {
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Thread.sleep(5000);

        webReferralsAdminPage.get().goToPage();
        Thread.sleep(8000);
        webReferralsAdminPage.get().isPageDisplayed();

        //Referrer Cashback Percentage
        webReferralsAdminPage.get().clickReferrerCashbackPercentageType();
        webReferralsAdminPage.get().enterReferrerCashbackPercent("50");
        webReferralsAdminPage.get().enterReferrerCashbackCapAmount("300");
        webReferralsAdminPage.get().enterReferrerCashbackOrdersNumber("1");

        //First Referred Reward Discount Percentage
        webReferralsAdminPage.get().clickFirstReferredRewardDiscountPercentageType();
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercent("50");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentCap("500");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentOrdersNumber("1");

        //Second Referred Reward Free Delivery
        webReferralsAdminPage.get().clickSecondReferredRewardFreeDeliveryType();
        webReferralsAdminPage.get().enterSecondReferredRewardFreeDeliveryOrdersNumber("1");

        //Number of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().selectNumberPlacedOrdersToBeAbleToUseReferral("1");

        //Total amount of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().enterTotalAmountPlacedOrdersToBeAbleToUseReferral("1");

        //Click Update Btn
        webReferralsAdminPage.get().clickUpdateBtn();

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .getFirst()
                        .getStatus(),
                "completed",
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );

        //Update Random Test Referral Code
        mobileAuthorizationApiClient.get().getUserReferralCode(defaultTestData.get().getRandomTestUser());
        defaultTestData.get().getSecondaryTestUser().setReferralCode(
                defaultTestData.get().getRandomTestUser().getReferralCode());

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get()
                        , defaultTestData.get().getSecondaryTestUser()));

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                    defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                    defaultTestData.get().getSecondaryTestUser()
                )
        );

        //Create order for Secondary user and set it in test data
        defaultTestData.get().getSecondaryTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getSecondaryTestUser()
                        , "10"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getTotal(),
                false);

        //Update test data with current order status
        defaultTestData.get().getSecondaryTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getSecondaryTestUser()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getSecondaryTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));

        Assert.assertEquals(defaultTestData.get().getSecondaryTestUser().getTestOrder().getReferralDiscountInFeesObject(),
                -(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5));

        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                .getSecondaryTestUser().getAllOrders().getFirst().getDeliveryFees(), 0.0f);

        Thread.sleep(5000);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Thread.sleep(3000);

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert CashBack for referrer
        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getRandomTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database"), @Tag("web")})
    public void verifyReferralCodeWithCashbackAndDiscountPercentageAndCashbackPercentage() throws InterruptedException {
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Thread.sleep(3000);

        webReferralsAdminPage.get().goToPage();
        Thread.sleep(8000);
        webReferralsAdminPage.get().isPageDisplayed();

        //Referrer Cashback Percentage
        webReferralsAdminPage.get().clickReferrerCashbackPercentageType();
        webReferralsAdminPage.get().enterReferrerCashbackPercent("50");
        webReferralsAdminPage.get().enterReferrerCashbackCapAmount("300");
        webReferralsAdminPage.get().enterReferrerCashbackOrdersNumber("1");

        //First Referred Reward Discount Percentage
        webReferralsAdminPage.get().clickFirstReferredRewardDiscountPercentageType();
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercent("50");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentCap("500");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentOrdersNumber("1");

        //Second Referred Reward Cashback Percentage
        webReferralsAdminPage.get().clickSecondReferredRewardCashbackPercentageType();
        webReferralsAdminPage.get().enterSecondReferredRewardCashbackPercent("50");
        webReferralsAdminPage.get().enterSecondReferredRewardCashbackCapAmount("300");
        webReferralsAdminPage.get().enterSecondReferredRewardCashbackPercentOrdersNumber("1");

        //Number of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().selectNumberPlacedOrdersToBeAbleToUseReferral("1");

        //Total amount of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().enterTotalAmountPlacedOrdersToBeAbleToUseReferral("1");

        //Click Update Btn
        webReferralsAdminPage.get().clickUpdateBtn();

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .getFirst()
                        .getStatus(),
                "completed",
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );

        //Update Random Test Referral Code
        mobileAuthorizationApiClient.get().getUserReferralCode(defaultTestData.get().getRandomTestUser());
        defaultTestData.get().getSecondaryTestUser().setReferralCode(
                defaultTestData.get().getRandomTestUser().getReferralCode());

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get()
                        , defaultTestData.get().getSecondaryTestUser()));

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                    defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                    defaultTestData.get().getSecondaryTestUser()
                )
        );

        //Create order for Secondary user and set it in test data
        defaultTestData.get().getSecondaryTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getSecondaryTestUser()
                        , "10"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getTotal(),
                false);

        //Update test data with current order status
        defaultTestData.get().getSecondaryTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getSecondaryTestUser()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getSecondaryTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));

        Assert.assertEquals(defaultTestData.get().getSecondaryTestUser().getTestOrder().getReferralDiscountInFeesObject(),
                -(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5));

        //Update test data with current user balance
        defaultTestData.get().getSecondaryTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getSecondaryTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());
        Thread.sleep(5000);

        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getSecondaryTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));

        Thread.sleep(3000);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert CashBack for referrer
        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getRandomTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database"), @Tag("web")})
    public void verifyReferralCodeWithCashbackAndDiscountPercentageAndNoneSecondReward() throws InterruptedException {
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Thread.sleep(3000);

        webReferralsAdminPage.get().goToPage();
        Thread.sleep(8000);
        webReferralsAdminPage.get().isPageDisplayed();

        //Referrer Cashback Percentage
        webReferralsAdminPage.get().clickReferrerCashbackPercentageType();
        webReferralsAdminPage.get().enterReferrerCashbackPercent("50");
        webReferralsAdminPage.get().enterReferrerCashbackCapAmount("300");
        webReferralsAdminPage.get().enterReferrerCashbackOrdersNumber("1");

        //First Referred Reward Discount Percentage
        webReferralsAdminPage.get().clickFirstReferredRewardDiscountPercentageType();
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercent("50");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentCap("500");
        webReferralsAdminPage.get().enterFirstReferredRewardDiscountPercentOrdersNumber("1");

        //Second None Referred Reward
        webReferralsAdminPage.get().clickNoneSecondReferredRewardType();

        //Number of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().selectNumberPlacedOrdersToBeAbleToUseReferral("1");

        //Total amount of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().enterTotalAmountPlacedOrdersToBeAbleToUseReferral("1");

        //Click Update Btn
        webReferralsAdminPage.get().clickUpdateBtn();

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .getFirst()
                        .getStatus(),
                "completed",
                String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())
        );

        //Update Random Test Referral Code
        mobileAuthorizationApiClient.get().getUserReferralCode(defaultTestData.get().getRandomTestUser());
        defaultTestData.get().getSecondaryTestUser().setReferralCode(
                defaultTestData.get().getRandomTestUser().getReferralCode());

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get()
                        , defaultTestData.get().getSecondaryTestUser()));

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                    defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                    defaultTestData.get().getSecondaryTestUser()
                )
        );

        //Create order for Secondary user and set it in test data
        defaultTestData.get().getSecondaryTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getSecondaryTestUser()
                        , "10"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getTotal(),
                false);

        //Update test data with current order status
        defaultTestData.get().getSecondaryTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getSecondaryTestUser()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getSecondaryTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));

        Assert.assertEquals(defaultTestData.get().getSecondaryTestUser().getTestOrder().getReferralDiscountInFeesObject(),
                -(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5));

        Thread.sleep(3000);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert CashBack for referrer
        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getRandomTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));

    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database"), @Tag("web")})
    public void verifyReferralCodeWithCashbackAndCashbackPercentageAndFreeDelivery() throws InterruptedException {
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Thread.sleep(5000);

        webReferralsAdminPage.get().goToPage();
        Thread.sleep(8000);
        webReferralsAdminPage.get().isPageDisplayed();

        //Referrer Cashback Percentage
        webReferralsAdminPage.get().clickReferrerCashbackPercentageType();
        webReferralsAdminPage.get().enterReferrerCashbackPercent("50");
        webReferralsAdminPage.get().enterReferrerCashbackCapAmount("500");
        webReferralsAdminPage.get().enterReferrerCashbackOrdersNumber("1");

        //First Referred Reward Cashback Percentage
        webReferralsAdminPage.get().clickFirstReferredRewardCashbackPercentageType();
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercent("50");
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercentCap("500");
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercentOrdersNumber("1");

        //Second Referred Reward Free Delivery
        webReferralsAdminPage.get().clickSecondReferredRewardFreeDeliveryType();
        webReferralsAdminPage.get().enterSecondReferredRewardFreeDeliveryOrdersNumber("1");

        //Number of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().selectNumberPlacedOrdersToBeAbleToUseReferral("1");

        //Total amount of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().enterTotalAmountPlacedOrdersToBeAbleToUseReferral("1");

        //Click Update Btn
        webReferralsAdminPage.get().clickUpdateBtn();

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .get(0)
                        .getStatus(),
                "completed",
                String.format("First Order ID: %s", defaultTestData.get().getRandomTestUser() .getAllOrders()
                        .get(0).getOrderId())
        );

        //Update Random Test Referral Code
        mobileAuthorizationApiClient.get().getUserReferralCode(defaultTestData.get().getRandomTestUser());
        defaultTestData.get().getSecondaryTestUser().setReferralCode(
                defaultTestData.get().getRandomTestUser().getReferralCode());

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get()
                        , defaultTestData.get().getSecondaryTestUser()));

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                    defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                    defaultTestData.get().getSecondaryTestUser()
                )
        );

        //Create order for Secondary user and set it in test data
        defaultTestData.get().getSecondaryTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getSecondaryTestUser()
                        , "10"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getTotal(),
                false);

        //Update test data with current order status
        defaultTestData.get().getSecondaryTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getSecondaryTestUser()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getSecondaryTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));

        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                .getSecondaryTestUser().getAllOrders().getFirst().getDeliveryFees(), 0);

        //Update test data with current user balance
        defaultTestData.get().getSecondaryTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getSecondaryTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());
        Thread.sleep(3000);

        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getSecondaryTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));

        Thread.sleep(3000);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert CashBack for referrer
        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getRandomTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));
    }

    @Test
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database"), @Tag("web")})
    public void verifyReferralCodeWithCashbackAndCashbackPercentageAndNoneSecondReward() throws InterruptedException {
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Thread.sleep(5000);

        webReferralsAdminPage.get().goToPage();
        Thread.sleep(8000);
        webReferralsAdminPage.get().isPageDisplayed();

        //Referrer Cashback Percentage
        webReferralsAdminPage.get().clickReferrerCashbackPercentageType();
        webReferralsAdminPage.get().enterReferrerCashbackPercent("50");
        webReferralsAdminPage.get().enterReferrerCashbackCapAmount("500");
        webReferralsAdminPage.get().enterReferrerCashbackOrdersNumber("1");

        //First Referred Reward Cashback Percentage
        webReferralsAdminPage.get().clickFirstReferredRewardCashbackPercentageType();
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercent("50");
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercentCap("500");
        webReferralsAdminPage.get().enterFirstReferredRewardCashbackPercentOrdersNumber("1");

        //Second None Referred Reward
        webReferralsAdminPage.get().clickNoneSecondReferredRewardType();

        //Number of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().selectNumberPlacedOrdersToBeAbleToUseReferral("1");

        //Total amount of Placed Orders to be able to use Referral
        webReferralsAdminPage.get().enterTotalAmountPlacedOrdersToBeAbleToUseReferral("1");

        //Click Update Btn
        webReferralsAdminPage.get().clickUpdateBtn();

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Complete order through the apis , with collected amount = order total
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal(),false);

        Assert.assertEquals(
                defaultTestData.get()
                        .getRandomTestUser()
                        .getAllOrders()
                        .get(0)
                        .getStatus(),
                "completed",
                String.format("First Order ID: %s", defaultTestData.get().getRandomTestUser() .getAllOrders()
                        .get(0).getOrderId())
        );

        //Update Random Test Referral Code
        mobileAuthorizationApiClient.get().getUserReferralCode(defaultTestData.get().getRandomTestUser());
        defaultTestData.get().getSecondaryTestUser().setReferralCode(
                defaultTestData.get().getRandomTestUser().getReferralCode());

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get()
                        , defaultTestData.get().getSecondaryTestUser()));

        defaultTestData.get().setSecondaryTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                    defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                    defaultTestData.get().getSecondaryTestUser()
                )
        );

        //Create order for Secondary user and set it in test data
        defaultTestData.get().getSecondaryTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getSecondaryTestUser()
                        , "10"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId(),
                "delivered",
                defaultTestData.get().getSecondaryTestUser().getTestOrder().getTotal(),
                false);

        //Update test data with current order status
        defaultTestData.get().getSecondaryTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getSecondaryTestUser()));

        //Assert order is completed
        Assert.assertEquals(defaultTestData.get()
                        .getSecondaryTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getSecondaryTestUser().getTestOrder().getOrderId()));

        //Update test data with current user balance
        defaultTestData.get().getSecondaryTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getSecondaryTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Thread.sleep(5000);

        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getSecondaryTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));

        Thread.sleep(3000);

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get()
                        .getRandomTestUser().getId(), defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be.");

        //Assert CashBack for referrer
        Assert.assertEquals(String.format("%.2f", Math.round(Double.parseDouble(defaultTestData.get().getRandomTestUser().getCurrentBalance()) * 100.0) / 100.0),
                String.format("%.2f", Math.round(defaultTestData.get().getSecondaryTestUser().getAllOrders().getFirst().getOrderProducts().getFirst().getTotal() * 0.5 * 100.0) / 100.0));
    }
}
