package customerApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.time.Duration;
import java.util.Collections;
import java.util.Random;

public class CreateOrderCcWithTippingFullyPaidFromBalanceSanityTests extends BaseTest {

    @Test(groups = {"order-smoke", "paymentfailures","testWithCallingAssignDA"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromBalanceAndFreeDeliveryCouponAndTippingDuringCheckout() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create Free delivery coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free delivery",
                "supermarket",
                "commercial",
                0,
                0,
                0,
                "active",
                true,
                false,
                false,
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Assign order to DA
        defaultTestData.get().setAssignedTrip(fleetAppApiClient.get().assignTrip(
                defaultTestData.get().getDaUser(), Collections.singletonList(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId())));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), true,
                "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , true , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1" , "0");
        //Asserting Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(
                defaultTestData.get().getTestCoupon().getCouponCode()
                , defaultTestData.get().getRandomTestUser().getTestOrder());

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());

        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);

        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0.0f, false);

        // TODO: Remove this wait after finalizing investigation in the reason of delaying sending the capture or void request to the payment services
        //Wait for 180 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert delivery fees = 0
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getTestOrder().getDeliveryFees(), 0
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        Thread.sleep(Duration.ofSeconds(30));

        //Get order Transaction details After completion
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Order Transaction updated to SUCCESS
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "SUCCESS");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance()
                , "Current user balance is null and it shouldn't be");

        //Assert User Balance has order amount deducted , coupon & collected amount Reflected
        Assert.assertEquals(String.format("%.2f", Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()))
                , String.format("%.2f", 500.0f - defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity())
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
    }

    @Test(groups = {"order-smoke","paymentFailures"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCFullyPaidFromBalanceWithTippingDuringCheckoutThenMarkOrderAsNotReceived() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to have amount of 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        // TODO: Remove this wait after finalizing investigation in the reason of delaying sending the capture or void request to the payment services
        //Wait for 180 secs until back to wallet reflects on balance of user
        Thread.sleep(Duration.ofMinutes(5));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct(),0);
        //Assert gratuity
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1","0" );

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Await order becomes processing first
        Thread.sleep(Duration.ofSeconds(60));

        //Mark order as not-received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Sync order
        testExecutionHelper.get().syncOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());

        //Assert order is marked as not received
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");

        //Update random test user balance with current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert balance still has the same amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 500
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get Order details from switcher and set to test order
        defaultTestData.get().getCustomerAppTestSession().setTestOrder(switcherApiClient.get()
                .getUserCompletedOrders(defaultTestData.get().getRandomTestUser()
                        ,defaultTestData.get().getAdminUser(),3));

        //Assert order details in switcher order object
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isPaidByInai());
        Assert.assertFalse(defaultTestData.get().getCustomerAppTestSession().getTestOrder().isCredit());
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getPaymentTitle()
                ,"Balance");
        Assert.assertEquals(defaultTestData.get().getCustomerAppTestSession().getTestOrder().getTotalInvoice()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal());
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getCustomerAppTestSession().getTestOrder().getBalanceUsedInOrder())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithCCAndTippingFullyPaidFromBalanceAndFreeGiftCouponAndMarkOrderAsCancelled() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Create free gift coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createDiscountProductOrFreeGiftCoupon(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "free gift",
                "commercial",
                1,
                "active",
                true,
                false,
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),
                "now"));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update user balance to 500
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("500.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "5"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , defaultTestData.get().getTestCoupon().getCouponCode()
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false, false,false,""));

        //Create & Pay for order in payment service
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get()
                ,true, "order");

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),true,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("inai","Credit/Debit Card"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "pending" ,false);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , true , defaultTestData.get().getTestCoupon());
        //Assert Coupon Object
        createOrderApiValidator.get().assertCouponObjectInResponse(defaultTestData.get().getTestCoupon().getCouponCode()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "5" , "0");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");

        //Assert Transaction Type is Payment
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getOrderPaymentTransactionType(), "PAYMENT");
        //Assert There is a wallet transaction and its amount is = to order total
        Assert.assertEquals(String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionAmount())
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity()));
        //Assert Transaction ID is  correct
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getWalletTransactionId(),
                defaultTestData.get().getRandomTestUser().getTestOrder()
                        .getOrderPaymentTransactions().getChargeWalletPaymentTransactionId());
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "AUTHORIZED");

        //Get Order Details from Control Room and set it to test order
        controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder());
        //Assert total to collect in control room is 0
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalToCollect(),0);
        //Assert Order Value in control room is = to Order total without gratuity or Due amount
        Assert.assertEquals(defaultTestData.get().getTestOrder().getTotalAmount()
                ,String.format("%.2f",defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()));

        //Await order become processing before cancelling it
        Thread.sleep(Duration.ofSeconds(120));

        //Mark order as cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",false);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is cancelled
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        Thread.sleep(Duration.ofMinutes(5));

        //Update random test user balance with current balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert balance still has the same amount
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                500
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Get order Transaction details
        paymentPanelApiClient.get().getOrderTransactionDetails(
                defaultTestData.get().getPaymentPanelUser(),defaultTestData.get().getRandomTestUser().getTestOrder(),"order");
        //Assert Wallet Transaction Status
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder()
                .getOrderPaymentTransactions().getChargeWalletPaymentTransactionStatus(), "VOIDED");
    }

}
