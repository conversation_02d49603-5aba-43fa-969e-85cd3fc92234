package customerApp.api;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.time.Duration;
import java.util.Random;

public class CreateOrderCodSanityTests extends BaseTest {

    @Test(groups = {"order-smoke" , "stable-test"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"),@Tag("database")})
    public void createOrderWithTippingDuringCheckoutAndCollectedAmountEqualOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,"Call me"));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false , false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);

        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "10" , "0");

        Assert.assertNotNull(defaultTestData.get()
                        .getRandomTestUser().getTestOrder().getGratuityAmount()
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Assert Gratuity value is correct after order creation
        Assert.assertEquals(Integer.valueOf(defaultTestData.get()
                        .getRandomTestUser().getTestOrder().getGratuityAmount()), 10
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        // call Get order API
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        // Assert user details (id, name, address details) in get order API
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getUser());

        // Assert order id in create order response = order id in get order API
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());
        // Assert order number in create order response = order number in get order API
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderNumber(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());

        // Assert order status, isCancellable, CurrentOrderStatus in get order API
        getOrderApiValidator.get().assertOrderStatus(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getStatus(),"processing");
        getOrderApiValidator.get().assertCancellable(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().isCancellable(),true);
        getOrderApiValidator.get().assertCurrentOrderStatus(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getCurrentStatus(),"orderPlaced");

        // Assert delivery note in get order API
        getOrderApiValidator.get().assertOnDeliveryNote(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getDeliveryNote(),"Call me");

        // Assert on payment method, payment title and balance used in get order API
        getOrderApiValidator.get().assertPaymentMethod(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getPaymentMethod(),"cod");
        getOrderApiValidator.get().assertPaymentTitle(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getPaymentTitle(),"Cash On Delivery");
        getOrderApiValidator.get().assertBalanceUsed(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getBalanceUsedInOrder(),0);

        //Assert on NOW,isTimeSlotShifted, scheduled_express, isScheduled is true in get order API
        getOrderApiValidator.get().assertNow(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().isNow(),true);
        getOrderApiValidator.get().assertScheduledExpress(defaultTestData.get().getRandomTestUser().getAllOrders()
                .getFirst().getIsScheduledExpress(),false);

        // Assert user details (id, name, address details) in get order API
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getUser());
        //Assert on Gratuity amount
        Assert.assertEquals(Integer.valueOf(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getGratuityAmount()), 10);
        // Assert on product list in get order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false , false);

        //Complete order cycle from the APIs where collected amount  = order total + gratuity
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotalWithGratuity(), false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId()
                        , defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert user balance is 0
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                0.0f
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithTippingDuringCheckoutAndFullyPayFromBalanceAndCancel() throws InterruptedException {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 1000
        defaultTestData.get().getRandomTestUser().
                setCurrentBalance(switcherApiClient.get().updateUserBalanceByPhoneNumber("1000.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Assert user balance updated to 1000
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())
                , 1000.0f);

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order using balance and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "15"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , true, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false , false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Balance"
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getTotal()
                ,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "15" , "15");

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Cancel order
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "cancelled",false);

        //Update order in test data with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is cancelled
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        Thread.sleep(Duration.ofMinutes(5));

        //Update user's balance in test data
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert user has been refunded order amount - gratuity
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                1000.0f
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

    }
    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithTippingDuringCheckoutAndNotReceived() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order COD with gratuity 10 and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false,false,false, true,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Asserting Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "10" , "0");
        //Asserting Gift Receipt
        createOrderApiValidator.get().assertGiftReceiptKeyInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder() , true);

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert Gratuity Amount in order is 10
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getGratuityAmount(), "10"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Mark order as not received
        orderApiClient.get().markOrderAsNotReceived(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "not received");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is not received
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "not-received"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderWithTippingDuringCheckoutAndFailed() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register & Create address for a user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create an order COD with gratuity =10 and set it as test order for a random test user
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "10"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Assert Gratuity
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "10" , "0");

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert gratuity value is 10 after placing order
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getGratuityAmount(), "10"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));

        //Mark order as failed
        orderApiClient.get().markOrderAsFailed(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "failed");

        //Assert order does not show in user orders
        Assert.assertTrue(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()).isEmpty());
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderCODBalancePositiveNotUsedCollectedAmountGreaterThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 1
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Complete order cycle through the apis , setting collected amount to value > order total by 1
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() + 1
                , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        //Assert current balance = existing balance (1) + additional collected value (1)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                2
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderCODBalancePositiveNotUsedAndTippingDuringRatingAndCollectedAmountLessThanOrderAmount() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Update random user balance to 20
        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("20.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);

        //Complete order cycle through the apis , setting collected amount to value < order total by 1
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getTotal() - 1.0f
                , false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is completed
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Create Tipping during Rating
        orderPaymentApiClient.get().createGratuityInRatingUsingApi(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder(), "wallet");

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        Assert.assertNotNull(defaultTestData.get().getRandomTestUser().getCurrentBalance());

        //Assert current balance = original balance(20) - amount collected less(1) - gratuity (5)
        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()),
                14
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }
    @Test(groups = {"order-smoke"})
    @Tags({@Tag("customer-app"), @Tag("mobile-shopping"), @Tag("database")})
    public void createOrderCODAndTippingDuringCheckoutAndMarkOrderAsCancelled() {

        // Update capacity to all timeslots
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllTimeSlotsId(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()),
                new Random().nextInt(20,1000));

        //Register and create address for a random user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Set delivery fees based on cart details
        checkoutApiClient.get().getCheckoutDetails(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                ,defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList());

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderWithMultipleProductsUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "1"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                        , ""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false,""));

        //Validate Product List in Create Order Response
        createOrderApiValidator.get().assertProductListInResponse(
                defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList().subList(0,2)
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderProducts()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),false,false);
        //Validate User Details in Create Order Response
        createOrderApiValidator.get().assertUserDetailsInResponse(defaultTestData.get().getRandomTestUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getUser());
        //Validate Payment Details in Create Order Response
        createOrderApiValidator.get().assertPaymentDetailsInResponse("cod","Cash On Delivery"
                ,0,defaultTestData.get().getRandomTestUser().getTestOrder());
        //Validate order type & status in Create Order Response
        createOrderApiValidator.get().assertOrderTypeAndStatusInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                ,true , "Now" , "processing" ,true);
        //Assert Fees Object & delivery Fees In Response
        createOrderApiValidator.get().assertFeesObjectAndDeliveryFeesInResponse(
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , defaultTestData.get().getRandomTestUser().getTestOrder()
                , false , defaultTestData.get().getTestCoupon());
        //Asserting order Total , Subtotal & Discounts in response
        createOrderApiValidator.get().assertOrderTotalAndDiscountsInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , defaultTestData.get().getCustomerAppTestSession().getSingleAndBundleProductsList()
                , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                , false , defaultTestData.get().getTestCoupon()
                , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock(),0);
        //Assert Gratuity Object
        createOrderApiValidator.get().assertGratuityObjectInResponse(
                defaultTestData.get().getRandomTestUser().getTestOrder()
                , "1","0");

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Mark order as cancelled
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "cancelled",false);

        //Update test order with the latest status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert order status is cancelled
        Assert.assertEquals(defaultTestData.get()
                        .getRandomTestUser().getAllOrders().getFirst().getStatus(), "cancelled"
                , String.format("Order ID: %s", defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()));
    }

}
