package customerApp.api.foodAggregator;

import base.BaseTest;
import helpers.factories.dataFactories.foodAggregatorDataFactories.RestaurantsDataFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.List;

@Test
public class FoodAggregatorRestaurantsDetailsApiTests extends BaseTest {

    @Test
    @Tags({@Tag("api"), @Tag("food-aggregator")})
    public void validateRestaurantsDetailsInGetAllRestaurantsApi() {

        Assert.assertTrue(new RestaurantsDataFactory(configs.get()).compareTwoListsOfRestaurants(
                defaultTestData.get().getFoodAggregatorTestSession().getYeloRestaurantsList()
                ,defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList())
                ,"Two Restaurants Lists are not The same");

    }

    @Test
    @Tags({@Tag("api"), @Tag("food-aggregator")})
    public void validateRestaurantsDetailsInGetSingleRestaurantApi() {

        Assert.assertTrue(new RestaurantsDataFactory(configs.get()).compareRestaurants(
                defaultTestData.get().getFoodAggregatorTestSession().getYeloRestaurantsList()
                        .stream()
                        .filter(r -> r.getYeloId() == (defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().getFirst().getYeloId()))
                        .findFirst()
                        .get()
                ,defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getFirst())
                ,"Two Restaurants are not the same");

    }

    @Test
    @Tags({@Tag("api"), @Tag("food-aggregator")})
    public void validateCategoryFilterWorksAsExpected() {

       Assert.assertTrue(foodAggregatorRestaurantsApiClient.get().getRestaurantsDataByCategoryId(
                       defaultTestData.get().getFoodAggregatorTestSession()
                               .getRestaurantsBusinessCategories().getFirst().getYeloId()).stream()
               .anyMatch(restaurant -> restaurant.getBusinessCategories() != null &&
                       restaurant.getBusinessCategories().contains(
                               defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsBusinessCategories()
                                       .getFirst().getName()))
               ,"Restaurant is not linked to the selected category");
    }

}
