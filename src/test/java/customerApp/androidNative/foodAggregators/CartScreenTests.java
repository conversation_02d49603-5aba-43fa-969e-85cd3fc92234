package customerApp.androidNative.foodAggregators;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class CartScreenTests extends BaseTest {
    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping"), @Tag("food-aggregator")})
    public void validateRestaurantsCartFunctionality(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        androidNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed();

        androidNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , "down"
                , "id=" + androidNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(
                        String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().getFirst().getYeloId())));

        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(String.valueOf(
                defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getFirst().getYeloId()));

        androidNativeFoodAggregatorResturantDetailsScreen.get().isPageDisplayed();

        androidNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(androidNativeFoodAggregatorResturantDetailsScreen.get().isFirstProductAddedToCart());
        Assert.assertEquals(androidNativeFoodAggregatorResturantDetailsScreen.get()
                .getCurrentQtyOfFirstProductInCart(), "1");

        androidNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertEquals(androidNativeFoodAggregatorResturantDetailsScreen.get()
                .getCurrentQtyOfFirstProductInCart(), "2");

        androidNativeFoodAggregatorResturantDetailsScreen.get().pressFirstDecreaseQtyBtn();
        Assert.assertEquals(androidNativeFoodAggregatorResturantDetailsScreen.get()
                .getCurrentQtyOfFirstProductInCart(), "1");

        androidNativeFoodAggregatorResturantDetailsScreen.get().pressFirstDecreaseQtyBtn();
        Assert.assertFalse(androidNativeFoodAggregatorResturantDetailsScreen.get().isFirstProductAddedToCart());

        androidNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(androidNativeFoodAggregatorResturantDetailsScreen.get().isFirstProductAddedToCart());

        androidNativeFoodAggregatorResturantDetailsScreen.get().pressBackBtn();

        androidNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , "down"
                , "id=" + androidNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(
                        String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().get(1).getYeloId())));
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(String.valueOf(
                defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().get(1).getYeloId()));
        androidNativeFoodAggregatorResturantDetailsScreen.get().isPageDisplayed();

        androidNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(androidNativeFoodAggregatorResturantDetailsScreen.get().isCartTypeErrorMsgDisplayed());

        androidNativeFoodAggregatorResturantDetailsScreen.get().pressKeepCurrentCartBtn();
        Assert.assertFalse(androidNativeFoodAggregatorResturantDetailsScreen.get().isCartTypeErrorMsgDisplayed());
        Assert.assertFalse(androidNativeFoodAggregatorResturantDetailsScreen.get().isFirstProductAddedToCart());

        androidNativeFoodAggregatorResturantDetailsScreen.get().goToCartScreen();
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        Assert.assertEquals(androidNativeCartScreen.get().getCountOfCartItems(), 1);
        Assert.assertEquals(androidNativeCartScreen.get().getTotalCountOfItemsInCart(), 1);
    }
}
