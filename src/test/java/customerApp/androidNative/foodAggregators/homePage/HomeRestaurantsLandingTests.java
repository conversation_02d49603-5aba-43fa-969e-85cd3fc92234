package customerApp.androidNative.foodAggregators.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import models.Restaurant;
import models.ValidationResults;
import org.openqa.selenium.WebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Test class for Restaurant Home Landing functionality
 */

public class HomeRestaurantsLandingTests extends BaseTest {
    private static final Logger log = LoggerFactory.getLogger(HomeRestaurantsLandingTests.class);

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"), @Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_655VerifyRestaurantStatusDisplayOnRestaurantCard() {
                // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen and navigate to restaurants
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        Assert.assertTrue(androidNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        androidNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , "down"
                , "id=" + androidNativeFoodAggregatorHomeScreen.get().getCategoryFilterIdSelector(String.valueOf(defaultTestData.get()
                        .getFoodAggregatorTestSession().getRestaurantsList().getLast().getYeloId())));

        // Compare restaurant operating status and make sure closed tag is shown in the app
        String apiRestaurantStatus = defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getLast().getOperatingStatus();

        Assert.assertEquals(
                apiRestaurantStatus.toLowerCase(),
                androidNativeFoodAggregatorHomeScreen.get().verifyRestaurantStatus(apiRestaurantStatus));
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"), @Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_655VerifyRatingVisibleWithHighReviewCount() {
                defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode()
        );

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        Assert.assertTrue(androidNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        List<Restaurant> allRestaurants = defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList();
        List<Restaurant> highReviewRestaurants = allRestaurants.stream()
                .filter(r -> r.getReviewCount() >= 50)
                .collect(Collectors.toList());

        ValidationResults finalResults = androidNativeAggregatorTestsExecutionHelper.get().progressiveScrollAndVerifyAllRestaurants(
                androidDriver.get(),
                androidNativeFoodAggregatorHomeScreen.get(),
                highReviewRestaurants,
                restaurantId -> androidNativeFoodAggregatorHomeScreen.get().checkIfRatingIsDisplayed(restaurantId),
                "High Review Rating Visibility Verification"
        );
        Assert.assertTrue(
                finalResults.isResult(),
                "❌ Rating visibility verification failed for restaurants with reviewCount >= 50. Issues found:\n" +
                        String.join("\n", finalResults.getValidationResults())
        );
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"), @Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_654VerifyRatingHiddenWithLowReviewCount() {
                defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode()
        );

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        Assert.assertTrue(androidNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        List<Restaurant> allRestaurants = defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList();
        List<Restaurant> lowReviewRestaurants = allRestaurants.stream()
                .filter(r -> r.getReviewCount() < 50)
                .collect(Collectors.toList());

        ValidationResults finalResults = androidNativeAggregatorTestsExecutionHelper.get().progressiveScrollAndVerifyAllRestaurants(
                androidDriver.get(),
                androidNativeFoodAggregatorHomeScreen.get(),
                lowReviewRestaurants,
                restaurantId -> !androidNativeFoodAggregatorHomeScreen.get().checkIfRatingIsDisplayed(restaurantId),
                "Low Review Rating Hidden Verification"
        );

        Assert.assertTrue(
                finalResults.isResult(),
                "❌ Rating verification failed for restaurants with reviewCount < 50. Issues found:\n" +
                        String.join("\n", finalResults.getValidationResults())
        );
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"), @Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_648VerifyDisplayOfRestaurantNameOnCard()
    {
                // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen and navigate to restaurants
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

          // Compare restaurant names from API and UI
        String getRestaurantNameFromAPI = String.valueOf(defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().get(0).getName());
        String getRestaurantNameFromUI= androidNativeFoodAggregatorHomeScreen.get().getRestaurantName(String.valueOf(androidNativeFoodAggregatorHomeScreen.get().getAllVisibleRestaurantCards().get(0)));
        Assert.assertEquals(getRestaurantNameFromAPI,getRestaurantNameFromUI);
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"), @Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_651VerifyDisplayOfRestaurantDeliveryTimeOnCard()
    {
                // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen and navigate to restaurants
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        // Compare delivery times from API and UI
        Restaurant firstRestaurant = defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().get(0);
        String restaurantIdFromAPI = String.valueOf(firstRestaurant.getYeloId());
        String expectedDeliveryTimeFromAPI = String.valueOf(firstRestaurant.getDeliveryTime());

        // Get actual delivery time from UI
        String actualDeliveryTimeFromUI = androidNativeFoodAggregatorHomeScreen.get().getRestaurantDeliveryTime(String.valueOf(androidNativeFoodAggregatorHomeScreen.get().getAllVisibleCategoryCards().getFirst())).replace(" min", "").trim();

        Assert.assertEquals(actualDeliveryTimeFromUI, expectedDeliveryTimeFromAPI,
                "Delivery time mismatch for restaurant ID: " + restaurantIdFromAPI);
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("food-aggregator"), @Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_652VerifyDisplayOfRestaurantDeliveryFeesOnCard()
    {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode()
        );

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        Assert.assertTrue(androidNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        List<Restaurant> allRestaurants = defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList();
        ValidationResults finalResults = androidNativeAggregatorTestsExecutionHelper.get().progressiveScrollAndVerifyAllRestaurants(
                androidDriver.get(),
                androidNativeFoodAggregatorHomeScreen.get(),
                allRestaurants,
                restaurantId -> androidNativeFoodAggregatorHomeScreen.get().isDeliveryFeeDisplayed(restaurantId),
                "Delivery Fee Visibility Verification"
        );

        Assert.assertTrue(
                finalResults.isResult(),
                "❌ Delivery fee visibility verification failed. Issues found:\n" +
                        String.join("\n", finalResults.getValidationResults())
        );
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_CheckRestaurantsListReturningWithLoggedOutUser()
    {
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed("egypt");
        androidNativeLandingScreen.get().pressExploreBtn();
        // Verify home screen and navigate to restaurants
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        //Assert the Restaurant page is shown for the logged out user
        Assert.assertTrue(androidNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());
    }

    @Test
    @Tags({@Tag("food-aggregator"), @Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_539_CheckAppWillBehaveNormallyAfterSendingToBG ()
    {
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Validate that home screen is opened
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        //click on Restaurants entry point
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        //Assert Restaurants landing screen are shown
        Assert.assertTrue(androidNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed(),"Restaurants landing screen is not Displayed yet!");

        // Send the app to the Background
        androidDriver.get().runAppInBackground(Duration.ofSeconds(6));
        androidDriver.get().activateApp("com.breadfast.testing");
        //Assert again the app is Up and the restaurants landing screen is working
        Assert.assertTrue(androidNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed(),"Restaurants landing screen is not Displayed yet!");
        try {
            Thread.sleep(6000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        List<WebElement> uiRestaurantIds = androidNativeFoodAggregatorHomeScreen.get().getAllVisibleRestaurantCards();
        String firstUiRestaurantId = String.valueOf(uiRestaurantIds.get(0));
        Assert.assertEquals(androidNativeFoodAggregatorHomeScreen.get().getRestaurantName(firstUiRestaurantId),defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().get(0).getName());
    }

    @Test
    @Tags({@Tag("food-aggregator"), @Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_635_CheckTheUserCanAccessAggregationCart ()
    {
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Validate that home screen is opened
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        //click on Restaurants entry point
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        Assert.assertTrue(androidNativeFoodAggregatorHomeScreen.get().isCartDisplayed(),"Cart Icon is not Displayed in Aggregation landing screen");
    }

    @Test
    @Tags({@Tag("food-aggregator"), @Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void TC_633_CheckFilterRestaurantsBasedOnCuisineIconIsDisplayed ()
    {
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Validate that home screen is opened
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        //click on Restaurants entry point
        androidNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        Assert.assertTrue(androidNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowDisplayed(),"Filter is not Displayed in Aggregation landing screen");
    }
}
