package customerApp.androidNative.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;


import java.util.stream.Collectors;


@Test
public class MyOffersTests extends BaseTest{
    @Test(groups = {"B10-42702"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping") , @Tag("database")})
    public void PlacingOrderUsingMyOfferCoupons() {

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "Supermarket",
                "commercial",
                100,
                0,
                0,
                "active",
                true,
                false,
                false,
                "now"));

        //Start logged user session
        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

       androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                       String.valueOf(
                              defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));
        androidNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();

        //add coupons to order from myOffer entry point
        androidNativePromosScreen.get().pressViewBtn();
        androidNativePromosScreen.get().isMyOffersBottomSheetDisplayed();
        androidNativePromosScreen.get().isMyOffersFirstCouponTitleDisplayed();
        androidNativePromosScreen.get().isMyOffersFirstPromoCouponBodyDisplayed();
        androidNativePromosScreen.get().isMyOffersFirstPromoCouponExpiryDateIsDisplayed();
        androidNativePromosScreen.get().pressFirstPromoCouponCopyBtn();
        androidNativePromosScreen.get().pressPromosCloseBtn();
        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //click on goto checkout button
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed ());

        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        //Scroll until coupons field
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCheckoutScreen.get().getPromoCodeTxtFieldContentDescription());


        //add myOfferCoupon to coupons field and click apply
        androidNativeCheckoutScreen.get().enterPromoCode(androidNativePromosScreen.get().getCopiedPromoCode());
        androidNativeCheckoutScreen.get().pressPromoCodeApplyBtn();

        // click on place order
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());
    }

}
