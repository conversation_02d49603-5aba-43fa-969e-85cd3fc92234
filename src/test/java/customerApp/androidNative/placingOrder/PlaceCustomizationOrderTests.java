package customerApp.androidNative.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.stream.Collectors;

public class PlaceCustomizationOrderTests extends BaseTest {
    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationProduct(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(defaultTestData.get()
                                        .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId())));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(defaultTestData.get()
                                .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId()));

        Assert.assertTrue(androidNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        androidTestsExecutionHelper.get().scrollToEnd(androidDriver.get());

        androidNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        androidNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // click on place order
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");
    }

    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationAndNonCustomizationProductsWithTipping(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(
                        defaultTestData.get()
                                .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(defaultTestData.get()
                                .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId())));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(defaultTestData.get()
                        .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId()));

        Assert.assertTrue(androidNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        androidTestsExecutionHelper.get().scrollToEnd(androidDriver.get());

        androidNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        androidNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // click on place order
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCheckoutScreen.get().getTipAmountContentDescription());
        androidNativeCheckoutScreen.get().pressTipAmount();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();

        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");
    }

    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationProductWithFixedCartCoupon() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Create fixed cart coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "fixed_cart",
                "supermarket",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                false,
                false,
                "now"));

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(defaultTestData.get().getCustomerAppTestSession()
                                .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(defaultTestData.get()
                                .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId())));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(defaultTestData.get()
                        .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId()));

        Assert.assertTrue(androidNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        androidTestsExecutionHelper.get().scrollToEnd(androidDriver.get());

        androidNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        androidNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // click on place order
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCheckoutScreen.get().getPromoCodeTxtFieldContentDescription());

        androidNativeCheckoutScreen.get().enterPromoCode(defaultTestData.get().getTestCoupon().getCouponCode());
        androidNativeCheckoutScreen.get().pressPromoCodeApplyBtn();
        Assert.assertTrue(androidNativeCheckoutScreen.get().isCouponValid());
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");
    }
    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationAndNonCustomizationProductsWithPercentageCartCoupon() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Create Percentage Cart coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "percent",
                "supermarket",
                "commercial",
                0,
                0,
                10,
                "active",
                true,
                false,
                true,
                "now"));

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(
                        defaultTestData.get()
                                .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(defaultTestData.get()
                                .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId())));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(defaultTestData.get()
                        .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId()));

        Assert.assertTrue(androidNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        androidTestsExecutionHelper.get().scrollToEnd(androidDriver.get());

        androidNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        androidNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // click on place order
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCheckoutScreen.get().getPromoCodeTxtFieldContentDescription());

        androidNativeCheckoutScreen.get().enterPromoCode(defaultTestData.get().getTestCoupon().getCouponCode());
        androidNativeCheckoutScreen.get().pressPromoCodeApplyBtn();
        Assert.assertTrue(androidNativeCheckoutScreen.get().isCouponValid());
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");
    }

    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationProductWithBackToWalletPercentageCoupon() {
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Create Back to wallet coupon with percentage value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "supermarket",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                false,
                true,
                "now"));

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(defaultTestData.get().getCustomerAppTestSession()
                                .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(defaultTestData.get()
                                .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId())));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(defaultTestData.get()
                        .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId()));

        Assert.assertTrue(androidNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        androidTestsExecutionHelper.get().scrollToEnd(androidDriver.get());

        androidNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        androidNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // click on place order
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCheckoutScreen.get().getPromoCodeTxtFieldContentDescription());

        androidNativeCheckoutScreen.get().enterPromoCode(defaultTestData.get().getTestCoupon().getCouponCode());
        androidNativeCheckoutScreen.get().pressPromoCodeApplyBtn();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");

    }

    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationProductWithBackToWalletCoupon() {

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "supermarket",
                "commercial",
                1000,
                0,
                0,
                "active",
                true,
                false,
                false,
                "now"));

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(defaultTestData.get().getCustomerAppTestSession()
                                .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(defaultTestData.get()
                                .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId())));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(defaultTestData.get()
                        .getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMysqlId()));

        Assert.assertTrue(androidNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        androidTestsExecutionHelper.get().scrollToEnd(androidDriver.get());

        androidNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        androidNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();

        // click on place order
        Assert.assertTrue(androidNativeCheckoutScreen.get().checkoutPageIsDisplayed());
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCheckoutScreen.get().getPromoCodeTxtFieldContentDescription());

        androidNativeCheckoutScreen.get().enterPromoCode(defaultTestData.get().getTestCoupon().getCouponCode());
        androidNativeCheckoutScreen.get().pressPromoCodeApplyBtn();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");
    }

}
