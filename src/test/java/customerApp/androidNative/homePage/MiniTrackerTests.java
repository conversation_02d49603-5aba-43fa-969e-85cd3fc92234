package customerApp.androidNative.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.stream.Collectors;

public class MiniTrackerTests extends BaseTest {
    @Test(groups = {"regression"})
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void validateMiniTrackerWillDisappearIfNoOrdersAreProcessing(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "15"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false
                        ,false
                        , false
                        ,false));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        //Expand the mini tracker
        androidNativeHomeScreen.get().clickExpandMiniTracker();

        //Click on View Order details
        androidNativeHomeScreen.get().clickViewOrderDetailsBtn();

        //Assert that the order details page opens
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeOrderDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeOrderDetailsScreen.get().getCancelOrderBtnContentDescription());

        androidNativeOrderDetailsScreen.get().pressCancelOrderBtn();
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isCancelOrderBottomSheetDisplayed());
        androidNativeOrderDetailsScreen.get().pressCancelAndRefundBtn();
        androidNativeOrderDetailsScreen.get().selectIOrderedOnWrongAddressOption();
        androidNativeOrderDetailsScreen.get().pressSubmitCancelReasonBtn();

        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isOrderCanceled());
        androidNativeOrderDetailsScreen.get().pressBackBtn();

        //Assert that the expand mini tracker button is not visible
        Assert.assertFalse(androidNativeHomeScreen.get().isMiniTrackerDisplayed());
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void validateClosingExpandedMiniTracker(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "15"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false
                        ,false
                        , false
                        ,false));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        //Expand the mini tracker
        androidNativeHomeScreen.get().clickExpandMiniTracker();

        //Click on View Order details
        androidNativeHomeScreen.get().clickCloseExpandedMiniTracker();

        //Assert that the expand mini tracker button is visible
        Assert.assertTrue(androidNativeHomeScreen.get().isMiniTrackerInvisible());
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void validateOrderDetailsBtnNavigatesToOrderDetailsPage(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "15"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false
                        ,false
                        , false
                        ,false));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        //Expand the mini tracker
        androidNativeHomeScreen.get().clickExpandMiniTracker();

        //Click on View Order details
        androidNativeHomeScreen.get().clickViewOrderDetailsBtn();

        //Assert that the order details page opens
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void validateMiniTrackerCopiesOrderDetailsPage(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "15"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false
                        ,false
                        , false
                        ,false));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        //Is Home page displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isMiniTrackerDisplayed());

        //Expand the mini tracker
        androidNativeHomeScreen.get().clickExpandMiniTracker();

        //Click on View Order details
        androidNativeHomeScreen.get().clickViewOrderDetailsBtn();

        //Assert that the order details page opens
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());

        //mini tracker displayed place order
        Assert.assertEquals(androidNativeOrderDetailsScreen.get().getOrderStatus(), "Order placed successfully");

        //Mark order as packed , call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //back to home page
        androidNativeOrderDetailsScreen.get().pressBackBtn();

        //mini tracker displayed
        Assert.assertTrue(androidNativeHomeScreen.get().preparingYourOrderMiniTrackingIsDisplayed());
    }
}
