package customerApp.androidNative.homePage;
import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import models.Batch;
import models.Product;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.List;

public class HomeCarouselsTests extends BaseTest {
    //Check the carousal section is displayed for the new user if the carousal is supported in user's FP
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void CheckThatCarouselWillAppearOnHomeScreenForTheLoggedInUserIFUserFPSupported(){
    List<Product>createdProductsCarousel= dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "active",
                true,
                false
        );
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

         // Validate that home screen is opened
         Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        // Carousel will appear on Home screen
        Assert.assertEquals(androidNativeCarouselScreen.get().getCarouselTitle(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))),defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName());
    }
    
    //Check the carousal section is displayed under Spotlight for the guest user if the carousal is supported in user's FP
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void CheckThatCarouselWillAppearOnHomeScreenForTheGuestUserIFUserFPSupported(){
      List<Product>createdProductsCarousel=dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "active",
                true,
                false
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed("egypt");
        androidNativeLandingScreen.get().pressExploreBtn();
        // Validate that home screen is opened
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        // Carousel will appear on Home screen for guest users
        Assert.assertTrue(androidNativeHomeScreen.get().isCollectionsAvailable());
        Assert.assertTrue(androidNativeCarouselScreen.get().isCarouselDisplayed(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))));
        Assert.assertEquals(androidNativeCarouselScreen.get().getCarouselTitle(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))),defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName());

    }
    //Check the carousal section will disappear if user switched from FP supports the carousal to another that doesn't support the carousal
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void CheckCarouselDisappearsWhenSwitchingFromSupportedToAnotherIsNotSupported(){
        List<Product>createdProductsCarousel=dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "active",
                true,
                false
        );
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Validate that home screen is opened
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        // Carousel will appear on Home screen
        Assert.assertTrue(androidNativeCarouselScreen.get().isCarouselDisplayed(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))));

        androidNativeHomeScreen.get().pressMoreBtn();
        androidNativeMoreScreen.get().pressAccountSettingsBtn();
        androidNativeAccountSettingsScreen.get().pressManageAddressesTab();
        androidNativeSavedAddressScreen.get().pressAddAddressBtn();
    }

    //Check that if the carousel is removed from the admin dashboard, it won't appear on the app
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void CheckWhenCarouselIsRemovedWontAppearInTheApp ()
    {
        List<Product>createdProductsCarousel=dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "active",
                true,
                false
        );
        //  Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();
        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Assert home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        // Assert Carousel will appear on Home screen
        Assert.assertEquals(androidNativeCarouselScreen.get().getCarouselTitle(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))),defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName());
        // Call list API to list the recent created carousels
        dynamicProductCarouselApiClient.get().listProductCarouselIds(
                defaultTestData.get().getAdminUser(),
                1,
                5
        );
        dynamicProductCarouselApiClient.get().deleteCarouselConfig(defaultTestData.get().getAdminUser());
        androidNativeHomeScreen.get().pressMoreBtn();
        androidNativeHomeScreen.get().pressHomeBtn();
        androidDriver.get().terminateApp("com.breadfast.testing");
        androidDriver.get().activateApp("com.breadfast.testing");

        //Assert the carousel wont be shown after removing
        Assert.assertNotEquals(androidNativeCarouselScreen.get().getCarouselTitle(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))),defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName());
    }

    // Check that if the carousel is inactive in the admin dashboard , it won't appear on the app
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void CheckWhenCarouselIsInActiveItWontAppear ()
    {
        List<Product>createdProductsCarousel=dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "inactive",
                true,
                false
        );
        //  Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Assert home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        // Assert Carousel will not appear on Home screen
        Assert.assertNotEquals(androidNativeCarouselScreen.get().getCarouselTitle(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))),defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName());
        
    }
    //Removing stocks _Check that the OOS products won't appear in the carousal section
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void CheckOOSProductsWontAppear() {
        List<Product>createdProductsCarousel=dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "active",
                true,
                false
        );
        // Deduct stock for the first product
        Product firstProduct = createdProductsCarousel.get(0);

        //  Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Assert home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        // Assert Carousel will appear on Home screen
        Assert.assertTrue(androidNativeCarouselScreen.get().isCarouselDisplayed(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))));
        Assert.assertEquals(androidNativeCarouselScreen.get().getCarouselTitle(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))),defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName());

        // Fetch the batches for the product
        List<Batch> batches = inventoryApiClient.get().getProductBatches(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                firstProduct,
                "deduct"
        );
        Batch selectedBatch = batches.isEmpty() ? null : batches.get(0);  // Choose the batch

        //Now,Call the deduct stock API with the selected batch and the returned product from create carousel
        inventoryApiClient.get().deductStock(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                firstProduct,
                selectedBatch,
                inventoryApiClient.get().reasonsList().getFirst(),
                "deduct"
        );
        androidDriver.get().terminateApp("com.breadfast.testing");
        androidDriver.get().activateApp("com.breadfast.testing");

        //Assert the carousel wont be shown after removing
        Assert.assertNotEquals(androidNativeCarouselScreen.get().getCarouselTitle(Integer.parseInt(firstProduct.getName())),defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName());
    }

    //Check that the EN Title of the carousal on app follow the EN title of carousal on the admin board
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void CheckEnTitleInAppWillFollowBETitle() {
        List<Product>createdProductsCarousel=dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "active",
                true,
                false
        );
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Assert home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        // Assert the created carousel is shown with the correct title
        Assert.assertTrue(androidNativeCarouselScreen.get().isCarouselDisplayed(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))));
        Assert.assertEquals(androidNativeCarouselScreen.get().getCarouselTitle(createdProductsCarousel.indexOf(createdProductsCarousel.get(0))),defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName());
    }

    // Check that the user can add / Increase or decrease items from the carousel section in the Home screen
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void CheckUserCanAddProductFromCarousel() {
        List<Product>createdProductsCarousel=dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "active",
                true,
                false
        );
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Assert home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeCarouselScreen.get().pressViewAllBtn(createdProductsCarousel.indexOf(createdProductsCarousel.get(0)));

        androidNativeCarouselScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(createdProductsCarousel.get(0).getMysqlId()));
        androidNativeCarouselScreen.get().clickCartView();
        Assert.assertTrue(androidNativeCartScreen.get().isClearAllBtnDisplayed());
    }

    //Check that user can decrease items from product details of a carousal product
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void CheckUserCanDecreaseProductFromCarousel() {
        List<Product>createdProductsCarousel=dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "active",
                true,
                false
        );
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Assert home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeCarouselScreen.get().pressViewAllBtn(createdProductsCarousel.indexOf(createdProductsCarousel.get(0)));

        androidNativeCarouselScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(createdProductsCarousel.get(0).getMysqlId()));
        androidNativeCarouselScreen.get().pressDecreaseBtnByProductId(String.valueOf(createdProductsCarousel.get(0).getMysqlId()));
    }
    //Check that user can add carousal product to the fav list
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    @Test
    public void CheckUserCanFavProductFromCarousel() {
        List<Product>createdProductsCarousel=dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "active",
                true,
                false
        );
        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        // Assert home screen is displayed
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeCarouselScreen.get().pressViewAllBtn(createdProductsCarousel.indexOf(createdProductsCarousel.get(0)));
        androidNativeCarouselScreen.get().pressAddToFavoritesBtnByProductId(
                String.valueOf(createdProductsCarousel.get(0).getMysqlId()));
        // Assert product has been added to the fav list successfully
        Assert.assertTrue(androidNativeCarouselScreen.get().isFavHintDisplayed());

    }
    // Removing Stocks_Check that if carousal section has  less than 4 products , carousel won't appear to the user
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void CheckIfCarousalHasFourProductsItWontAppearTry() {
        List<Product>createdProductsCarousel=dynamicProductCarouselApiClient.get().createProductCarousel(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselEnglishName(),
                defaultTestData.get().getCustomerAppTestSession().getCarouselArabicName(),
                new String[]{defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()},
                defaultTestData.get()
                        .getCustomerAppTestSession()
                        .getSingleProductsWithoutDiscount()
                        .subList(0, Math.min(6, defaultTestData.get()
                                .getCustomerAppTestSession()
                                .getSingleProductsWithoutDiscount()
                                .size())),
                "active",
                true,
                false
        );
        // Deduct stock for the fetched products
        Product firstProduct = createdProductsCarousel.get(0);
        Product secondProduct = createdProductsCarousel.get(1);

        // Fetch the batches for the first product
        List<Batch> batches = inventoryApiClient.get().getProductBatches(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                firstProduct,
                "deduct"
        );
        Batch selectedBatch = batches.isEmpty() ? null : batches.get(0);  // Choose the batch
        // Call the deduct stock API with the selected batch and the returned product from create carousel
        inventoryApiClient.get().deductStock(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                firstProduct,
                selectedBatch,
                inventoryApiClient.get().reasonsList().getFirst(),
                "deduct"
        );
        // Second product
        // Fetch the batches for the second product
        List<Batch> secondBatches = inventoryApiClient.get().getProductBatches(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                secondProduct,
                "deduct"
        );
        Batch secondSelectedBatch = secondBatches.isEmpty() ? null : secondBatches.get(0);  // Choose the batch

        // Call the deduct stock API with the selected batch and the returned product from create carousel
        inventoryApiClient.get().deductStock(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId(),
                secondProduct,
                secondSelectedBatch,
                inventoryApiClient.get().reasonsList().getFirst(),
                "deduct"
        );
    }
}
