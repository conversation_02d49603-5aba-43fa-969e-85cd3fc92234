package customerApp.androidNative.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class HomeGridTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void validateHomeGridSectionIsDisplayed ()
    {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        // Verify Home Grid section is displayed
        Assert.assertTrue(androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                        , androidNativeHomeScreen.get().getScrollableContentContainer()
                        ,"down"
                        ,"id=" +  androidNativeHomeScreen.get().getSuperMarketLocator()) ,"Home Grid is displayed"
        );
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void validateSuperMarketEntryPointIsOpened ()
    {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        // Verify Home Grid section is displayed
        Assert.assertTrue(androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                ,"down"
                , "id=" + androidNativeHomeScreen.get().getSuperMarketLocator()) ,"Home Grid is displayed"
        );

        // verify SuperMarket entry point opens
        androidNativeHomeScreen.get().clickSuperMarketBtn();
        Assert.assertTrue(androidNativeHomeScreen.get().isAllCategoriesBottomSheetDisplayed());

    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void validateShopsEntryPointIsOpened ()
    {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        // Verify Home Grid section is displayed
        Assert.assertTrue(androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                ,"down"
                ,"id=" + androidNativeHomeScreen.get().getSuperMarketLocator()) ,"Home Grid is displayed"
        );

        // verify Shops entry point opens
        androidNativeHomeScreen.get().clickShopsBtn();
        Assert.assertTrue(androidNativeHomeScreen.get().isShopsLandingPageDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app-native"), @Tag("mobile-shopping")})
    public void validateRestaurantEntryPointIsOpened ()
    {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        // Verify Home Grid section is displayed
        Assert.assertTrue(androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                ,"down"
                , "id=" + androidNativeHomeScreen.get().getSuperMarketLocator()) ,"Home Grid is displayed"
        );

        // verify Restaurants entry point opens
        androidNativeHomeScreen.get().clickRestaurantBtn();
        Assert.assertTrue(androidNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());
    }

}
