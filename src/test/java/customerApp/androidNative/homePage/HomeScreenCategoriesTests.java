package customerApp.androidNative.homePage;
import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class HomeScreenCategoriesTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateThatMainCategoryItemIsClickable() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());

        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateBackButtonFunctionality() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        //Assert on clicking back from the Categories Details screen
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isBackBtnDisplayed());
        androidNativeCategoriesDetailsScreen.get().pressBackBtn();

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateThatSubCategoriesAreClickable() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getSubCategoriesScrollableContentContainer(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()))
                , "right"
                , androidNativeCategoriesDetailsScreen.get().getSubCategoryTabContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowSubcategoryWithPositiveStock().getId())));

        androidNativeCategoriesDetailsScreen.get().pressSubCategoryTab(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession()
                        .getNowSubcategoryWithPositiveStock().getId()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        //Assert that product associated with subCategory is displayed
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isProductCardDisplayed(String.valueOf(
                defaultTestData.get()
                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())));
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateThatCategoriesBottomSheetIsAccessibleFromCategoryDetailsScreen() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getSubCategoriesScrollableContentContainer(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()))
                , "right"
                , androidNativeCategoriesDetailsScreen.get().getSubCategoryTabContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowSubcategoryWithPositiveStock().getId())));

        androidNativeCategoriesDetailsScreen.get().pressSubCategoryTab(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession()
                        .getNowSubcategoryWithPositiveStock().getId()));

        androidNativeCategoriesDetailsScreen.get().openCategoriesBottomSheet();

        //Assert that bottom sheet is shown with title
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isCategoriesBottomSheetDisplayed());
    }
}
