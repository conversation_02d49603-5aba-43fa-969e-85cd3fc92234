package customerApp.androidNative.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class GuestMoodTests extends BaseTest {
    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void validateGuestUserOpensHelpSection() {
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
         androidNativeLandingScreen.get().pressExploreBtn();

        androidNativeHomeScreen.get().pressMoreBtn();

        //Assert if the user is on the More screen in Guest mood
        Assert.assertTrue(androidNativeMoreScreen.get().isPageDisplayed());
        androidNativeMoreScreen.get().pressHelpBtn();

        // Assert user in guest mood can open help screen section
        Assert.assertTrue(androidNativeMoreScreen.get().isHelpPageDisplayed());
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void validateGuestUserCanChangeLang() {
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressExploreBtn();

        androidNativeHomeScreen.get().pressMoreBtn();

        //Assert if the user is on the More screen in Guest mood
        Assert.assertTrue(androidNativeMoreScreen.get().isPageDisplayed());
        androidNativeMoreScreen.get().pressLanguageBtn();

        //Assert if the bottom sheet of lang button is displayed
        Assert.assertTrue(androidNativeMoreScreen.get().isBottomActionSheetDisplayed());
        androidNativeMoreScreen.get().pressArabicLanguageBtn();
        androidNativeMoreScreen.get().pressChangeBtn();

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeHomeScreen.get().pressMoreBtn();
        Assert.assertTrue(androidNativeMoreScreen.get().isPageDisplayed());

        Assert.assertEquals(androidNativeMoreScreen.get().getCurrentlySelectedLanguage(), "اللغة العربية");
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void validateGuestUserCanChaneCountry() {
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressExploreBtn();

        androidNativeHomeScreen.get().pressMoreBtn();

        //Assert if the user is on the More screen in Guest mood
        Assert.assertTrue(androidNativeMoreScreen.get().isPageDisplayed());
        androidNativeMoreScreen.get().pressCountryBtn();

        //Assert if the bottom sheet of countries is displayed
        Assert.assertTrue(androidNativeMoreScreen.get().isBottomActionSheetDisplayed());
        androidNativeMoreScreen.get().pressCountryOptionByCountryName("KSA");
        androidNativeMoreScreen.get().pressChangeBtn();

        //Assert if the change country modal is displayed
       Assert.assertTrue(androidNativeChangeCountryModalScreen.get().isModalDisplayed());
       androidNativeMoreScreen.get().pressChangeBtn();

       Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeHomeScreen.get().pressMoreBtn();

        Assert.assertEquals(androidNativeMoreScreen.get().getCurrentlySelectedCountryName(), "Saudi Arabia");
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void validateGuestUserCanClickOnTalkToUS() {
        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressExploreBtn();

        androidNativeHomeScreen.get().pressMoreBtn();

        //Assert if the user is on the More screen in Guest mood
        Assert.assertTrue(androidNativeMoreScreen.get().isPageDisplayed());
        androidNativeMoreScreen.get().pressTalkToUsBtn();

        // Assert the user is on talk to us screen
       Assert.assertTrue(androidNativeChatBotScreen.get().isPageDisplayed());
    }
}
