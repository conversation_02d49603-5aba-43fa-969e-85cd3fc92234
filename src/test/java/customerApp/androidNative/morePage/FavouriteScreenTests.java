package customerApp.androidNative.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class FavouriteScreenTests extends BaseTest {
    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void CheckUserCanClickFavAProduct() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeMoreScreen.get().pressMoreTabBtn();

        androidNativeMoreScreen.get().pressFavoritesBtn();

        //Assert user is on fav screen and it is empty
        Assert.assertTrue(androidNativeFavouriteScreen.get().isFavListEmpty()
                ,"User favourite screen list is not in the empty state!");

        androidNativeFavouriteScreen.get().pressExploreBtn();

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToFavoritesBtnByProductId(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isProductAddedToFavoritesSuccessMsgDisplayed()
                , "Product is not added and the success message is not displayed");

        androidNativeCategoriesDetailsScreen.get().pressViewFavoritesListBtn();

        //Assert user is on the fav screen after adding some products to the fav list
        Assert.assertTrue(androidNativeFavouriteScreen.get().isFavoriteProductsListDisplayed()
                ,"Favorite products list isn't displayed and list might be empty");

        // Assert the product card is displayed
        Assert.assertTrue(androidNativeFavouriteScreen.get().isProductCardDisplayed(
                String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowProductWithPositiveStock().getMysqlId()))
                ,"Product card is not displayed on the fav list");
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void CheckUserCanAddProductsToCartFromFavList() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeMoreScreen.get().pressMoreTabBtn();

        androidNativeMoreScreen.get().pressFavoritesBtn();

        //Assert user is on fav screen and it is empty
        Assert.assertTrue(androidNativeFavouriteScreen.get().isFavListEmpty()
                ,"User favourite screen list is not in the empty state!");

        androidNativeFavouriteScreen.get().pressExploreBtn();

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToFavoritesBtnByProductId(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        //Assert is the product added to the  fav list
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isProductAddedToFavoritesSuccessMsgDisplayed()
                , "Product is not added and the success message is not displayed");

        androidNativeCategoriesDetailsScreen.get().pressViewFavoritesListBtn();

        androidNativeFavouriteScreen.get().pressAddToCartBtnByProductId(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidNativeFavouriteScreen.get().pressCartBtn();

        //Assert that user is on the cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed()
                ,"User is not landed on the cart screen");

        //Assert that the user is on cart screen with some products added
        Assert.assertTrue(androidNativeCartScreen.get().isCheckoutBtnDisplayed()
                ,"Cart is empty and has no products!");

        //Assert user is on cart screen and the cart has some products added , screen is not on the empty sate
        Assert.assertFalse(androidNativeCartScreen.get().isCartEmpty()
                ,"Something went wrong! Cart is empty!");
    }
}
