package customerApp.androidNative.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class MoreScreenTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void checkUserCanLogoutProperly () {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeMoreScreen.get().pressMoreTabBtn();
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeMoreScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeMoreScreen.get().getLogoutBtnContentDescription());

        androidNativeMoreScreen.get().pressLogoutBtn();

        //Assert user is logged out and the guest mood is displayed
        androidNativeMoreScreen.get().pressMoreTabBtn();
        Assert.assertTrue(androidNativeMoreScreen.get().isUserLoggedOut()
                ,"The welcome text is not displayed, User is not logged out successfully");
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void checkUserCanChangeLanguageFromMoreScreen () {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeHomeScreen.get().pressMoreBtn();
        Assert.assertTrue(androidNativeMoreScreen.get().isPageDisplayed());

        androidNativeMoreScreen.get().pressLanguageBtn();

        //Assert if the bottom sheet of lang button is displayed
        Assert.assertTrue(androidNativeMoreScreen.get().isBottomActionSheetDisplayed());
        androidNativeMoreScreen.get().pressArabicLanguageBtn();
        androidNativeMoreScreen.get().pressChangeBtn();

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());
        androidNativeHomeScreen.get().pressMoreBtn();
        Assert.assertTrue(androidNativeMoreScreen.get().isPageDisplayed());

        Assert.assertEquals(androidNativeMoreScreen.get().getCurrentlySelectedLanguage(), "اللغة العربية");
    }
}
