package customerApp.androidNative.morePage;

import base.BaseTest;
import helpers.factories.dataFactories.UserDataFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class UpdatePersonalInfoTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void validateUSerCanUpdateHisPersonalInfo() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeMoreScreen.get().pressMoreTabBtn();
        androidNativeMoreScreen.get().pressAccountSettingsBtn();
        Assert.assertTrue(androidNativeAccountSettingsScreen.get().isPageDisplayed());

        androidNativeAccountSettingsScreen.get().pressPersonalInfoTab();

        //Assert user is on the personal information screen
        Assert.assertTrue(androidNativePersonalInfoScreen.get().isPageDisplayed());

        // Update user info in cached object and on UI too
        defaultTestData.get().setRandomTestUser(new UserDataFactory(configs.get())
                .generateRandomUserDetails(defaultTestData.get().getRandomTestUser()));

        androidNativePersonalInfoScreen.get().enterNameAndPressSaveBtn(
                defaultTestData.get().getRandomTestUser().getFirstName()
                , defaultTestData.get().getRandomTestUser().getLastName());

        //Assert the changes have been saved successfully
        Assert.assertTrue(androidNativePersonalInfoScreen.get().isPersonalInfoSaved());
    }
}

