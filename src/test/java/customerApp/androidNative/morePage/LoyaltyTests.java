package customerApp.androidNative.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.stream.Collectors;

public class LoyaltyTests extends BaseTest{
    @Test(groups = {"place-order", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void VerifyLoyaltyIsShownInCheckout(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeHomeScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeHomeScreen.get().getCategoryContentDescription(
                        String.valueOf(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getNowCategoryWithPositiveStock().getId())));

        androidNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDismissed());
        Assert.assertTrue(androidNativeCategoriesDetailsScreen.get().isPageDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getName()));

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCategoriesDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCategoriesDetailsScreen.get().getProductCardContentDescription(
                        String.valueOf(
                                defaultTestData.get()
                                        .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId())
                ));

        androidNativeCategoriesDetailsScreen.get().pressAddToCartBtnByProductId(
                String.valueOf(
                        defaultTestData.get()
                                .getCustomerAppTestSession().getNowProductWithPositiveStock().getMysqlId()));

        androidNativeCategoriesDetailsScreen.get().pressCartBtn();

        //cart screen
        Assert.assertTrue(androidNativeCartScreen.get().isCartScreenDisplayed());
        androidNativeCartScreen.get().pressGoToCheckoutBtn();
        androidNativeCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeCheckoutScreen.get().getPromoCodeTxtFieldContentDescription());

        // click on free delivery
        androidNativeCheckoutScreen.get().pressFreeShippingOption();
        //Click on redeem
        androidNativeCheckoutScreen.get().pressRedeemBtn();
        //Assert.assertTrue(androidNativeCheckoutScreen.get().isGotItBottomSheetDisplayed());
        androidNativeCheckoutScreen.get().pressGotItBtn();
        androidNativeCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(androidNativeOrderDetailsScreen.get().isPageDisplayed());
    }
}
