package customerApp.androidNative.morePage;

import base.BaseTest;
import helpers.factories.dataFactories.OtpFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class DeleteAccountTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void checkUserCanDeleteAccount() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeMoreScreen.get().pressMoreTabBtn();

        androidNativeMoreScreen.get().pressAccountSettingsBtn();

        androidNativeAccountSettingsScreen.get().pressDeleteAccountTab();

        //Assert user is On the delete account page
        Assert.assertTrue(androidNativeDeleteAccountScreen.get().isDeleteAccountPageDisplayed()
                ,"Delete Account page is not displayed.");

        androidNativeDeleteAccountScreen.get().pressDeleteFirstReason();

        androidNativeDeleteAccountScreen.get().pressContinueBtnToDelete();

        //Assert user is on the last step to delete his account
        Assert.assertTrue(androidNativeDeleteAccountScreen.get().isDeleteAccountFinalScreenDisplayed()
                ,"User is not on the last step to delete");

        androidNativeDeleteAccountScreen.get().pressVerifyMobileNumber();

        //Assert user is on the verify mobile number screen to delete his account
         Assert.assertTrue(androidNativeDeleteAccountVerifyNumberScreen.get().isVerifyDeletingAccountScreenDisplayed()
                 ,"User is not on the verify screen and can not delete");

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get()).enterOtpInTextField(
                androidNativeDeleteAccountVerifyNumberScreen.get().getOtpTextField(),
                defaultTestData.get(),
                "deleteAccount",
                defaultTestData.get().getRandomTestUser()));

        androidNativeDeleteAccountVerifyNumberScreen.get().pressTermsAndConditionsCheckbox();
        androidNativeDeleteAccountVerifyNumberScreen.get().pressOnDeleteAccount();

        //Assert the confirmation msg of delete account is shown
        Assert.assertTrue(androidNativeDeleteAccountScreen.get().isAccountDeletedConfirmationMsgDisplayed()
                ,"The user account is not deleted");

        androidNativeDeleteAccountScreen.get().pressCloseBtn();

        //Assert user is logged out after the account delete request is sent
        androidNativeMoreScreen.get().pressMoreTabBtn();

        Assert.assertTrue(androidNativeMoreScreen.get().isUserLoggedOut()
                ,"User is not logged out after the delete request is sent");
    }
}
