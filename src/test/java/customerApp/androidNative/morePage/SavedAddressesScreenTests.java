package customerApp.androidNative.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class SavedAddressesScreenTests extends BaseTest {

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateLoggedInUseCanViewHisAddresses(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeMoreScreen.get().pressMoreTabBtn();
        androidNativeMoreScreen.get().pressAccountSettingsBtn();
        Assert.assertTrue(androidNativeAccountSettingsScreen.get().isPageDisplayed());

        androidNativeAccountSettingsScreen.get().pressManageAddressesTab();

        // Assert the user is no the saved addresses screen
        Assert.assertTrue(androidNativeSavedAddressScreen.get().isAddressesPageDisplayed());

        // Assert the user is no the saved addresses screen
        Assert.assertTrue(androidNativeSavedAddressScreen.get().isAddressesPageDisplayed()
                ,"User is not on the saved addresses screen");
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("android"), @Tag("mobile-shopping")})
    public void validateLoggedInUseCanNotDeleteHisDefaultAddress(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeMoreScreen.get().pressMoreTabBtn();
        androidNativeMoreScreen.get().pressAccountSettingsBtn();
        Assert.assertTrue(androidNativeAccountSettingsScreen.get().isPageDisplayed());

        androidNativeAccountSettingsScreen.get().pressManageAddressesTab();

        // Assert the user is on the saved addresses screen
        Assert.assertTrue(androidNativeSavedAddressScreen.get().isAddressesPageDisplayed()
                ,"User is not on the saved addresses screen");

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidNativeSavedAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidNativeSavedAddressScreen.get().getAddressCardContentDescription(
                        Integer.valueOf(defaultTestData.get().getRandomTestUser().getAddress().getId()))
        );

        Assert.assertTrue(androidNativeSavedAddressScreen.get().isAddressCardDisplayed(Integer.valueOf(
                defaultTestData.get().getRandomTestUser().getAddress().getId()))
                , "Address with ID: "
                        + defaultTestData.get().getRandomTestUser().getAddress().getId()
                        + " is not displayed");

        Assert.assertTrue(androidNativeSavedAddressScreen.get().isAddressMarkedAsDefault(Integer.valueOf(
                defaultTestData.get().getRandomTestUser().getAddress().getId()))
                , "Address with ID: "
                        + defaultTestData.get().getRandomTestUser().getAddress().getId()
                        + " is not marked as default");

        androidNativeSavedAddressScreen.get().pressAddressCard(Integer.valueOf(
                defaultTestData.get().getRandomTestUser().getAddress().getId()));

        //Click to delete the user default address
        androidNativeAddressDetailsScreen.get().pressOnDeleteBtn(Integer.valueOf(
                defaultTestData.get().getRandomTestUser().getAddress().getId()));

        //Assert the error message of you can't delete a default address is displayed
        Assert.assertTrue(androidNativeAddressDetailsScreen.get().isDeleteDefaultAddressErrMsgDisplayed()
                ,"Delete default address error message is not displayed");
    }
}
