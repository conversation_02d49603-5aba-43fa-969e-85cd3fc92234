package customerApp.androidNative.morePage;

import base.BaseTest;
import helpers.factories.dataFactories.UserDataFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class EmailAddressScreenTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("android")})
    public void checkLoggedUseCanViewHisAddresses(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        androidNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        androidNativeLandingScreen.get().pressAuthBtn();

        androidNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                androidNativePhoneNumberScreen.get(),
                androidNativePhoneCountrySelectionDropdownScreen.get(),
                androidNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidNativeHomeScreen.get().isPageDisplayed());

        androidNativeMoreScreen.get().pressMoreTabBtn();
        androidNativeMoreScreen.get().pressAccountSettingsBtn();

        //Assert user is on the account settings screen
        Assert.assertTrue(androidNativeAccountSettingsScreen.get().isPageDisplayed());
        androidNativeAccountSettingsScreen.get().pressEMailAddressBtn();

        // Assert the user is on the mail address screen
        Assert.assertTrue(androidNativeEmailAddressScreen.get().isPageDisplayed());

        //Update user mail
        defaultTestData.get().setRandomTestUser(new UserDataFactory(configs.get())
                .generateRandomUserEmailAndPassword(defaultTestData.get().getRandomTestUser()));
        androidNativeEmailAddressScreen.get().enterEmailIntoEmailTxtField(
                defaultTestData.get().getRandomTestUser().getEmailAddress());
        androidNativeEmailAddressScreen.get().pressOnSubmitBtn();

        // Assert the mail is updated normally
        Assert.assertTrue(androidNativeEmailAddressScreen.get().isEmailUpdatedMsgDisplayed());
    }
}
