package customerApp.android.chatbot;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class NonOfTheAboveTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})

    public void validateTheResponseOnReturnProductThatDoesNotMeetExpectationsThanksNoOtherQuestion()
     {
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
         defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
         // create order
         defaultTestData.get().getRandomTestUser().setTestOrder(
                 orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                         , "20"
                         , "0"
                         , "cod"
                         , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                         ,""
                         , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                         , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                         ,false,false, false,false));

         //Mark order as packed
         controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

         //Mark order as picked up
         controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

         //Mark order as in-route
         controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

         //Mark order as delivering, call & parse list orders to set new values after order status change
         controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

         //Mark order as delivered, call & parse list orders to set new values after order status change
         controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

         //Update test data with current order status
         defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                 defaultTestData.get().getRandomTestUser()));

         //Assert that order status has been changed to completed
         //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
         Assert.assertEquals(defaultTestData.get()
                 .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
         Assert.assertFalse(defaultTestData.get()
                 .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
         //Login with the same user we created an order for
         androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
         androidLandingScreen.get().pressAuthHyperLink();

         androidTestsExecutionHelper.get().login(defaultTestData.get()
                 , testExecutionHelper.get()
                 , androidPhoneNumberScreen.get()
                 , androidCountriesListScreen.get()
                 , androidOtpVerificationScreen.get()
                 , androidSetAddressScreen.get()
                 , defaultTestData.get().getTestCountryCode());
         //Accept location permission
         androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
         androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
         androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                 , androidAddressSelectionScreen.get()
                 , androidLocationPermissionAlert.get()
                 , androidSetAddressScreen.get()
                 , androidDriver.get());
         // enter to Chat bot
         androidHomeScreen.get().pressFreshChatBtn();
         androidFreshChatScreen.get().isPageDisplayed();
         // chat with cx
         androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
         androidChatbotScreen.get().pressSendMessageBtn();

         androidChatbotScreen.get().pressNonOfTheAbove();
         androidChatbotScreen.get().pressIWouldLikeToReturnAProduct();

         //Assert that the message of the products dropdown is displayed
         Assert.assertTrue(androidChatbotScreen.get().isWhichProductYouAreReferringToMsgDisplayed());

         //Choose None of the current order products to move to none of the above flow
         androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
         androidChatbotScreen.get().pressProductSelectionCheckBox();
         androidChatbotScreen.get().pressProductNameSelector();
         androidChatbotScreen.get().presSSendBtn();
         androidChatbotScreen.get().pressCloseBtn();

         //Assert that all three options for product return is displayed
         Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfOrderedByMistakeDisplayed());
         Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfItDidnotMeetExpectations());
         Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfQualityDisplayed());

         //Click one of the reasons for the return
         androidChatbotScreen.get().clickReturnBecauseOfItDidnotMeetExpectations();

         //Assert that the message to clarify the reason to product to not meet expectation is displayed correctly
         Assert.assertTrue(androidChatbotScreen.get().isMessageToWhyProductDoesnotMeetExpectationsDisplayed());

         //Send the message
         androidChatbotScreen.get().enterTextIntoChatbotTxtField("reason 1");
         androidChatbotScreen.get().presSSendBtn();

         //Assert that pickup order message is displayed correctly
         Assert.assertTrue(androidChatbotScreen.get().isPickupMessageDisplayedAfterEnteringMessageDoesnotMeetExpectations());

         androidChatbotScreen.get().pressOk();

         //Assert on the Ending flow message
         Assert.assertTrue(androidChatbotScreen.get().isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})

    public void validateTheResponseOnReturnProductThatDoesNotMeetExpectationsSpeaksToAnAgent()
    {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // create order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // enter to chat bot
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        // chat with cx
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        androidChatbotScreen.get().pressSendMessageBtn();

        androidChatbotScreen.get().pressNonOfTheAbove();
        androidChatbotScreen.get().pressIWouldLikeToReturnAProduct();

        //Assert that the message of the products dropdown is displayed
        Assert.assertTrue(androidChatbotScreen.get().isWhichProductYouAreReferringToMsgDisplayed());

        //Choose None of the current order products to move to none of the above flow
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressProductSelectionCheckBox();
        androidChatbotScreen.get().pressProductNameSelector();
        androidChatbotScreen.get().presSSendBtn();
        androidChatbotScreen.get().pressCloseBtn();

        //Assert that all three options for product return is displayed
        Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfOrderedByMistakeDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfItDidnotMeetExpectations());
        Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfQualityDisplayed());

        //Click one of the reasons for the return
        androidChatbotScreen.get().clickReturnBecauseOfItDidnotMeetExpectations();

        //Assert that the message to clarify the reason to product to not meet expectation is displayed correctly
        Assert.assertTrue(androidChatbotScreen.get().isMessageToWhyProductDoesnotMeetExpectationsDisplayed());

        //Send the message
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("reason 1");
        androidChatbotScreen.get().presSSendBtn();

        //Assert that pickup order message is displayed correctly
        Assert.assertTrue(androidChatbotScreen.get().isPickupMessageDisplayedAfterEnteringMessageDoesnotMeetExpectations());

        androidChatbotScreen.get().pressNoIWantToSpeakToAnAgent();

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})

    public void validateTheResponseOnReturnProductIOrderedItByMistakeThanksNoOtherQuestion()
    {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // create order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // enter to chat bot
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        // chat with cx
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        androidChatbotScreen.get().pressSendMessageBtn();

        androidChatbotScreen.get().pressNonOfTheAbove();
        androidChatbotScreen.get().pressIWouldLikeToReturnAProduct();

        //Assert that the message of the products dropdown is displayed
        Assert.assertTrue(androidChatbotScreen.get().isWhichProductYouAreReferringToMsgDisplayed());

        //Choose None of the current order products to move to none of the above flow
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressProductSelectionCheckBox();
        androidChatbotScreen.get().pressProductNameSelector();
        androidChatbotScreen.get().presSSendBtn();
        androidChatbotScreen.get().pressCloseBtn();

        //Assert that all three options for product return is displayed
        Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfOrderedByMistakeDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfItDidnotMeetExpectations());
        Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfQualityDisplayed());

        //Click one of the reasons for the return
        androidChatbotScreen.get().clickReturnBecauseOfOrderedByMistakeDisplayed();

        //Assert that pickup order message is displayed correctly
        Assert.assertTrue(androidChatbotScreen.get().isPickupMessageDisplayedAfterEnteringMessageDoesnotMeetExpectations());

        androidChatbotScreen.get().pressOk();

        //Assert on the Ending flow message
        Assert.assertTrue(androidChatbotScreen.get().isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})

    public void validateTheResponseOnReturnProductIOrderedItByMistakeWithOtherQuestion()
    {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // create order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // enter to chat bot
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        // chat with cx
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        androidChatbotScreen.get().pressSendMessageBtn();

        androidChatbotScreen.get().pressNonOfTheAbove();
        androidChatbotScreen.get().pressIWouldLikeToReturnAProduct();

        //Assert that the message of the products dropdown is displayed
        Assert.assertTrue(androidChatbotScreen.get().isWhichProductYouAreReferringToMsgDisplayed());

        //Choose None of the current order products to move to none of the above flow
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressProductSelectionCheckBox();
        androidChatbotScreen.get().pressProductNameSelector();
        androidChatbotScreen.get().presSSendBtn();
        androidChatbotScreen.get().pressCloseBtn();

        //Assert that all three options for product return is displayed
        Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfOrderedByMistakeDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfItDidnotMeetExpectations());
        Assert.assertTrue(androidChatbotScreen.get().isReturnBecauseOfQualityDisplayed());

        //Click one of the reasons for the return
        androidChatbotScreen.get().clickReturnBecauseOfOrderedByMistakeDisplayed();

        //Assert that pickup order message is displayed correctly
        Assert.assertTrue(androidChatbotScreen.get().isPickupMessageDisplayedAfterEnteringMessageDoesnotMeetExpectations());

        androidChatbotScreen.get().pressNoIWantToSpeakToAnAgent();

    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateSuggestToAddOneProduct() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();
        //click on I have a suggestion
        androidChatbotScreen.get().pressIHaveASuggestion();
        //Assert on displayed message
        androidChatbotScreen.get().isWeLoveToHearASuggestionFromCustomerMsgDisplayed();
        // suggestion to add a product
        androidChatbotScreen.get().pressIHaveSuggestionToAddProductAtBreadFastBtn();
        // assert on please mention the product Msg
        androidChatbotScreen.get().isPleaseTypeTheProductYouWouldLikeToAddMsgDisplayed();
        //type product name
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("corn flex");
        androidChatbotScreen.get().pressSendMessageBtn();
        //click on no that's it
        androidChatbotScreen.get().pressNOThatIsIt();
        //assert on Thanks Msg
        androidChatbotScreen.get().isThanksMsgAfterSuggestionDisplayed();
}
        @Test
        @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
        public void validateSuggestToAddMoreThanOneProduct() {
            //Register with A valid Phone number
            androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                    , androidLandingScreen.get()
                    , androidPhoneNumberScreen.get()
                    , androidCountriesListScreen.get()
                    , androidOtpVerificationScreen.get()
                    , testExecutionHelper.get()
                    , defaultTestData.get()
                    , androidCreateAccountInfoScreen.get()
                    , androidRegisterSuccessScreen.get()
                    , androidHomeScreen.get()
                    , defaultTestData.get().getTestCountryCode());
            androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                    , androidAddressSelectionScreen.get()
                    , androidLocationPermissionAlert.get()
                    , androidSetAddressScreen.get()
                    , androidDriver.get());
            // Validate that home screen is opened
            Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
            //click on fresh chat btn
            androidHomeScreen.get().pressFreshChatBtn();
            //Start the conversation
            androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
            androidChatbotScreen.get().pressSendMessageBtn();
            // click on none of the above btn
            androidChatbotScreen.get().pressNonOfTheAbove();
            //click on I have a suggestion
            androidChatbotScreen.get().pressIHaveASuggestion();
            //Assert on displayed message
            androidChatbotScreen.get().isWeLoveToHearASuggestionFromCustomerMsgDisplayed();
            // suggestion to add a product
            androidChatbotScreen.get().pressIHaveSuggestionToAddProductAtBreadFastBtn();
            // assert on please mention the product Msg
            androidChatbotScreen.get().isPleaseTypeTheProductYouWouldLikeToAddMsgDisplayed();
            //type product name
            androidChatbotScreen.get().enterTextIntoChatbotTxtField("corn flex");
            androidChatbotScreen.get().pressSendMessageBtn();
            //click on yes please
            androidChatbotScreen.get().pressYesPlease();
            //type product name
            androidChatbotScreen.get().enterTextIntoChatbotTxtField("corn flex");
            androidChatbotScreen.get().pressSendMessageBtn();
            androidChatbotScreen.get().isThanksMsgAfterSecondSuggestionDisplayed();
}
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateGeneralSuggestionFlow() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();
        //click on I have a suggestion
        androidChatbotScreen.get().pressIHaveASuggestion();
        //Assert on displayed message
        androidChatbotScreen.get().isWeLoveToHearASuggestionFromCustomerMsgDisplayed();
        //click on I have A general Suggestion
        androidChatbotScreen.get().pressIHaveAGeneralSuggestionBtn();
        //Assert on Msg
        androidChatbotScreen.get().isGeneralSuggestionMsgDisplayed();
        //type suggestion
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("I would to suggest add more product");
        androidChatbotScreen.get().pressSendMessageBtn();
}
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateIWouldLikeToSharePositiveFeedBackFlow() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();
        //click on I have a suggestion
        androidChatbotScreen.get().pressIHaveASuggestion();
        //Assert on displayed message
        androidChatbotScreen.get().isWeLoveToHearASuggestionFromCustomerMsgDisplayed();
        //click on I would like to share positive feedback
        androidChatbotScreen.get().pressIWouldLikeToShareAPositiveFeedbackBtn();
        //assert on positive feed back Msg
        androidChatbotScreen.get().isPositiveFeedbackMsgDisplayed();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateIHaveReceivedAnExtraItem() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();
        //click on I have received an extra Item
        androidChatbotScreen.get().pressIReceivedAnExtraItem();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("corn");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().isReceivedAnExtraItemMsgDisplayed();
}

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void iWouldLikeToWorkAtBreadFast(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();
        //Click on I would like to work at BreadFast
        androidChatbotScreen.get().pressIWouldLikeToWorkAtBreadFast();
        //Validate that the career email is displayed
        Assert.assertTrue(androidChatbotScreen.get().isCareerEmailDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void iWouldLikeToSellAProduct(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();
        //Click on I would like to work at BreadFast
        androidChatbotScreen.get().pressIWouldLikeToSellAProduct();
        //Validate that the career email is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPartnersEmailDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void someThingElseNoneOfTheAbove(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();

        //Click on Something else in the none of the above flow
        androidChatbotScreen.get().pressSomethingElseBtnNoneOfTheAbove();
        //Validate that the 1st cx message appeared
        Assert.assertTrue(androidChatbotScreen.get().isCxInSomethingElseDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void iWouldLikeToReturnAProductDidNotMeetExpectationsOk(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // create order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //Check that the home page is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();

        //Click on I would like to return a product
        androidChatbotScreen.get().pressReturnAnItemBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichProductYouAreReferringToMsgDisplayed());

        //Choose None of the current order products to move to none of the above flow
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressProductSelectionCheckBox();
        androidChatbotScreen.get().presSSendBtn();

        //choose it did not meet my expectations
        androidChatbotScreen.get().clickReturnBecauseOfItDidnotMeetExpectations();
        //assert on displayed message
        Assert.assertTrue(androidChatbotScreen.get().isMessageToWhyProductDoesnotMeetExpectationsDisplayed());
        //enter the reason why it did not meet my expectations
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("because of the quality");
        androidChatbotScreen.get().pressSendMessageBtn();
        //assert on the displayed message
        Assert.assertTrue(androidChatbotScreen.get().isTheItDidNotMeetMyExpectationsPickUpMessageDisplayedInNoneOfTheAboveFlow());
        //click ok
        androidChatbotScreen.get().pressOkThankYouBtn();
        //assert that the message thank you will be displayed
        Assert.assertTrue(androidChatbotScreen.get().isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void iWouldLikeToReturnAProductDidNotMeetExpectationsAssignedToCx(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // create order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //Check that the home page is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();

        //Click on I would like to return a product
        androidChatbotScreen.get().pressReturnAnItemBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichProductYouAreReferringToMsgDisplayed());

        //Choose None of the current order products to move to none of the above flow
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressProductSelectionCheckBox();
        androidChatbotScreen.get().presSSendBtn();

        //choose it did not meet my expectations
        androidChatbotScreen.get().clickReturnBecauseOfItDidnotMeetExpectations();
        //assert on displayed message
        Assert.assertTrue(androidChatbotScreen.get().isMessageToWhyProductDoesnotMeetExpectationsDisplayed());
        //enter the reason why it did not meet my expectations
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("because of the quality");
        androidChatbotScreen.get().pressSendMessageBtn();
        //assert on the displayed message
        Assert.assertTrue(androidChatbotScreen.get().isTheItDidNotMeetMyExpectationsPickUpMessageDisplayedInNoneOfTheAboveFlow());
        //click on I want to speak to an agent
        androidChatbotScreen.get().pressNoIWantToSpeakToAnAgent();
        //assert that it got assigned to a cx agent
        Assert.assertTrue(androidChatbotScreen.get().isOkCustomerSupportAgentWillBeWithYouShortlyDisplayed());

    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void iWouldLikeToReturnAProductOrderedByMistakeOk() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // create order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Check that the home page is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();

        //Click on I would like to return a product
        androidChatbotScreen.get().pressReturnAnItemBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichProductYouAreReferringToMsgDisplayed());

        //Choose None of the current order products to move to none of the above flow
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressProductSelectionCheckBox();
        androidChatbotScreen.get().presSSendBtn();

        //choose it order by mistake
        androidChatbotScreen.get().clickReturnBecauseOfOrderedByMistakeDisplayed();
        //assert on the displayed message
        Assert.assertTrue(androidChatbotScreen.get().isTheItDidNotMeetMyExpectationsPickUpMessageDisplayedInNoneOfTheAboveFlow());
        //click ok
        androidChatbotScreen.get().pressOkThankYouBtn();
        //assert that the message thank you will be displayed
        Assert.assertTrue(androidChatbotScreen.get().isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void iWouldLikeToReturnAProductOrderedByMistakeAssignedToCx() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // create order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //Check that the home page is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();

        //Click on I would like to return a product
        androidChatbotScreen.get().pressReturnAnItemBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichProductYouAreReferringToMsgDisplayed());

        //Choose None of the current order products to move to none of the above flow
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressProductSelectionCheckBox();
        androidChatbotScreen.get().presSSendBtn();

        //choose order by mistake
        androidChatbotScreen.get().clickReturnBecauseOfOrderedByMistakeDisplayed();
        //assert on the displayed message
        Assert.assertTrue(androidChatbotScreen.get().isTheItDidNotMeetMyExpectationsPickUpMessageDisplayedInNoneOfTheAboveFlow());
        //click no I want it assigned to a cx Agent
        androidChatbotScreen.get().pressNoIWantToSpeakToAnAgent();
        //assert that it got assigned to CX Agent
        Assert.assertTrue(androidChatbotScreen.get().isOkCustomerSupportAgentWillBeWithYouShortlyDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void iWouldLikeToReturnAProductFromADifferentOrder() {

            //Register using API , Create address for user & create test order
            defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
            defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        for(int i =0; i<3; i++) {
            // create order
            defaultTestData.get().getRandomTestUser().setTestOrder(
                    orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                            , "20"
                            , "0"
                            , "cod"
                            , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                            ,""
                            , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                            , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                            , false, false, false,false));

            //Mark order as packed
            controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

            //Mark order as picked up
            controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

            //Mark order as in-route
            controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

            //Mark order as delivering, call & parse list orders to set new values after order status change
            controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

            //Mark order as delivered, call & parse list orders to set new values after order status change
            controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

            //Update test data with current order status
            defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                    defaultTestData.get().getRandomTestUser()));

            //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
            Assert.assertEquals(defaultTestData.get()
                    .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
            Assert.assertFalse(defaultTestData.get()
                    .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        }
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Check that the home page is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //click on fresh chat btn
        androidHomeScreen.get().pressFreshChatBtn();
        //Start the conversation
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        // click on none of the above btn
        androidChatbotScreen.get().pressNonOfTheAbove();
        //Click on I would like to return a product
        androidChatbotScreen.get().pressReturnAnItemBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichProductYouAreReferringToMsgDisplayed());
        //press on none of these products from the list of products
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressNoneOfTheseProducts();
        androidChatbotScreen.get().presSSendBtn();
        //Assert that the which order are you referring to msg is displayed
        Assert.assertTrue(androidChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        //Choose one of the 3 orders shown
        androidChatbotScreen.get().pressOnARandomOrder();
        //Assert that which product are you referring to msg is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPleaseReturnTheProductYouAreReferringToMsgDisplayed());
        //Choose from the items on the selected order
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressProductSelectionCheckBox();
        androidChatbotScreen.get().presSSendBtn();
        //choose order by mistake
        androidChatbotScreen.get().clickReturnBecauseOfOrderedByMistakeDisplayed();
        //assert on the displayed message
        Assert.assertTrue(androidChatbotScreen.get().isTheItDidNotMeetMyExpectationsPickUpMessageDisplayedInNoneOfTheAboveFlow());
        //click no I want it assigned to a cx Agent
        androidChatbotScreen.get().pressNoIWantToSpeakToAnAgent();
        //assert that it got assigned to CX Agent
        Assert.assertTrue(androidChatbotScreen.get().isOkCustomerSupportAgentWillBeWithYouShortlyDisplayed());
    }
}
