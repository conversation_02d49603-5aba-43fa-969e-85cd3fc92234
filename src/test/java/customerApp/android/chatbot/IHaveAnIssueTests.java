package customerApp.android.chatbot;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class IHaveAnIssueTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateChatbotAssignOrderToCxAfterOrderIsLate(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtnWhilePlacedOrder();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        androidChatbotScreen.get().pressIHaveAnIssue();
        androidChatbotScreen.get().pressOrderIsLate();
        Assert.assertTrue(androidChatbotScreen.get().isAssignedToDaMsgIsDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateChatbotAssignOrderToCxAfterUserNeedToEditOrderOkOption(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());
        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtnWhilePlacedOrder();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        androidChatbotScreen.get().pressIHaveAnIssue();
        androidChatbotScreen.get().pressEditMyOrder();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField(androidPhoneNumberScreen.get().getPhoneNumberFieldValue());
        androidChatbotScreen.get().pressOk();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateChatbotAssignOrderToCxAfterUserNeedToEditOrderSpeakToAgentOption(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtnWhilePlacedOrder();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        androidChatbotScreen.get().pressIHaveAnIssue();
        androidChatbotScreen.get().pressEditMyOrder();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField(androidPhoneNumberScreen.get().getPhoneNumberFieldValue());
        androidChatbotScreen.get().pressNoIWantToSpeakToAnAgent();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateChatbotAssignOrderToCxAfterUserNeedToChangePaymentMethodOption(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());
        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtnWhilePlacedOrder();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAnIssue();
        androidChatbotScreen.get().pressEditMyOrder();
        androidChatbotScreen.get().pressPaymentMethod();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateChatbotAssignUserToCxToCancelOrderWhileOrderIsPacked(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());
        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Mark order as packed , call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert packaged key in statuses in order object has a timestamp value , indicating order has been packaged successfully
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("packaged").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtnWhilePlacedOrder();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        androidChatbotScreen.get().pressIHaveAnIssue();
        androidChatbotScreen.get().pressEditMyOrder();
        androidChatbotScreen.get().pressNonOfTheAbove();
        androidChatbotScreen.get().pressYesCancelMyOrder();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateChatbotAssignUserToCxToKeepOrderWhileOrderIsPacked(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());
        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Mark order as packed , call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert packaged key in statuses in order object has a timestamp value , indicating order has been packaged successfully
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("packaged").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtnWhilePlacedOrder();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        androidChatbotScreen.get().pressIHaveAnIssue();
        androidChatbotScreen.get().pressEditMyOrder();
        androidChatbotScreen.get().pressNonOfTheAbove();
        androidChatbotScreen.get().pressNoKeepMyOrder();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateChatbotDisplayEditOptionsWhileOrderIsntPacked(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());
        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtnWhilePlacedOrder();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        androidChatbotScreen.get().pressIHaveAnIssue();
        androidChatbotScreen.get().pressEditMyOrder();
        androidChatbotScreen.get().pressNonOfTheAbove();
        Assert.assertTrue(androidChatbotScreen.get().isCancelMsgIsDisplayed());
        androidChatbotScreen.get().pressOk();
        Assert.assertTrue(androidChatbotScreen.get().isAddAProductBtnDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isRemoveAProductBtnDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isChangeAddressBtnDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isNonOfAboveBtnDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isThankYouForChattingWithUsMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateChatbotAssignToCxToEditOrderWhileOrderIsntPacked(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());
        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtnWhilePlacedOrder();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        androidChatbotScreen.get().pressIHaveAnIssue();
        androidChatbotScreen.get().pressEditMyOrder();
        androidChatbotScreen.get().pressNonOfTheAbove();
        androidChatbotScreen.get().pressNoIWantToSpeakToAnAgent();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateChatbotShowCancelOrderStepsWhileOrderIsntPacked(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());
        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtnWhilePlacedOrder();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        androidChatbotScreen.get().pressIHaveAnIssue();
        androidChatbotScreen.get().pressCancelMyOrder();
        Assert.assertTrue(androidChatbotScreen.get().isCancelMsgIsDisplayed());
    }
    //I have an issue with order #(Order number )-> Order is late
    // -> Order within the promised time (& Display the promised time slot) -> Ok (Ends flow)
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void haveIssueWithOrderThenOrderIsLateThenClickOkWillEndChat(){
        // Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "15"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        androidChatbotScreen.get().pressIHaveAnIssue();
       androidChatbotScreen.get().pressOrderIsLate();
       Assert.assertTrue(androidChatbotScreen.get().isYourCurrentOrderStatusDisplayed());
       androidChatbotScreen.get().pressOk();
        androidChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
       Assert.assertTrue(androidChatbotScreen.get().isThankYouForChattingWithUsMsgDisplayed());
    }
    //I have an issue with order #(Order number )-> Order is late ->
    // Order within the promised time (& Display the promised time slot)
    // -> Wants to speak with an agent -> Assign to CX-conversation group
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void haveIssueWithOrderThenOrderIsLateThenClickIWantToSpeakWithAgentWillRedirectUserToCX() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        androidChatbotScreen.get().pressIHaveAnIssue();
        androidChatbotScreen.get().pressOrderIsLate();
        Assert.assertTrue(androidChatbotScreen.get().isYourCurrentOrderStatusDisplayed());
        androidChatbotScreen.get().pressNoIWantToSpeakToAnAgent();
        androidChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(androidChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
        Assert.assertTrue(androidChatbotScreen.get().getChatBotRedirectedUserToTheCxAgentText()
                .contains("agent will get assigned to you"));
    }
        @Test
        @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
        public void cancelOrderWithPackedStatues() {
            //place order
            defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
            defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
            defaultTestData.get().getRandomTestUser().setTestOrder(
                    orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                            , "20"
                            , "25"
                            , "cod"
                            , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                            ,""
                            , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                            , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                            ,false,false, false,false));
            //Mark order as Packed
            controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
            defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                    defaultTestData.get().getRandomTestUser()));
            //login
            androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
            androidLandingScreen.get().pressAuthHyperLink();
            androidTestsExecutionHelper.get().login(defaultTestData.get()
                    , testExecutionHelper.get()
                    , androidPhoneNumberScreen.get()
                    , androidCountriesListScreen.get()
                    , androidOtpVerificationScreen.get()
                    , androidSetAddressScreen.get()
                    , defaultTestData.get().getTestCountryCode());
            //Click on mini tracker down arrow
            androidHomeScreen.get().pressDownArrowBtn();
            // Validate that home screen is opened
            Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
            //navigate to chatbot screen
            androidHomeScreen.get().pressFreshChatBtn();
            androidChatbotScreen.get().isPageDisplayed();
            androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
            androidChatbotScreen.get().pressSendMessageBtn();
            //select I have an issue with processing order option
            androidChatbotScreen.get().pressIHaveAnIssueWithProcessingOrder();
            //press I would like to cancel my order
            androidChatbotScreen.get().pressCancelMyOrder();
            //Assert that cancellation reasons is Displayed
            androidChatbotScreen.get().isWrongPaymentMethodBtnDisplayed();
            androidChatbotScreen.get().isIOrderOnAWrongAddressBtnDisplayed();
            androidChatbotScreen.get().isDoNotNeedOrderAnYMoreBtnDisplayed();
            androidChatbotScreen.get().isNonOfTheAboveBtnDisplayed();
            //click on any reason
            androidChatbotScreen.get().PressWrongPaymentMethod();
            //assert that your order will be cancelled Msg Displayed
            androidChatbotScreen.get().isYourOrderWillBeCancelledMsgDisplayed();
        }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void cancelOrderAndSelectNoneOfTheAboveReasonWithPackedStatues() {
        //place order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Mark order as Packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //login
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Click on mini tracker down arrow
        androidHomeScreen.get().pressDownArrowBtn();
        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue with processing order option
        androidChatbotScreen.get().pressIHaveAnIssueWithProcessingOrder();
        //press I would like to cancel my order
        androidChatbotScreen.get().pressCancelMyOrder();
        //Assert that cancellation reasons is Displayed
        androidChatbotScreen.get().isWrongPaymentMethodBtnDisplayed();
        androidChatbotScreen.get().isIOrderOnAWrongAddressBtnDisplayed();
        androidChatbotScreen.get().isDoNotNeedOrderAnYMoreBtnDisplayed();
        androidChatbotScreen.get().isNonOfTheAboveBtnDisplayed();
        // click on none of the above button
        androidChatbotScreen.get().pressNonOfTheAbove();
        //type the cancellation reason
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("there is a wrong items is added to the order");
        androidChatbotScreen.get().pressSendMessageBtn();
        //assert that your order will be cancelled Msg Displayed
        androidChatbotScreen.get().isYourOrderWillBeCancelledMsgDisplayed();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void haveAnIssueWithOrderAndChangePaymentMethod() {
        //place order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //login
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue with processing order option
        androidChatbotScreen.get().pressIHaveAnIssueWithProcessingOrder();
        // press change payment method
        androidChatbotScreen.get().PressChangePaymentMethodBtn();
        //assert on the displayed message
        androidChatbotScreen.get().isAssignCustomerToAnAgentMsgDisplayed();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void haveAnIssueWithOrderAndSelectNonOfTheAbove() {
        //place order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //login
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue with processing order option
        androidChatbotScreen.get().pressIHaveAnIssueWithProcessingOrder();
        // press something else
        androidChatbotScreen.get(). PressOrderSomeThingElseBtn();
        //assert on the displayed message
        androidChatbotScreen.get().isAssignCustomerToAnAgentMsgDisplayed();
    }
}
