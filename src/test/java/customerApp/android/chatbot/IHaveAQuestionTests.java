package customerApp.android.chatbot;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class IHaveAQuestionTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutWhatIsBreadfastPoints() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressWhatIsBreadfastPointsBtn();
        Assert.assertTrue(androidChatbotScreen.get().isProgramFunctionalityMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutHowToRedeemPointsWithoutAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressHowToRedeemPointsBtn();
        Assert.assertTrue(androidChatbotScreen.get().isRedeemStepsMsgDisplayed());
        androidChatbotScreen.get().pressThankYouThatHelpedBtn();
        Assert.assertTrue(androidChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isThankYouForChattingWithUsMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutPointsExpirationWithoutAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressWhenWillMyPointsExpireBtn();
        Assert.assertTrue(androidChatbotScreen.get().isPointsExpirationMsgDisplayed());
        androidChatbotScreen.get().pressThankYouThatHelpedBtn();
        Assert.assertTrue(androidChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isThankYouForChattingWithUsMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutHowToRedeemPointsWithAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressHowToRedeemPointsBtn();
        Assert.assertTrue(androidChatbotScreen.get().isRedeemStepsMsgDisplayed());
        androidChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("What is the expiration duration for the points");
        Assert.assertTrue(androidChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutWhenWillMyPointsExpireWithAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressWhenWillMyPointsExpireBtn();
        Assert.assertTrue(androidChatbotScreen.get().isPointsExpirationMsgDisplayed());
        androidChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("How to redeem the points?");
        Assert.assertTrue(androidChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutPendingPointsWithoutAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressIHavePendingPointsBtn();
        Assert.assertTrue(androidChatbotScreen.get().isPendingPointsMsgDisplayed());
        androidChatbotScreen.get().pressThankYouThatHelpedBtn();
        Assert.assertTrue(androidChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isThankYouForChattingWithUsMsgDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutPendingPointsWithAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressIHavePendingPointsBtn();
        Assert.assertTrue(androidChatbotScreen.get().isPendingPointsMsgDisplayed());
        androidChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("How to redeem the points?");
        Assert.assertTrue(androidChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutRewardsProgramWithoutAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        //Enter a text in the text box
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();

        //Steps of the reward program flow , Thank you that helps (End flow)
        androidChatbotScreen.get().pressRewardsProgramBtn();
        androidChatbotScreen.get().pressWhatIsBreadfastPointsBtn();
        androidChatbotScreen.get().pressThankYouThatHelpedBtn();

        //Assert that the thanks messages are displayed
        Assert.assertTrue(androidChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutCoffeeStoresWithoutAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        //Enter a text in the text box
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();

        //Steps of the reward program flow , Thank you that helps (End flow)
        androidChatbotScreen.get().pressCoffeeStoresBtn();

        //Assert that the links for coffee store and menu are displayed
        Assert.assertTrue(androidChatbotScreen.get().isCoffeeStoreMenuLinksDisplayed());

        androidChatbotScreen.get().pressThankYouThatHelpedBtn();

        //Assert that the thanks messages are displayed
        Assert.assertTrue(androidChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutCoffeeStoresAndHaveAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        //Enter a text in the text box
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();

        //Steps of the reward program flow , Thank you that helps (End flow)
        androidChatbotScreen.get().pressCoffeeStoresBtn();

        //Assert that the links for coffee store and menu are displayed
        Assert.assertTrue(androidChatbotScreen.get().isCoffeeStoreMenuLinksDisplayed());

        androidChatbotScreen.get().pressIHaveAnotherQuestionBtn();

        //Assert that "Type your message is displayed "
        Assert.assertTrue(androidChatbotScreen.get().isTypeAMessageMsgDisplayed());

        //Enter the question you have and send it
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();

        //Assert that the thanks messages are displayed
        Assert.assertTrue(androidChatbotScreen.get().isReviwingIhaveAnotherQuestionMessageDisplayed());

    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutMyCurrentBalanceWithoutAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();

        //Enter a text in the text box
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();

        //Click I have a question to start the flow
        androidChatbotScreen.get().pressIHaveAQuestionBtn();

        //Steps of the reward program flow , Thank you that helps (End flow)
        androidChatbotScreen.get().pressMyBalanceValueBtn();

        //Assert that the links for coffee store and menu are displayed
        Assert.assertTrue(androidChatbotScreen.get().isCheckYourBalanceStepsMsgDisplayed());

        androidChatbotScreen.get().pressThankYouThatHelpedBtn();

        //Assert that the thanks messages are displayed
        Assert.assertTrue(androidChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());

    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutMyCurrentBalanceHavingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();

        //Enter a text in the text box
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();

        //Click I have a question to start the flow
        androidChatbotScreen.get().pressIHaveAQuestionBtn();

        //Steps of the reward program flow , Thank you that helps (End flow)
        androidChatbotScreen.get().pressMyBalanceValueBtn();

        //Assert that the links for coffee store and menu are displayed
        Assert.assertTrue(androidChatbotScreen.get().isCheckYourBalanceStepsMsgDisplayed());

        androidChatbotScreen.get().pressIHaveAnotherQuestionBtn();

        //Assert that the thanks messages are displayed
        Assert.assertTrue(androidChatbotScreen.get().isTypeAMessageMsgDisplayed());

        //Send the Other Question
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();

        //Assert that the thanks messages are displayed
        Assert.assertTrue(androidChatbotScreen.get().isReviwingIhaveAnotherQuestionMessageDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenSelectingMyQuestionIsNotHere() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressMyQuestionIsNotHereBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("What is Breadfast points?");
        Assert.assertTrue(androidChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenSelectingNoneOfTheAboveAndAskingWhatDoesCashBackMean() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        // home page displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressNonOfTheAbove();
        Assert.assertTrue(androidChatbotScreen.get().isFAQsMsgDisplayed());
        androidChatbotScreen.get().pressWhatDoesCashbackMeanBtn();
        Assert.assertTrue(androidChatbotScreen.get().isCashbackFunctionalityMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseWhenAskingAboutCashBackMeaning() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressNoneOfTheAboveFromQuestionsBtn();
        //Assert that FAQ message appeared
        Assert.assertTrue(androidChatbotScreen.get().isFAQsMsgDisplayed(),"FQA message doesn't appear");
        androidChatbotScreen.get().pressWhatDoesCashbackMeanBtn();
        //Assert that Cashback message appeared
        Assert.assertTrue(androidChatbotScreen.get().isCashbackFunctionalityMsgDisplayed(),"Cashback message doesn't appear");
        androidChatbotScreen.get().pressIHaveAnotherQuestionMsg();
        Assert.assertTrue(androidChatbotScreen.get().isAskAQuestionMsgDisplayed());
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("What is loyalty program?");
        androidChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(androidChatbotScreen.get().isReviewQuestionMsgMsgDisplayed(),"Review Question message doesn't appear");
}
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseOfWhereDoYouDeliverOtherQuestion() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressNoneOfTheAboveFromQuestionsBtn();
        androidChatbotScreen.get().pressWhatDoYouDeliverBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhatDoYouDeliverMsgDisplayed(),"where do you deliver answer doesn't appear?");
        androidChatbotScreen.get().pressIHaveAnotherQuestionMsg();
        Assert.assertTrue(androidChatbotScreen.get().isAskAQuestionMsgDisplayed());
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Where do you deliver");
        androidChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(androidChatbotScreen.get().isReviewQuestionMsgMsgDisplayed(),"Review Question message doesn't appear");
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseOfWhereDoYouDeliver() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressNoneOfTheAboveFromQuestionsBtn();
        androidChatbotScreen.get().pressWhatDoYouDeliverBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhatDoYouDeliverMsgDisplayed(),"where do you deliver answer doesn't appear?");
        androidChatbotScreen.get().pressThankYouThatHelpedBtn();
        Assert.assertTrue(androidChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseOfHowToTipDeliveryAgent() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressNoneOfTheAboveFromQuestionsBtn();
        androidChatbotScreen.get().pressHowCanITipTheDeliveryAgentBtn();
        Assert.assertEquals(androidChatbotScreen.get().isHowCanITipTheDeliveryAgentMsgDisplayed(),"You can tip your delivery associate either during the order rating process or while checking out.\n" +
                "\n" +
                "To rate your order, follow these steps: Navigate to \"More,\" then \"Activity History,\" select \"Orders,\" choose the specific order, tap on the rating stars, and a rating screen will appear. Provide your order rating, enter the tip amount for the delivery associate, and submit your rating.\n" +
                "\n" +
                "Alternatively, during checkout, you'll find the option to tip the delivery associate using the \"Say thank you with a tip\" field on the checkout screen. Proceed with placing your order, and the tipping amount will be added to your order total.");
        androidChatbotScreen.get().pressThanksForHelpFromQuestionsBtn();
        Assert.assertTrue(androidChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isThankYouForChattingWithUsMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseOfHowToTipDeliveryAgentAnotherQuestion() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressNoneOfTheAboveFromQuestionsBtn();
        androidChatbotScreen.get().pressHowCanITipTheDeliveryAgentBtn();
        androidChatbotScreen.get().pressIHaveAnotherQuestionMsg();
        Assert.assertTrue(androidChatbotScreen.get().isAskAQuestionMsgDisplayed());
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Where do you deliver");
        androidChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(androidChatbotScreen.get().isReviewQuestionMsgMsgDisplayed(),"Review Question message doesn't appear");

    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseOfApplyPromoCode() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressNoneOfTheAboveFromQuestionsBtn();
        androidChatbotScreen.get().pressCanIApplyMoreThanOneCouponCodeBtn();
        Assert.assertTrue(androidChatbotScreen.get().isCanIApplyMoreThanOneCouponCodeMsgDisplayed());
        androidChatbotScreen.get().pressThanksForHelpFromQuestionsBtn();
        Assert.assertTrue(androidChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());
        Assert.assertTrue(androidChatbotScreen.get().isThankYouForChattingWithUsMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseOfApplyPromoCodeAnotherQuestion() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressNoneOfTheAboveFromQuestionsBtn();
        androidChatbotScreen.get().pressCanIApplyMoreThanOneCouponCodeBtn();
        Assert.assertTrue(androidChatbotScreen.get().isCanIApplyMoreThanOneCouponCodeMsgDisplayed());
        androidChatbotScreen.get().pressIHaveAnotherQuestionMsg();
        Assert.assertTrue(androidChatbotScreen.get().isAskAQuestionMsgDisplayed());
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("How Can i get the promo codes?");
        androidChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(androidChatbotScreen.get().isReviewQuestionMsgMsgDisplayed(), "Review Question message doesn't appear");
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTheResponseOfMyQuestionIsNotHere() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAQuestionBtn();
        androidChatbotScreen.get().pressNoneOfTheAboveFromQuestionsBtn();
        androidChatbotScreen.get().pressMyQuestionIsNotHereBtn();
        Assert.assertTrue(androidChatbotScreen.get().isAskAQuestionMsgDisplayed());
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("How Can i get the promo codes?");
        androidChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(androidChatbotScreen.get().isReviewQuestionMsgMsgDisplayed(), "Review Question message doesn't appear");

    }

}
