package customerApp.android.chatbot;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.time.Duration;
import java.util.Collections;
import java.util.stream.Collectors;

@Test
public class IHaveAProblemComplaintTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void OrderPaymentBalanceValueNoneOfTheAbove() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // create order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // enter to chatbot
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        // chat with cx
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        androidChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose BalanceChargeIssue
        androidChatbotScreen.get().pressBalanceChargeIssueBtn();
        // choose something else
        androidChatbotScreen.get().pressSomethingElse();
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void OrderPaymentBalanceValueBackToMainOptions(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // crete order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // enter to chatbot
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        // chat with cx
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        androidChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose BalanceChargeIssue
        androidChatbotScreen.get().pressBalanceChargeIssueBtn();
        // back
        androidChatbotScreen.get().pressBackBtn();
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    // I have a problem/ Complaint -> Service bills & Donations payments
    // ->Something else-> Redirect customer to an agent in CX -Conversation Group
    public void serviceBillSomethingElseWillRedirectCustomerToAgentInCX() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
//Handle Unsupported location screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
//Assert that Home screen displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
//navigate to chatbot screen
        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        //Enter a text in the text box
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        //select i have problem/complaint option
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        androidChatbotScreen.get().pressBillsDonationsPaymentBtn();
        androidChatbotScreen.get().pressSomethingElse();
        androidChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(androidChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
        Assert.assertTrue(androidChatbotScreen.get().getChatBotRedirectedUserToTheCxAgentText()
                .contains("agent will get assigned to you"));
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    // I have a problem/ Complaint -> Service bills & Donations payments ->Back-> Redirect customer to "Have Problem with " -options
    public void serviceBillThenBackWillRedirectCustomerToHaveProblemMenu() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        androidHomeScreen.get().pressFreshChatBtn();
        androidChatbotScreen.get().isPageDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        androidChatbotScreen.get().pressBillsDonationsPaymentBtn();
        androidChatbotScreen.get().pressBackBtn();
        androidChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(androidChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem/ Complaint -> The Delivery Associate  ->In-appropriate behavior  ->Enter Free text - > Redirect customer to an agent in CX -Conversation Group
    public void theDeliveryAssociateThenInappropriateBehaviorWillRedirectCustomerToAgentInCX() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        androidHomeScreen.get().pressFreshChatBtn();
        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        androidChatbotScreen.get().pressTheDeliveryAssociateBtn();
        androidChatbotScreen.get().pressInappropriateBehaviorBtn();
        // Assert that chatbot replied to customer and asked him for more details
        androidChatbotScreen.get().isPleaseGiveUsMoreDetailsMsgDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("the DA shouted at me");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(androidChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
        Assert.assertTrue(androidChatbotScreen.get().getChatBotRedirectedUserToTheCxAgentText()
                .contains("agent will get assigned to you"));
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem/ Complaint -> The Delivery Associate
    // ->He reported collecting a wrong amount ->Enters amount DA collected -- > Ok -> Assign to CX-bot back-office
    public void theDeliveryAssociateThenReportedCollectingWrongAmountThenPressOkWillEndConversation() {
        // Register Using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
//Login Using Registered User
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
//Assert that homevscreen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        androidHomeScreen.get().pressFreshChatBtn();
        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        androidChatbotScreen.get().pressTheDeliveryAssociateBtn();
        androidChatbotScreen.get().pressHeReportedCollectingAWrongAmountBtn();
        androidChatbotScreen.get().isHowMuchDidTheAgentCollectMsgMsgDisplayed();
        //enter the value of collected amount
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("200");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().isWeWillReviewTheCaseAndAdjustTheCollectionShortlyMsgMsgDisplayed();
        androidChatbotScreen.get().pressOk();
        androidChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(androidChatbotScreen.get().isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed());
        Assert.assertEquals(androidChatbotScreen.get().getThankYouWeWillAddressThisShortlyGoodByeText(),"Thank you, we'll address this shortly. Goodbye for now.");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    // I have a problem/ Complaint -> The Delivery Associate
    //->He reported collecting a wrong amount ->Enters amount DA collected -- > Asks to be assigned to an agent ->Assign to an agent  in CX - conversation group
    public void theDeliveryAssociateThenReportedCollectingWrongAmountWillRedirectCustomerToAgentInCX() {
        // Register Using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
//Login Using Registered User
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
//Assert that homevscreen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        androidHomeScreen.get().pressFreshChatBtn();
        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        androidChatbotScreen.get().pressTheDeliveryAssociateBtn();
        androidChatbotScreen.get().pressHeReportedCollectingAWrongAmountBtn();
        androidChatbotScreen.get().isHowMuchDidTheAgentCollectMsgMsgDisplayed();
        //enter the value of collected amount
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("200");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().isWeWillReviewTheCaseAndAdjustTheCollectionShortlyMsgMsgDisplayed();
        androidChatbotScreen.get().pressNoIWantToSpeakToAnAgent();
        androidChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(androidChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
        Assert.assertTrue(androidChatbotScreen.get().getChatBotRedirectedUserToTheCxAgentText()
                .contains("agent will get assigned to you"));
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //  I have a problem/ Complaint -> The Delivery Associate ->None of the above-> Enters the scenario that happened -- > Assign to CX- bot backoffice agent
    public void theDeliveryAssociateThenNoneOfTheAboveThenEnterScenarioWillEndConversation() {
        // Register Using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
//Login Using Registered User
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);
//Assert that homevscreen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        androidHomeScreen.get().pressFreshChatBtn();
        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        androidChatbotScreen.get().pressTheDeliveryAssociateBtn();
        androidChatbotScreen.get().pressNonOfTheAbove();
        androidChatbotScreen.get().isPleaseShareWhatWentWrongMsgDisplayed();
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("The DA didn't want to deliver the order ");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(androidChatbotScreen.get().isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed());
        Assert.assertEquals(androidChatbotScreen.get().getThankYouWeWillAddressThisShortlyGoodByeText(),"Thank you, we'll address this shortly. Goodbye for now.");
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})

    //  I have a problem/Complaint => My Delivered Order -> Choose wrong order/product->Assert on displayed message->
    //  Choose "Order is wrong " ->Assert the displayed message ->Enter the wrong order/product -> Assign chat to CX conversation group

    public void theWholeDeliveredOrderIsWrongFlow() {
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

       // Create Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

       // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

       // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressIncorrectItemsOrOrderBtn();
        Assert.assertTrue(androidChatbotScreen.get().isHowWrongItemsWereWrongMsgDisplayed());

        // press the whole order is wrong button
        androidChatbotScreen.get().pressWholeOrderBtn();

        // Assert if 'Thank you for the clarification, please enter the order number on the bags you received and an agent will be with you shortly' is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPleaseEnterTheOrderNumberOnTheBagsYouReceivedMsgDisplayed());

        // Enter the Order number
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("123456");
        androidChatbotScreen.get().pressSendMessageBtn();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isThankYouForClarificationAnAgentWillBeWithYouShortlyMsgDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem / Complaint => My Delivered Order -> Choose wrong order /product ->Assert on displayed message ->
    // Choose "One or more items is wrong  " - >Assert the displayed message - > Enter the product- >
    //  Assert on the displayed message  - >  chat should be assign to CX conversation group

    public void thereIsAWrongProductInTheDeliveredOrderFlow() {

        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

       // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressIncorrectItemsOrOrderBtn();
        Assert.assertTrue(androidChatbotScreen.get().isHowWrongItemsWereWrongMsgDisplayed());
        // press one or more item is wrong
        androidChatbotScreen.get().pressOneOrMoreItemsBtn();

        // Assert if 'Thank you for the clarification, please enter the order number on the bags you received and an agent will be with you shortly' is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPleaseEnterProductYouReceivedInsteadWhatShouldBeReceivedMsgDisplayed());

        // Enter the wrong product instead of the expected to receive
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Orange Juice instead of " +
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getName());
        androidChatbotScreen.get().pressSendMessageBtn();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isThankYouForClarificationAnAgentWillBeWithYouShortlyMsgDisplayed());
    }
    //----------------------------------IHaveAProblem/Complaint>> ApplyingACouponCode----------------------//
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void ApplingValidPromoCode(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // create order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(
                defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // enter to chatbot
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        // chat with cx
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        androidChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose pressApplyingACouponCode
        androidChatbotScreen.get().pressApplyingACouponCodeBtn();
        // add vaid promocode
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("test98");
        androidChatbotScreen.get().pressSendMessageBtn();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void ApplingInValidPromoCode(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // create order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(
                defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // enter to chatbot
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        // chat with cx
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        androidChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose pressApplyingACouponCode
        androidChatbotScreen.get().pressApplyingACouponCodeBtn();
        // add invaid promocode
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("abcd");
        androidChatbotScreen.get().pressSendMessageBtn();
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})

    //  I have a problem/ Complaint --> Making an order --> Redirect customer to an agent in CX -Conversation Group - >

    public void makingAnOrderFlow () {

        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start th flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMakingAnOrderBtn();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isThankYouAgentWillGetAssignedToYouYouShortlyMsgDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //  I have a problem/ Complaint --> Order Payment/ Balance Value --> Order value was deducted several times --> Redirect customer to an agent in CX -Conversation Group

    public void orderValueIsDeductedSeveralTimesFlow () {

        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressBalanceChargeIssueBtn();
        Assert.assertTrue(androidChatbotScreen.get().isTheIssueIsMsgDisplayed());
        androidChatbotScreen.get().pressIGotChargedMoreThanOnceBtn();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isThankYouAgentWillGetAssignedToYouYouShortlyMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //  I have a problem/ Complaint --> Order Payment/ Balance Value --> My Balance Value is inaccurate --> Redirect customer to an agent in CX -Conversation Group

    public void myBalanceValueIsInaccurateFlow () {

        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressBalanceChargeIssueBtn();
        Assert.assertTrue(androidChatbotScreen.get().isTheIssueIsMsgDisplayed());
        androidChatbotScreen.get().pressInaccurateBalanceValueBtn();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isThankYouAgentWillGetAssignedToYouYouShortlyMsgDisplayed());
    }
   //------------------- I have a problem/ Complaint --> Service bills & Donations payments -->I cannot pay my bill-----------------//
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void Icantpaymybill () {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // enter to chatbot
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        // chat with cx
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        androidChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose Service bills & Donations payments
        androidChatbotScreen.get().pressBillsDonationsPaymentBtn();
        //  choose cannot pay my bill
        androidChatbotScreen.get().pressICantPayMyBillBtn();
        Assert.assertTrue(androidChatbotScreen.get().isServiceBillsDonationsPaymentsMsgDisplayed());
    }
    //------------------- I have a problem/ Complaint --> Service bills & Donations payments -->Service fee got deducted but it didn't work-----------------//
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void servicefeegotdeductedbutitdidntwork () {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // enter to chatbot
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        // chat with cx
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        androidChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose Service bills & Donations payments
        androidChatbotScreen.get().pressBillsDonationsPaymentBtn();
        //  choose Service fee got deducted but it didn't work
        androidChatbotScreen.get().pressBillPaidButTheServiceDoesntWorkBtn();
        Assert.assertTrue(androidChatbotScreen.get().isServiceBillsDonationsPaymentsMsgDisplayed());
    }
    //------------------- I have a problem/ Complaint --> Service bills & Donations payments -->Service fee got deducted more than once-----------------//
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void servicefeegotdeductedmorethanone () {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());
        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // enter to chatbot
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        // chat with cx
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        androidChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose Service bills & Donations payments
        androidChatbotScreen.get().pressBillsDonationsPaymentBtn();
        //  choose Service fee got deducted more than once
        androidChatbotScreen.get().pressIGotChargedMoreThanOnceBtn();
        Assert.assertTrue(androidChatbotScreen.get().isServiceBillsDonationsPaymentsMsgDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //  I have a problem /Complaint - > My Delivered Order - > Missing Products - > Choose None of the above - > Assert that last order is displayed - >Choose the order- >
    //  Choose one of chosen order product- >Choose to refund/adjust balance - > payment method isn't cc -> Assign to CX conversation group
    public void missingProductAndPaymentIsNotCCFlow () {
        // Register and create order using API

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressMissingItemBtn();
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressNonOfThisProductsCheckBox();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        // Select the created order from API
        androidChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Select Missing product
        androidChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        androidChatbotScreen.get().presSSendBtn();
        // Press Refund option
        androidChatbotScreen.get().pressRefundMoneyBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());

    }
    @Test
    @Tags({ @Tag("customer-app"), @Tag("mobile-shopping") })
    //I have a problem /Complaint - > My Delivered Order - > Missing Products - > Choose None of the above - > Assert that last order is displayed - >Choose the order
    // - > Choose one of chosen order product- >Choose to refund/adjust balance - > payment method is cc -> Refund to CC- > Assert on the displayed message -> Chat should be assigned to CX- Conversation group
    public void missingProductAndPaymentMethodIsCCThenRefundToCCFlow () throws InterruptedException {
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,true,false, false,false));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        Thread.sleep(Duration.ofSeconds(5));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressMissingItemBtn();
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressNonOfThisProductsCheckBox();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());

        // Select the created order from API
        androidChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();

        // Select Missing product
        androidChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        androidChatbotScreen.get().presSSendBtn();

        // Press Refund option
        androidChatbotScreen.get().pressRefundMoneyBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsgDisplayed());

        // Select Refund to Credit cart
        androidChatbotScreen.get().pressCreditCardBtn();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());
    }
    @Test
    @Tags({ @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem /Complaint - > My Delivered Order - > Missing Products - > Choose None of the above - > Assert that last order is displayed - >Choose the order
    // - >  Choose one of chosen order  product- >Choose to refund/adjust balance - > payment method is cc ->  Refund to wallet - > Assert on the displayed message -> Chat should be assigned to CX- Conversation group
    public void missingProductAndPaymentMethodIsCCThenRefundToWalletFlow () throws InterruptedException {

        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,true,false, false,false));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        Thread.sleep(Duration.ofSeconds(5));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressMissingItemBtn();
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressNonOfThisProductsCheckBox();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());

        // Select the created order from API
        androidChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();

        // Select Missing product
        androidChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        androidChatbotScreen.get().presSSendBtn();

        // Press Refund Option
        androidChatbotScreen.get().pressRefundMoneyBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsgDisplayed());

        // Select Refund to Wallet
        androidChatbotScreen.get().pressWalletBtn();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
//  I have a problem/ Complaint --> Order Payment/ Balance Value --> I paid more or less than what the DA reported--> Enter the amount -->
//  Assert on the displayed message--> press Ok -->Redirect customer to an agent in CX -Conversation Group
    public void iPaidMoreOrLessThanWhatTheDeliveryAssociateReportedThenPressOkFlow () {
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressBalanceChargeIssueBtn();
        Assert.assertTrue(androidChatbotScreen.get().isTheIssueIsMsgDisplayed());
        androidChatbotScreen.get().pressIPaidMoreOrLessThanWhatTheDAReportedBtn();
        Assert.assertTrue(androidChatbotScreen.get().isHowMuchDidTheAgentCollectMsgMsgDisplayed());
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("200");
        androidChatbotScreen.get().pressSendMessageBtn();

        // Assert that we will review the case is displayed
        Assert.assertTrue(androidChatbotScreen.get().isWeWillReviewTheCaseAndAdjustTheCollectionShortlyMsgMsgDisplayed());

        // Press Ok option
        androidChatbotScreen.get().pressOk();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //  I have a problem/ Complaint --> Order Payment/ Balance Value --> I paid more or less than what the DA reported--> Enter the amount -->
    //  Assert on the displayed message--> press No I Want To Speak To An Agent -->Redirect customer to an agent in CX -Conversation Group
    public void iPaidMoreOrLessThanWhatTheDeliveryAssociateReportedThenPressSpekToAgentFlow () {

        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressBalanceChargeIssueBtn();
        Assert.assertTrue(androidChatbotScreen.get().isTheIssueIsMsgDisplayed());
        androidChatbotScreen.get().pressIPaidMoreOrLessThanWhatTheDAReportedBtn();
        Assert.assertTrue(androidChatbotScreen.get().isHowMuchDidTheAgentCollectMsgMsgDisplayed());
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("200");
        androidChatbotScreen.get().pressSendMessageBtn();

        // Assert that we will review the case is displayed
        Assert.assertTrue(androidChatbotScreen.get().isWeWillReviewTheCaseAndAdjustTheCollectionShortlyMsgMsgDisplayed());

        // Press I want to speak to agent option
        androidChatbotScreen.get().pressNoIWantToSpeakToAnAgent();

        // Assert that there is no new messages appear and user is assigned to agent
        Assert.assertFalse(androidChatbotScreen.get().isNewMessageButtonDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    // I have a problem / Complaint - > My Delivered Order - > Assert that options related to issue are displayed correctly ->Choose "poor quality or damaged items " - >
    // Assert the products displayed -> Choose one of the products = > insert the production Date  - > Chat should be assigned to CX conversation Group
    public void myDeliveredOrderPoorQualityThenSelectTheProductFlow(){

        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        // Assert options for my delivered issue are displayed
        Assert.assertTrue(androidChatbotScreen.get().areOptionsForMyDeliveredOrderIssueDisplayed());
        androidChatbotScreen.get().pressPoorQualityOrDamagedItemsBtn();
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert the order product is displayed
        Assert.assertTrue(androidChatbotScreen.get().isOrderProductDisplayed());
        // Select product
        androidChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isCanYouShareImgOrProductionDateMsgDisplayed());
        // Enter production date
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("20-6-2022");
        androidChatbotScreen.get().pressSendMessageBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isCustomerSupportWillBeWithYouShortlyMsgDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
   // I have a problem / Complaint - > My Delivered Order - > Assert that options related to issue are displayed correctly - > Choose "poor quality or damaged items " - > Assert the products displayed ->
   // None of these products = >  Assert the last order is displayed -> Choose Order -> Choose one of the products - > Insert Production Date - >Assign chat to CX_conversation Group
    public void myDeliveredOrderPoorQualitySelectNoneOfTheseThenSelectProductFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        // Assert options for my delivered issue are displayed
        Assert.assertTrue(androidChatbotScreen.get().areOptionsForMyDeliveredOrderIssueDisplayed());
        androidChatbotScreen.get().pressPoorQualityOrDamagedItemsBtn();
        // open order products dropdown
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        Assert.assertTrue(androidChatbotScreen.get().isOrderProductDisplayed());
        androidChatbotScreen.get().pressNonOfThisProductsCheckBox();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        // Select the created order from API
        androidChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert order product is displayed
        Assert.assertTrue(androidChatbotScreen.get().isOrderProductDisplayed());
        // Select product
        androidChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isCanYouShareImgOrProductionDateMsgDisplayed());
        // Enter production date
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("20-6-2022");
        androidChatbotScreen.get().pressSendMessageBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isCustomerSupportWillBeWithYouShortlyMsgDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem / Complaint - > My Delivered Order - > Assert that options related to issue are displayed correctly - > Choose "poor quality or damaged items " - > Assert the products displayed
    // -> None of these products= > Assert the last order is displayed -> Choose Order
    //- > Assert that order products are displayed correctly -> Choose None of the above  - > Assign chat to CX_conversation Group
    public void myDeliveredOrderPoorQualitySelectNoneOfTheseFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        // Assert options for my delivered issue are displayed
        Assert.assertTrue(androidChatbotScreen.get().areOptionsForMyDeliveredOrderIssueDisplayed());
        androidChatbotScreen.get().pressPoorQualityOrDamagedItemsBtn();
        // open order products dropdown
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        Assert.assertTrue(androidChatbotScreen.get().isOrderProductDisplayed());
        androidChatbotScreen.get().pressNonOfThisProductsCheckBox();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        // Select the created order from API
        androidChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert order product is displayed
        Assert.assertTrue(androidChatbotScreen.get().isOrderProductDisplayed());
        // Select None of these products option
        androidChatbotScreen.get().pressNonOfThisProductsCheckBox();
        androidChatbotScreen.get().presSSendBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isCustomerSupportWillBeWithYouShortlyMsgDisplayed());

    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem / Complaint- > My Delivered Order - > Missing Products - > Assert that products related to last order should be displayed - > Choose product - >
    // Assert on displayed message - > Choose to Deliver- >Chat should be assigned to CX- Conversation Group
    public void missingProductSelectTheProductThenSelectToDeliverItFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressMissingItemBtn();
        // open order products dropdown
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert the order product is displayed
        Assert.assertTrue(androidChatbotScreen.get().isOrderProductDisplayed());
        // Select missing product
        androidChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isDoYouWantToDeliverMissingItemOrRefundMsgDisplayed());
        // select to deliver missing item
        androidChatbotScreen.get().pressDeliverMissingItemsBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem/Complaint - > My Delivered Order - > Missing Products - > Assert that products related to last order should be displayed - > Choose product
    // - > Assert on displayed message - > Choose to Refund / Adjust Balance - > Order isn't CC- > Assert on the displayed message - >Chat should be assigned to CX conversation Group
    public void missingProductSelectTheProductThenSelectRefundTheOrderIsNotCCFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressMissingItemBtn();
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert the order product is displayed
        Assert.assertTrue(androidChatbotScreen.get().isOrderProductDisplayed());
        // Select product
        androidChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        androidChatbotScreen.get().presSSendBtn();
        // Press Refund option
        Assert.assertTrue(androidChatbotScreen.get().isDoYouWantToDeliverMissingItemOrRefundMsgDisplayed());
        androidChatbotScreen.get().pressRefundMoneyBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem /Complaint - > My Delivered Order - > Missing Products - > Choose None of the above
    // - > Assert the last order is displayed - > Choose order ->Choose None of these products - > Conversation should be assigned to CX conversation Group
    public void missingProductThenSelectNoneOfTheseFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressMissingItemBtn();
        // open order products dropdown
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressNonOfThisProductsCheckBox();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        // Select the created order from API
        androidChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        // open order products dropdown
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressNonOfThisProductsCheckBox();
        androidChatbotScreen.get().presSSendBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isThankYouAgentWillGetAssignedToYouYouShortlyMsgDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    // I have a problem /Complaint - > My Delivered Order - > Missing Products - > Choose None of the above - > Assert the last order is displayed - >
    // Choose the order - >  Choose product- >Choose to Deliver the missed item - > Assert on the displayed message- >  Assert that chat is assigned to the CX conversation Group
    public void missingProductSelectNoneOfTheseThenSelectProductAndSelectToDeliverItFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // Create Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressMissingItemBtn();
        // open order products dropdown
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        androidChatbotScreen.get().pressNonOfThisProductsCheckBox();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        // Select the created order from API
        androidChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        // open order products dropdown
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Select product
        androidChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isDoYouWantToDeliverMissingItemOrRefundMsgDisplayed());
        // select to deliver missing item
        androidChatbotScreen.get().pressDeliverMissingItemsBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());

    }

    @Test
    @Tags({ @Tag("customer-app"), @Tag("mobile-shopping")})
//I have a problem / Complaint - > My Delivered Order - > Missing Products - >Assert that products related to last order should be displayed - >
// Choose product - > Assert on displayed message - > Choose to Refund / Adjust Balance - > Order is CC - > User asks to refund on Wallet - > Chat assigned to CX conversation Group
    public void missingProductSelectTheProductThenSelectRefundToWalletFlow() throws InterruptedException{
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,true,false, false,false));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        Thread.sleep(Duration.ofSeconds(5));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressMissingItemBtn();
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert the order product is displayed
        Assert.assertTrue(androidChatbotScreen.get().isOrderProductDisplayed());
        // Select product
        androidChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        androidChatbotScreen.get().presSSendBtn();
        Assert.assertTrue(androidChatbotScreen.get().isDoYouWantToDeliverMissingItemOrRefundMsgDisplayed());
        // Press Refund Option
        androidChatbotScreen.get().pressRefundMoneyBtn();
        // Select Refund to Wallet
        Assert.assertTrue(androidChatbotScreen.get().isWouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsgDisplayed());
        androidChatbotScreen.get().pressWalletBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isCustomerSupportAgentWillGetAssignedToYouNowMsgDisplayed());

    }

    @Test
    @Tags({ @Tag("customer-app"), @Tag("mobile-shopping")})
//I have a problem /Complaint- > My Delivered Order - > Missing Products - > Assert that products related to last order should be displayed - >
// Choose product - > Assert on displayed message - > Choose to Refund / Adjust Balance - > Order is CC - > User asks to refund on CC- > Chat will be assigned to CX conversation Group
    public void missingProductSelectTheProductThenSelectRefundToCCFlow()throws InterruptedException{
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,true,false, false,false));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        Thread.sleep(Duration.ofSeconds(5));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        // Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // select the created location
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        //Assert that home screen is displayed
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        androidHomeScreen.get().pressFreshChatBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(androidChatbotScreen.get().isPageDisplayed());

        // Start the Flow
        androidChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        androidChatbotScreen.get().pressSendMessageBtn();
        androidChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        androidChatbotScreen.get().pressMyDeliveredOrderBtn();
        androidChatbotScreen.get().pressMissingItemBtn();
        androidChatbotScreen.get().pressSelectAllThatApplyDropdown();

        // Assert the order product is displayed
        Assert.assertTrue(androidChatbotScreen.get().isOrderProductDisplayed());
        // Select product
        androidChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        androidChatbotScreen.get().presSSendBtn();
        // Press Refund option
        Assert.assertTrue(androidChatbotScreen.get().isDoYouWantToDeliverMissingItemOrRefundMsgDisplayed());
        androidChatbotScreen.get().pressRefundMoneyBtn();
        Assert.assertTrue(androidChatbotScreen.get().isWouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsgDisplayed());
        // Select Refund to Credit cart
        androidChatbotScreen.get().pressCreditCardBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(androidChatbotScreen.get().isCustomerSupportAgentWillGetAssignedToYouNowMsgDisplayed());

    }
}

