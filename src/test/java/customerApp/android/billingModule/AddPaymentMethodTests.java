package customerApp.android.billingModule;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class AddPaymentMethodTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateAddingCardFromPayScreenIsWorkingCorrectly() {
        //register
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Check HomePage is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //Open Pay screen and check the screen is opened
        androidHomeScreen.get().pressPayTabBtn();

        Assert.assertTrue(androidPayScreen.get().isPageDisplayed(), "Pay Page isn't displayed");

        //Click on Saved Card Icon
        androidPayScreen.get().pressSavedCardIcon();

        //check Saved Card Displayed
        Assert.assertTrue(androidSavedCardScreen.get().isSavedCardPageDisplayed()
                , "Saved card page isn't displayed");

        // click on plus button
        androidSavedCardScreen.get().pressPlusBtn();

        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());

        //click on Add button
        androidAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(androidSavedCardScreen.get().isSavedCardPageDisplayed()
                , "Saved card page isn't displayed");

        //check that card is saved successfully
        Assert.assertTrue(androidSavedCardScreen.get().isCardRowDisplayed(defaultTestData.get().getTestCreditCard()
                        .substring(defaultTestData.get().getTestCreditCard().length() - 4))
                , "Saved card List isn't displayed");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateAddingCardFromMoreScreenIsWorkingCorrectly(){
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Check HomePage is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //click on more tab
        androidHomeScreen.get().pressMoreTabBtn();

        // click on account sitting icon
        androidMoreScreen.get().pressAccountSettingsBtn();

        // click on saved card button
        androidAccountSettingsPage.get().pressSavedCardBtn();

        // click on plus button
        androidSavedCardScreen.get().pressPlusBtn();

        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());

        //click on Add button
        androidAddCardInfoScreen.get().pressAddCardBtn();

        //check that card saved successfully
        Assert.assertTrue(androidSavedCardScreen.get().isSavedCardPageDisplayed()
                , "Saved card List isn't displayed");

        //check that card is saved successfully
        Assert.assertTrue(androidSavedCardScreen.get().isCardRowDisplayed(defaultTestData.get().getTestCreditCard()
                        .substring(defaultTestData.get().getTestCreditCard().length() - 4))
                , "Saved card List isn't displayed");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateEditingCardLabelFromPayScreenIsWorkingCorrectly() {
        //register
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Check HomePage is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //Open Pay screen and check the screen is opened
        androidHomeScreen.get().pressPayTabBtn();

        Assert.assertTrue(androidPayScreen.get().isPageDisplayed(), "Pay Page isn't displayed");

        //Click on Saved Card Icon
        androidPayScreen.get().pressSavedCardIcon();

        //check Saved Card Displayed
        Assert.assertTrue(androidSavedCardScreen.get().isSavedCardPageDisplayed(), "Saved card page isn't displayed");

        // click on plus button
        androidSavedCardScreen.get().pressPlusBtn();

        //Fill card info
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());

        //click on Add button
        androidAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(androidSavedCardScreen.get().isSavedCardPageDisplayed(), "Saved card page isn't displayed");

        //check that card is saved successfully
        Assert.assertTrue(androidSavedCardScreen.get().isCardRowDisplayed(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4)), "Saved card List isn't displayed");

        //Click the Action button for Saved Card
        androidSavedCardScreen.get().getSavedCardActionsBtnUiElement((defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4))).click();

        //Assert that Edit label for saved card is displayed
        Assert.assertTrue(androidSavedCardScreen.get().isEditLabelCardBtnDisplayed());

        //Edit Saved Card Label
        androidSavedCardScreen.get().pressEditLabelCardButton();
        androidSavedCardScreen.get().enterCardLabelForSavedCard(defaultTestData.get().getRandomTestUser().getFullName());
        androidSavedCardScreen.get().clickSavedCardSubmitBtn(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4));

        //Assert on Edit label of Saved Card
        Assert.assertNotEquals(androidSavedCardScreen.get().getSavedCardCurrentLabelTxt(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4)),defaultTestData.get().getRandomTestUser().getFullName());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateEditingCardLabelFromMoreScreenIsWorkingCorrectly() {
        //Register
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Check HomePage is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //click on more tab
        androidHomeScreen.get().pressMoreTabBtn();

        // click on account sitting icon
        androidMoreScreen.get().pressAccountSettingsBtn();

        // click on saved card button
        androidAccountSettingsPage.get().pressSavedCardBtn();

        // click on plus button
        androidSavedCardScreen.get().pressPlusBtn();

        //Fill card info
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());

        //click on Add button
        androidAddCardInfoScreen.get().pressAddCardBtn();

        //check that card saved successfully
        Assert.assertTrue(androidSavedCardScreen.get().isSavedCardPageDisplayed(), "Saved card List isn't displayed");

        //check that card is saved successfully
        Assert.assertTrue(androidSavedCardScreen.get().isCardRowDisplayed(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4)), "Saved card List isn't displayed");

        //Click on the Saved Card Action Button
        androidSavedCardScreen.get().getSavedCardActionsBtnUiElement((defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4))).click();

        //Assert that Edit label for saved card is displayed
        Assert.assertTrue(androidSavedCardScreen.get().isEditLabelCardBtnDisplayed());

        //Edit the Saved Card Label
        androidSavedCardScreen.get().pressEditLabelCardButton();
        androidSavedCardScreen.get().enterCardLabelForSavedCard(defaultTestData.get().getRandomTestUser().getFullName());
        androidSavedCardScreen.get().clickSavedCardSubmitBtn(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4));

        //Asserts after Edit Saved card label
        Assert.assertNotEquals(androidSavedCardScreen.get().getSavedCardCurrentLabelTxt(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4)),defaultTestData.get().getRandomTestUser().getFullName());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateDeletingSavedCardFromPayScreenIsWorkingCorrectly() {
        //register
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Check HomePage is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //Open Pay screen and check the screen is opened
        androidHomeScreen.get().pressPayTabBtn();

        Assert.assertTrue(androidPayScreen.get().isPageDisplayed(), "Pay Page isn't displayed");

        //Click on Saved Card Icon
        androidPayScreen.get().pressSavedCardIcon();

        //check Saved Card Displayed
        Assert.assertTrue(androidSavedCardScreen.get().isSavedCardPageDisplayed(), "Saved card page isn't displayed");

        // click on plus button
        androidSavedCardScreen.get().pressPlusBtn();

        //Fill card Info
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());

        //click on Add button
        androidAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(androidSavedCardScreen.get().isSavedCardPageDisplayed(), "Saved card page isn't displayed");

        //check that card is saved successfully
        Assert.assertTrue(androidSavedCardScreen.get().isCardRowDisplayed(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4)), "Saved card List isn't displayed");

        androidSavedCardScreen.get().getSavedCardActionsBtnUiElement((defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4))).click();

        //Delete the saved Card
        androidSavedCardScreen.get().pressRemoveCardButton();

        //Assert that the Confirm Delete Card button is displayed
        Assert.assertTrue(androidSavedCardScreen.get().isConfirmDeleteCardBtnDisplayed(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4)));

        androidSavedCardScreen.get().confirmDeleteSavedCard(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4));

        //Assert that card list is empty after deletion and the press Add card button is displayed
        Assert.assertTrue(androidSavedCardScreen.get().isPressAddCardButtonDisplayed(),"List not empty");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateDeletingSavedCardFromMoreScreenIsWorkingCorrectly() {
        //Register
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Check HomePage is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //click on more tab
        androidHomeScreen.get().pressMoreTabBtn();

        // click on account sitting icon
        androidMoreScreen.get().pressAccountSettingsBtn();

        // click on saved card button
        androidAccountSettingsPage.get().pressSavedCardBtn();

        // click on plus button
        androidSavedCardScreen.get().pressPlusBtn();

        //Fill Card Info
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());

        //click on Add button
        androidAddCardInfoScreen.get().pressAddCardBtn();

        //check that card saved successfully
        Assert.assertTrue(androidSavedCardScreen.get().isSavedCardPageDisplayed(), "Saved card List isn't displayed");

        //check that card is saved successfully
        Assert.assertTrue(androidSavedCardScreen.get().isCardRowDisplayed(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4)), "Saved card List isn't displayed");

        androidSavedCardScreen.get().getSavedCardActionsBtnUiElement((defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4))).click();

        //Delete the saved Card
        androidSavedCardScreen.get().pressRemoveCardButton();

        //Assert that confirm card deletion button is displayed
        Assert.assertTrue(androidSavedCardScreen.get().isConfirmDeleteCardBtnDisplayed(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4)));

        androidSavedCardScreen.get().confirmDeleteSavedCard(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4));

        //Assert that card list is empty after deletion and press Add card button is displayed
        Assert.assertTrue(androidSavedCardScreen.get().isPressAddCardButtonDisplayed(),"List is not empty");
    }
}
