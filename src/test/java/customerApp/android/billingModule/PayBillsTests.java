package customerApp.android.billingModule;

import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;
import base.BaseTest;

@Test
public class PayBillsTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    // Update user balance where Balance > total and use the balance to pay a bill
    public void validatePayingBillWithBalanceGreaterThanTotal() {
         // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        androidHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        androidPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        androidBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        androidProviderScreen.get().openServiceList();
        androidProviderScreen.get().isServicesListDropDownDisplayed();
        androidProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        androidProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice summary screen
        Assert.assertTrue(androidInvoiceSummaryScreen.get().IsTotalDisplayed());
        Assert.assertTrue (androidInvoiceSummaryScreen.get().IsTaxAndFeesRowDisplayed());

        // Grand total should equals to Zero because the user is using all his balance
        Assert.assertEquals(androidInvoiceSummaryScreen.get().getGrandTotalValue(), 0.0, 0.001);

        //Assert the Used balance section is shown
       Assert.assertTrue (androidInvoiceSummaryScreen.get().checkBalanceSection());

        //Scroll until you find the pay button
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidInvoiceSummaryScreen.get().getScrollableContentContainer()
                , "down"
                , androidInvoiceSummaryScreen.get().getPayBtnContentDescription());

        // wait until the Payment methods section is displayed
        Assert.assertTrue(androidInvoiceSummaryScreen.get().isCurrentBalanceSectionDisplayed()
                ,"User current balance section is displayed");

        //press pay bill
        androidInvoiceSummaryScreen.get().pressPayBillBtn();

        // Assertion on success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed()
                , "Payment completed successfully!");

        Assert.assertTrue( androidPaymentSuccessScreen.get().IsDoneBtnDisplayed()
                , "Payment completed successfully!");
    }

    // Update user balance where Balance > total and use the Card only to pay a bill
     @Test
     @Tags({@Tag("android"), @Tag("customer-app")})
    public void validatePayingBillWithBalanceHigherThanBillAndBalanceToggleIsOff() {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        androidHomeScreen.get().pressPayTabBtn();
        //navigate to BPay screen
        androidHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        androidPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        androidBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        androidProviderScreen.get().openServiceList();
        androidProviderScreen.get().isServicesListDropDownDisplayed();
        androidProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        androidProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice summary screen
        Assert.assertTrue(androidInvoiceSummaryScreen.get().IsTotalDisplayed());
        Assert.assertTrue (androidInvoiceSummaryScreen.get().IsTaxAndFeesRowDisplayed());

        // Grand total should be equals to 0 because the balance toggle is On by default
        Assert.assertEquals(androidInvoiceSummaryScreen.get().getGrandTotalValue(), 0.0, 0.001);

        //Scroll until you find the pay button
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidInvoiceSummaryScreen.get().getScrollableContentContainer()
                , "down"
                , androidInvoiceSummaryScreen.get().getPayBtnContentDescription());

        // wait until the Payment methods section is displayed
        Assert.assertTrue(androidInvoiceSummaryScreen.get().checkBalanceSection(),
                "User current balance section isn't displayed");

        //Turn off the Balance toggle
        androidInvoiceSummaryScreen.get().pressBalanceToggle();

        // Total should not be zero because balance toggle is Off
        Assert.assertNotEquals(androidInvoiceSummaryScreen.get().getTotalValue(), 0.0, 0.001);

        //press pay bill to pay with Card
        androidInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        androidAddCardInfoScreen.get().pressAddCardBtn();

        //Save Bill Transaction Info from the payment success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(androidPaymentSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                androidPaymentSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(androidPaymentSuccessScreen.get().getPaymentAmount());
        defaultTestData.get().getTestBill().setCurrency(androidPaymentSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( androidPaymentSuccessScreen.get().IsDoneBtnDisplayed()
                , "Payment success screen isn't displayed!");

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    // Update user balance where balance < total and pay with balance and card, where toggle is ON
    public void validatePayingBillWithBalanceAndCreditCard () {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("9.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        // Assert the Home screen is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        androidHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        androidPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        androidBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        androidProviderScreen.get().openServiceList();
        androidProviderScreen.get().isServicesListDropDownDisplayed();
        androidProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        androidProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice summary screen
        Assert.assertTrue(androidInvoiceSummaryScreen.get().IsTotalDisplayed());
        Assert.assertTrue (androidInvoiceSummaryScreen.get().IsTaxAndFeesRowDisplayed());

        // Grand total should not be 0 because the user balance is less than the total
        Assert.assertNotEquals(androidInvoiceSummaryScreen.get().getGrandTotalValue(), 0.0, 0.001);

        Assert.assertTrue(androidInvoiceSummaryScreen.get().checkBalanceSection());

        //Scroll until you find the pay button
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidInvoiceSummaryScreen.get().getScrollableContentContainer()
                , "down"
                , androidInvoiceSummaryScreen.get().getPayBtnContentDescription());

        // wait until the Payment methods section is displayed
        Assert.assertTrue(androidInvoiceSummaryScreen.get().isCurrentBalanceSectionDisplayed()
                ," User current balance section is displayed");

        //Assert the toggle is ON while the user balance is less than the total
        Assert.assertTrue(androidInvoiceSummaryScreen.get().isBalanceToggleON(),"User balance toggle is ON");

        //press pay bill to pay with Card
        androidInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        androidAddCardInfoScreen.get().pressAddCardBtn();

        //Save Bill Transaction Info from the payment success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(androidPaymentSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                androidPaymentSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(androidPaymentSuccessScreen.get().getPaymentAmount());
        defaultTestData.get().getTestBill().setCurrency(androidPaymentSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( androidPaymentSuccessScreen.get().IsDoneBtnDisplayed()
                , "Payment success screen isn't displayed!");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    // Update User balance where Balance < total and pay with card only, Where toggle is off
    public void validatePayingBillWithBalanceBelowTotalAndBalanceToggleOff () {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("9.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        //Assert Home screen is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        androidHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        androidPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        androidBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        androidProviderScreen.get().openServiceList();
        androidProviderScreen.get().isServicesListDropDownDisplayed();
        androidProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        androidProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice summary screen
        Assert.assertTrue(androidInvoiceSummaryScreen.get().IsTotalDisplayed());
        Assert.assertTrue (androidInvoiceSummaryScreen.get().IsTaxAndFeesRowDisplayed());

        // Grand total should not be 0 because the user balance is less than the total
        Assert.assertNotEquals(androidInvoiceSummaryScreen.get().getGrandTotalValue(), 0.0, 0.001);

        Assert.assertTrue (androidInvoiceSummaryScreen.get().checkBalanceSection());

        //Scroll until you find the pay button
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidInvoiceSummaryScreen.get().getScrollableContentContainer()
                , "down"
                , androidInvoiceSummaryScreen.get().getPayBtnContentDescription());

        // turn off the balance toggle
        androidInvoiceSummaryScreen.get().pressBalanceToggle();

        Assert.assertFalse(androidInvoiceSummaryScreen.get().isBalanceToggleON(),"The Balance Toggle isn't Off");

        // wait until the Payment methods section is displayed
        Assert.assertTrue(androidInvoiceSummaryScreen.get().isCurrentBalanceSectionDisplayed()
                ," User current Balance section is displayed");

        //press pay bill
        androidInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        androidAddCardInfoScreen.get().pressAddCardBtn();

        //Save Bill Transaction Info from the payment success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(androidPaymentSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                androidPaymentSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(androidPaymentSuccessScreen.get().getPaymentAmount());
        defaultTestData.get().getTestBill().setCurrency(androidPaymentSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( androidPaymentSuccessScreen.get().IsDoneBtnDisplayed()
                , "Payment success screen isn't displayed!");
    }

    // Update user balance to be = 0 and pay a bill when toggle is ON
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validatePayingBillWithZeroBalanceAndToggleOn (){
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("0.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        androidHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        androidPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        androidBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        androidProviderScreen.get().openServiceList();
        androidProviderScreen.get().isServicesListDropDownDisplayed();
        androidProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        androidProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice screen
        Assert.assertTrue(androidInvoiceSummaryScreen.get().IsTotalDisplayed());
        Assert.assertTrue (androidInvoiceSummaryScreen.get().IsTaxAndFeesRowDisplayed());

        // Grand total should not be displayed because the user balance is equals to Zero
        Assert.assertFalse(androidInvoiceSummaryScreen.get().isGrandTotalDisplayed(),"Grand Total should not be displayed because the user balance is Zero");

        Assert.assertTrue(androidInvoiceSummaryScreen.get().isBalanceToggleON(),"The Balance Toggle is ON");

        // wait until the Payment methods section is displayed
        Assert.assertTrue(androidInvoiceSummaryScreen.get().isCurrentBalanceSectionDisplayed(),"Payment section is displayed");

        //press pay bill
        androidInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        androidAddCardInfoScreen.get().pressAddCardBtn();

        //Save Bill Transaction Info from the payment success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(androidPaymentSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                androidPaymentSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(androidPaymentSuccessScreen.get().getPaymentAmount());
        defaultTestData.get().getTestBill().setCurrency(androidPaymentSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( androidPaymentSuccessScreen.get().IsDoneBtnDisplayed()
                , "Payment success screen isn't displayed!");
    }

    // Update user balance to be = 0 and pay a bill when toggle is Off
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validatePayingBillWithZeroBalanceAndToggleOff (){
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("0.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

       androidHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        androidHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        androidPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        androidBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        androidProviderScreen.get().openServiceList();
        androidProviderScreen.get().isServicesListDropDownDisplayed();
        androidProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        androidProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice screen
        Assert.assertTrue(androidInvoiceSummaryScreen.get().IsTotalDisplayed());
        Assert.assertTrue (androidInvoiceSummaryScreen.get().IsTaxAndFeesRowDisplayed());

        // Grand total should not be displayed because the user balance is equals to Zero

        Assert.assertFalse(androidInvoiceSummaryScreen.get().isGrandTotalDisplayed()
                ,"Grand Total should not be displayed because the user balance is Zero");

        //Scroll until you find the pay button
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidInvoiceSummaryScreen.get().getScrollableContentContainer()
                , "down"
                , androidInvoiceSummaryScreen.get().getPayBtnContentDescription());

        //Turn off the Balance toggle
        androidInvoiceSummaryScreen.get().pressBalanceToggle();

        Assert.assertFalse(androidInvoiceSummaryScreen.get().isBalanceToggleON()
                ,"The Balance Toggle is ON and it shouldn't be");

        // wait until the Payment methods section is displayed
        Assert.assertTrue(androidInvoiceSummaryScreen.get().isCurrentBalanceSectionDisplayed()
                ,"User current Balance section is displayed");

        //press pay bill
        androidInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        androidAddCardInfoScreen.get().pressAddCardBtn();

        //Save Bill Transaction Info from the payment success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(androidPaymentSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                androidPaymentSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(androidPaymentSuccessScreen.get().getPaymentAmount());
        defaultTestData.get().getTestBill().setCurrency(androidPaymentSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( androidPaymentSuccessScreen.get().IsDoneBtnDisplayed()
                , "Payment success screen isn't displayed!");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    // Update user balance to be negative amount and pay a bill
    public void validatePayingBillWithNegativeBalance () {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("-10"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

         androidHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        androidHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        androidPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        androidBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        androidProviderScreen.get().openServiceList();
        androidProviderScreen.get().isServicesListDropDownDisplayed();
        androidProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        androidProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice screen
        Assert.assertTrue(androidInvoiceSummaryScreen.get().IsTotalDisplayed());
        Assert.assertTrue (androidInvoiceSummaryScreen.get().IsTaxAndFeesRowDisplayed());

        // Grand total should be displayed because the user has a negative balance

        Assert.assertTrue(androidInvoiceSummaryScreen.get().isGrandTotalDisplayed()
                ,"Grand Total should be displayed because the user has negative balance");

        //Scroll until you find the pay button
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidInvoiceSummaryScreen.get().getScrollableContentContainer()
                , "down"
                , androidInvoiceSummaryScreen.get().getPayBtnContentDescription());

        Assert.assertNotEquals(androidInvoiceSummaryScreen.get().getBalanceUsedValue(),0.0, 0.001);
        Assert.assertNotEquals(androidInvoiceSummaryScreen.get().getBalanceUsedValue(),-10.0, -10.00);

        //press pay bill
        androidInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        androidAddCardInfoScreen.get().pressAddCardBtn();

        //Save Bill Transaction Info from the payment success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(androidPaymentSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                androidPaymentSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(androidPaymentSuccessScreen.get().getPaymentAmount());
        defaultTestData.get().getTestBill().setCurrency(androidPaymentSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( androidPaymentSuccessScreen.get().IsDoneBtnDisplayed()
                , "Payment success screen isn't displayed!");
    }
}
