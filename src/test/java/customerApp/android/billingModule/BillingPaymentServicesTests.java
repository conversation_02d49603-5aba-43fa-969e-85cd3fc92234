package customerApp.android.billingModule;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class BillingPaymentServicesTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validatePayingMobileBillUsingCreditCardIsWorking () {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        androidHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        androidPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        androidBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        androidProviderScreen.get().openServiceList();
        androidProviderScreen.get().isServicesListDropDownDisplayed();
        androidProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        androidProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //press pay bill
        androidInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        androidAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void payBillAndValidateItExistsInBillsTab () {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        androidHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        androidPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        androidBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        androidProviderScreen.get().openServiceList();
        androidProviderScreen.get().isServicesListDropDownDisplayed();
        androidProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        androidProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                            defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                            , "name"
                            , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //press pay bill
        androidInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        androidAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        androidAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(androidPaymentSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(androidPaymentSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                androidPaymentSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(androidPaymentSuccessScreen.get().getPaymentAmount());
        defaultTestData.get().getTestBill().setCurrency(androidPaymentSuccessScreen.get().getPaymentCurrency());
        androidPaymentSuccessScreen.get().pressDoneBtn();

        androidHomeScreen.get().pressMoreTabBtn();

        // navigate to activity history screen
         androidMoreScreen.get().pressActivityHistoryBtn();

        //select bill tab
        androidActivityHistoryScreen.get().selectBillsTab();

        //assert that bill is displayed
        Assert.assertTrue(androidBillsTabScreen.get().
            isBillCardDisplayed(defaultTestData.get().getTestBill().getTransactionId())
            ,"Bill card is not displayed for transaction ID: "
                    + defaultTestData.get().getTestBill().getTransactionId());

        //Assert that bills count is 1
        Assert.assertEquals(androidBillsTabScreen.get().getBillsCardCount(), 1,
                "Expected only one bill card to be displayed, but found " +
                        defaultTestData.get().getTestBill().getTransactionId());
    }
}
