package customerApp.android.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class GuestModeTests extends BaseTest {
    @Test(alwaysRun = true)
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void enterGuestMode(){
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressExploreBtn();

        Assert.assertTrue(androidLocationPermissionAlert.get().isLocationPermissionAlertDisplayed());
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        //Set address Screen -> Skip it
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateNotifyMeInCategoryScreenRedirection_caseRegister(){

        //Enter Guest Mode
        androidTestsExecutionHelper.get().enterAsGuest(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get());

        /*Scroll to find a category without of stock product > Click on it > Scroll to
        find a subcategory > Click on It > Locate out of stock Product*/
        androidTestsExecutionHelper.get().findAProductInCategoryScreen(
                androidHomeScreen.get(),androidCategoryScreen.get(),androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down","right"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyCategory().getId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyCategory().getId()
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlySubcategory().getId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlySubcategory().getId()
                , androidCategoryScreen.get().getProductContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId());

        //Assert Product is displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId()));

        //Click on notify me button - Bell Icon
        androidCategoryScreen.get().pressProductNotifyMeBtn(defaultTestData.get().
                getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId());

        //Assert Notify Me Pop Up is Displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductNotifyMePopUpDisplayed());

        //Assert Notify Me Pop Up description is correct
        Assert.assertTrue(androidCategoryScreen.get().isProductNotifyMePopUpDescriptionDisplayedCorrectly(
                defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getName()));

        //Click On Register/Login Button in Notify Me Pop UP
        androidCategoryScreen.get().pressRegisterLoginBtnInNotifyMe();

        //Assert user gets redirected to login screen
        Assert.assertTrue(androidPhoneNumberScreen.get().isPageHeaderDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateNotifyMeInCategoryScreenRedirection_caseNoThanks(){

        //Enter Guest Mode
        androidTestsExecutionHelper.get().enterAsGuest(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get());

        /*Scroll to find a category without of stock product > Click on it > Scroll to
        find a subcategory > Click on It > Locate out of stock Product*/
        androidTestsExecutionHelper.get().findAProductInCategoryScreen(
                androidHomeScreen.get(),androidCategoryScreen.get(),androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down","right"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyCategory().getId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyCategory().getId()
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlySubcategory().getId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlySubcategory().getId()
                , androidCategoryScreen.get().getProductContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId());

        //Assert Product is displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId()));

        //Click on notify me button - Bell Icon
        androidCategoryScreen.get().pressProductNotifyMeBtn(defaultTestData.get().
                getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId());

        //Assert Notify Me Pop Up is Displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductNotifyMePopUpDisplayed());

        //Assert Notify Me Pop Up description is correct
        Assert.assertTrue(androidCategoryScreen.get().isProductNotifyMePopUpDescriptionDisplayedCorrectly(
                defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getName()));

        //Click On No Thanks Button in Notify Me Pop UP
        androidCategoryScreen.get().pressNoThanksBtnInNotifyMe();

        //Assert user stays in category screen
        Assert.assertTrue(androidCategoryScreen.get().isPageDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateNotifyMeInProductScreenRedirection_caseRegister(){

        //Enter Guest Mode
        androidTestsExecutionHelper.get().enterAsGuest(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get());

        /*Scroll to find a category without of stock product > Click on it > Scroll to
        find a subcategory > Click on It > Locate out of stock Product*/
        androidTestsExecutionHelper.get().findAProductInCategoryScreen(
                androidHomeScreen.get(),androidCategoryScreen.get(),androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down","right"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyCategory().getId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyCategory().getId()
                , androidCategoryScreen.get().getSubCategoryContentDescription(defaultTestData.get().
                        getCustomerAppTestSession().getOutOfStockOnlySubcategory().getId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlySubcategory().getId()
                , androidCategoryScreen.get().getProductContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId());

        //Assert Product is displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId()));

        //Click On Out-of-Stock Product
        androidCategoryScreen.get().pressProduct(defaultTestData.get()
                .getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId());

        //Assert user got directed to product screen
        Assert.assertTrue(androidProductScreen.get().isProductPageDisplayed(defaultTestData.get()
                .getCustomerAppTestSession().getOutOfStockOnlyProduct().getMysqlId()));

        //Click on Notify Me Button
        androidProductScreen.get().pressProductNotifyMeBtn(defaultTestData.get().getCustomerAppTestSession()
                .getOutOfStockOnlyProduct().getMysqlId());

        //Assert Notify Me Pop Up is Displayed
        Assert.assertTrue(androidProductScreen.get().isProductNotifyMePopUpDisplayed());

        //Assert Notify Me Pop Up description is correct
        Assert.assertTrue(androidProductScreen.get().isProductNotifyMePopUpDescriptionDisplayedCorrectly(
                defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getName()));

        //Click On Register/Login Button in Notify Me Pop UP
        androidProductScreen.get().pressRegisterLoginBtnInNotifyMe();

        //Assert user gets redirected to login screen
        Assert.assertTrue(androidPhoneNumberScreen.get().isPageHeaderDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateNotifyMeInProductScreenRedirection_caseNoThanks(){

        //Enter Guest Mode
        androidTestsExecutionHelper.get().enterAsGuest(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get());

        /*Scroll to find a category without of stock product > Click on it > Scroll to
        find a subcategory > Click on It > Locate out of stock Product*/
        androidTestsExecutionHelper.get().findAProductInCategoryScreen(
                androidHomeScreen.get(),androidCategoryScreen.get(),androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down","right"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyCategory().getId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyCategory().getId()
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlySubcategory().getId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlySubcategory().getId()
                , androidCategoryScreen.get().getProductContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId())
                , defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId());

        //Assert Product is displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId()));

        //Click On Out-of-Stock Product
        androidCategoryScreen.get().pressProduct(defaultTestData.get()
                .getCustomerAppTestSession().getOutOfStockOnlyProduct().getMongoId());

        //Assert user got directed to product screen
        Assert.assertTrue(androidProductScreen.get().isProductPageDisplayed(defaultTestData.get()
                .getCustomerAppTestSession().getOutOfStockOnlyProduct().getMysqlId()));

        //Click on Notify Me Button
        androidProductScreen.get().pressProductNotifyMeBtn(defaultTestData.get().getCustomerAppTestSession()
                .getOutOfStockOnlyProduct().getMysqlId());

        //Assert Notify Me Pop Up is Displayed
        Assert.assertTrue(androidProductScreen.get().isProductNotifyMePopUpDisplayed());

        //Assert Notify Me Pop Up description is correct
        Assert.assertTrue(androidProductScreen.get().isProductNotifyMePopUpDescriptionDisplayedCorrectly(
                defaultTestData.get().getCustomerAppTestSession().getOutOfStockOnlyProduct().getName()));

        //Click On No Thanks Button in Notify Me Pop UP
        androidProductScreen.get().pressNoThanksBtnInNotifyMe();

        //Assert user stays in product screen
        Assert.assertTrue(androidProductScreen.get().isProductPageDisplayed(defaultTestData.get()
                .getCustomerAppTestSession().getOutOfStockOnlyProduct().getMysqlId()));
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateClickingOnCartIconInCategoryScreenWillRedirectToLoginPage(){

        //Enter Guest Mode
        androidTestsExecutionHelper.get().enterAsGuest(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get());

        //Scroll And find a certain category using text
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer(),
                "down",androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Click On Category
        androidHomeScreen.get().pressCategory(defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        //Assert User is in Category Screen
        androidCategoryScreen.get().isPageDisplayed();

        //Click On Cart Icon
        androidCategoryScreen.get().pressCartBtn();

        //Assert user got redirected to login screen
        Assert.assertTrue(androidPhoneNumberScreen.get().isPageHeaderDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateClickingOnCartIconInProductScreenWillRedirectToLoginPage(){

        //Enter Guest Mode
        androidTestsExecutionHelper.get().enterAsGuest(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get());

         /*Scroll to find a certain category > Click on it > Scroll to
        find a subcategory > Click on It > Locate a certain Product*/
        androidTestsExecutionHelper.get().findAProductInCategoryScreen(
                androidHomeScreen.get(),androidCategoryScreen.get(),androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down","right"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())
                , defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId())
                , defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()
                , androidCategoryScreen.get().getProductContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId())
                , defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId());

        //Assert Product is displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));

        //Click On Product
        androidCategoryScreen.get().pressProduct(defaultTestData.get()
                .getCustomerAppTestSession().getNowOnlyProduct().getMongoId());

        //Assert user got directed to product screen
        Assert.assertTrue(androidProductScreen.get().isProductPageDisplayed(defaultTestData.get()
                .getCustomerAppTestSession().getNowOnlyProduct().getMysqlId()));

        //Click On Cart Icon
        androidProductScreen.get().clickOnCartIcon();

        //Assert user got redirected to login screen
        Assert.assertTrue(androidPhoneNumberScreen.get().isPageHeaderDisplayed());
    }
}
