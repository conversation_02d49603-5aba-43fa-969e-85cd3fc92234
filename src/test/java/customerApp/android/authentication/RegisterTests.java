package customerApp.android.authentication;

import base.BaseTest;
import helpers.factories.dataFactories.OtpFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class RegisterTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void registerAndLoginWithValidLocalPhoneNumber() {
        //Press the Login or signup hyperlink in the landing page
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        Assert.assertTrue(androidPhoneNumberScreen.get().isPageHeaderDisplayed());

        //Enter the phoneNumber
        androidPhoneNumberScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());
        Assert.assertEquals(androidPhoneNumberScreen.get().getPhoneNumberFieldValue()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());
        androidPhoneNumberScreen.get().pressNextBtn();

        //Enter OTP
        Assert.assertTrue(androidOtpVerificationScreen.get().isPageHeaderDisplayed(),
                "Page Header is not displayed.");
        Assert.assertTrue(androidOtpVerificationScreen.get().isPageSubHeaderDisplayed(),
                "Page subHeader is not displayed.");
        Assert.assertTrue(androidOtpVerificationScreen.get()
                        .isPhoneNumberDisplayed(defaultTestData.get().getRandomTestUser().getPhoneNumber())
                , "MobileNumber is not displayed");

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "register", defaultTestData.get().getRandomTestUser()));

        androidOtpVerificationScreen.get().enterOTP(defaultTestData.get().getRandomTestUser().getOtp());

        //Enter account Information
        Assert.assertTrue(androidCreateAccountInfoScreen.get().isTopHeaderDisplayed());
        Assert.assertTrue(androidCreateAccountInfoScreen.get().isSubHeaderDisplayed());
        androidCreateAccountInfoScreen.get().fillInAccountInformationForm(defaultTestData.get());
        androidCreateAccountInfoScreen.get().pressSubmitBtn();

        //Proceed from Success screen
        Assert.assertTrue(androidRegisterSuccessScreen.get().isConfirmationMessageDisplayed());
        androidRegisterSuccessScreen.get().pressProceedBtn();

        //Home Screen
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        androidHomeScreen.get().pressMoreTabBtn();

        //More Screen
        androidMoreScreen.get().dismissCoachMarksIfDisplayed();
        Assert.assertTrue(androidMoreScreen.get().isFullNameDisplayed(defaultTestData.get().getRandomTestUser().getFirstName(),
                defaultTestData.get().getRandomTestUser().getLastName()));
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get(),
                androidMoreScreen.get().getScrollableContentContainer()
                , "down"
                , androidMoreScreen.get().getLogoutBtnContentDescription());
        androidMoreScreen.get().pressLogoutBtn();

        //Location Permission After Logout
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        //Set Address Screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Home Screen
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();

        //More Screen
        androidMoreScreen.get().pressLoginBtn();

        androidPhoneNumberScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidPhoneNumberScreen.get().pressNextBtn();

        //Enter OTP
        Assert.assertTrue(androidOtpVerificationScreen.get().isPageHeaderDisplayed(),
                "Page Header is not displayed.");
        Assert.assertTrue(androidOtpVerificationScreen.get().isPageSubHeaderDisplayed(),
                "Page subHeader is not displayed.");
        Assert.assertTrue(androidOtpVerificationScreen.get()
                        .isPhoneNumberDisplayed(defaultTestData.get().getRandomTestUser().getPhoneNumber())
                , "MobileNumber is not displayed");

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "login", defaultTestData.get().getRandomTestUser()));

        androidOtpVerificationScreen.get().enterOTP(defaultTestData.get().getRandomTestUser().getOtp());
        androidOtpVerificationScreen.get().waitTillPageIsDismissed();

        //Validate that app didn't get stuck on OTP screen or directed to create account page
        Assert.assertFalse(androidOtpVerificationScreen.get().isPageHeaderDisplayed());
        Assert.assertFalse(androidCreateAccountInfoScreen.get().isTopHeaderDisplayed());

        //Validate that app went to hemeScreen
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("foreign-country")})
    public void registerAndLoginWithValidForeignPhoneNumber() {
        //Press the Login or signup hyperlink in the landing page
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());

        //Press the Login or signup hyperlink in the landing page
        androidLandingScreen.get().pressAuthHyperLink();
        Assert.assertTrue(androidPhoneNumberScreen.get().isPageHeaderDisplayed());

        //Open the countries drop down
        androidPhoneNumberScreen.get().pressCountryCodeBtn(defaultTestData.get().getRandomTestUser().getPhoneCountry());
        Assert.assertTrue(androidCountriesListScreen.get().isCountriesListScreenDisplayed());

        //Search for another country
        androidCountriesListScreen.get().searchForCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());
        androidCountriesListScreen.get().selectCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountry());

        androidPhoneNumberScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getForeignLocalPhoneNumber());

        androidPhoneNumberScreen.get().pressNextBtn();

        //Enter OTP
        Assert.assertTrue(androidOtpVerificationScreen.get().isPageHeaderDisplayed(),
                "Page Header is not displayed.");
        Assert.assertTrue(androidOtpVerificationScreen.get().isPageSubHeaderDisplayed(),
                "Page subHeader is not displayed.");
        Assert.assertTrue(androidOtpVerificationScreen.get()
                        .isPhoneNumberDisplayed(defaultTestData.get().getRandomTestUser().getForeignPhoneNumber())
                , "MobileNumber is not displayed");

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "register", defaultTestData.get().getRandomTestUser()));

        androidOtpVerificationScreen.get().enterOTP(defaultTestData.get().getRandomTestUser().getOtp());

        //Enter account Information
        Assert.assertTrue(androidCreateAccountInfoScreen.get().isTopHeaderDisplayed());
        Assert.assertTrue(androidCreateAccountInfoScreen.get().isSubHeaderDisplayed());
        androidCreateAccountInfoScreen.get().fillInAccountInformationForm(defaultTestData.get());
        androidCreateAccountInfoScreen.get().pressSubmitBtn();

        //Proceed from Success screen
        Assert.assertTrue(androidRegisterSuccessScreen.get().isConfirmationMessageDisplayed());
        androidRegisterSuccessScreen.get().pressProceedBtn();

        //Home Screen
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        androidHomeScreen.get().pressMoreTabBtn();

        //More Screen
        androidMoreScreen.get().dismissCoachMarksIfDisplayed();
        Assert.assertTrue(androidMoreScreen.get().isFullNameDisplayed(defaultTestData.get().getRandomTestUser().getFirstName(),
                defaultTestData.get().getRandomTestUser().getLastName()));
        androidMoreScreen.get().pressLogoutBtn();

        //Location Permission After Logout
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        //Set Address Screen
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Home Screen
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();

        //More Screen
        androidMoreScreen.get().pressLoginBtn();

        //Change country and enter Phone number
        androidPhoneNumberScreen.get().pressCountryCodeBtn(defaultTestData.get().getRandomTestUser().getPhoneCountry());

        androidCountriesListScreen.get().searchForCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());
        androidCountriesListScreen.get().selectCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountry());

        androidPhoneNumberScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getForeignLocalPhoneNumber());

        androidPhoneNumberScreen.get().pressNextBtn();

        //Enter OTP
        Assert.assertTrue(androidOtpVerificationScreen.get().isPageHeaderDisplayed(),
                "Page Header is not displayed.");
        Assert.assertTrue(androidOtpVerificationScreen.get().isPageSubHeaderDisplayed(),
                "Page subHeader is not displayed.");
        Assert.assertTrue(androidOtpVerificationScreen.get()
                        .isPhoneNumberDisplayed(defaultTestData.get().getRandomTestUser().getForeignPhoneNumber())
                , "MobileNumber is not displayed");

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "login", defaultTestData.get().getRandomTestUser()));

        androidOtpVerificationScreen.get().enterOTP(defaultTestData.get().getRandomTestUser().getOtp());
        androidOtpVerificationScreen.get().waitTillPageIsDismissed();

        //Validate that app didn't get stuck on OTP screen or directed to create account page
        Assert.assertFalse(androidOtpVerificationScreen.get().isPageHeaderDisplayed());
        Assert.assertFalse(androidCreateAccountInfoScreen.get().isTopHeaderDisplayed());

        //Validate that app went to hemeScreen
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
    }

    @Test(alwaysRun = true)
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void enterInvalidLocalPhoneNumbers() {
        //Press the Login or signup hyperlink in the landing page
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Enter Mobile Number
        Assert.assertTrue(androidPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");
        androidPhoneNumberScreen.get().enterPhoneNumber("1234");
        androidPhoneNumberScreen.get().unFocusFromMobileNumberField();

        Assert.assertTrue(androidPhoneNumberScreen.get().isPhoneErrorValidationMsgDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void enterInvalidForeignPhoneNumbers() {
        //Press the Login or signup hyperlink in the landing page
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        //Open the countries drop down
        androidPhoneNumberScreen.get().pressCountryCodeBtn(defaultTestData.get().getRandomTestUser().getPhoneCountry());
        Assert.assertTrue(androidCountriesListScreen.get().isCountriesListScreenDisplayed());

        //Search for another country
        androidCountriesListScreen.get().searchForCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());
        androidCountriesListScreen.get().selectCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountry());

        //Enter Mobile Number
        Assert.assertTrue(androidPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");
        androidPhoneNumberScreen.get().enterPhoneNumber("1234");
        androidPhoneNumberScreen.get().unFocusFromMobileNumberField();

        Assert.assertTrue(androidPhoneNumberScreen.get().isPhoneErrorValidationMsgDisplayed());
    }
}
