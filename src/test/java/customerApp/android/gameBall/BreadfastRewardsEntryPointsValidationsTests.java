package customerApp.android.gameBall;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class BreadfastRewardsEntryPointsValidationsTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void enterBreadfastRewardScreenFromCartScreen() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();
        //Navigate to Breadfast Reward screen
        androidCartScreen.get().pressBreadfastRewardBtn();
        // Assert that breadfast reward page is displayed
        Assert.assertTrue(androidBreadfastRewardsScreen.get().isPageDisplayed());
        Assert.assertTrue(androidBreadfastRewardsScreen.get().isPageTitleDisplayed());
        Assert.assertEquals(androidBreadfastRewardsScreen.get().getBreadfastRewardsTitle()
                , "Welcome to Breadfast Rewards");
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void enterBreadfastRewardScreenFromAndroidSuccessScreen() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        //add Address
        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //scroll to select cash on delivery option
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get(),
                androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        //select cash on delivery option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        //press place order button
        androidCheckoutScreen.get().pressPlaceOrderBtn();

        //assert that order success screen is displayed
        androidOrderSuccessScreen.get().isPageDisplayed();
        //press on breadfast reward button
        androidOrderSuccessScreen.get().pressBreadfastRewardBtn();

        // Assert that breadfast reward page is displayed
        Assert.assertTrue(androidBreadfastRewardsScreen.get().isPageDisplayed());
        Assert.assertTrue(androidBreadfastRewardsScreen.get().isPageTitleDisplayed());
        Assert.assertEquals(androidBreadfastRewardsScreen.get().getBreadfastRewardsTitle()
                , "Welcome to Breadfast Rewards");

    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void enterBreadfastRewardScreenFromOrderDetailsScreen()  {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        //add Address
        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //scroll to select cash on delivery option
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get(),
                androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        //select cash on delivery option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        //press checkout button
        androidCheckoutScreen.get().pressPlaceOrderBtn();

        //assert that order success screen is displayed
        androidOrderSuccessScreen.get().isPageDisplayed();
        //navigate to home screen
        androidDriver.get().navigate().back();

        //press more tab
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();

        //navigate to activity history screen
        androidMoreScreen.get().pressActivityHistoryBtn();

        // assert that activity history page is displayed
        Assert.assertTrue(androidActivityHistoryScreen.get().isPageDisplayed());
        //open order details screen
        androidActivityHistoryScreen.get().selectOrdersCard(0);

        //open Breadfast reward screen
        androidOrderDetailsScreen.get().pressBreadfastRewardBtn();

        // Assert that breadfast reward page is displayed
        Assert.assertTrue(androidBreadfastRewardsScreen.get().isPageDisplayed());
        Assert.assertTrue(androidBreadfastRewardsScreen.get().isPageTitleDisplayed());
        Assert.assertEquals(androidBreadfastRewardsScreen.get().getBreadfastRewardsTitle()
                , "Welcome to Breadfast Rewards");
    }
}
