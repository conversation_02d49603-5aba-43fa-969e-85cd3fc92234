package customerApp.android.referral;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class RegistrationWithReferralTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void registerWithValidInvitationCode() {

        //Enable Referrals For AdminUser
        switcherApiClient.get().setUserReferralStatusByPhoneNumber(false
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getAdminUser().getPhoneNumber());

        //Fill User info in registration form With Admin Invitation Code
        androidTestsExecutionHelper.get().fillInAccountRegistrationFormWithReferralCode(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , defaultTestData.get().getTestCountryCode()
                , defaultTestData.get().getAdminUser().getReferralCode());

        //Validate Invitation Code is valid check mark is displayed
        Assert.assertTrue(androidCreateAccountInfoScreen.get().isValidInvitationCodeCheckMarkDisplayed());
        //Checkmark has the same xpath as the error mark requires double assertion
        Assert.assertFalse(androidCreateAccountInfoScreen.get().isInvalidInvitationCodeErrorDisplayed());

        // Validate User is able to register and home screen is displayed
        androidCreateAccountInfoScreen.get().pressSubmitBtn();
        Assert.assertTrue(androidRegisterSuccessScreen.get().isConfirmationMessageDisplayed());
        androidRegisterSuccessScreen.get().pressProceedBtn();
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Validate User is able to login after logout
        androidTestsExecutionHelper.get().logoutSkipLocationAndAddressAlertsThenLogin(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode()
                , androidDriver.get()
                , androidHomeScreen.get()
                , androidMoreScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get());

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void registerWithInvitationCodeFromUserWithReferralsDisabled() {

        //Disable Referrals For AdminUser
        switcherApiClient.get().setUserReferralStatusByPhoneNumber(true
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getAdminUser().getPhoneNumber());

        //Register User With Admin Invitation Code
        androidTestsExecutionHelper.get().fillInAccountRegistrationFormWithReferralCode(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , defaultTestData.get().getTestCountryCode()
                , defaultTestData.get().getAdminUser().getReferralCode());

        //Validate that invitation code is invalid and error is shown to user
        Assert.assertTrue(androidCreateAccountInfoScreen.get().isInvalidInvitationCodeErrorDisplayed());

        //Validate user is unable to register and stays in create account info Screen
        androidCreateAccountInfoScreen.get().pressSubmitBtn();
        Assert.assertTrue(androidCreateAccountInfoScreen.get().isInvalidInvitationCodeToastDisplayed());
        Assert.assertTrue(androidCreateAccountInfoScreen.get().isTopHeaderDisplayed());

       //Validate user is able to register after deleting invitation code
        androidCreateAccountInfoScreen.get().clearReferralCodeField();
        androidCreateAccountInfoScreen.get().pressSubmitBtn();
        Assert.assertTrue(androidRegisterSuccessScreen.get().isConfirmationMessageDisplayed());
        androidRegisterSuccessScreen.get().pressProceedBtn();
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Validate user is able to login again after logout
        androidTestsExecutionHelper.get().logoutSkipLocationAndAddressAlertsThenLogin(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode()
                , androidDriver.get()
                , androidHomeScreen.get()
                , androidMoreScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get());

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
    }
}
