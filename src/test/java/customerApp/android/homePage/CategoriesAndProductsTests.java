package customerApp.android.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class CategoriesAndProductsTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatAllNowCategoriesRetrievedFromApiAreDisplayed(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        validationResults.set(androidTestsExecutionHelper.get().areAllCategoriesDisplayed(androidDriver.get()
                , androidHomeScreen.get()
                , androidCategoryScreen.get()
                , defaultTestData.get().getCustomerAppTestSession().getNowCategoriesInWarehouse()
                , "now"
                , validationResults.get()));

        Assert.assertTrue(validationResults.get().isResult()
                , String.join("\n", validationResults.get().getValidationResults()));
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void AddProductToFavListFromCardView(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressCategory( defaultTestData.get().getCustomerAppTestSession().getNowCategoriesInWarehouse().get(1).getId());
        androidCategoryScreen.get().isPageDisplayed();
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void AddProductToCartListFromCardView(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());
        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressCategory( defaultTestData.get().getCustomerAppTestSession().getNowCategoriesInWarehouse().get(1).getId());
        androidCategoryScreen.get().isPageDisplayed();
        androidProductScreen.get().clickAddToCart();

    }
}
