package customerApp.android.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class ReferralIconTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateReferralCodeAndShareBtnIsDisplayedForOldUsers() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Logout
        androidTestsExecutionHelper.get().logout(androidDriver.get(), defaultTestData.get(), androidHomeScreen.get(),
                androidMoreScreen.get());

        //Location Permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        //Set address Screen -> Skip it
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressLoginBtn();

        //Login
        androidTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                androidSetAddressScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidHomeScreen.get().isHomePageDisplayed();

        //Referral asserts
        Assert.assertTrue(androidHomeScreen.get().isReferralBtnDisplayed());
        androidHomeScreen.get().pressReferralBtn();
        Assert.assertTrue(androidReferralScreen.get()
                .isRefScreenDisplayed(),"referral screen isn't displayed");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateReferralProgressBarsAreDisplayedForNewUsers() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Logout
        androidTestsExecutionHelper.get().logout(androidDriver.get(), defaultTestData.get(), androidHomeScreen.get(),
                androidMoreScreen.get());

        //Location Permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        //Set address Screen -> Skip it
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Validate the redirection and that More tab is displayed
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressLoginBtn();

        //Login
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        //Referral asserts
        androidHomeScreen.get().isReferralBtnDisplayed();
        androidHomeScreen.get().pressReferralBtn();
        Assert.assertTrue(androidReferralScreen.get().isRefScreenDisplayed(),"referral screen isn't displayed");
        Assert.assertTrue(androidReferralScreen.get().isRefNewUserTextDisplayed());
        Assert.assertTrue(androidReferralScreen.get().isRefNewUserWidgetDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateReferralIconIsNotDisplayedInGuestMode() {
        androidTestsExecutionHelper.get().enterAsGuest(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidLocationPermissionAlert.get(), androidSetAddressScreen.get(),
                androidHomeScreen.get(), defaultTestData.get());

        Assert.assertFalse(androidHomeScreen.get().isReferralBtnDisplayed());
    }

}
