package customerApp.android.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;
public class NowAndTomorrowTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateNowProductIsDisplayedForRegisteredUser() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        //Scroll horizontally and select the subCategory With the now only product if it's not already selected
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , "right"
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

        androidCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        //Scroll and try to find the Now only product in the list
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));

        //Validate that now only product is not displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTomorrowProductIsDisplayedForRegisteredUser() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        //Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        //Switch to Tomorrow
        androidHomeScreen.get().pressNowTomorrowTopBanner();
        androidNowAndTomorrowModal.get().changeServeType("tomorrow");

        androidHomeScreen.get().isHomePageDisplayed();

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryUiSelector(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId());

        //Scroll horizontally and select the subCategory With the now only product if it's not already selected
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , "right"
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlySubcategory().getId()));

        androidCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlySubcategory().getId());

        //Scroll and try to find the Now only product in the list
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyProduct().getMongoId()));

        //Validate that now only product is not displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyProduct().getMongoId()));
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateNowProductIsDisplayedForGuestUsers(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().enterAsGuest(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryUiSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        //Scroll horizontally and select the subCategory With the now only product if it's not already selected
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , "right"
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

        androidCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        //Scroll and try to find the Now only product in the list
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));

        //Validate that now only product is not displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateTomorrowProductIsDisplayedForGuestUsers(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().enterAsGuest(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get());

        //Switch to Tomorrow
        androidHomeScreen.get().pressNowTomorrowTopBanner();
        androidNowAndTomorrowModal.get().changeServeType("tomorrow");

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryUiSelector(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId());

        //Scroll horizontally and select the subCategory With the now only product if it's not already selected
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , "right"
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlySubcategory().getId()));

        androidCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlySubcategory().getId());

        //Scroll and try to find the Now only product in the list
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyProduct().getMongoId()));

        //Validate that now only product is not displayed
        Assert.assertTrue(androidCategoryScreen.get().isProductDisplayed(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyProduct().getMongoId()));
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    // check that delivery options bottom sheet opens after the fresh install
    public void validateSelectDeliveryOptionsOpensAutomaticallyAfterInstallTheAppInKSA(){

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    // check that user can change delivery mode by accessing Select delivery options bottom sheet after pressing the address section in the Home screen
    public void checkUserCanChangeDeliveryModeAfterClickingOnTheAddressAtTopOfHomeScreen(){

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());
        //close bottom sheet
        androidAddressSelectionScreen.get().pressCloseIcon();
        androidHomeScreen.get().pressChangeAddressBtn();
        androidAddressSelectionScreen.get().pressTomorrowOptionBtn();
        Assert.assertTrue(androidHomeScreen.get().isTomorrowDeliveryTimeDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkUserDeliveryOptionWillBeStoredAfterLoggingOutThenLoggingInAgain(){

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());
        // Select Tomorrow option
        androidAddressSelectionScreen.get().pressTomorrowOptionBtn();
        Assert.assertTrue(androidHomeScreen.get().isTomorrowDeliveryTimeDisplayed());
        androidHomeScreen.get().pressMoreTabBtn();
        //Logout
        androidMoreScreen.get().pressLogoutBtn();
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        androidHomeScreen.get().pressMoreTabBtn();
        //Login again with the same user credentials
        androidMoreScreen.get().pressLoginBtn();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        // check user selection is as it is Tomorrow
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        Assert.assertTrue(androidHomeScreen.get().isTomorrowDeliveryTimeDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckDefaultSelectionIsNowWhenDismissingSelectDeliveryOptionAfterFreshInstall(){
        //add capacity to an instant slot
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().changeCountryCode();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "5");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 5);
        Assert.assertTrue(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getInstantFlag());

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());
        //close bottom sheet
        androidAddressSelectionScreen.get().pressCloseIcon();
        // Now is the default
        Assert.assertTrue(androidHomeScreen.get().isInstantDeliveryTimeDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("web"), @Tag("delivery-capacity-management")})
    public void checkClickingSwitchToTomorrowInBusyModalWillTriggerAddressModal(){
        // DCM For KSA not migrated yet, but it will be migrated soon, so I added it for the future
        // remove fp capacity
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().changeCountryCode();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getWarehouseStatus()
                , "BUSY");

    //Register with A valid Phone number
    androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
            androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
            androidOtpVerificationScreen.get(), testExecutionHelper.get(),
            defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
            androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidBusyModalScreen.get().isBusyModalDisplayed());
        androidBusyModalScreen.get().pressSwitchToTomorrowOption();
        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());
        Assert.assertTrue(androidAddressSelectionScreen.get().isTomorrowOnlyAvailableOptionTxtDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("web"), @Tag("delivery-capacity-management")})
    public void checkDismissingBusyModalWillTriggerAddressModal(){
        // DCM For KSA not migrated yet, but it will be migrated soon, so I added it for the future
        // remove fp capacity
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().changeCountryCode();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getWarehouseStatus()
                , "BUSY");

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidBusyModalScreen.get().isBusyModalDisplayed());
        androidBusyModalScreen.get().pressCloseIcon();
        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());
        Assert.assertTrue(androidAddressSelectionScreen.get().isTomorrowOnlyAvailableOptionTxtDisplayed());
    }
}
