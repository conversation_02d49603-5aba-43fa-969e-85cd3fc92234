package customerApp.android.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class FreshChatTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void FreshChatLoggedIn() {

        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().logout(
                androidDriver.get(),
                defaultTestData.get(),
                androidHomeScreen.get(),
                androidMoreScreen.get());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressLoginBtn();

        androidTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                androidSetAddressScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidHomeScreen.get().isHomePageDisplayed();
        Assert.assertTrue(androidHomeScreen.get().isFreshChatBtnDisplayed(), "Create button isn't disabled");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateFreshChatIconIsNotDisplayedInGuestMode() {
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressExploreBtn();
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidHomeScreen.get().isHomePageDisplayed();
        Assert.assertTrue(androidHomeScreen.get().isFreshChatBtnDisplayed(), "Create button isn't disabled");

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void FreshChatRedirection() {

        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().logout(
                androidDriver.get(),
                defaultTestData.get(),
                androidHomeScreen.get(),
                androidMoreScreen.get());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressLoginBtn();

        androidTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                androidSetAddressScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidHomeScreen.get().isHomePageDisplayed();
        Assert.assertTrue(androidHomeScreen.get().isFreshChatBtnDisplayed(), "Create button isn't disabled");

        //Go to Fresh Chat page
        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().isPageDisplayed();
        Assert.assertEquals(androidFreshChatScreen.get().getFreshChatText(), "I have a question");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void FreshChatBackBtn() {

        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().logout(
                androidDriver.get(),
                defaultTestData.get(),
                androidHomeScreen.get(),
                androidMoreScreen.get());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressLoginBtn();

        androidTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                androidSetAddressScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidHomeScreen.get().isHomePageDisplayed();
        Assert.assertTrue(androidHomeScreen.get().isFreshChatBtnDisplayed(), "Create button isn't disabled");

        androidHomeScreen.get().pressFreshChatBtn();
        androidFreshChatScreen.get().pressBackBtn();
        androidHomeScreen.get().isHomePageDisplayed();
    }
}

