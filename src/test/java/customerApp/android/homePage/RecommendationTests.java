package customerApp.android.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class RecommendationTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateRecommendationsAreDisplayedForExistingUsers() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().logout(
                androidDriver.get(),
                defaultTestData.get(),
                androidHomeScreen.get(),
                androidMoreScreen.get());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressLoginBtn();

        androidTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                androidSetAddressScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidHomeScreen.get().isHomePageDisplayed();
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get(), androidHomeScreen.get().getHomeScreenScrollableContentContainer(),
                "down",
                androidHomeScreen.get().getRecommendationContentDescription()
        );
        Assert.assertTrue(androidHomeScreen.get().isRecommendationDisplay(),
                "Recommendation isn't displayed");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateRecommendationDoesNotExistForGuestUser() {
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressExploreBtn();
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidHomeScreen.get().isHomePageDisplayed();
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get(), androidHomeScreen.get().getHomeScreenScrollableContentContainer(),
                "down",
                androidHomeScreen.get().getRecommendationContentDescription()
        );
        Assert.assertFalse(androidHomeScreen.get().isRecommendationDisplay(),
                "Recommendation is displayed");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateRecommendationsRedirection() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Logout
        androidTestsExecutionHelper.get().logout(androidDriver.get(), defaultTestData.get(), androidHomeScreen.get(),
                androidMoreScreen.get());

        //Location Permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        //Set address Screen -> Skip it
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressLoginBtn();

        //Login
        androidTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                androidSetAddressScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidHomeScreen.get().isHomePageDisplayed();
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get(), androidHomeScreen.get().getHomeScreenScrollableContentContainer(),
                "down",
                androidHomeScreen.get().getRecommendationContentDescription()
        );
        androidHomeScreen.get().pressRecViewAllBtn();
        Assert.assertTrue(androidRecommendationScreen.get().isRecommendationTitleDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateRecommendationsItemsAvailability() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Logout
        androidTestsExecutionHelper.get().logout(androidDriver.get(), defaultTestData.get(), androidHomeScreen.get(),
                androidMoreScreen.get());

        //Location Permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        //Set address Screen -> Skip it
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressLoginBtn();

        //Login
        androidTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                androidSetAddressScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidHomeScreen.get().isHomePageDisplayed();
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get(), androidHomeScreen.get().getHomeScreenScrollableContentContainer(),
                "down",
                androidHomeScreen.get().getRecommendationContentDescription()
        );
        androidHomeScreen.get().pressRecViewAllBtn();
        Assert.assertEquals(androidRecommendationScreen.get().getRecommendationTitle(), "Top Picks For You ");
        Assert.assertTrue(androidRecommendationScreen.get().isCartIconDisplayed(), "Cart icon isn't displayed");
        androidRecommendationScreen.get().pressBackBtn();
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed(), "Home page isn't displayed");
    }
}
