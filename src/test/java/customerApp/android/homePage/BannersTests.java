package customerApp.android.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class BannersTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatBannerAppearsOnHomeScreen(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        Assert.assertTrue(androidHomeScreen.get().isHomeScreenFirstBannerDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatBannerAppearsOnHomeScreenAfterReload(){
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        androidHomeScreen.get().pullToRefresh();
        Assert.assertTrue(androidHomeScreen.get().isHomeScreenFirstBannerDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void clickingOnFirstBannerToOpen () {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        Assert.assertTrue(androidHomeScreen.get().isHomeScreenFirstBannerDisplayed());
        androidHomeScreen.get().clickFirstBanner();
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void takeActionBasedOnTheOpenedBanner() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        Assert.assertTrue(androidHomeScreen.get().isHomeScreenFirstBannerDisplayed());
        androidHomeScreen.get().clickFirstBanner();
        androidOpenedBannerModalScreen.get().getOpenedBannerAction();

        //Assert that the page header is redisplayed
        Assert.assertTrue(androidHomeScreen.get().isPageHeaderArDisplayed());
    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void changeFPAndCheckBanners() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        Assert.assertTrue(androidHomeScreen.get().isHomeScreenFirstBannerDisplayed());

        //Create second address
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(3);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        Assert.assertTrue(androidHomeScreen.get().isHomeScreenFirstBannerDisplayed());
    }
}
