package customerApp.android.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class BalanceToggleChecksTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void ToggleOnWhenBalanceGreaterThanTotal () {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(androidCheckoutScreen.get().isPageDisplayed());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // Check if the balance toggle is ON
        Assert.assertTrue(androidCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to COD
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // Implement assertion logic to ensure the total is = 0 when the Balance is selected as payment method
        Assert.assertTrue(androidCheckoutScreen.get().isBalanceDisplayed());
        Assert.assertEquals(androidCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void ToggleOnWhenBGreaterThanTotalAndCCIsSelected () {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(androidCheckoutScreen.get().isPageDisplayed());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // Check if the balance toggle is ON
        Assert.assertTrue(androidCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to CC
        androidCheckoutScreen.get().pressCreditCardPaymentOption();

        // Implement assertion logic to ensure the total is  0 even if the CC is selected
        Assert.assertTrue(androidCheckoutScreen.get().isBalanceDisplayed());
        Assert.assertEquals(androidCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }

   @Test
   @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void WhenBalancePartiallyWhenCODIsSelected () {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(androidCheckoutScreen.get().isPageDisplayed());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // Check if the balance toggle is ON
        Assert.assertTrue(androidCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to COD
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // Implement assertion logic to ensure the total is not equals 0 even if the user uses the balance
        Assert.assertTrue(androidCheckoutScreen.get().isBalanceDisplayed());
        Assert.assertNotEquals(androidCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void WhenBalancePartiallyWhenCCIsSelected () {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(androidCheckoutScreen.get().isPageDisplayed());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // Check if the balance toggle is ON
        Assert.assertTrue(androidCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to CC
        androidCheckoutScreen.get().pressCreditCardPaymentOption();

        // Implement assertion logic to ensure the total is not equals 0 even if the user uses the balance
        Assert.assertTrue(androidCheckoutScreen.get().isBalanceDisplayed());
        Assert.assertNotEquals(androidCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void BalanceWhenNegativeAndCCIsSelected () {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("-100.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(androidCheckoutScreen.get().isPageDisplayed());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // Check if the balance toggle is OFF
        Assert.assertFalse(androidCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to CC
        androidCheckoutScreen.get().pressCreditCardPaymentOption();
        Assert.assertTrue(androidCardSelectionModal.get().isModalDisplayed());
        androidCardSelectionModal.get().pressAddNewCardBtn();

        // Implement assertion logic to ensure the total is not equals 0 and Due Amount is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPreviousDueAmountContentDescription());
        Assert.assertTrue(androidCheckoutScreen.get().DueAmountIsDisplayed());

        Assert.assertNotEquals(androidCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void BalanceWhenNegativeAndCODIsSelected () {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("-100.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(androidCheckoutScreen.get().isPageDisplayed());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // Check if the balance toggle is OFF
        Assert.assertFalse(androidCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to CC
        androidCheckoutScreen.get().pressCreditCardPaymentOption();
        Assert.assertTrue(androidCardSelectionModal.get().isModalDisplayed());
        androidCardSelectionModal.get().pressAddNewCardBtn();

        // Implement assertion logic to ensure the total is not equals 0 and Due Amount is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPreviousDueAmountContentDescription());
        Assert.assertTrue(androidCheckoutScreen.get().DueAmountIsDisplayed());

        Assert.assertNotEquals(androidCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }
}
