package customerApp.android.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class PlaceOrderTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateDeliveryTimeIs60MinutesOrLess() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        //add Address
        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        defaultTestData.get().getTestOrder().setExpectedDeliveryTime(
                testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy") + " "
                        + (androidCheckoutScreen.get().getInstantDeliveryExpectedTime().contains("before")
                                ? androidCheckoutScreen.get().getInstantDeliveryExpectedTime()
                                : "in " + androidCheckoutScreen.get().getInstantDeliveryExpectedTime()));

        //click on place order
        androidCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(androidOrderSuccessScreen.get().isPageDisplayed());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidOrderSuccessScreen.get().getScrollableContentContainer()
                , "down"
                , androidOrderSuccessScreen.get().getDeliveryDateTimeContainerContentDescription());

        defaultTestData.get().getTestOrder().setActualDeliveryTime(
                androidOrderSuccessScreen.get().getDeliveryDateValue());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingOrderWithCodAndWithoutBalance() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //Validate that checkout screen is opened
        androidCheckoutScreen.get().isCheckoutPageDisplayed();

        //Scroll to payments section
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // select the Cod payment method option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // switch off UseMyBalance Toggle
        androidCheckoutScreen.get().pressUseMyBalanceToggle();

        // place order
        androidCheckoutScreen.get().pressPlaceOrderBtn();

        // validate order success screen is displayed
        Assert.assertTrue(androidOrderSuccessScreen.get().isPageDisplayed(),
                "order success page doesn't appear");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingNowOrdersAndSelectASlot() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //select a delivery slot
        androidCheckoutScreen.get().pressNowDeliveryScheduleBtn();
        androidCheckoutScreen.get().selectFirstAvailableDeliveryTimeSlot();

        //click on place order
        androidCheckoutScreen.get().pressPlaceOrderBtn();

        // validate order success screen is displayed
        Assert.assertTrue(androidOrderSuccessScreen.get().isPageDisplayed(),
                "order success page doesn't appear");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validatePlacingTomorrowOrdersAndSelectASlot() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Switch to Tomorrow
        androidHomeScreen.get().pressNowTomorrowTopBanner();
        androidNowAndTomorrowModal.get().changeServeType("tomorrow");

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //select a delivery slot
        androidCheckoutScreen.get().pressSelectTomorrowDeliveryTime();
        androidCheckoutScreen.get().selectFirstAvailableDeliveryTimeSlot();

        //click on place order
        androidCheckoutScreen.get().pressPlaceOrderBtn();

        // validate order success screen is displayed
        Assert.assertTrue(androidOrderSuccessScreen.get().isPageDisplayed(),
                "order success page doesn't appear");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatOrderExistsInOrdersListAfterCreation() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        //add Address
        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //scroll to select cash on delivery option
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get(),
                androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        //select cash on delivery option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        //press checkout button
        androidCheckoutScreen.get().pressPlaceOrderBtn();

        //navigate to home screen
        androidOrderSuccessScreen.get().pressBackBtn();

        //press more tab
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();

        //navigate to activity history screen
        androidMoreScreen.get().pressActivityHistoryBtn();

        // assert that activity history page is displayed
        Assert.assertTrue(androidActivityHistoryScreen.get().isPageDisplayed());

        //Assert that Order Card is displayed
        Assert.assertEquals(androidActivityHistoryScreen.get().getOrderHistoryCardCount(), 1,
                "Expected only one order card to be displayed, but found " +
                        androidActivityHistoryScreen.get().getOrderHistoryCardCount());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateAddingTipWithCodThroughCheckout() {

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //Validate that checkout screen is opened
        androidCheckoutScreen.get().isCheckoutPageDisplayed();

        //Scroll to payments section
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getUseMyBalanceContentDescription());

        // select the Cod payment method option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // Turn off Use MyBalance toggle
        androidCheckoutScreen.get().pressUseMyBalanceToggle();

        // Scroll till Tipping section
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getTippingSectionContentDescription());

        // select Tip Amount
        androidCheckoutScreen.get().selectTipAmountElement(5);

        // scroll to end of checkout screen
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getDeliveryFeesContentDescription());

        // Check if the tip amount on order total section equals selected tip amount
        Assert.assertEquals(androidCheckoutScreen.get().getTipValue(),5);

        // Check if the tip amount is added to the total order amount
        Assert.assertEquals(androidCheckoutScreen.get().getOrderSubTotalValue()
                        +androidCheckoutScreen.get().getDeliveryFeesValue
                        (androidCheckoutScreen.get().getDeliveryFeesUiElement().getText())
                        +androidCheckoutScreen.get().getTipValue(),
                androidCheckoutScreen.get().getOrderGrandTotalValue());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateAddingTipWithCCThroughCheckout() {

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //Validate that checkout screen is opened
        androidCheckoutScreen.get().isCheckoutPageDisplayed();

        //Scroll to payments section
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getUseMyBalanceContentDescription());

        // select the CC payment method option and turn off Use MyBalance toggle
        androidCheckoutScreen.get().pressCreditCardPaymentOption();
        androidCardSelectionModal.get().pressAddNewCardBtn();
        androidCheckoutScreen.get().pressUseMyBalanceToggle();

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getTippingSectionContentDescription());

        // select Tip Amount
        androidCheckoutScreen.get().selectTipAmountElement(5);

        // scroll to end of checkout screen
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getDeliveryFeesContentDescription());

        // Check if the tip amount on order total section equals selected tip amount
        Assert.assertEquals(androidCheckoutScreen.get().getTipValue(),5);

        // Check if the tip amount is added to the total order amount
        Assert.assertEquals(androidCheckoutScreen.get().getOrderSubTotalValue()
                        +androidCheckoutScreen.get().getDeliveryFeesValue
                        (androidCheckoutScreen.get().getDeliveryFeesUiElement().getText())
                        +androidCheckoutScreen.get().getTipValue(),
                androidCheckoutScreen.get().getOrderGrandTotalValue());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateCustomTipAmountLimit()
    {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //Validate that checkout screen is opened
        androidCheckoutScreen.get().isCheckoutPageDisplayed();

        //Scroll to payments section
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // select the Cod payment method option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // Scroll to the tipping section
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getTippingSectionContentDescription());

        // Press Custom Tip Amount and validate Amount Limit
        androidCheckoutScreen.get().pressCustomTipOption();
        androidCheckoutScreen.get().enterCustomTipAmount("999");
        Assert.assertTrue(androidCheckoutScreen.get().
                isTipLimitErrorMsgDisplayed(),"Tip Limit Message Doesn't Appear");

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateInsertTextToTheCustomTipField()
    {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //Validate that checkout screen is opened
        androidCheckoutScreen.get().isCheckoutPageDisplayed();

        //Scroll to payments section
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // select the Cod payment method option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // scroll to the tipping section
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getTippingSectionContentDescription());

        // Press Custom Tip field and insert text with numbers
        androidCheckoutScreen.get().pressCustomTipOption();
        androidCheckoutScreen.get().enterCustomTipAmount("randomText+100");

        // Validate that the Custom Tip field accepts numbers only
        Assert.assertTrue(androidCheckoutScreen.get().getCustomTipTextValue()
                .equalsIgnoreCase("100"));

    }
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("web"), @Tag("delivery-capacity-management")})
    public void CheckUserCanPlaceInstantOrderInKSA(){

        //add capacity to an instant slot
     webLoginPage.get().goToLoginPage();
     webLoginPage.get().changeCountryCode();
     webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
             , defaultTestData.get().getAdminUser().getBypassScriptPassword());
     Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
     webDeliveryCapacityManagementPage.get().goToPage();
     webDeliveryCapacityManagementPage.get().choosefp();
     webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
     Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
     webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "5");
     Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
             defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 5);
     Assert.assertTrue(deliveryCapacityManagementApiClient.get().getAllSlotsData(
             defaultTestData.get().getAdminUser(),
             defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getInstantFlag());

        // Register with valid phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());
        //close bottom sheet
        androidAddressSelectionScreen.get().pressCloseIcon();
        // Now is the default
        Assert.assertTrue(androidHomeScreen.get().isInstantDeliveryTimeDisplayed());

        //Scroll until Category is displayed
    androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
             androidDriver.get()
             , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
             , "down"
             , androidHomeScreen.get().getCategoryContentDescription(
                     defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

     //Tab on the category
     androidHomeScreen.get().pressCategory(
             defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

     //add item to the cart and then click on cart icon
     androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
             , androidCategoryScreen.get().getProductsListScrollableContentContainer()
             , "down"
             , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
     androidCategoryScreen.get().pressAddToCartIconByIndex(1);
     androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //Validate that checkout screen is opened
        androidCheckoutScreen.get().isCheckoutPageDisplayed();

        //Scroll to payments section
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // select the Cod payment method option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // place order
        androidCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderDetailsPageDisplayed());
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderPlacedSuccessfullyDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckUserCanPlaceScheduledOrderInKSA() {

        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());
        //close bottom sheet
        androidAddressSelectionScreen.get().pressCloseIcon();
        // Now is the default
        Assert.assertTrue(androidHomeScreen.get().isInstantDeliveryTimeDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //select a delivery slot
        androidCheckoutScreen.get().pressNowDeliveryScheduleBtn();
        Assert.assertNotEquals(androidCheckoutScreen.get().getDisplayedSlotsCount(), 0);
        androidCheckoutScreen.get().selectFirstAvailableDeliveryTimeSlot();

        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        //place order
        androidCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderDetailsPageDisplayed());
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderPlacedSuccessfullyDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckUserCanPlaceTomorrowOrderInKSA() {

        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());

        // Select Tomorrow option
        androidAddressSelectionScreen.get().pressTomorrowOptionBtn();
        Assert.assertTrue(androidHomeScreen.get().isTomorrowDeliveryTimeDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //select a delivery slot
        androidCheckoutScreen.get().pressSelectTomorrowDeliveryTime();
        Assert.assertNotEquals(androidCheckoutScreen.get().getDisplayedSlotsCount(), 0);
        androidCheckoutScreen.get().selectFirstAvailableDeliveryTimeSlot();

        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        //place order
        androidCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderDetailsPageDisplayed());
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderPlacedSuccessfullyDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckMiniTrackerIsDisplayedAfterNowOrder(){
        //  DCM For KSA not migrated yet but it will be migrated soon, so I added it for the future
        //add capacity to an instant slot
     /*   webLoginPage.get().goToLoginPage();
        webLoginPage.get().changeCountryCode();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "5");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 5);
        Assert.assertTrue(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getInstantFlag());*/

        // Register with valid phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());
        //close bottom sheet
        androidAddressSelectionScreen.get().pressCloseIcon();
        // Now is the default
        Assert.assertTrue(androidHomeScreen.get().isInstantDeliveryTimeDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //Validate that checkout screen is opened
        androidCheckoutScreen.get().isCheckoutPageDisplayed();

        //Scroll to payments section
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        // select the Cod payment method option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        //place order and check mini tracker displayed
        androidCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderDetailsPageDisplayed());
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderPlacedSuccessfullyDisplayed());
        androidOrderDetailsScreen.get().pressBackBtn();
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        Assert.assertTrue(androidHomeScreen.get().isMiniTrackerDisplayed());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckMiniTrackerIsDisplayedAfterScheduledOrder() {
        //  DCM For KSA not migrated yet but it will be migrated soon, so I added it for the future
        //add capacity to an instant slot
     /*   webLoginPage.get().goToLoginPage();
        webLoginPage.get().changeCountryCode();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webDeliveryCapacityManagementPage.get().goToPage();
        webDeliveryCapacityManagementPage.get().choosefp();
        webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
        webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "5");
        Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 5);
        Assert.assertTrue(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                defaultTestData.get().getAdminUser(),
                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getInstantFlag());*/

        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());
        //close bottom sheet
        androidAddressSelectionScreen.get().pressCloseIcon();
        // Now is the default
        Assert.assertTrue(androidHomeScreen.get().isInstantDeliveryTimeDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //select a delivery slot
        androidCheckoutScreen.get().pressNowDeliveryScheduleBtn();
        Assert.assertNotEquals(androidCheckoutScreen.get().getDisplayedSlotsCount(), 0);
        androidCheckoutScreen.get().selectFirstAvailableDeliveryTimeSlot();

        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        //place order and check mini tracker displayed
        androidCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderDetailsPageDisplayed());
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderPlacedSuccessfullyDisplayed());
        androidOrderDetailsScreen.get().pressBackBtn();
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        Assert.assertTrue(androidHomeScreen.get().isMiniTrackerDisplayed());
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckMiniTrackerIsDisplayedAfterTomorrowOrder() {

        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidAddressSelectionScreen.get().isSelectDeliveryOptionsHeaderIsDisplayed());

        // Select Tomorrow option
        androidAddressSelectionScreen.get().pressTomorrowOptionBtn();
        Assert.assertTrue(androidHomeScreen.get().isTomorrowDeliveryTimeDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getTomorrowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //click on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //select a delivery slot
        androidCheckoutScreen.get().pressSelectTomorrowDeliveryTime();
        Assert.assertNotEquals(androidCheckoutScreen.get().getDisplayedSlotsCount(), 0);
        androidCheckoutScreen.get().selectFirstAvailableDeliveryTimeSlot();

        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        //place order and check mini tracker displayed
        androidCheckoutScreen.get().pressPlaceOrderBtn();
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderDetailsPageDisplayed());
        Assert.assertTrue(androidOrderDetailsScreen.get().isOrderPlacedSuccessfullyDisplayed());
        androidOrderDetailsScreen.get().pressBackBtn();
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        Assert.assertTrue(androidHomeScreen.get().isMiniTrackerDisplayed());
    }

}
