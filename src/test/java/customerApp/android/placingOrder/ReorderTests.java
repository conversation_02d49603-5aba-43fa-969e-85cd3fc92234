package customerApp.android.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class ReorderTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateReOrderAfterCancelledOrders() {

        // Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        //add Address
        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        Assert.assertTrue(androidCheckoutScreen.get().isPageDisplayed());

        //scroll to select cash on delivery option
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get(),
                androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        //select cash on delivery option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        //press checkout button
        androidCheckoutScreen.get().pressPlaceOrderBtn();
        //navigate to home screen
        androidDriver.get().navigate().back();
        //press more tab
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();

        //navigate to activity history screen
        androidMoreScreen.get().pressActivityHistoryBtn();

        // assert that activity history page is displayed
        Assert.assertTrue(androidActivityHistoryScreen.get().isPageDisplayed());

        // Assert that Order Card is displayed
        Assert.assertEquals(androidActivityHistoryScreen.get().getOrderHistoryCardCount(), 1,
                "Expected only one order card to be displayed, but found " +
                        androidActivityHistoryScreen.get().getOrderHistoryCardCount());

        //open order details screen
        androidActivityHistoryScreen.get().selectOrdersCard(0);
        //wait for order status in Order Details screen to be displayed
        androidOrderDetailsScreen.get().isOderStatusDisplayed();
        //scroll to cancel button
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get(),
                androidOrderDetailsScreen.get().getScrollableContentContainer()
                , "down"
                , androidOrderDetailsScreen.get().getCancelButtonContentDescription());
        //cancel the order
        androidOrderDetailsScreen.get().pressCancelBtn();
        androidOrderDetailsScreen.get().pressConfirmCancelBtn();

        // wait for Cancellation Reason Bottom Sheet to be Displayed
        androidOrderDetailsScreen.get().isCancellationReasonsBottomSheetDisplayed();
        androidOrderDetailsScreen.get().selectCancellationReason(2);
        androidOrderDetailsScreen.get().pressSubmitCancellationReasonBtn();

        //Assert that order is cancelled
        Assert.assertTrue(androidOrderDetailsScreen.get().isCancelledTextDisplayed(), "Order Is not Cancelled ");
        //Assert that ReOrder Btn Displayed
        Assert.assertTrue(androidOrderDetailsScreen.get().isReOrderBtnDisplayed(), "Reorder Button is not displayed");
        // press reorder button
        androidOrderDetailsScreen.get().pressReOrderBtn();

        Assert.assertFalse(androidCartScreen.get().isCartEmpty(),
                "Expected products added to cart, but it's not added");
        //Reorder
        androidCartScreen.get().pressGoToCheckoutBtn();
        //Assert that checkout screen is displayed
        Assert.assertTrue(androidCheckoutScreen.get().isPageDisplayed());
        androidCheckoutScreen.get().pressPlaceOrderBtn();
        //navigate to home screen
        androidDriver.get().navigate().back();
        //press more tab
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();

        //navigate to activity history screen
        androidMoreScreen.get().pressActivityHistoryBtn();
        // assert that activity history page is displayed
        Assert.assertTrue(androidActivityHistoryScreen.get().isPageDisplayed());

        //Assert that the cancelled and the placed Orders Cards are displayed
        Assert.assertEquals(androidActivityHistoryScreen.get().getOrderHistoryCardCount(), 2,
                "Expected only two order card to be displayed, but found " +
                        androidActivityHistoryScreen.get().getOrderHistoryCardCount());

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateReorderIsNotShownForProcessingOrders() {
        // Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        //add item to the cart and then click on cart icon
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCategoryScreen.get().getProductsListScrollableContentContainer()
                , "down"
                , androidCategoryScreen.get().getProductAddToCartBtnContentDescription());
        androidCategoryScreen.get().pressAddToCartIconByIndex(1);
        androidCategoryScreen.get().pressCartIcon();

        //press on go to checkout button
        androidCartScreen.get().pressGoToCheckoutBtn();

        //add Address
        // Enter address details and submit the form
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //Assert that checkout screen is displayed
        Assert.assertTrue(androidCheckoutScreen.get().isPageDisplayed());

        //scroll to select cash on delivery option
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get(),
                androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        //  select cash on delivery option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        //press checkout button
        androidCheckoutScreen.get().pressPlaceOrderBtn();
        //navigate to home screen
        androidDriver.get().navigate().back();
        //press more tab
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        //navigate to activity history screen
        androidMoreScreen.get().pressActivityHistoryBtn();
        // assert that activity history page is displayed
        Assert.assertTrue(androidActivityHistoryScreen.get().isPageDisplayed());
        // Assert that Order Card is displayed
        Assert.assertEquals(androidActivityHistoryScreen.get().getOrderHistoryCardCount(), 1,
                "Expected only one order card to be displayed, but found " +
                        androidActivityHistoryScreen.get().getOrderHistoryCardCount());

        //open order details screen
        androidActivityHistoryScreen.get().selectOrdersCard(0);
        androidOrderDetailsScreen.get().isOderStatusDisplayed();
        //Assert that reorder button is not displayed
        Assert.assertFalse(androidOrderDetailsScreen.get().isReOrderBtnDisplayed(),
                "Expected Reorder Btn will not be shown, but shown ");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateUserCanReorderAfterCompletingOrders() {
        // Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "15"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Complete the created order
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered",
                600f,
                false);
        //Login with the same user we created an order for
        androidCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        androidLandingScreen.get().pressAuthHyperLink();

        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().takeActionIfAnyUnknownLocationViewElementsDisplayed(2);

        Assert.assertTrue(androidHomeScreen.get().isHomePageDisplayed());
        //Tab on more and go to order history
        androidHomeScreen.get().pressMoreTabBtn();
        //navigate to activity history screen
        androidMoreScreen.get().pressActivityHistoryBtn();
        // assert that activity history page is displayed
        Assert.assertTrue(androidActivityHistoryScreen.get().isPageDisplayed());
        // Assert that Order Card is displayed
        Assert.assertEquals(androidActivityHistoryScreen.get().getOrderHistoryCardCount(), 1,
                "Expected only one order card to be displayed, but found " +
                        androidActivityHistoryScreen.get().getOrderHistoryCardCount());

        //open order details screen
        androidActivityHistoryScreen.get().selectOrdersCard(0);

        //wait for  delivered to be displayed
        androidOrderDetailsScreen.get().isOrderDelivered();

        //Assert that reorder button is displayed
        Assert.assertTrue(androidOrderDetailsScreen.get().isReOrderBtnDisplayed(),
                "Reorder Button should be shown but it's not");

        androidOrderDetailsScreen.get().pressReOrderBtn();
        //Assert that cart is not empty
        Assert.assertFalse(androidCartScreen.get().isCartEmpty(),
                "Expected products added to cart, but it's not added");
        //Reorder
        androidCartScreen.get().pressGoToCheckoutBtn();
        //Assert that checkout screen is displayed
        Assert.assertTrue(androidCheckoutScreen.get().isPageDisplayed());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get(),
                androidCheckoutScreen.get().getScrollableContentContainer()
                , "down"
                , androidCheckoutScreen.get().getPaymentSectionContentDescription());

        //  select cash on delivery option
        androidCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        //click on place order
        androidCheckoutScreen.get().pressPlaceOrderBtn();
        //navigate to home screen
        androidDriver.get().navigate().back();
        //press more tab
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        //navigate to activity history screen
        androidMoreScreen.get().pressActivityHistoryBtn();
        // assert that activity history page is displayed
        Assert.assertTrue(androidActivityHistoryScreen.get().isPageDisplayed());

        //Assert that the cancelled and the placed Orders Cards are displayed
        Assert.assertEquals(androidActivityHistoryScreen.get().getOrderHistoryCardCount(), 2,
                "Expected only two order card to be displayed, but found " +
                        androidActivityHistoryScreen.get().getOrderHistoryCardCount());
    }
}
