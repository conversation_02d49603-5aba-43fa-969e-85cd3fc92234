package customerApp.android.search;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class SearchResultsTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void SearchResultsAreRelevantToValidSearchKeyword() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        //Open search screen and check the screen is opened
        androidHomeScreen.get().pressSearchTabBtn();

        // search with valid keyword
        androidSearchScreen.get().isPageDisplayed();
        androidSearchScreen.get().enterKeywordForSearch(defaultTestData.get().getCustomerAppTestSession()
                .getNowOnlyProduct().getName());

        Assert.assertFalse(androidSearchResultScreen.get().ifNoResultFoundLabelReturned()
                , "No Results Found Page Is Returned");

        Assert.assertTrue(androidSearchResultScreen.get().checkProductResultsMatchKeyword(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowOnlyProduct().getName())
                , "There is an item doesn't match search keyword");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void NoSearchResultsAreDisplayedForNotValidSearchKeyword() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        //Open search screen and insert invalid search keyword
          androidHomeScreen.get().pressSearchTabBtn();
          androidSearchScreen.get().enterKeywordForSearch("AAAA" );

        //  Validate that no result for this invalid keyword
          Assert.assertTrue(androidSearchResultScreen.get().ifNoResultFoundLabelReturned()
                  ,"Some products are returned");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void ValidateAddToFavoriteOption() {
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        //Open search screen and insert valid search keyword
        androidHomeScreen.get().pressSearchTabBtn();
        androidSearchScreen.get().enterKeywordForSearch(defaultTestData.get().getCustomerAppTestSession()
                .getNowOnlyProduct().getName());

        // add item# to favorites list
        androidSearchResultScreen.get().pressFavIconOfItem(1);

        // go to favorites list
        androidSearchResultScreen.get().pressViewListBtn();

        // validate that the item added successfully to the favorites list
        Assert.assertFalse(androidFavoritesScreen.get().isFavListEmpty()
                ,"Item doesn't exist in favorites list");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateCategoryIsDisplayedInSearchResults() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        //Open search screen and check the screen is opened
        androidHomeScreen.get().pressSearchTabBtn();

        // search with valid keyword
        androidSearchScreen.get().enterKeywordForSearch(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getName());

        Assert.assertTrue(androidSearchResultScreen.get().isCategoryDisplayedInSearchResults(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())
                ,"category not returned");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
   public void validateSubCategoryIsDisplayedInSearchResults() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        //Open search screen and check the screen is opened
        androidHomeScreen.get().pressSearchTabBtn();

        // search with valid keyword
        androidSearchScreen.get().enterKeywordForSearch(defaultTestData.get().getCustomerAppTestSession()
                .getNowOnlySubCategory().getName());

        Assert.assertTrue(androidSearchResultScreen.get().isSubCategoryDisplayedInSearchResults(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()
        )," subcategory not returned");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateProductIsDisplayedInSearchResults() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        //Open search screen and check the screen is opened
        androidHomeScreen.get().pressSearchTabBtn();

        // search with valid keyword
        androidSearchScreen.get().enterKeywordForSearch(defaultTestData.get().getCustomerAppTestSession()
                .getNowOnlyProduct().getName());

        Assert.assertTrue(androidSearchResultScreen.get().isProductDisplayedInSearchResults(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMysqlId()
        ),"product not returned");
    }
}
