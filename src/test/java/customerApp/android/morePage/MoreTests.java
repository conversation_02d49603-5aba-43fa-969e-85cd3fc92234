package customerApp.android.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.springframework.context.annotation.Description;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class MoreTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    @Description("Check the more screen tabs with new user")
    public void validateMoreScreenRedirectionsAfterRegister(){
        // Register
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Click on more tab button
        androidHomeScreen.get().pressMoreTabBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageTabsDisplayed());

        // Navigating to Active History screen
        androidMoreScreen.get().pressActivityHistoryBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        Assert.assertTrue(androidActivityHistoryScreen.get().areOrdersAndBillsTabsDisplayed());
        androidActivityHistoryScreen.get().pressActivityHistoryBackButton();

        // Navigating to Favorites screen
        // ToDo: Add support for checking disabled Favorites button in more screen after register

        // Navigating to Breadfast Reward
        androidMoreScreen.get().pressRewardsBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        Assert.assertTrue(androidBreadfastRewardsScreen.get().isPageDisplayed());
        Assert.assertEquals(androidBreadfastRewardsScreen.get().getBreadfastRewardsTitle()
                ,"Welcome to Breadfast Rewards");
        Assert.assertEquals(androidBreadfastRewardsScreen.get().getPointsTitle(),"Points");
        androidBreadfastRewardsScreen.get().pressCloseBtn();

        //    Navigating to Free Credits tab
        //ToDo: Add support for checking disabled free-Credits button in more screen after register

        //    Navigating to Account Settings tab
        androidMoreScreen.get().pressAccountSettingsBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        Assert.assertTrue(androidAccountSettingsScreen.get().isAccountSettingsRelatedButtonsDisplayed());
        androidAccountSettingsScreen.get().pressAccountSettingsBackBtn();

        //    Navigating to Help tab
        androidMoreScreen.get().pressHelpBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        androidHelpScreen.get().pressFreeCreditBackBtn();

        //    Navigating to Talk to Us tab
        androidMoreScreen.get().pressTalkToUsBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        Assert.assertTrue(androidTalkToUsScreen.get().isPageDisplayed());
        androidTalkToUsScreen.get().pressBackBtn();
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    @Description("Check the more screen tabs with valid user")
    public void validateMoreScreenRedirectionsAfterLoginWithExistingOrders(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Logout
        androidTestsExecutionHelper.get().logout(androidDriver.get(), defaultTestData.get(), androidHomeScreen.get(),
                androidMoreScreen.get());

        //Location Permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        //Set address Screen -> Skip it
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressLoginBtn();

        //Login
        androidTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                androidSetAddressScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //      Click on more tab button
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().isPageTabsDisplayed();

        // Navigating to Active History screen
        androidMoreScreen.get().pressActivityHistoryBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        Assert.assertTrue(androidActivityHistoryScreen.get().isPageDisplayed());
        Assert.assertTrue(androidActivityHistoryScreen.get().areOrdersAndBillsTabsDisplayed());
        androidActivityHistoryScreen.get().pressActivityHistoryBackButton();

        //    Navigating to Favorites screen
        androidMoreScreen.get().pressFavoritesBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        Assert.assertTrue(androidFavoritesScreen.get().isPageDisplayed());
        androidFavoritesScreen.get().pressBackBtn();

        //    Navigating to Breadfast Reward
        androidMoreScreen.get().pressRewardsBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        Assert.assertTrue(androidBreadfastRewardsScreen.get().isPageDisplayed());
        androidBreadfastRewardsScreen.get().pressCloseBtn();

        //    Navigating to Free Credits tab
        androidFreeCreditScreen.get().pressFreeCreditBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        androidFreeCreditScreen.get().pressFreeCreditBackBtn();

        //    Navigating to Account Settings tab
        androidMoreScreen.get().pressAccountSettingsBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        Assert.assertTrue(androidAccountSettingsScreen.get().isAccountSettingsRelatedButtonsDisplayed());
        androidAccountSettingsScreen.get().pressAccountSettingsBackBtn();

        //    Navigating to Help tab
        androidMoreScreen.get().pressHelpBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        androidHelpScreen.get().pressFreeCreditBackBtn();

        //    Navigating to Talk to Us tab
        androidMoreScreen.get().pressTalkToUsBtn();
        Assert.assertTrue(androidMoreScreen.get().isPageDismissed());
        Assert.assertTrue(androidTalkToUsScreen.get().isPageDisplayed());
        androidTalkToUsScreen.get().pressBackBtn();
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void changeLanguage(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());

        //Navigate to home screen
        androidHomeScreen.get().isHomePageDisplayed();

        //Change app language to arabic
        androidTestsExecutionHelper.get().changeAppLanguage(androidHomeScreen.get(),androidMoreScreen.get(),
                androidChooseLanguageModal.get(),"ar");
        Assert.assertTrue(androidHomeScreen.get().isPageHeaderArDisplayed(),"التوصيل إلى");

    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void changeCountry(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());

        //Navigate to home screen
        androidHomeScreen.get().isHomePageDisplayed();

        //Change app country to ksa
        androidTestsExecutionHelper.get().changeAppCountry(androidHomeScreen.get(),androidLandingScreen.get(),
                androidMoreScreen.get(), androidChooseCountryModal.get(),"ksa");
    }
}
