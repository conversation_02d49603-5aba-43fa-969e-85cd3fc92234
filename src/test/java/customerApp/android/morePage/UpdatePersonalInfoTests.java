package customerApp.android.morePage;

import base.BaseTest;
import helpers.factories.dataFactories.OtpFactory;
import helpers.factories.dataFactories.UserDataFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.annotations.Test;

@Test
public class UpdatePersonalInfoTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateTheUpdateUserInfoIsWorkingCorrectly(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //check if home page is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //Go To account settings page
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().dismissCoachMarksIfDisplayed();
        androidMoreScreen.get().pressAccountSettingsBtn();

        // Change user's first and last name
        defaultTestData.get().setRandomTestUser(new UserDataFactory(configs.get())
                .generateRandomUserDetails(defaultTestData.get().getRandomTestUser()));

        // press on update personal information
        androidAccountSettingsPage.get().pressUpdatePerInfoBtn();

        androidUpdateAccountSettingScreen.get().enterFirstName(defaultTestData.get().getRandomTestUser().getFirstName());
        androidUpdateAccountSettingScreen.get().enterLastName(defaultTestData.get().getRandomTestUser().getLastName());
        androidUpdateAccountSettingScreen.get().pressSaveChangesBtn();

        //ToDo: Add assertions (After information update, logout & login again then validate info is added correctly
        // to the screen as previously entered.)
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void validateTheUpdatePhoneNumberIsWorkingCorrectly(){

        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //check if home page is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //Go To account settings page
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().dismissCoachMarksIfDisplayed();
        androidMoreScreen.get().pressAccountSettingsBtn();

        // press on update phone number
        androidAccountSettingsPage.get().pressUpdatePerInfoBtn();

        //Change phone Number
        defaultTestData.get().setRandomTestUser(new UserDataFactory(configs.get())
                .generateRandomUserMobileNumber(defaultTestData.get().getRandomTestUser()));

        androidUpdateAccountSettingScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());
        androidUpdateAccountSettingScreen.get().pressSaveChangesBtn();

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get()).enterOtpInTextField(
                androidUpdatePhoneNumberOTPVerificationScreen.get().getOtpTxtField(),
                defaultTestData.get(),
                "updateAccount",
                defaultTestData.get().getRandomTestUser()));

        androidUpdatePhoneNumberOTPVerificationScreen.get().pressUpdatePhoneNumber();

        //ToDo: Add assertions (After updating the phone number -> Logout then login using the new phone number)
    }
}
