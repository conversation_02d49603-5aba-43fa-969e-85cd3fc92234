package customerApp.android.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class MyAddressTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void manageAddAddressAfterRegistration() {
        // Register
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Click on more tab button
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressAccountSettingsBtn();
        androidAccountSettingsScreen.get().pressManageAddressesTab();

        //Assert that "Add new Address Button is displayed
        Assert.assertTrue(androidMyAddressesScreen.get().isNewAddressBtnDisplayed());
        androidMyAddressesScreen.get().pressAddNewAddressButton();

        //Dismess location modal if displayed
        androidDisabledLocationAccess.get().dismissDisabledLocationAccessModal();

        //Confirm Default selected Address
        androidTestsExecutionHelper.get().searchByTextAndSelectASpecificLocation(androidDriver.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get());

        //Filling the Address Form (Address Label , Address Details , Flat Number , Floor Number , etc ...)
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());

        // Save address and dismiss modal
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        // Assert on My Addresses Page , That header is displayed after address creation
        Assert.assertTrue(androidMyAddressesScreen.get().isMyAddressesHeaderDisplayed());

        //Assert on the addresses count
        Assert.assertEquals(androidMyAddressesScreen.get().getAddressesCount(), 1, "Element count is not as expected");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void deletedNewlyAddedAddress() {
        // Register
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Click on more tab button
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressAccountSettingsBtn();
        androidAccountSettingsScreen.get().pressManageAddressesTab();

        // Assert that "Add new Address Button is displayed
        Assert.assertTrue(androidMyAddressesScreen.get().isNewAddressBtnDisplayed());
        androidMyAddressesScreen.get().pressAddNewAddressButton();

        //Dismess location modal
        androidDisabledLocationAccess.get().dismissDisabledLocationAccessModal();

        //Confirm Default selected Address
        androidTestsExecutionHelper.get().searchByTextAndSelectASpecificLocation(androidDriver.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get());

        //Filling the Address Form (Address Label , Address Details , Flat Number , Floor Number , etc ...)
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());

        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        // Add a second address to be deleted
        androidMyAddressesScreen.get().pressAddNewAddressButton();
        //Confirm Default selected Address
        androidTestsExecutionHelper.get().searchByTextAndSelectASpecificLocation(androidDriver.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get());

        //Fill Second Address Details & Save
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription()
        );

        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        // Delete Second Address (Non-Default Address)

        androidMyAddressesScreen.get().clickAddressRecord(2);

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());

        //Assert that Delete Button is displayed to the user
        Assert.assertTrue(androidCreateAddressScreen.get().isDeleteButtonDisplayed());

        //Confirm Address Deletion
        androidCreateAddressScreen.get().pressDeleteAddressButton();
        androidAddressCreateSuccessModal.get().dismissModal();

        //Assert number of addresses after deletion
        Assert.assertEquals(androidMyAddressesScreen.get().getAddressesCount(), 1, "Element count is not as expected");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void saveAddAddressAfterUpdate() {
        // Register
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Click on more tab button
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressAccountSettingsBtn();
        androidAccountSettingsScreen.get().pressManageAddressesTab();

        //Assert that "Add new Address Button is displayed
        Assert.assertTrue(androidMyAddressesScreen.get().isNewAddressBtnDisplayed());
        androidMyAddressesScreen.get().pressAddNewAddressButton();

        //Dismess location modal
        androidDisabledLocationAccess.get().dismissDisabledLocationAccessModal();

        //Confirm Default selected Address
        androidTestsExecutionHelper.get().searchByTextAndSelectASpecificLocation(androidDriver.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get());

        //Filling the Address Form (Address Label , Address Details , Flat Number , Floor Number , etc ...)
        androidCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());

        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());

        // Save address and dismiss modal
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        // Open address Record again to Edit
        androidMyAddressesScreen.get().clickAddressRecord(1);

        //Update Address Info
        androidCreateAddressScreen.get().enterAddressLabel(defaultTestData.get().getRandomTestUser().getAddress().getAddressLabel());
        androidCreateAddressScreen.get().enterDeliveryInstructions(defaultTestData.get().getRandomTestUser().getAddress().getFullAddress());

        //Scroll down to save address button
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidCreateAddressScreen.get().getScrollableContentContainer()
                , "down"
                , androidCreateAddressScreen.get().getSaveAddressBtnContentDescription());

        //Assert Save/Update address button is displayed
        Assert.assertTrue(androidCreateAddressScreen.get().isSaveAddressBtnDisplayed());

        //Save the Address after "Update"
        androidCreateAddressScreen.get().pressSaveAddressBtn();
        androidAddressCreateSuccessModal.get().dismissModal();

        //Assert on the number of address records
        Assert.assertEquals(androidMyAddressesScreen.get().getAddressesCount(), 1, "Element count is not as expected");
    }
}
