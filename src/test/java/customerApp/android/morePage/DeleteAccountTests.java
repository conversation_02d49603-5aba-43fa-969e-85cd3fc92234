package customerApp.android.morePage;

import base.BaseTest;
import helpers.factories.dataFactories.OtpFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class DeleteAccountTests extends BaseTest {
    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void deleteAccountWithValidOtp(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get()
                , androidLandingScreen.get()
                , androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , androidCreateAccountInfoScreen.get()
                , androidRegisterSuccessScreen.get()
                , androidHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        //check if home page is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //Go To account settings page
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().dismissCoachMarksIfDisplayed();
        androidMoreScreen.get().pressAccountSettingsBtn();

        //Check validation of deleting account
        androidAccountSettingsPage.get().pressDeleteAccountBtn();
        Assert.assertTrue(androidDeleteAccountScreen.get().isPageDisplayed());

        androidDeleteAccountScreen.get().selectFirstReasonDisplayedInTheReasonsList();

        //Scroll to continue Btn
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidDeleteAccountScreen.get().getPageScrollableContentContainer()
                , "down"
                , androidDeleteAccountScreen.get().getContinueBtnContentDescription());

        //Press continue Btn
        androidDeleteAccountScreen.get().pressContinueBtn();

        androidVerifyDeleteAccountScreen.get().pressVerifyMobileNumberBtn();

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get()).enterOtpInTextField(
                androidDeleteAccountOTPVerificationScreen.get().getOtpTextField(),
                defaultTestData.get(),
                "deleteAccount",
                defaultTestData.get().getRandomTestUser()));

        androidDeleteAccountOTPVerificationScreen.get().pressTermsAndConditionsCheckbox();
        androidDeleteAccountOTPVerificationScreen.get().pressDeleteAccountBtn();

        Assert.assertTrue(androidDeleteRequestScreen.get().isDeleteConfirmationHeaderDisplayed()
                ,"Success page is not displayed or header is not displayed correctly.");

        androidDeleteRequestScreen.get().clickOnCloseBtn();

        //Location Permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");

        //Set address Screen -> Skip it
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        //Validate the redirection and that More tab is displayed
        androidHomeScreen.get().pressHomeTabBtn();
        androidHomeScreen.get().isHomePageDisplayed();
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().pressLoginBtn();

        //Login
        androidTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get(),
                androidPhoneNumberScreen.get()
                , androidCountriesListScreen.get()
                , androidOtpVerificationScreen.get()
                , androidSetAddressScreen.get()
                , defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(androidOtpVerificationScreen.get().isGreetingAfterDeletedAccountLoginDisplayed()
                ,"Greeting message is not displayed ");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app")})
    public void deleteAccountWithInValidOtp(){
        //Register with A valid Phone number
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(),
                androidLandingScreen.get(), androidPhoneNumberScreen.get(),
                androidCountriesListScreen.get(), androidOtpVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), androidCreateAccountInfoScreen.get(),
                androidRegisterSuccessScreen.get(), androidHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //check if home page is displayed
        androidHomeScreen.get().isHomePageDisplayed();

        //Go To account settings page
        androidHomeScreen.get().pressMoreTabBtn();
        androidMoreScreen.get().dismissCoachMarksIfDisplayed();
        androidMoreScreen.get().pressAccountSettingsBtn();

        //Check validation of deleting account
        androidAccountSettingsPage.get().pressDeleteAccountBtn();
        Assert.assertTrue(androidDeleteAccountScreen.get().isPageDisplayed());

        androidDeleteAccountScreen.get().selectFirstReasonDisplayedInTheReasonsList();

        //Scroll to continue Btn
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(androidDriver.get()
                , androidDeleteAccountScreen.get().getPageScrollableContentContainer()
                , "down"
                , androidDeleteAccountScreen.get().getContinueBtnContentDescription());

        androidDeleteAccountScreen.get().pressContinueBtn();

        androidVerifyDeleteAccountScreen.get().pressVerifyMobileNumberBtn();

        androidDeleteAccountOTPVerificationScreen.get().enterOtp("1234");
        androidDeleteAccountOTPVerificationScreen.get().pressTermsAndConditionsCheckbox();
        androidDeleteAccountOTPVerificationScreen.get().pressDeleteAccountBtn();

        Assert.assertTrue(androidDeleteAccountOTPVerificationScreen.get().isErrorMsgTextDisplayed()
                , "Error message is displayed correctly.");
    }
}
