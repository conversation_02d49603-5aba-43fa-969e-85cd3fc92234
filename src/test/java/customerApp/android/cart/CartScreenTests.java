package customerApp.android.cart;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

public class CartScreenTests extends BaseTest {

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkIncreasingItems() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        // Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        // Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        // Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll horizontally and select the subCategory With the now only product if it's not already selected
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , "right"
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

        androidCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Add items to the cart
        androidCategoryScreen.get().pressProduct(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

       androidCategoryScreen.get().pressCartBtn();

        // Assert the initial item quantity is 1
        assertEquals(androidCartScreen.get().getProductCurrentQtyInCart(1), 1);

        // Capture the total and delivery fees before increasing the quantity
        double totalAndDeliveryBeforeIncrease = androidCartScreen.get().getCurrentTotalAndDelivery();

        // Press the "increase item" button
        androidCartScreen.get().pressIncreaseQtyBtn(1);

        // Assert the item quantity is no longer 1 after the increase
        Assert.assertNotEquals(androidCartScreen.get().getProductCurrentQtyInCart(1), 1);

        // Assert that the total and delivery fees combined have increased after the quantity increase
        double totalAndDeliveryAfterIncrease = androidCartScreen.get().getCurrentTotalAndDelivery();

        // Assert that the total and delivery fees combined have increased after the quantity increase
        Assert.assertNotEquals(totalAndDeliveryBeforeIncrease, totalAndDeliveryAfterIncrease);
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkDecreasingItems() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        // Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        // Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        // Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll horizontally and select the subCategory With the now only product if it's not already selected
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , "right"
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

        androidCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Add items to the cart
        androidCategoryScreen.get().pressProduct(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

        androidCategoryScreen.get().pressCartBtn();

        // Assert the initial item quantity is 1
        assertEquals(androidCartScreen.get().getProductCurrentQtyInCart(1), 1);

        // Press the "increase item" button
        androidCartScreen.get().pressIncreaseQtyBtn(1);

        // Capture the total and delivery fees before decreasing the quantity
        double totalAndDeliveryBeforeDecrease = androidCartScreen.get().getCurrentTotalAndDelivery();

        // Press the "increase item" button
        androidCartScreen.get().pressDecreaseQtyBtn(1);

        // Assert the item quantity is decreased
        Assert.assertNotEquals(androidCartScreen.get().getProductCurrentQtyInCart(1), 1);

        // Assert that the total and delivery fees combined have decreased after the quantity decrease
        double totalAndDeliveryAfterDecrease = androidCartScreen.get().getCurrentTotalAndDelivery();

        // Assert that the total and delivery fees combined have decreased after the quantity decrease
        Assert.assertNotEquals(totalAndDeliveryBeforeDecrease, totalAndDeliveryAfterDecrease);
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkWhenDecreasingWithDeleteQty() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        // Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        // Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        // Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll horizontally and select the subCategory With the now only product if it's not already selected
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , "right"
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

        androidCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Add items to the cart
        androidCategoryScreen.get().pressProduct(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

        androidCategoryScreen.get().pressCartBtn();

        // Assert the initial item quantity is 1
        assertEquals(androidCartScreen.get().getProductCurrentQtyInCart(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId())
                , 1);

        // Press the "increase item" button
        androidCartScreen.get().pressIncreaseQtyBtn(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Capture the total and delivery fees before deleting a qty
        double totalAndDeliveryBeforeDelete = androidCartScreen.get().getCurrentTotalAndDelivery();

        // Press Delete item qty
        androidCartScreen.get().pressProductDeleteBtn(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()
        );

        // Assert the item quantity is decreased due to delete qty
        assertEquals(androidCartScreen.get().getProductCurrentQtyInCart(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId())
                , 1);

        // Assert that the total and delivery fees combined have decreased after the quantity decrease
        double totalAndDeliveryAfterDelete = androidCartScreen.get().getCurrentTotalAndDelivery();

        // Assert that the total and delivery fees combined have decreased after the quantity decrease
        Assert.assertNotEquals(totalAndDeliveryBeforeDelete, totalAndDeliveryAfterDelete);
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkWhenDeletingAllProductQty() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        // Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        // Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        // Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll horizontally and select the subCategory With the now only product if it's not already selected
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , "right"
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

        androidCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Add items to the cart
        androidCategoryScreen.get().pressProduct(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        androidCategoryScreen.get().pressCartBtn();

        // Assert the initial item quantity is 1
        assertEquals(androidCartScreen.get().getProductCurrentQtyInCart(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()), 1);

       // Press Delete item qty
        androidCartScreen.get().pressProductDeleteBtn(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

       // Assert the checkout button is not clickable, and the cart is empty
        Assert.assertFalse(androidCartScreen.get().isCheckoutBtnClickable()
                , "Checkout button should not be clickable");
        Assert.assertFalse(androidCartScreen.get().isTotalDisplayed(), "Total should not be displayed");
        Assert.assertFalse(androidCartScreen.get().isFessDisplayed(), "Fees should not be displayed");
    }

    @Test
    @Tags({@Tag("android"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckDeliveryFeesChanged() {
        androidTestsExecutionHelper.get().register(androidCountriesSelectionScreen.get(), androidLandingScreen.get(),
                androidPhoneNumberScreen.get(), androidCountriesListScreen.get(),
                androidOtpVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), androidCreateAccountInfoScreen.get(), androidRegisterSuccessScreen.get(),
                androidHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        // Accept location permission
        androidLocationPermissionAlert.get().takeActionIfAlertDisplayed("whileUsing");
        androidSetAddressScreen.get().goToCurrentLocationAndConfirmIfDisplayed();

        androidTestsExecutionHelper.get().addNewAddressAsCurrentLocation(androidHomeScreen.get()
                , androidAddressSelectionScreen.get()
                , androidLocationPermissionAlert.get()
                , androidSetAddressScreen.get()
                , androidDriver.get());

        // Validate that home screen is opened
        androidHomeScreen.get().isHomePageDisplayed();

        // Scroll until Category is displayed
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidHomeScreen.get().getHomeScreenScrollableContentContainer()
                , "down"
                , androidHomeScreen.get().getCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        // Tab on the category
        androidHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll horizontally and select the subCategory With the now only product if it's not already selected
        androidTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                androidDriver.get()
                , androidCategoryScreen.get().getSubCategoriesScrollableContentContainer()
                , "right"
                , androidCategoryScreen.get().getSubCategoryContentDescription(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

        androidCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Add items to the cart
        androidCategoryScreen.get().pressProduct(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        androidCategoryScreen.get().pressCartBtn();

        // Assert the initial item quantity is 1
        assertEquals(androidCartScreen.get().getProductCurrentQtyInCart(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()), 1);

        // Once the loop exits, it means the cart total is now greater than or equals 250
        androidCartScreen.get().updateCartUntilTotalReached(250);
       //Check if the delivery fees have changed to the expected values
        assertEquals(androidCartScreen.get().getCurrentCartDeliveryFees(), "12.5");
       // Check if fees are displayed
        assertTrue(androidCartScreen.get().isFessDisplayed(), "Order is above 250, Enjoy cheaper delivery");
    }
}
