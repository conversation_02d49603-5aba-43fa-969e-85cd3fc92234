package customerApp.ios.cart;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertTrue;

@Test
public class CartScreenTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateIncreasingAndDecreasingQtyIsAffectingCartTotal() {
        //Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //Press the Login or signup button
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(), iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Accept the notifications/tracking permissions request
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Accept location permission alert
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        iosTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Navigate to home screen
        iosHomeScreen.get().isHomePageDisplayed();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        // Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll horizontally and select the subCategory With the now only product if it's not already selected
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        iosCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Add items to the cart
        iosCategoryScreen.get().pressProductCard(String.valueOf(81655));

         iosCategoryScreen.get().pressCartBtn();
        // Assert the initial item quantity is 1
        Assert.assertEquals(iosCartScreen.get().getProductCurrentQtyInCart(81655), 1);

        // Capture the total and delivery fees before increasing the quantity
        double totalAndDeliveryBeforeIncrease = iosCartScreen.get().getCurrentTotalAndDelivery();

        // Press the "increase item" button
        iosCartScreen.get().pressProductIncreaseQtyBtn(81655);

        // Assert the item quantity is no longer 1 after the increase
        Assert.assertNotEquals(iosCartScreen.get().getProductCurrentQtyInCart(81655), 1);

        // Assert that the total and delivery fees combined have increased after the quantity increase
        double totalAndDeliveryAfterIncrease = iosCartScreen.get().getCurrentTotalAndDelivery();

        // Assert that the total and delivery fees combined have increased after the quantity increase
        Assert.assertNotEquals(totalAndDeliveryBeforeIncrease, totalAndDeliveryAfterIncrease);
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkDecreasingItems() {
        //Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //Press the Login or signup button
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(), iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Accept the notifications/tracking permissions request
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Accept location permission alert
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        iosTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Navigate to home screen
        iosHomeScreen.get().isHomePageDisplayed();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        // Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll horizontally and select the subCategory With the now only product if it's not already selected
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        iosCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Add items to the cart
        iosCategoryScreen.get().pressProductCard(String.valueOf(81655));

        iosCategoryScreen.get().pressCartBtn();
        // Assert the initial item quantity is 1
        Assert.assertEquals(iosCartScreen.get().getProductCurrentQtyInCart(81655), 1);

        // Press the "increase item" button
        iosCartScreen.get().pressProductIncreaseQtyBtn(81655);

        // Capture the total and delivery fees before decreasing the quantity
        double totalAndDeliveryBeforeDecrease = iosCartScreen.get().getCurrentTotalAndDelivery();

        // Press the "increase item" button
        iosCartScreen.get().pressProductDecreaseQtyBtn(81655);

        // Assert the item quantity is decreased
        Assert.assertNotEquals(iosCartScreen.get().getProductCurrentQtyInCart(81655), 1);

        // Assert that the total and delivery fees combined have decreased after the quantity decrease
        double totalAndDeliveryAfterDecrease = iosCartScreen.get().getCurrentTotalAndDelivery();

        // Assert that the total and delivery fees combined have decreased after the quantity decrease
        Assert.assertNotEquals(totalAndDeliveryBeforeDecrease, totalAndDeliveryAfterDecrease);
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkWhenDecreasingWithDeleteQty() {
        //Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //Press the Login or signup button
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(), iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Accept the notifications/tracking permissions request
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Accept location permission alert
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        iosTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Navigate to home screen
        iosHomeScreen.get().isHomePageDisplayed();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        // Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll horizontally and select the subCategory With the now only product if it's not already selected
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        iosCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Add items to the cart
        iosCategoryScreen.get().pressProductCard(String.valueOf(81655));

        iosCategoryScreen.get().pressCartBtn();

        // Assert the initial item quantity is 1
        Assert.assertEquals(iosCartScreen.get().getProductCurrentQtyInCart(81655), 1);

        // Press the "increase item" button
        iosCartScreen.get().pressProductIncreaseQtyBtn(81655);

        // Capture the total and delivery fees before deleting a qty
        double totalAndDeliveryBeforeDelete = iosCartScreen.get().getCurrentTotalAndDelivery();

        // Press Delete item qty
        iosCartScreen.get().pressProductDeleteBtn(81655);

        // Assert the item quantity is decreased due to delete qty
        Assert.assertEquals(iosCartScreen.get().getProductCurrentQtyInCart(81655), 1);

        // Assert that the total and delivery fees combined have decreased after the quantity decrease
        double totalAndDeliveryAfterDelete = iosCartScreen.get().getCurrentTotalAndDelivery();

        // Assert that the total and delivery fees combined have decreased after the quantity decrease
        Assert.assertNotEquals(totalAndDeliveryBeforeDelete, totalAndDeliveryAfterDelete);
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void checkWhenDeletingAllProductQty() {
        //Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //Press the Login or signup button
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(), iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Accept the notifications/tracking permissions request
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Accept location permission alert
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        iosTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Navigate to home screen
        iosHomeScreen.get().isHomePageDisplayed();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        // Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll horizontally and select the subCategory With the now only product if it's not already selected
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        iosCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Add items to the cart
        iosCategoryScreen.get().pressProductCard(String.valueOf(81655));

        iosCategoryScreen.get().pressCartBtn();

        // Assert the initial item quantity is 1
        assertEquals(iosCartScreen.get().getProductCurrentQtyInCart(81655), 1);

        // Press Delete item qty
        iosCartScreen.get().pressProductDeleteBtn(81655);

        // Assert the checkout button is not clickable, and the cart is empty
        Assert.assertFalse(iosCartScreen.get().isCheckoutBtnClickable(),
                "Checkout button should not be clickable");

        Assert.assertFalse(iosCartScreen.get().isTotalDisplayed(), "Total should not be displayed");

        Assert.assertFalse(iosCartScreen.get().isFessDisplayed(), "Fees should not be displayed");

    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void CheckDeliveryFeesChanged() {
        //Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //Press the Login or signup button
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(), iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Accept the notifications/tracking permissions request
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Accept location permission alert
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        iosTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Navigate to home screen
        iosHomeScreen.get().isHomePageDisplayed();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        // Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

        // Scroll horizontally and select the subCategory With the now only product if it's not already selected
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        iosCategoryScreen.get().pressSubCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

        // Add items to the cart
        iosCategoryScreen.get().pressProductCard(String.valueOf(81655));

        iosCategoryScreen.get().pressCartBtn();

        // Assert the initial item quantity is 1
        assertEquals(iosCartScreen.get().getProductCurrentQtyInCart(81655), 1);

        // Once the loop exits, it means the cart total is now greater than or equals 250
        iosCartScreen.get().updateCartUntilTotalReached(250);

        //Check if the delivery fees have changed to the expected values
        assertEquals(iosCartScreen.get().getCurrentCartDeliveryFees(), "12.5");

        // Check if fees are displayed
        assertTrue(iosCartScreen.get().isFessDisplayed(), "Order is above 250, Enjoy cheaper delivery");
    }
}
