package customerApp.ios.billingModule;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class PayBillsTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    // Update user balance where Balance > total and use the balance to pay a bill
    public void validatePayingBillWithBalanceGreaterThanTotal() {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());

       iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
       iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

       iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        iosHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        iosPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        iosBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        iosProviderScreen.get().openServiceList();
        iosProviderScreen.get().isServicesListDropDownDisplayed();
        iosProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        iosProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosProviderScreen.get().pressPageTitleToDismissKeyboard(String.valueOf(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()));

        iosProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                        , "name"
                        , "mobile").getPaymentServiceProviders()
                    , "name"
                    , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice summary screen
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isTotalDisplayed());
        Assert.assertTrue (iosInvoiceSummaryScreen.get().isTaxAndFeesDisplayed());

        // Grand total should equals to Zero because the user is using all his balance
        Assert.assertEquals(iosInvoiceSummaryScreen.get().getGrandTotalAmount(), 0.0, 0.001);

        //Assert the Used balance section is shown
        Assert.assertTrue (iosInvoiceSummaryScreen.get().isBalanceSectionDisplayed());

        // wait until the Payment methods section is displayed
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isUserCurrentBalanceDisplayed()
                ,"User current balance section isn't displayed");

        //press pay bill
        iosInvoiceSummaryScreen.get().pressPayBillBtn();

        // Assertion on success screen
        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( iosInvoiceSuccessScreen.get().isDoneBtnDisplayed()
                , "Done button isn't displayed");
    }

    // Update user balance where Balance > total and use the Card only to pay a bill
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validatePayingBillWithBalanceHigherThanBillAndBalanceToggleIsOff() {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

       iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //navigate to pay screen
        iosHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        iosPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        iosBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        iosProviderScreen.get().openServiceList();
        iosProviderScreen.get().isServicesListDropDownDisplayed();
        iosProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        iosProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosProviderScreen.get().pressPageTitleToDismissKeyboard(String.valueOf(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()));

        iosProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice summary screen
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isTotalDisplayed());
        Assert.assertTrue (iosInvoiceSummaryScreen.get().isTaxAndFeesDisplayed());

        // Grand total should be equals to 0 because the balance toggle is On by default
        Assert.assertEquals(iosInvoiceSummaryScreen.get().getGrandTotalAmount(), 0.0, 0.001);

        // wait until the Payment methods section is displayed
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isBalanceSectionDisplayed()
                ,"User current balance section is displayed");

        //Turn off the Balance toggle
        iosInvoiceSummaryScreen.get().pressBalanceToggle();

        // Total should not be zero because balance toggle is Off
        Assert.assertNotEquals(iosInvoiceSummaryScreen.get().getTotalValue(), 0.0, 0.001);

        //press pay bill to pay with Card
        iosInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(iosInvoiceSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                iosInvoiceSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(iosInvoiceSuccessScreen.get().getTotalPaidAmount());
        defaultTestData.get().getTestBill().setCurrency(iosInvoiceSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( iosInvoiceSuccessScreen.get().isDoneBtnDisplayed()
                , "Done button isn't displayed");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    // Update user balance where balance < total and pay with balance and card, where toggle is ON
    public void validatePayingBillWithBalanceAndCreditCard () {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

       iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("9.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        // Assert the Home screen is displayed
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //navigate to pay screen
        iosHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        iosPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        iosBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        iosProviderScreen.get().openServiceList();
        iosProviderScreen.get().isServicesListDropDownDisplayed();
        iosProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        iosProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosProviderScreen.get().pressPageTitleToDismissKeyboard(String.valueOf(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()));

        iosProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice summary screen
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isTotalDisplayed());
        Assert.assertTrue (iosInvoiceSummaryScreen.get().isTaxAndFeesDisplayed());

        // Grand total should not be 0 because the user balance is less than the total
        Assert.assertNotEquals(iosInvoiceSummaryScreen.get().getGrandTotalAmount(), 0.0, 0.001);

        Assert.assertTrue(iosInvoiceSummaryScreen.get().isBalanceSectionDisplayed());

        // wait until the Payment methods section is displayed
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isUserCurrentBalanceDisplayed()
                ," User current balance section isn't displayed");

        //Assert the toggle is ON while the user balance is less than the total
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isBalanceToggleON(),"User balance toggle is OFF");

        //press pay bill to pay with Card
        iosInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(iosInvoiceSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                iosInvoiceSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(iosInvoiceSuccessScreen.get().getTotalPaidAmount());
        defaultTestData.get().getTestBill().setCurrency(iosInvoiceSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( iosInvoiceSuccessScreen.get().isDoneBtnDisplayed()
                , "Done button isn't displayed");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    // Update User balance where Balance < total and pay with card only, Where toggle is off
    public void validatePayingBillWithBalanceBelowTotalAndBalanceToggleOff () {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

       iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
       iosLandingScreen.get().pressAuthHyperLink();

        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("9.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        //Assert Home screen is displayed
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //navigate to pay screen
        iosHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        iosPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        iosBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        iosProviderScreen.get().openServiceList();
        iosProviderScreen.get().isServicesListDropDownDisplayed();
        iosProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        iosProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosProviderScreen.get().pressPageTitleToDismissKeyboard(String.valueOf(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()));

        iosProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice summary screen
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isTotalDisplayed());
        Assert.assertTrue (iosInvoiceSummaryScreen.get().isTaxAndFeesDisplayed());

        // Grand total should not be 0 because the user balance is less than the total
        Assert.assertNotEquals(iosInvoiceSummaryScreen.get().getGrandTotalAmount(), 0.0, 0.001);

        Assert.assertTrue (iosInvoiceSummaryScreen.get().isBalanceSectionDisplayed());

        // Switch off the balance toggle
        iosInvoiceSummaryScreen.get().pressBalanceToggle();

        Assert.assertFalse(iosInvoiceSummaryScreen.get().isBalanceToggleON(),"The Balance Toggle is Off");

        // wait until the Payment methods section is displayed
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isUserCurrentBalanceDisplayed()
                ," User current Balance section isn't displayed");

        //press pay bill
        iosInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(iosInvoiceSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                iosInvoiceSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(iosInvoiceSuccessScreen.get().getTotalPaidAmount());
        defaultTestData.get().getTestBill().setCurrency(iosInvoiceSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( iosInvoiceSuccessScreen.get().isDoneBtnDisplayed()
                , "Done button isn't displayed");
    }

    // Update user balance to be = 0 and pay a bill when toggle is ON
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validatePayingBillWithZeroBalanceAndToggleOn (){
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("0.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

       iosHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        iosHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        iosPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        iosBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        iosProviderScreen.get().openServiceList();
        iosProviderScreen.get().isServicesListDropDownDisplayed();
        iosProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        iosProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosProviderScreen.get().pressPageTitleToDismissKeyboard(String.valueOf(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()));

        iosProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice screen
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isTotalDisplayed());
        Assert.assertTrue (iosInvoiceSummaryScreen.get().isTaxAndFeesDisplayed());

        // Grand total should not be displayed because the user balance is equals to Zero
        Assert.assertFalse(iosInvoiceSummaryScreen.get().isGrandTotalDisplayed()
                ,"Grand Total should not be displayed because the user balance is Zero");

        Assert.assertTrue(iosInvoiceSummaryScreen.get().isBalanceToggleON(),"The Balance Toggle is OFF");

        // wait until the Payment methods section is displayed
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isUserCurrentBalanceDisplayed()
                ,"Payment section isn't displayed!");

        //press pay bill
        iosInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(iosInvoiceSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                iosInvoiceSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(iosInvoiceSuccessScreen.get().getTotalPaidAmount());
        defaultTestData.get().getTestBill().setCurrency(iosInvoiceSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( iosInvoiceSuccessScreen.get().isDoneBtnDisplayed()
                , "Done button isn't displayed");
    }

    // Update user balance to be = 0 and pay a bill when toggle is Off
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validatePayingBillWithZeroBalanceAndToggleOff (){
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("0.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //navigate to pay screen
        iosHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        iosPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        iosBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        iosProviderScreen.get().openServiceList();
        iosProviderScreen.get().isServicesListDropDownDisplayed();
        iosProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        iosProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosProviderScreen.get().pressPageTitleToDismissKeyboard(String.valueOf(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()));

        iosProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice screen
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isTotalDisplayed());
        Assert.assertTrue (iosInvoiceSummaryScreen.get().isTaxAndFeesDisplayed());

        // Grand total should not be displayed because the user balance is equals to Zero

        Assert.assertFalse(iosInvoiceSummaryScreen.get().isGrandTotalDisplayed()
                ,"Grand Total should not be displayed because the user balance is Zero");

        //Turn off the Balance toggle
        iosInvoiceSummaryScreen.get().pressBalanceToggle();

        Assert.assertFalse(iosInvoiceSummaryScreen.get().isBalanceToggleON(),"The Balance Toggle is ON");

        // wait until the Payment methods section is displayed
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isUserCurrentBalanceDisplayed()
                ,"User current Balance section isn't displayed");

        //press pay bill
        iosInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(iosInvoiceSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                iosInvoiceSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(iosInvoiceSuccessScreen.get().getTotalPaidAmount());
        defaultTestData.get().getTestBill().setCurrency(iosInvoiceSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( iosInvoiceSuccessScreen.get().isDoneBtnDisplayed()
                , "Done button isn't displayed");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    // Update user balance to be negative amount and pay a bill
    public void validatePayingBillWithNegativeBalance () {
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("-10"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //navigate to pay screen
        iosHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        iosPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        iosBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        iosProviderScreen.get().openServiceList();
        iosProviderScreen.get().isServicesListDropDownDisplayed();
        iosProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        iosProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosProviderScreen.get().pressPageTitleToDismissKeyboard(String.valueOf(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()));

        iosProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice screen
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isTotalDisplayed());
        Assert.assertTrue (iosInvoiceSummaryScreen.get().isTaxAndFeesDisplayed());

        // Grand total should be displayed because the user has a negative balance

        Assert.assertTrue(iosInvoiceSummaryScreen.get().isGrandTotalDisplayed()
                ,"Grand Total should be displayed because the user has negative balance");

        Assert.assertNotEquals(iosInvoiceSummaryScreen.get().getUsedBalanceValue(),0.0, 0.001);
        Assert.assertNotEquals(iosInvoiceSummaryScreen.get().getUsedBalanceValue(),-10.0, -10.00);

        //press pay bill
        iosInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(iosInvoiceSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                iosInvoiceSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(iosInvoiceSuccessScreen.get().getTotalPaidAmount());
        defaultTestData.get().getTestBill().setCurrency(iosInvoiceSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( iosInvoiceSuccessScreen.get().isDoneBtnDisplayed()
                , "Done button isn't displayed");
    }
}
