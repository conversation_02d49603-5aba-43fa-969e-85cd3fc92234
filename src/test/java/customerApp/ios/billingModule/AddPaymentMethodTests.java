package customerApp.ios.billingModule;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class AddPaymentMethodTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateAddingCardFromPayScreenIsWorkingCorrectly(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get()
                , iosLandingScreen.get()
                , iosNotificationsPermissionsAlert.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , iosCreateAccountScreen.get()
                , iosRegisterSuccessScreen.get()
                , iosTrackingPermissionAlert.get()
                , iosHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().pressPayTabBtn();
        iosPayScreen.get().isPayPageDisplayed();
        iosPayScreen.get().pressSavedCardsBtn();

        Assert.assertTrue(iosSavedCardsScreen.get().isPageDisplayed());
        Assert.assertTrue(iosSavedCardsScreen.get().isListEmpty());

        iosSavedCardsScreen.get().pressAddCardBtnFromHeader();

        Assert.assertTrue(iosAddCardInfoScreen.get().isPageDisplayed());
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosSavedCardsScreen.get().isPageDisplayed(), "Saved card list isn't displayed");

        Assert.assertTrue(iosSavedCardsScreen.get().isCardDisplayed(defaultTestData.get().getTestCreditCard()
                        .substring(defaultTestData.get().getTestCreditCard().length() - 4))
                , "Card didn't get added and it's not displayed in the list");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateAddingCardFromMoreScreenIsWorkingCorrectly(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get()
                , iosLandingScreen.get()
                , iosNotificationsPermissionsAlert.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , iosCreateAccountScreen.get()
                , iosRegisterSuccessScreen.get()
                , iosTrackingPermissionAlert.get()
                , iosHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().isPageDisplayed();
        iosMoreScreen.get().pressAccountSettingsBtn();
        iosAccountSettingsScreen.get().isPageDisplayed();

        iosAccountSettingsScreen.get().pressSavedCardsBtn();
        Assert.assertTrue(iosSavedCardsScreen.get().isPageDisplayed());
        Assert.assertTrue(iosSavedCardsScreen.get().isListEmpty());

        iosSavedCardsScreen.get().pressAddCardBtnFromHeader();

        Assert.assertTrue(iosAddCardInfoScreen.get().isPageDisplayed());
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosSavedCardsScreen.get().isPageDisplayed(), "Saved card list isn't displayed");

        Assert.assertTrue(iosSavedCardsScreen.get().isCardDisplayed(defaultTestData.get().getTestCreditCard()
                .substring(defaultTestData.get().getTestCreditCard().length() - 4))
                , "Card didn't get added and it's not displayed in the list");
    }
}
