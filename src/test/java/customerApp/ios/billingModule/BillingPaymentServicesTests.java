package customerApp.ios.billingModule;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class BillingPaymentServicesTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validatePayingMobileBillUsingCreditCardIsWorking (){
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Update user balance based on the Test needed
        switcherApiClient.get().updateUserBalanceByPhoneNumber("0.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosHomeScreen.get().isHomePageDisplayed();

        //navigate to pay screen
        iosHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        iosPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        iosBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        iosProviderScreen.get().openServiceList();
        iosProviderScreen.get().isServicesListDropDownDisplayed();
        iosProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        iosProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosProviderScreen.get().pressPageTitleToDismissKeyboard(String.valueOf(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()));

        iosProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice screen
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isTotalDisplayed());
        Assert.assertTrue (iosInvoiceSummaryScreen.get().isTaxAndFeesDisplayed());

        // Grand total should not be displayed because the user balance is equals to Zero
        Assert.assertFalse(iosInvoiceSummaryScreen.get().isGrandTotalDisplayed()
                ,"Grand Total should not be displayed because the user balance is Zero");

        Assert.assertTrue(iosInvoiceSummaryScreen.get().isBalanceToggleON(),"The Balance Toggle is OFF");

        // wait until the Payment methods section is displayed
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isUserCurrentBalanceDisplayed()
                ,"Payment section isn't displayed!");

        //press pay bill
        iosInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(iosInvoiceSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                iosInvoiceSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(iosInvoiceSuccessScreen.get().getTotalPaidAmount());
        defaultTestData.get().getTestBill().setCurrency(iosInvoiceSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( iosInvoiceSuccessScreen.get().isDoneBtnDisplayed()
                , "Done button isn't displayed");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    // Validate that bill exists in bills history page after it's being paid
    public void validateThatBillExistsInBillsTabAfterPayingIt (){
        // Build Payment test data
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.set(testExecutionHelper.get()
                .buildPaymentCategoriesForCustomerAppTestSession(defaultTestData.get()));

        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //navigate to pay screen
        iosHomeScreen.get().pressPayTabBtn();

        //press on mobile category
        iosPayScreen.get().pressCategory(testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                , "name"
                , "mobile").getCategoryId());

        //select orange provider
        iosBillingCategoryScreen.get().pressProvider(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId());

        //select a service from service list
        iosProviderScreen.get().openServiceList();
        iosProviderScreen.get().isServicesListDropDownDisplayed();
        iosProviderScreen.get().selectServiceType(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //enter the mobile number and press next btn
        iosProviderScreen.get().enterMobileNumber(
                testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getInputParamsList().get(0).getInputParamId()
                ,"0" + defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosProviderScreen.get().pressPageTitleToDismissKeyboard(String.valueOf(
                testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()));

        iosProviderScreen.get().pressNextBtn(testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                        testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                , "name"
                                , "mobile").getPaymentServiceProviders()
                        , "name"
                        , "orange").getServiceProviderId()
                , testExecutionHelper.get().getPaymentServiceByCertainCriteria(
                        testExecutionHelper.get().getPaymentServiceProviderByCertainCriteria(
                                testExecutionHelper.get().getPaymentCategoryByCertainCriteria(
                                        defaultTestData.get().getCustomerAppTestSession().getPaymentCategories()
                                        , "name"
                                        , "mobile").getPaymentServiceProviders()
                                , "name"
                                , "orange").getPaymentServices()
                        , "type"
                        , "bill").getServiceId());

        //Assert the user is on Invoice screen
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isTotalDisplayed());
        Assert.assertTrue (iosInvoiceSummaryScreen.get().isTaxAndFeesDisplayed());

        // Grand total should not be displayed because the user balance is equals to Zero

        Assert.assertFalse(iosInvoiceSummaryScreen.get().isGrandTotalDisplayed()
                ,"Grand Total should not be displayed because the user balance is Zero");

        //Turn off the Balance toggle
        iosInvoiceSummaryScreen.get().pressBalanceToggle();

        Assert.assertFalse(iosInvoiceSummaryScreen.get().isBalanceToggleON(),"The Balance Toggle is ON");

        // wait until the Payment methods section is displayed
        Assert.assertTrue(iosInvoiceSummaryScreen.get().isUserCurrentBalanceDisplayed()
                ,"User current Balance section isn't displayed");

        //press pay bill
        iosInvoiceSummaryScreen.get().pressPayBillBtn();

        //Add card info and press add card
        iosAddCardInfoScreen.get().fillCardInfoForm(defaultTestData.get().getTestCreditCard()
                , defaultTestData.get().getTestExpiryDate()
                , defaultTestData.get().getTestCVC()
                , defaultTestData.get().getRandomTestUser().getEmailAddress()
                , defaultTestData.get().getRandomTestUser().getFullName());
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed());
        defaultTestData.get().getTestBill().setTransactionId(iosInvoiceSuccessScreen.get().getTransactionId());
        defaultTestData.get().getTestBill().setPaymentDateTime(
                iosInvoiceSuccessScreen.get().getTransactionDateTime());
        defaultTestData.get().getTestBill().setPaymentAmount(iosInvoiceSuccessScreen.get().getTotalPaidAmount());
        defaultTestData.get().getTestBill().setCurrency(iosInvoiceSuccessScreen.get().getPaymentCurrency());

        // Assertion on success screen
        Assert.assertTrue(iosInvoiceSuccessScreen.get().isPageDisplayed()
                , "Payment success screen isn't displayed!");

        Assert.assertTrue( iosInvoiceSuccessScreen.get().isDoneBtnDisplayed()
                , "Done button isn't displayed");

        // Go to More screen
        iosInvoiceSuccessScreen.get().pressDoneBtn();
        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().isPageDisplayed();

        // Go to Bills tab
        iosMoreScreen.get().pressActivityHistoryBtn();
        iosActivityHistoryScreen.get().selectBillsTab();

        Assert.assertTrue(iosBillsTabScreen.get().isBillCardDisplayed(
                        defaultTestData.get().getTestBill().getTransactionId())
                ,"Bill card is not displayed for transaction ID: "
                        + defaultTestData.get().getTestBill().getTransactionId());

        Assert.assertEquals(iosBillsTabScreen.get().getBillsCardCount(), 1
                , "Expected only one bill card to be displayed, but found "
                        + defaultTestData.get().getTestBill().getTransactionId());
    }
}
