package customerApp.ios.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class BalanceToggleChecksTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateCreatingAnOrderWithBalanceGreaterThanOrderTotal () {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());

        // Check if the balance toggle is ON
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to COD
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // Implement assertion logic to ensure the total is = 0 when the Balance is selected as payment method
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceDisplayed());
        Assert.assertEquals(iosCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void ToggleOnWhenBGreaterThanTotalAndCCIsSelected () {
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get()
                , iosAddressSelectionScreen.get()
                , iosLocationPermissionAlert.get()
                , iosSetAddressScreen.get()
                , iosDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                ,"down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();
        iosAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());
        // Check if the balance toggle is ON
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to CC
        iosCheckoutScreen.get().pressCreditCardPaymentOption();
        Assert.assertTrue(iosCardSelectionModal.get().isModalDisplayed());
        iosCardSelectionModal.get().pressAddNewCardOption();

        // Implement assertion logic to ensure the total is  0 even if the CC is selected
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceDisplayed());
        Assert.assertEquals(iosCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }

   @Test
   @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void WhenBalancePartiallyWhenCODIsSelected () {
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get()
                , iosAddressSelectionScreen.get()
                , iosLocationPermissionAlert.get()
                , iosSetAddressScreen.get()
                , iosDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();
        iosAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());

        // Check if the balance toggle is ON
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to COD
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();

        // Implement assertion logic to ensure the total is not equals 0 even if the user uses the balance
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceDisplayed());
        Assert.assertNotEquals(iosCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void WhenBalancePartiallyWhenCCIsSelected () {
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get()
                , iosAddressSelectionScreen.get()
                , iosLocationPermissionAlert.get()
                , iosSetAddressScreen.get()
                , iosDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();
        iosAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());

        // Check if the balance toggle is ON
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to CC
        iosCheckoutScreen.get().pressCreditCardPaymentOption();
        Assert.assertTrue(iosCardSelectionModal.get().isModalDisplayed());
        iosCardSelectionModal.get().pressAddNewCardOption();

        // Implement assertion logic to ensure the total is not equals 0 even if the user uses the balance
        Assert.assertTrue(iosCheckoutScreen.get().isBalanceDisplayed());
        Assert.assertNotEquals(iosCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void BalanceWhenNegativeAndCCIsSelected () {
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        switcherApiClient.get().updateUserBalanceByPhoneNumber("-100.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get()
                , iosAddressSelectionScreen.get()
                , iosLocationPermissionAlert.get()
                , iosSetAddressScreen.get()
                , iosDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();
        iosAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());
        // Check if the balance toggle is OFF
        Assert.assertFalse(iosCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to CC
        iosCheckoutScreen.get().pressCreditCardPaymentOption();

        Assert.assertTrue(iosCheckoutScreen.get().isModalDisplayed());
        iosCheckoutScreen.get().pressAddNewCardBtn();

        // Implement assertion logic to ensure the total is not equals 0 and Due Amount is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getOrderFeesBreakDownNameSelector());
        Assert.assertTrue(iosCheckoutScreen.get().isPreviousDueAmountIsDisplayed());

        Assert.assertNotEquals(iosCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void BalanceWhenNegativeAndCODIsSelected () {
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        switcherApiClient.get().updateUserBalanceByPhoneNumber("-100.00"
                , defaultTestData.get().getAdminUser()
                , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get()
                , iosAddressSelectionScreen.get()
                , iosLocationPermissionAlert.get()
                , iosSetAddressScreen.get()
                , iosDriver.get());

        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Scroll until Category is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        //Tab on the category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());

        // Scroll to the first Add to cart button and press it then go to cart details
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCategoryScreen.get().getScrollableContentContainer("product")
                , iosCategoryScreen.get().getAddToCartBtnNameSelector());
        iosCategoryScreen.get().pressAddToCartIconByIndex(1);
        iosCategoryScreen.get().pressCartIcon();

        //click on goto check out button
        iosCartScreen.get().pressGoToCheckoutBtn();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();
        iosAddressCreateSuccessModal.get().dismissModal();

        // Check if checkout page is displayed and scroll down to the balance part
        Assert.assertTrue(iosCheckoutScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());

        // Check if the balance toggle is OFF
        Assert.assertFalse(iosCheckoutScreen.get().isBalanceToggleEnabled()
                , "Balance toggle is expected to be ON.");

        // Update the Payment method to CC
        iosCheckoutScreen.get().pressCreditCardPaymentOption();

        Assert.assertTrue(iosCheckoutScreen.get().isModalDisplayed());
        iosCheckoutScreen.get().pressAddNewCardBtn();

        // Implement assertion logic to ensure the total is not equals 0 and Due Amount is displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getOrderFeesBreakDownNameSelector());
        Assert.assertTrue(iosCheckoutScreen.get().isPreviousDueAmountIsDisplayed());

        Assert.assertNotEquals(iosCheckoutScreen.get().getOrderGrandTotalValue(), 0.0, 0.001);
    }
}
