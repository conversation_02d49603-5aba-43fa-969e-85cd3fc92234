package customerApp.ios.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class ReorderTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateReorderIsNotShownForProcessingOrders() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());
        
        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());
        
        iosHomeScreen.get().isHomePageDisplayed();
        
        //Scroll until category displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));
        
        // Select category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId());
        
        iosCategoryScreen.get().isPageDisplayed();
        
        iosCategoryScreen.get().pressProductCard((
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMongoId()));
        
        iosProductDetailsScreen.get().pressAddToCartBtn();
        
        //Open Cart screen
        iosProductDetailsScreen.get().pressGoToCartBtn();
        iosCartScreen.get().pressGoToCheckoutBtn();
        
        // Enter address details and submit
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        
        iosCreateAddressScreen.get().pressSaveAddressBtn();
        
        iosAddressCreateSuccessModal.get().isModalDisplayed();
        
        iosAddressCreateSuccessModal.get().dismissModal();
        
        iosCheckoutScreen.get().isPageDisplayed();
        
        // Scroll until payment section then select COD
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCheckoutScreen.get().getScrollableContentContainer()
                , iosCheckoutScreen.get().getPaymentSectionNameSelector());
        
        iosCheckoutScreen.get().pressCashOnDeliveryPaymentOption();
        
        iosCheckoutScreen.get().pressPlaceOrderBtn();
        
        iosOrderSuccessScreen.get().isPageDisplayed();
        
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosOrderSuccessScreen.get().getScrollableContentContainer()
                , iosOrderSuccessScreen.get().getTrackYourOrderBtnNameSelector());
        
        iosOrderSuccessScreen.get().pressTrackYourOrderBtn();
        
        Assert.assertFalse(iosOrderDetailsScreen.get().isReorderBtnDisplayed(),
                "Expected Reorder Btn will not be shown, but shown ");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateReorderBtnIsShownForCompletedOrders() {
        // Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        //Create Order using Create API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "15"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Complete the created order
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered"
                , 600f
                , false);
        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        //Tab on more and go to order history
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();
        iosMoreScreen.get().pressActivityHistoryBtn();
        iosActivityHistoryScreen.get().pressOrderCard(
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());
        Assert.assertTrue(iosOrderDetailsScreen.get().isReorderBtnDisplayed(),
                "Reorder Button should be shown but it's not");
        iosOrderDetailsScreen.get().pressReorderBtn();
        Assert.assertFalse(iosCartScreen.get().isCartEmpty(),
                "Expected products added to cart, but it's not added");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateReorderBtnIsShownForCanceledOrders() {
        // Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //Create Order using Create API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "15"
                        ,"0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        orderApiClient.get().cancelOrder(defaultTestData.get().getRandomTestUser()
                , defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(), "cancelled",false);
        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        //Tab on more and go to order history
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();
        iosMoreScreen.get().pressActivityHistoryBtn();
        iosActivityHistoryScreen.get().pressOrderCard(
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId());
        Assert.assertTrue(iosOrderDetailsScreen.get().isReorderBtnDisplayed(),
                "Reorder Button should be shown but it's not");
        iosOrderDetailsScreen.get().pressReorderBtn();
        Assert.assertFalse(iosCartScreen.get().isCartEmpty(),
                "Expected products added to cart, but it's not added");
    }
}
