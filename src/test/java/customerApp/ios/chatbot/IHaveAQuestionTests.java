package customerApp.ios.chatbot;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class IHaveAQuestionTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingAnotherQuestionAfterAskingAboutBreadfastRewards(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressBreadfastRewardBtn();
        iosChatbotScreen.get().pressWhatAreBreadfastPointsBtn();
        Assert.assertTrue(iosChatbotScreen.get().isBreadfastPointsMsgDisplayed());
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        iosChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("How Can I redeem the Points");
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCxAgentWillReviewYouQuestionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingAboutHowToRedeemMyPoints(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressBreadfastRewardBtn();
        iosChatbotScreen.get().pressHowToRedeemMyPointsBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isHowToRedeemMyPointsMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingAboutHowToRedeemMyPointsThenAskAnotherQuestion(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressBreadfastRewardBtn();
        iosChatbotScreen.get().pressHowToRedeemMyPointsBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isHowToRedeemMyPointsMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        iosChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("When will my points expire?");
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCxAgentWillReviewYouQuestionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingWhenWillMyPointsExpire(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressBreadfastRewardBtn();
        iosChatbotScreen.get().pressWhenWillMyPointsExpireBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isWhenWillMyPointsExpireMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingWhenWillMyPointsExpireThenAskAnotherQuestion(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressBreadfastRewardBtn();
        iosChatbotScreen.get().pressWhenWillMyPointsExpireBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isWhenWillMyPointsExpireMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
        iosChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("How Can I redeem my points?");
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCxAgentWillReviewYouQuestionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingAboutIHavePendingPoints(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressBreadfastRewardBtn();
        iosChatbotScreen.get().pressIHavePendingPointsBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isIHavePendingPointsMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingAboutIHavePendingPointsThenAskAnotherQuestion(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressBreadfastRewardBtn();
        iosChatbotScreen.get().pressIHavePendingPointsBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isIHavePendingPointsMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("How Can I redeem my points?");
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCxAgentWillReviewYouQuestionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingAboutBreadfastRewardThenSelectMyQuestionIsNotHere(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressBreadfastRewardBtn();
        iosChatbotScreen.get().pressMyQuestionIsNotHereBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("When will my points expire?");
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCxAgentWillReviewYouQuestionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingWhatDoesCashBackMean(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressSomeThingElseBtn();
        iosChatbotScreen.get().pressWhatDoesCashBackMeanBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isWhatDoesCashBackMeanMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingWhatDoesCashBackMeanThenAskAnotherQuestion(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressSomeThingElseBtn();
        iosChatbotScreen.get().pressWhatDoesCashBackMeanBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isWhatDoesCashBackMeanMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
        iosChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("How can I apply the coupon?");
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCxAgentWillReviewYouQuestionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingWhereDoYouDeliverThenAskAnotherQuestion(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressSomeThingElseBtn();
        iosChatbotScreen.get().pressWhereDoYouDeliverBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isWhereDoYouDeliverMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        iosChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("How can I apply the coupon?");
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCxAgentWillReviewYouQuestionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingWhereDoYouDeliver(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressSomeThingElseBtn();
        iosChatbotScreen.get().pressWhereDoYouDeliverBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isWhereDoYouDeliverMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingHowCanITipTheDeliveryAgent(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressSomeThingElseBtn();
        iosChatbotScreen.get().pressHowCanITipTheDeliveryAgentBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isHowCanITipTheDeliveryAgentMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingHowCanITipTheDeliveryAgentThenAskAnotherQuestion(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressSomeThingElseBtn();
        iosChatbotScreen.get().pressHowCanITipTheDeliveryAgentBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isHowCanITipTheDeliveryAgentMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        iosChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Is there max. ot min. amount to add it as a tip?");
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCxAgentWillReviewYouQuestionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingCanIApplyMoreThanOneCoupon(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressSomeThingElseBtn();
        iosChatbotScreen.get().pressCanIApplyMoreThanOneCouponBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isCanIApplyMoreThanOneCouponMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenAskingCanIApplyMoreThanOneCouponThenAskAnotherQuestion(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressSomeThingElseBtn();
        iosChatbotScreen.get().pressCanIApplyMoreThanOneCouponBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isCanIApplyMoreThanOneCouponMsgDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThanksForTheHelpMsgDisplayed());
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        iosChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("How can I apply the coupon?");
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCxAgentWillReviewYouQuestionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ValidateTheResponseWhenSelectingMyQuestionIsNotHere(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressSomeThingElseBtn();
        iosChatbotScreen.get().pressMyQuestionIsNotHereBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("How can I apply the coupon?");
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCxAgentWillReviewYouQuestionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateTheResponseWhenAskingAboutRewardsProgramWithoutAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();

        //Steps of the reward program flow , Thank you that helps (End flow)
        iosChatbotScreen.get().pressBreadfastRewardBtn();
        iosChatbotScreen.get().pressWhatAreBreadfastPointsBtn();

        //Assert that the thanks messages are displayed
        Assert.assertTrue(iosChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateTheResponseWhenAskingAboutCoffeeStoresWithoutAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();

        //Steps of the reward program flow , Thank you that helps (End flow)
        iosChatbotScreen.get().pressCoffeeStoresLocationBtn();

        //Assert that the links for coffee store and menu are displayed
        Assert.assertTrue(iosChatbotScreen.get().coffeeStoresLocationMsgDisplayed());

        //Assert that the thanks messages are displayed
        Assert.assertTrue(iosChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateTheResponseWhenAskingAboutCoffeeStoresAndHaveAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressCoffeeStoresLocationBtn();
        //Assert that the links for coffee store and menu are displayed
        Assert.assertTrue(iosChatbotScreen.get().coffeeStoresLocationMsgDisplayed());
        iosChatbotScreen.get().pressIHaveAnotherQuestionBtn();
        //Enter the question you have and send it
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //Assert that the thanks messages are displayed
        Assert.assertTrue(androidChatbotScreen.get().isReviwingIhaveAnotherQuestionMessageDisplayed());

    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateTheResponseWhenAskingAboutMyCurrentBalanceWithoutAskingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //Click I have a question to start the flow
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressBalanceStepsBtn();
        //Assert that the links for coffee store and menu are displayed
        Assert.assertTrue(iosChatbotScreen.get().balanceStepsMsgDisplayed());
        //Assert that the thanks messages are displayed
        Assert.assertTrue(iosChatbotScreen.get().isGladWeCouldAssistMsgDisplayed());

    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateTheResponseWhenAskingAboutMyCurrentBalanceHavingAnotherQuestion() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();

        //Click I have a question to start the flow
        iosChatbotScreen.get().pressIHaveAQuestionBtn();

        //Steps of the reward program flow , Thank you that helps (End flow)
        iosChatbotScreen.get().pressBalanceStepsBtn();

        //Assert that the links for coffee store and menu are displayed
        Assert.assertTrue(iosChatbotScreen.get().balanceStepsMsgDisplayed());

        iosChatbotScreen.get().pressIHaveAnotherQuestionBtn();

        //Send the Other Question
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //Assert that the thanks messages are displayed
        Assert.assertTrue(androidChatbotScreen.get().isReviwingIhaveAnotherQuestionMessageDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateTheResponseWhenSelectingMyQuestionIsNotHere() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAQuestionBtn();
        iosChatbotScreen.get().pressMyQuestionIsNotHereBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("What is Breadfast points?");
        Assert.assertTrue(androidChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateTheResponseWhenSelectingNoneOfTheAboveAndAskingWhatDoesCashBackMean() {

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressNonOfTheAbove();
        iosChatbotScreen.get().pressWhatDoesCashBackMeanBtn();
        Assert.assertTrue(iosChatbotScreen.get().isWhatDoesCashBackMeanMsgDisplayed());
    }
}
