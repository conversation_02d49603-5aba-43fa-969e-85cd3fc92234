package customerApp.ios.chatbot;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class NonOfTheAboveTests extends BaseTest {
    //None of the above - > I have a suggestion or feedback - > Assert on the displayed message - > Choose " I have a suggestion to add product on breadfast"
    // - > Assert on the displayed message
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void SuggestionToAddProductOnBreadfast(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        //navigate to chatbot screen
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().isPageDisplayed();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressNoneOfTheAboveBtn();
        iosChatbotScreen.get().pressIHaveASuggestionOrFeedback();
        iosChatbotScreen.get().LoveToHearSuggestionsMsgDisplayed();
        iosChatbotScreen.get().pressSuggestionToAddProductOnBreadfast();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        iosChatbotScreen.get().SuggestionThankYouMsgDisplayed();
    }
    //None of the above - > I have a suggestion or feedback - > Assert on the displayed message - > Choose " I have a General suggestion" - > Assert on the displayed message - > Enter the General suggestion then send - > Assert that chat is assigned to "CX-Bot feedback"
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void GeneralSuggestion(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        //navigate to chatbot screen
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().isPageDisplayed();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressNoneOfTheAboveBtn();
        iosChatbotScreen.get().pressIHaveASuggestionOrFeedback();
        iosChatbotScreen.get().pressGeneralSuggestion();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        iosChatbotScreen.get().GeneralSuggestionMsgDisplayed();
    }
    //None of the above - > I have a suggestion or feedback - > I Would like to share positive feedback - > Assert on the displayed message - > Assert on the free text field - > Enter the feedback then send - > Assert that chat is assigned to "CX-bot feedback"
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void IWouldLikeToSharePositiveFeedback(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        //navigate to chatbot screen
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().isPageDisplayed();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressNoneOfTheAboveBtn();
        iosChatbotScreen.get().pressIHaveASuggestionOrFeedback();
        iosChatbotScreen.get().pressSharePositiveFeedback();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        iosChatbotScreen.get().SharePositiveFeedbackMsgDisplayed();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("thank you so much");
        iosChatbotScreen.get().pressSendMessageBtn();
    }
    //None of the above - > I Received an extra item - > Assert on the displayed message - > Enter the extra product - > Assert that chat is assigned to Group "CX- conversation Group"
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void ReceivedExtraItem(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false, false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        //navigate to chatbot screen
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().isPageDisplayed();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressNoneOfTheAboveBtn();
        iosChatbotScreen.get().pressIReceivedExtraItem();
        iosChatbotScreen.get().IReceivedExtraItemMsgDisplayed();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("ice coffee");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().IAnagentWillGetAssignedToYouNowMsgDisplayed();
    }
}
