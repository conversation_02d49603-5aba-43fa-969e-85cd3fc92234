package customerApp.ios.chatbot;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.time.Duration;

public class IHaveAProblemComplaintTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void orderPaymentBalanceValueNoneOfTheAbove(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Press the freshDesk icon
       // iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose BalanceChargeIssue
        iosChatbotScreen.get().pressBalanceChargeIssueBtn();
        // choose NonOfTheAbove
        iosChatbotScreen.get().pressSomethingElseBtn();
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void orderPaymentBalanceValueBackToMainOptions(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Login with the same user we created an order for
        //Login
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        //Press the freshDesk icon
        //iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose BalanceChargeIssue
        iosChatbotScreen.get().pressBalanceChargeIssueBtn();
        // back
        iosChatbotScreen.get().pressBackBtn();
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void serviceBillSomethingElseWillRedirectCustomerToAgentInCX() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Press the freshDesk icon
        // iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select i have problem/complaint option
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        iosChatbotScreen.get().pressBillsDonationsPaymentBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().pressSomethingElse();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
        Assert.assertTrue(iosChatbotScreen.get().getChatBotRedirectedUserToTheCxAgentText()
                .contains("agent will get assigned to you now to review the case & take the needed action."));
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void serviceBillThenBackWillRedirectCustomerToHaveProblemMenu() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Press the freshDesk icon
        // iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        iosChatbotScreen.get().pressBillsDonationsPaymentBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().pressBackBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void theDeliveryAssociateThenInappropriateBehaviorWillRedirectCustomerToAgentInCX() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Press the freshDesk icon
        // iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        iosChatbotScreen.get().pressTheDeliveryAssociateBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().pressInappropriateBehaviorBtn();
        // Assert that chatbot replied to customer and asked him for more details
        iosChatbotScreen.get().isPleaseGiveUsMoreDetailsMsgDisplayed();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("the DA shouted at me");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
        Assert.assertTrue(iosChatbotScreen.get().getChatBotRedirectedUserToTheCxAgentText()
                .contains("agent will get assigned to you now to review the case & take the needed action."));
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void theDeliveryAssociateThenReportedCollectingWrongAmountThenPressOkWillEndConversation() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Press the freshDesk icon
        // iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        iosChatbotScreen.get().pressTheDeliveryAssociateBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().pressHeReportedCollectingAWrongAmountBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().isHowMuchDidTheAgentCollectMsgMsgDisplayed();
        //enter the value of collected amount
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("200");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().isWeWillReviewTheCaseAndAdjustTheCollectionShortlyMsgMsgDisplayed();
        iosChatbotScreen.get().pressOk();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed());
        Assert.assertEquals(iosChatbotScreen.get().getThankYouWeWillAddressThisShortlyGoodByeText(),"Thank you, we'll address this shortly. Goodbye for now.");
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void theDeliveryAssociateThenReportedCollectingWrongAmountWillRedirectCustomerToAgentInCX() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Press the freshDesk icon
        // iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        iosChatbotScreen.get().pressTheDeliveryAssociateBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().pressHeReportedCollectingAWrongAmountBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().isHowMuchDidTheAgentCollectMsgMsgDisplayed();
        //enter the value of collected amount
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("200");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().isWeWillReviewTheCaseAndAdjustTheCollectionShortlyMsgMsgDisplayed();
        iosChatbotScreen.get().pressNoIWantToSpeakToAnAgent();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isChatBotRedirectedUserToTheCxAgent());
        Assert.assertTrue(iosChatbotScreen.get().getChatBotRedirectedUserToTheCxAgentText()
                .contains("agent will get assigned to you now to review the case & take the needed action."));
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void theDeliveryAssociateThenNoneOfTheAboveThenEnterScenarioWillEndConversation() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //Update test data with current order status
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert that order status has been changed to completed
        //Assert completed key in statuses in order object has a timestamp value , indicating order has been marked as delivered successfully
        Assert.assertEquals(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatus(), "completed");
        Assert.assertFalse(defaultTestData.get()
                .getRandomTestUser().getAllOrders().getFirst().getStatuses().optString("completed").isEmpty());

        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Press the freshDesk icon
        // iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().isChatBotRedirectedUserToHaveProblemOrComplaintMenu();
        iosChatbotScreen.get().pressTheDeliveryAssociateBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().pressNonOfTheAbove();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        iosChatbotScreen.get().isPleaseShareWhatWentWrongMsgDisplayed();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("The DA didn't want to deliver the order ");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().takeActionIfNewMessageButtonDisplayed();
        Assert.assertTrue(iosChatbotScreen.get().isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed());
        Assert.assertEquals(iosChatbotScreen.get().getThankYouWeWillAddressThisShortlyGoodByeText(),"Thank you, we'll address this shortly. Goodbye for now.");
    }

    //----------------------------------IHaveAProblem/Complaint>> ApplyingACouponCode----------------------//
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void applyingValidPromoCode(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));
        //Login with the same user we created an order for
        //Login
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressApplyingACouponCodeBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("test98");
        iosChatbotScreen.get().pressSendMessageBtn();
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void applyingInValidPromoCode(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));
        //Login with the same user we created an order for
        //Login
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        //Press the freshDesk icon
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressApplyingACouponCodeBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("abc");
        iosChatbotScreen.get().pressSendMessageBtn();
    }
    //------------------- I have a problem/ Complaint --> Service bills & Donations payments -->I cannot pay my bill-----------------//
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void Icannotpaymybill () {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        //Login
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        //Press the freshDesk icon
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose Bills donations
        iosChatbotScreen.get().pressBillsDonationsPaymentBtn();
        // I can't pay
        iosChatbotScreen.get().pressICantPayMyBillBtn();
        Assert.assertTrue(iosChatbotScreen.get().isServiceBillsDonationsPaymentsMsgDisplayed());
    }
    //------------------- I have a problem/ Complaint --> Service bills & Donations payments -->Service fee got deducted but it didn't work-----------------//
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void servicefeegotdeductedbutitdidntwork () {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        //Login
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        //Press the freshDesk icon
        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose Bills donations
        iosChatbotScreen.get().pressBillsDonationsPaymentBtn();
        // I can't pay
        iosChatbotScreen.get().pressBillPaidButTheServiceDoesntWorkBtn();
        Assert.assertTrue(iosChatbotScreen.get().isServiceBillsDonationsPaymentsMsgDisplayed());

    }
    //------------------- I have a problem/ Complaint --> Service bills & Donations payments -->Service fee got deducted more than once-----------------//
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void servicefeegotdeductedmorethanone () {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        //Login
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        //Press the freshDesk icon
        iosHomeScreen.get().pressFreshDeskBtn();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("HI");
        iosChatbotScreen.get().pressSendMessageBtn();
        // choose IHaveAProblemComplaint
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        // choose Bills donations
        iosChatbotScreen.get().pressBillsDonationsPaymentBtn();
        //  choose Service fee got deducted more than once
        iosChatbotScreen.get().pressIGotChargedMoreThanOnceBtn();
        Assert.assertTrue(iosChatbotScreen.get().isServiceBillsDonationsPaymentsMsgDisplayed());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //  I have a problem /Complaint - > My Delivered Order - > Missing Products - > Choose None of the above -> Choose the order- >
    // Choose to refund/adjust balance - > payment method isn't cc -> Assign to CX conversation group
     public void missingProductNoneOfAboveAndPaymentIsNotCCFlow () throws InterruptedException {
        // Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        // Create Order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Assert the order has been created normally
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test data
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));
        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        Thread.sleep(100000);

        //Tab on more and go to order history
        iosHomeScreen.get().pressMoreTabBtn();
        // Press chatbot icon
        iosChatbotScreen.get().pressChatBotFromMore();

        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        Thread.sleep(70000);
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        iosChatbotScreen.get().pressMissingItemBtn();
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        iosChatbotScreen.get().pressNonOfThisProductsCheckBox();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        // Select the created order from API
        iosChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Select Missing product
        iosChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        iosChatbotScreen.get().pressSendMessageBtn();
        // Press Refund option
        iosChatbotScreen.get().pressRefundMoneyBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(iosChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem /Complaint - > My Delivered Order - > Missing Products - > Choose None of the above - >
    //Choose one of chosen order product- >Choose to refund/adjust balance - > payment method is cc -> Refund to CC- > Assert on the displayed message -> Chat should be assigned to CX- Conversation group
    public void missingProductNoneOfAboveAndPaymentMethodIsCCThenRefundToCCFlow () throws InterruptedException {
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,true,false,false,false));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        Thread.sleep(Duration.ofSeconds(5));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Assert that home screen is displayed
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        // Press chatbot icon
        iosHomeScreen.get().pressFreshDeskBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        iosChatbotScreen.get().pressMissingItemBtn();
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        iosChatbotScreen.get().pressNonOfThisProductsCheckBox();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());

        // Select the created order from API
        iosChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();

        // Select Missing product
        iosChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        iosChatbotScreen.get().pressSendMessageBtn();

        // Press Refund option
        iosChatbotScreen.get().pressRefundMoneyBtn();
        Assert.assertTrue(iosChatbotScreen.get().isWouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsgDisplayed());

        // Select Refund to Credit cart
        iosChatbotScreen.get().pressCreditCardBtn();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(iosChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());
    }
    @Test
    @Tags({ @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem /Complaint - > My Delivered Order - > Missing Products - > Choose None of the above - > Assert that last order is displayed - >Choose the order
    // - >  Choose one of chosen order  product- >Choose to refund/adjust balance - > payment method is cc ->  Refund to wallet - > Assert on the displayed message -> Chat should be assigned to CX- Conversation group
    public void missingProductAndPaymentMethodIsCCThenRefundToWalletFlow () throws InterruptedException {

        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        //Create order for random user and set it in test data
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cc"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,true,false,false,false));

        //Create & Pay for order in payment service & inai
        testExecutionHelper.get().createAndPayForOrderInPaymentService(defaultTestData.get(), false, "order");

        //Open Redirection page to authenticate payment
        webDriver.get().navigate().to(orderPaymentApiClient.get().payOrderInInai(defaultTestData.get().getRandomTestUser().getTestOrder(), "order",true));
        Thread.sleep(Duration.ofSeconds(5));

        //Complete order cycle through the apis
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(), 0, false);

        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        //Assert that home screen is displayed
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        iosHomeScreen.get().pressFreshDeskBtn();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a compliant!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        iosChatbotScreen.get().pressMissingItemBtn();
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        iosChatbotScreen.get().pressNonOfThisProductsCheckBox();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());

        // Select the created order from API
        iosChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();

        // Select Missing product
        iosChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        iosChatbotScreen.get().pressSendMessageBtn();

        // Press Refund Option
        iosChatbotScreen.get().pressRefundMoneyBtn();
        Assert.assertTrue(iosChatbotScreen.get().isWouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsgDisplayed());

        // Select Refund to Wallet
        iosChatbotScreen.get().pressWalletBtn();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(iosChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
//  I have a problem/ Complaint --> Order Payment/ Balance Value --> I paid more or less than what the DA reported--> Enter the amount -->
//  Assert on the displayed message--> press Ok -->Redirect customer to an agent in CX -Conversation Group
    public void iPaidMoreOrLessThanWhatTheDeliveryAssociateReportedThenPressOkFlow () {
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        //Assert that home screen is displayed
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();

        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressBalanceChargeIssueBtn();
        Assert.assertTrue(iosChatbotScreen.get().isTheIssueIsMsgDisplayed());
        iosChatbotScreen.get().pressIPaidMoreOrLessThanWhatTheDAReportedBtn();
        Assert.assertTrue(iosChatbotScreen.get().isHowMuchDidTheAgentCollectMsgMsgDisplayed());
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("200");
        iosChatbotScreen.get().pressSendMessageBtn();

        // Assert that we will review the case is displayed
        Assert.assertTrue(iosChatbotScreen.get().isWeWillReviewTheCaseAndAdjustTheCollectionShortlyMsgMsgDisplayed());

        // Press Ok option
        iosChatbotScreen.get().pressOk();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(iosChatbotScreen.get().isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //  I have a problem/ Complaint --> Order Payment/ Balance Value --> I paid more or less than what the DA reported--> Enter the amount -->
    //  Assert on the displayed message--> press No I Want To Speak To An Agent -->Redirect customer to an agent in CX -Conversation Group
    public void iPaidMoreOrLessThanWhatTheDeliveryAssociateReportedThenPressSpekToAgentFlow () {
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressBalanceChargeIssueBtn();
        Assert.assertTrue(iosChatbotScreen.get().isTheIssueIsMsgDisplayed());
        iosChatbotScreen.get().pressIPaidMoreOrLessThanWhatTheDAReportedBtn();
        Assert.assertTrue(iosChatbotScreen.get().isHowMuchDidTheAgentCollectMsgMsgDisplayed());
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("200");
        iosChatbotScreen.get().pressSendMessageBtn();

        // Assert that we will review the case is displayed
        Assert.assertTrue(iosChatbotScreen.get().isWeWillReviewTheCaseAndAdjustTheCollectionShortlyMsgMsgDisplayed());

        // Press I want to speak to agent option
        iosChatbotScreen.get().pressNoIWantToSpeakToAnAgent();

        // Assert that there is no new messages appear and user is assigned to agent
        Assert.assertFalse(iosChatbotScreen.get().isNewMessageButtonDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    // I have a problem / Complaint - > My Delivered Order - > Assert that options related to issue are displayed correctly ->Choose "poor quality or damaged items " - >
    // Assert the products displayed -> Choose one of the products = > insert the production Date  - > Chat should be assigned to CX conversation Group
    public void myDeliveredOrderHasPoorQualityThenSelectTheProductFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        // Create Order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the created order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        // Assert options for my delivered issue are displayed
        Assert.assertTrue(iosChatbotScreen.get().areOptionsForMyDeliveredOrderIssueDisplayed());
        iosChatbotScreen.get().pressPoorQualityOrDamagedItemsBtn();
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert the order product is displayed
        Assert.assertTrue(iosChatbotScreen.get().isOrderProductDisplayed());
        // Select product
        iosChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCanYouShareImgOrProductionDateMsgDisplayed());
        // Enter the production date
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("15-12-2023");
        iosChatbotScreen.get().pressSendMessageBtn();
        // Assert on the displayed message
        Assert.assertTrue(iosChatbotScreen.get().isCustomerSupportWillBeWithYouShortlyMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    // I have a problem / Complaint - > My Delivered Order - > Assert that options related to issue are displayed correctly - > Choose "poor quality or damaged items " - > Assert the products displayed ->
    // None of these products = >  Assert the last order is displayed -> Choose Order -> Choose one of the products - > Insert Production Date - >Assign chat to CX_conversation Group
    public void myDeliveredOrderHasPoorQualitySelectNoneOfTheseThenSelectProductFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        // Create the Order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        // Assert options for my delivered issue are displayed
        Assert.assertTrue(iosChatbotScreen.get().areOptionsForMyDeliveredOrderIssueDisplayed());
        iosChatbotScreen.get().pressPoorQualityOrDamagedItemsBtn();
        // open order products dropdown
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        Assert.assertTrue(iosChatbotScreen.get().isOrderProductDisplayed());
        iosChatbotScreen.get().pressNonOfThisProductsCheckBox();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        // Select the created order from API
        iosChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert order product is displayed
        Assert.assertTrue(iosChatbotScreen.get().isOrderProductDisplayed());
        // Select a product
        iosChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isCanYouShareImgOrProductionDateMsgDisplayed());
        // Enter production date
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("15-12-2023");
        iosChatbotScreen.get().pressSendMessageBtn();
        // Assert on the displayed message
        Assert.assertTrue(iosChatbotScreen.get().isCustomerSupportWillBeWithYouShortlyMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem / Complaint - > My Delivered Order - > Assert that options related to issue are displayed correctly - > Choose "poor quality or damaged items " - > Assert the products displayed
    // -> None of these products= > Assert the last order is displayed -> Choose Order
    //- > Assert that order products are displayed correctly -> Choose None of the above  - > Assign chat to CX_conversation Group
    public void myDeliveredOrderHasPoorQualitySelectNoneOfTheseFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        // Create Order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        // Assert options for my delivered issue are displayed
        Assert.assertTrue(iosChatbotScreen.get().areOptionsForMyDeliveredOrderIssueDisplayed());
        iosChatbotScreen.get().pressPoorQualityOrDamagedItemsBtn();
        // open order products dropdown
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        Assert.assertTrue(iosChatbotScreen.get().isOrderProductDisplayed());
        iosChatbotScreen.get().pressNonOfThisProductsCheckBox();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        // Select the created order from API
        iosChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert order product is displayed
        Assert.assertTrue(iosChatbotScreen.get().isOrderProductDisplayed());
        // Select None of these products option
        iosChatbotScreen.get().pressNonOfThisProductsCheckBox();
        iosChatbotScreen.get().pressSendMessageBtn();
        // Assert on the displayed message
        Assert.assertTrue(iosChatbotScreen.get().isCustomerSupportWillBeWithYouShortlyMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem / Complaint- > My Delivered Order - > Missing Products - > Assert that products related to last order should be displayed - > Choose product - >
    // Assert on displayed message - > Choose to Deliver- >Chat should be assigned to CX- Conversation Group
    public void missingProductSelectTheProductThenSelectToDeliverItFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        // Create Order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);
        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        iosChatbotScreen.get().pressMissingItemBtn();
        // open order products dropdown
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert the order product is displayed
        Assert.assertTrue(iosChatbotScreen.get().isOrderProductDisplayed());
        // Select missing product
        iosChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isDoYouWantToDeliverMissingItemOrRefundMsgDisplayed());
        // select to deliver missing item
        iosChatbotScreen.get().pressDeliverMissingItemsBtn();
        // Assert on the displayed message
        Assert.assertTrue(iosChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem/Complaint - > My Delivered Order - > Missing Products - > Assert that products related to last order should be displayed - > Choose product
    // - > Assert on displayed message - > Choose to Refund / Adjust Balance - > Order isn't CC- > Assert on the displayed message - >Chat should be assigned to CX conversation Group
    public void missingProductSelectTheProductThenSelectRefundTheOrderIsNotCCFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        // Create Order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);
        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start chatbot the Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        iosChatbotScreen.get().pressMissingItemBtn();
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Assert the order product is displayed
        Assert.assertTrue(iosChatbotScreen.get().isOrderProductDisplayed());
        // Select product
        iosChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        iosChatbotScreen.get().pressSendMessageBtn();
        // Press Refund option
        Assert.assertTrue(iosChatbotScreen.get().isDoYouWantToDeliverMissingItemOrRefundMsgDisplayed());
        iosChatbotScreen.get().pressRefundMoneyBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(iosChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //I have a problem /Complaint - > My Delivered Order - > Missing Products - > Choose None of the above
    // - > Assert the last order is displayed - > Choose order ->Choose None of these products - > Conversation should be assigned to CX conversation Group
    public void missingProductThenSelectNoneOfTheseFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        // Create Order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());
        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        iosChatbotScreen.get().pressMissingItemBtn();
        // open order products dropdown
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        iosChatbotScreen.get().pressNonOfThisProductsCheckBox();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        // Select the created order from API
        iosChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        // open order products dropdown
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        iosChatbotScreen.get().pressNonOfThisProductsCheckBox();
        iosChatbotScreen.get().pressSendMessageBtn();
        // Assert on the displayed message
        Assert.assertTrue(iosChatbotScreen.get().isThankYouAgentWillGetAssignedToYouYouShortlyMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    // I have a problem /Complaint - > My Delivered Order - > Missing Products - > Choose None of the above - > Assert the last order is displayed - >
    // Choose the order - >  Choose product- >Choose to Deliver the missed item - > Assert on the displayed message- >  Assert that chat is assigned to the CX conversation Group
    public void missingProductSelectNoneOfTheseThenSelectProductAndSelectToDeliverItFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        // Create Order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();
        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start the Chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        iosChatbotScreen.get().pressMissingItemBtn();
        // open order products dropdown
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        iosChatbotScreen.get().pressNonOfThisProductsCheckBox();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isWhichOrderDidYouFaceAProblemWithMsgDisplayed());
        // Select the created order from API
        iosChatbotScreen.get().selectOrderNumberElement(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        // open order products dropdown
        iosChatbotScreen.get().pressSelectAllThatApplyDropdown();
        // Select product
        iosChatbotScreen.get().pressFirstProductInTheOrderProductsList();
        iosChatbotScreen.get().pressSendMessageBtn();
        Assert.assertTrue(iosChatbotScreen.get().isDoYouWantToDeliverMissingItemOrRefundMsgDisplayed());
        // select to deliver missing item
        iosChatbotScreen.get().pressDeliverMissingItemsBtn();
        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(iosChatbotScreen.get().isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    // I have a problem /Complaint - > My Delivered Order - > Wrong order - >
    // Assert the displayed message - >  Enter the wrong order /product  - > Assign chat to CX conversation group
    public void wrongOrderFlow(){
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        // Create Order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Assert order has been created
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);

        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();
        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start the chatbot flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        iosChatbotScreen.get().pressIncorrectItemsOrOrderBtn();
        Assert.assertTrue(iosChatbotScreen.get().isHowWrongItemsWereWrongMsgDisplayed());

        // press the whole order is wrong button
        iosChatbotScreen.get().pressWholeOrderBtn();

        // Assert on the displayed message
        Assert.assertTrue(iosChatbotScreen.get().isPleaseEnterTheOrderNumberOnTheBagsYouReceivedMsgDisplayed());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        // Enter the Order number from the test data
        iosChatbotScreen.get().enterTextIntoChatbotTxtField(defaultTestData.get().getRandomTestUser().getAllOrders().getLast().getOrderId());
        iosChatbotScreen.get().pressSendMessageBtn();

        // Assert the Msg to assign user to an agent is displayed successfully
        Assert.assertTrue(iosChatbotScreen.get().isThankYouForClarificationAnAgentWillBeWithYouShortlyMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void thereIsAWrongProductInTheDeliveredOrder() {
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        // Create Order using API
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());

        // Complete the order
        testExecutionHelper.get().completeOrderCycleFromTheApis(defaultTestData.get(),0,false);
       //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();
        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start chatbot Flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMyDeliveredOrderBtn();
        iosChatbotScreen.get().pressIncorrectItemsOrOrderBtn();
        Assert.assertTrue(iosChatbotScreen.get().isHowWrongItemsWereWrongMsgDisplayed());
        // press one or more item is wrong
        iosChatbotScreen.get().pressOneOrMoreItemsBtn();

        // Assert if 'Thank you for the clarification, please enter the order number on the bags you received and an agent will be with you shortly' is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPleaseEnterProductYouReceivedInsteadWhatShouldBeReceivedMsgDisplayed());

        // Enter the wrong product instead of the expected
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Cheese croissant" +
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getName());
        iosChatbotScreen.get().pressSendMessageBtn();

        // Assert on the displayed message
        Assert.assertTrue(iosChatbotScreen.get().isThankYouForClarificationAnAgentWillBeWithYouShortlyMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //  I have a problem/ Complaint --> Making an order
    public void makingAnOrderFlow () {
        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();
        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start th flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressMakingAnOrderBtn();

        // Assert on the displayed message
        Assert.assertTrue(iosChatbotScreen.get().isThankYouAgentWillGetAssignedToYouYouShortlyMsgDisplayed());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //  I have a problem/ Complaint --> Order Payment/ Balance Value --> Order value was deducted several times
    public void orderValueIsDeductedSeveralTimesFlow () {

        // Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();

        // Assert that chatbot screen is displayed
        Assert.assertTrue(iosChatbotScreen.get().isPageDisplayed());

        // Start Chatbot flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressBalanceChargeIssueBtn();
        Assert.assertTrue(iosChatbotScreen.get().isTheIssueIsMsgDisplayed());
        iosChatbotScreen.get().pressIGotChargedMoreThanOnceBtn();

        // Assert on the displayed message
        Assert.assertTrue(iosChatbotScreen.get().isThankYouAgentWillGetAssignedToYouYouShortlyMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    //  I have a problem/ Complaint --> Order Payment/ Balance Value --> My Balance Value is inaccurate

    public void myBalanceValueIsInaccurateFlow () {
// Register and create order using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        //Login with the same user we created an order for
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Register with A valid Phone number
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();
        // Press chatbot icon
        iosHomeScreen.get().pressMoreTabBtn();
        iosChatbotScreen.get().pressChatBotFromMore();

        // Start the Chatbot flow
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hello! I have a complaint!");
        iosChatbotScreen.get().pressSendMessageBtn();
        iosChatbotScreen.get().pressIHaveAProblemComplaintBtn();
        iosChatbotScreen.get().pressBalanceChargeIssueBtn();
        Assert.assertTrue(iosChatbotScreen.get().isTheIssueIsMsgDisplayed());
        iosChatbotScreen.get().pressInaccurateBalanceValueBtn();

        // Assert on the displayed message
        Assert.assertTrue(iosChatbotScreen.get().isThankYouAgentWillGetAssignedToYouYouShortlyMsgDisplayed());
    }
}
