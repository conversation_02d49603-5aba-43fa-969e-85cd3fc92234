package customerApp.ios.chatbot;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class IHaveAnIssueTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateChatbotAssignOrderToCxAfterOrderIsLate(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        iosChatbotScreen.get().pressIHaveAnIssue();
        iosChatbotScreen.get().pressOrderIsLate();
        Assert.assertTrue(iosChatbotScreen.get().isAssignedToDaMsgIsDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateChatbotAssignOrderToCxAfterUserNeedToEditOrderOkOption() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        iosChatbotScreen.get().pressIHaveAnIssue();
        iosChatbotScreen.get().pressEditMyOrder();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField(defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());
        iosChatbotScreen.get().pressOk();
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateChatbotAssignOrderToCxAfterUserNeedToEditOrderSpeakToAgentOption() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        iosChatbotScreen.get().pressIHaveAnIssue();
        iosChatbotScreen.get().pressEditMyOrder();
        iosChatbotScreen.get().pressNoIWantToSpeakToAnAgent();
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateChatbotAssignOrderToCxAfterUserNeedToChangePaymentMethodOption() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        iosChatbotScreen.get().pressIHaveAnIssue();
        iosChatbotScreen.get().pressEditMyOrder();
        iosChatbotScreen.get().pressPaymentMethod();
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateChatbotAssignUserToCxToCancelOrderWhileOrderIsPacked() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        iosChatbotScreen.get().pressIHaveAnIssue();
        iosChatbotScreen.get().pressEditMyOrder();
        iosChatbotScreen.get().pressNonOfTheAbove();
        iosChatbotScreen.get().pressYesCancelMyOrder();
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateChatbotAssignUserToCxToKeepOrderWhileOrderIsPacked() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        iosChatbotScreen.get().pressIHaveAnIssue();
        iosChatbotScreen.get().pressEditMyOrder();
        iosChatbotScreen.get().pressNonOfTheAbove();
        iosChatbotScreen.get().pressNoKeepMyOrder();
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateChatbotDisplayEditOptionsWhileOrderIsntPacked() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        iosChatbotScreen.get().pressIHaveAnIssue();
        iosChatbotScreen.get().pressEditMyOrder();
        iosChatbotScreen.get().pressNonOfTheAbove();
        Assert.assertTrue(iosChatbotScreen.get().isCancelMsgIsDisplayed());
        iosChatbotScreen.get().pressOk();
        Assert.assertTrue(iosChatbotScreen.get().isAddAProductBtnDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isRemoveAProductBtnDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isChangeAddressBtnDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isNonOfAboveBtnDisplayed());
        Assert.assertTrue(iosChatbotScreen.get().isThankYouForChattingWithUsMsgDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateChatbotAssignToCxToEditOrderWhileOrderIsntPacked() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        iosChatbotScreen.get().pressIHaveAnIssue();
        iosChatbotScreen.get().pressEditMyOrder();
        iosChatbotScreen.get().pressNonOfTheAbove();
        iosChatbotScreen.get().pressNoIWantToSpeakToAnAgent();
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateChatbotShowCancelOrderStepsWhileOrderIsntPacked() {
        //Register user with API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "25"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false, false,false));
        //Assert order has been created and can retrieve the order id
        Assert.assertFalse(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId().isEmpty());

        //Get User orders and parse them to test user
        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        //Assert test order is the same as the order in all orders list
        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId()
                , defaultTestData.get().getRandomTestUser().getAllOrders().getFirst().getOrderId());
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue option
        iosChatbotScreen.get().pressIHaveAnIssue();
        iosChatbotScreen.get().pressCancelMyOrder();
        Assert.assertTrue(iosChatbotScreen.get().isCancelMsgIsDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void cancelOrderWithPackedStatues() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        // close mini tracker
        iosHomeScreen.get().pressDownArrowBtn();
        //navigate to chatbot screen
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().isPageDisplayed();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue with processing order option
        iosChatbotScreen.get().pressIHaveAnIssueWithProcessingOrder();
        //press I would like to cancel my order
        iosChatbotScreen.get().pressIWouldLikeToCancelMyOrderBtn();
        //Assert that cancellation reasons is Displayed
        iosChatbotScreen.get().isWrongPaymentMethodBtnDisplayed();
        iosChatbotScreen.get().isIOrderOnAWrongAddressBtnDisplayed();
        iosChatbotScreen.get().isDoNotNeedOrderAnYMoreBtnDisplayed();
        iosChatbotScreen.get().isNonOfTheAboveBtnDisplayed();
        //click on any reason
        iosChatbotScreen.get().PressdoNotNeedOrderAnyMoreBtn();
        iosChatbotScreen.get().pressNewMsgBtnIfDisplayed();
        //assert that your order will be cancelled Msg Displayed
        iosChatbotScreen.get().islastMsgDisplayed();
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void cancelOrderAndSelectNoneOfTheAboveReasonWithPackedStatues() {
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        //navigate to chatbot screen
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().isPageDisplayed();
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue with processing order option
        iosChatbotScreen.get().pressIHaveAnIssueWithProcessingOrder();
        //press I would like to cancel my order
        iosChatbotScreen.get().pressIWouldLikeToCancelMyOrderBtn();
        //Assert that cancellation reasons is Displayed
        iosChatbotScreen.get().isWrongPaymentMethodBtnDisplayed();
        iosChatbotScreen.get().isIOrderOnAWrongAddressBtnDisplayed();
        iosChatbotScreen.get().isDoNotNeedOrderAnYMoreBtnDisplayed();
        iosChatbotScreen.get().isNonOfTheAboveBtnDisplayed();
        // click on none of the above button
        iosChatbotScreen.get().pressNonOfTheAbove();
        //type the cancellation reason
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("there is a wrong items is added to the order");
        iosChatbotScreen.get().pressSendMessageBtn();
        //assert that your order will be cancelled Msg Displayed
        iosChatbotScreen.get().islastMsgDisplayed();
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void haveAnIssueWithOrderAndChangePaymentMethod(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        // close mini tracker
        iosHomeScreen.get().pressDownArrowBtn();
        //navigate to chatbot screen
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().isPageDisplayed();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue with processing order option
        iosChatbotScreen.get().pressIHaveAnIssueWithProcessingOrder();
        //change payment methods
        iosChatbotScreen.get().PressChangePaymentMethodBtn();
        iosChatbotScreen.get().islastMsgDisplayed();
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void haveAnIssueWithOrderAndSelectNonOfTheAbove(){
        //Register using API , Create address for user & create test order
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // creat order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false, false,false));

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        //Login with the same user we created an order for
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        // Validate that home screen is opened
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        // close mini tracker
        iosHomeScreen.get().pressDownArrowBtn();
        //navigate to chatbot screen
        iosHomeScreen.get().pressFreshDeskBtn();
        iosChatbotScreen.get().isPageDisplayed();
        // chat with cx
        iosChatbotScreen.get().enterTextIntoChatbotTxtField("Hi");
        iosChatbotScreen.get().pressSendMessageBtn();
        //select I have an issue with processing order option
        iosChatbotScreen.get().pressIHaveAnIssueWithProcessingOrder();
        //none of the above
        iosChatbotScreen.get().pressSomethingElseBtn();
        iosChatbotScreen.get().islastMsgDisplayed();
    }
}
