package customerApp.ios.authentication;

import base.BaseTest;
import helpers.factories.dataFactories.OtpFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class RegisterTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void registerAndLoginWithLocalValidPhoneNumber(){
        // Choose a country and press login
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        //Accept the notifications/tracking permissions request
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Enter Mobile Number
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");
        iosPhoneNumberScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());
        iosPhoneNumberScreen.get().pressNextBtn();

        //Validate OTP Screen
        Assert.assertTrue(iosOTPVerificationScreen.get().isPageHeaderDisplayed(),
                "OTP Page header is not displayed");
        Assert.assertTrue(iosOTPVerificationScreen.get().isPageSubHeaderDisplayed(),
                "OTP Page sub header is not displayed");
        Assert.assertTrue(iosOTPVerificationScreen.get()
                .isPhoneNumberDisplayed(defaultTestData.get().getRandomTestUser().getPhoneNumber()),
                "Mobile number is not displayed");

        //Get OTP from Slack
        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "register", defaultTestData.get().getRandomTestUser()));

        //Enter OTP
        iosOTPVerificationScreen.get().enterOTP(defaultTestData.get().getRandomTestUser().getOtp());

        //Enter account Information
        Assert.assertTrue(iosCreateAccountScreen.get().isTopHeaderDisplayed(),
                "CreateAccount page's top header is not displayed");
        Assert.assertTrue(iosCreateAccountScreen.get().isSubHeaderDisplayed(),
                "CreateAccount page's sub header is not displayed");
        iosCreateAccountScreen.get().fillInAccountInformationForm(defaultTestData.get());
        iosCreateAccountScreen.get().unFocusFromInputField();
        iosCreateAccountScreen.get().pressSubmitBtn();

        //Press the success button
        Assert.assertTrue(iosRegisterSuccessScreen.get().isConfirmationMessageDisplayed(),
                "Success message is not displayed");
        iosRegisterSuccessScreen.get().pressProceedBtn();

        //Reject Tracking permission
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("reject");

        //Go to more Tab
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressMoreTabBtn();

        //Dismiss the coachmarks
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();

        //Logout
        iosMoreScreen.get().isFullNameDisplayed(
                defaultTestData.get().getRandomTestUser().getFirstName(), defaultTestData.get().getRandomTestUser().getLastName());
        iosMoreScreen.get().pressLogoutBtn();

        //Location Permission
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        //Set address Screen -> Skip it
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Validate the redirection and that More tab is displayed
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().pressLoginBtn();

        //Login
        //Enter Mobile Number
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");
        iosPhoneNumberScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());
        iosPhoneNumberScreen.get().pressNextBtn();

        //Validate OTP Screen after login
        Assert.assertTrue(iosOTPVerificationScreen.get().isPageHeaderDisplayed(),
                "OTP Page header is not displayed");
        Assert.assertTrue(iosOTPVerificationScreen.get().isPageSubHeaderDisplayed(),
                "OTP Page sub header is not displayed");
        Assert.assertTrue(iosOTPVerificationScreen.get()
                        .isPhoneNumberDisplayed(defaultTestData.get().getRandomTestUser().getPhoneNumber()),
                "Mobile number is not displayed");

        //Get OTP from Slack
        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "login", defaultTestData.get().getRandomTestUser()));

        //Enter OTP
        iosOTPVerificationScreen.get().enterOTP(defaultTestData.get().getRandomTestUser().getOtp());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("foreign-country")})
    public void registerAndLoginWithValidForeignPhoneNumber(){
        // Choose a country and press login
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        //Accept the notifications/tracking permissions request
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Phone number screen validation
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");

        //Switch the country
        iosPhoneNumberScreen.get().pressCountryCodeDropDownBtn(
                defaultTestData.get().getRandomTestUser().getPhoneCountry());
        Assert.assertTrue(iosCountriesListScreen.get().isCountriesListScreenDisplayed());
        iosCountriesListScreen.get().searchForCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());
        iosCountriesListScreen.get().selectCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());

        //Enter the foreign phone number
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");
        iosPhoneNumberScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getForeignLocalPhoneNumber());
        iosPhoneNumberScreen.get().pressNextBtn();

        //Validate OTP Screen
        Assert.assertTrue(iosOTPVerificationScreen.get().isPageHeaderDisplayed(),
                "OTP Page header is not displayed");
        Assert.assertTrue(iosOTPVerificationScreen.get().isPageSubHeaderDisplayed(),
                "OTP Page sub header is not displayed");
        Assert.assertTrue(iosOTPVerificationScreen.get()
                        .isPhoneNumberDisplayed(defaultTestData.get().getRandomTestUser().getForeignPhoneNumber()),
                "Mobile number is not displayed");

        //Get OTP from Slack
        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "register", defaultTestData.get().getRandomTestUser()));

        //Enter OTP
        iosOTPVerificationScreen.get().enterOTP(defaultTestData.get().getRandomTestUser().getOtp());

        //Enter account Information
        Assert.assertTrue(iosCreateAccountScreen.get().isTopHeaderDisplayed(),
                "CreateAccount page's top header is not displayed");
        Assert.assertTrue(iosCreateAccountScreen.get().isSubHeaderDisplayed(),
                "CreateAccount page's sub header is not displayed");
        iosCreateAccountScreen.get().fillInAccountInformationForm(defaultTestData.get());
        iosCreateAccountScreen.get().unFocusFromInputField();
        iosCreateAccountScreen.get().pressSubmitBtn();

        //Press the success button
        Assert.assertTrue(iosRegisterSuccessScreen.get().isConfirmationMessageDisplayed(),
                "Success message is not displayed");
        iosRegisterSuccessScreen.get().pressProceedBtn();

        //Reject Tracking permission
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("reject");

        //Go to more Tab
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
        iosHomeScreen.get().pressMoreTabBtn();

        //Dismiss the coach-marks
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();

        //Logout
        iosMoreScreen.get().isFullNameDisplayed(
                defaultTestData.get().getRandomTestUser().getFirstName(), defaultTestData.get().getRandomTestUser().getLastName());
        iosMoreScreen.get().pressLogoutBtn();

        //Location Permission
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        //Set address Screen -> Skip it
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Validate the redirection and that More tab is displayed
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().pressLoginBtn();

        //Login
        //Enter Mobile Number
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");

        //Switch the country
        iosPhoneNumberScreen.get().pressCountryCodeDropDownBtn(
                defaultTestData.get().getRandomTestUser().getPhoneCountry());
        Assert.assertTrue(iosCountriesListScreen.get().isCountriesListScreenDisplayed());
        iosCountriesListScreen.get().searchForCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());
        iosCountriesListScreen.get().selectCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());

        //Enter the foreign phone number
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");
        iosPhoneNumberScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getForeignLocalPhoneNumber());
        iosPhoneNumberScreen.get().pressNextBtn();

        //Validate OTP Screen after login
        Assert.assertTrue(iosOTPVerificationScreen.get().isPageHeaderDisplayed(),
                "OTP Page header is not displayed");
        Assert.assertTrue(iosOTPVerificationScreen.get().isPageSubHeaderDisplayed(),
                "OTP Page sub header is not displayed");
        Assert.assertTrue(iosOTPVerificationScreen.get()
                        .isPhoneNumberDisplayed(defaultTestData.get().getRandomTestUser().getForeignPhoneNumber()),
                "Mobile number is not displayed");

        //Get OTP from Slack
        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get())
                .fetchOtp(defaultTestData.get(), "login", defaultTestData.get().getRandomTestUser()));

        //Enter OTP
        iosOTPVerificationScreen.get().enterOTP(defaultTestData.get().getRandomTestUser().getOtp());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void enterInvalidLocalPhoneNumbers(){
        // Choose a country and press login
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        //Accept the notifications/tracking permissions request
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Enter Mobile Number
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");
        iosPhoneNumberScreen.get().enterPhoneNumber("1234");
        iosPhoneNumberScreen.get().unFocusFromMobileNumberField();
        Assert.assertTrue(iosPhoneNumberScreen.get().isPhoneErrorValidationMsgDisplayed());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void enterInvalidForeignPhoneNumbers(){
        // Choose a country and press login
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        //Accept the notifications/tracking permissions request
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Phone number screen validation
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");

        //Switch the country
        iosPhoneNumberScreen.get().pressCountryCodeDropDownBtn(
                defaultTestData.get().getRandomTestUser().getPhoneCountry());
        Assert.assertTrue(iosCountriesListScreen.get().isCountriesListScreenDisplayed());
        iosCountriesListScreen.get().searchForCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());
        iosCountriesListScreen.get().selectCountry(defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());

        //Enter the foreign phone number
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen is not displayed");
        iosPhoneNumberScreen.get().enterPhoneNumber("1234");
        iosPhoneNumberScreen.get().unFocusFromMobileNumberField();
        Assert.assertTrue(iosPhoneNumberScreen.get().isPhoneErrorValidationMsgDisplayed());
    }
}
