package customerApp.ios.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class LoginTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void loginWithValidLocalPhoneNumber(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Logout
        iosTestsExecutionHelper.get().logout(iosDriver.get(), defaultTestData.get(), iosHomeScreen.get()
                , iosMoreScreen.get());

        //Location Permission
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        //Set address Screen -> Skip it
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Validate the redirection and that More tab is displayed
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().pressLoginBtn();

        //Login
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("foreign-country")})
    public void loginWithValidForeignPhoneNumber(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                testExecutionHelper.get(), defaultTestData.get(), iosCreateAccountScreen.get(),
                iosRegisterSuccessScreen.get(), iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());

        //Logout
        iosTestsExecutionHelper.get().logout(iosDriver.get(), defaultTestData.get(), iosHomeScreen.get()
                , iosMoreScreen.get());

        //Location Permission
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        //Set address Screen -> Skip it
        Assert.assertTrue(iosSetAddressScreen.get().isConfirmLocationBtnDisplayed());
        iosSetAddressScreen.get().pressConfirmLocationBtn();

        //Validate the redirection and that More tab is displayed
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().pressLoginBtn();

        //Assert the redirection to Phone screen
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed());

        //Login
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
    }
}
