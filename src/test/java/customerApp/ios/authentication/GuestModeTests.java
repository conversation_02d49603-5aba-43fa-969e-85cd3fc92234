package customerApp.ios.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class GuestModeTests extends BaseTest {

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void enterGuestMode(){
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressExploreBtn();

        Assert.assertTrue(iosLocationPermissionAlert.get().isLocationPermissionAlertDisplayed());
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        Assert.assertTrue(iosNotificationsPermissionsAlert.get().isNotificationsPermissionsAlertDisplayed());
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Set address Screen -> Skip it
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateCartBtnRedirectionInGuestMode(){
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressExploreBtn();

        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Set address Screen -> Skip it
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        iosHomeScreen.get().pressCartTabBtn();

        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed());
    }
}
