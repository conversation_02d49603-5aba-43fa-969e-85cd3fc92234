package customerApp.ios.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;

/**
 * Comprehensive iOS Login Validation Test Suite
 *
 * This test class validates user login functionality for the iOS customer app following
 * the Page Object Model pattern. It includes both positive and negative test scenarios
 * to ensure robust login validation.
 *
 * Test Coverage:
 * - Valid login scenarios (local and foreign phone numbers)
 * - Login after logout functionality
 * - Invalid phone number validation
 * - Empty field validation
 * - Invalid OTP validation
 * - Phone number field input validation
 *
 * Prerequisites:
 * - Tests use API calls to create user accounts for test data setup
 * - Tests are configured to run on Integration environment
 * - Tests follow Maven and TestNG framework patterns
 *
 * <AUTHOR> Automation Framework
 * @version 1.0
 */
@Test
public class LoginValidationTests extends BaseTest {

    /**
     * Test successful login with valid local phone number
     *
     * This test validates the complete login flow using a valid local phone number.
     * It uses API registration to create test data and verifies successful navigation
     * to the home screen after login.
     *
     * Test Steps:
     * 1. Register user via API to create test data
     * 2. Navigate to login screen
     * 3. Handle permission alerts
     * 4. Perform login with valid credentials
     * 5. Verify successful navigation to home screen
     *
     * Expected Result: User successfully logs in and home screen is displayed
     */
    @Test(groups = {"smoke"})
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void loginWithValidLocalPhoneNumber() {
        Reporter.log("Starting loginWithValidLocalPhoneNumber test");

        // Arrange - Register user using API to create test data
        Reporter.log("Setting up test data using API registration", 3, true);
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        // Act - Navigate to login screen and perform login
        Reporter.log("Navigating to login screen", 3, true);
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        // Handle permissions
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        Reporter.log("Performing login with valid local phone number", 3, true);
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Assert - Validate successful login
        Reporter.log("Validating successful login navigation to home screen", 3, true);
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed(),
                "Home page should be displayed after successful login");
    }

    /**
     * Test successful login with valid foreign phone number
     *
     * This test validates the login flow using a valid foreign phone number.
     * It verifies that the system correctly handles international phone numbers
     * and successfully authenticates users from different countries.
     *
     * Test Steps:
     * 1. Register user via API with foreign phone number
     * 2. Navigate to login screen
     * 3. Handle permission alerts
     * 4. Perform login with foreign phone number
     * 5. Verify successful navigation to home screen
     *
     * Expected Result: User with foreign phone number successfully logs in
     */
    @Test(groups = {"smoke"})
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("foreign-country")})
    public void loginWithValidForeignPhoneNumber() {
        Reporter.log("Starting loginWithValidForeignPhoneNumber test");

        // Arrange - Register user using API with foreign phone number
        Reporter.log("Setting up test data with foreign phone number using API registration", 3, true);
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        // Act - Navigate to login screen and perform login
        Reporter.log("Navigating to login screen", 3, true);
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        // Handle permissions
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        Reporter.log("Performing login with valid foreign phone number", 3, true);
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getForeignPhoneCountryCode());

        // Assert - Validate successful login
        Reporter.log("Validating successful login navigation to home screen", 3, true);
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed(),
                "Home page should be displayed after successful login with foreign phone number");
    }

    /**
     * Test login functionality after user logout
     *
     * This test validates that users can successfully log back in after logging out.
     * It ensures the complete logout-login cycle works correctly and maintains
     * user session integrity.
     *
     * Test Steps:
     * 1. Register and login user initially
     * 2. Perform logout
     * 3. Handle location permissions and address setup
     * 4. Navigate to login screen
     * 5. Perform login again
     * 6. Verify successful re-login
     *
     * Expected Result: User can successfully log back in after logout
     */
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void loginAfterLogout() {
        Reporter.log("Starting loginAfterLogout test");

        // Arrange - Register user and then logout
        Reporter.log("Setting up test data and performing initial registration", 3, true);
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Reporter.log("Performing logout", 3, true);
        iosTestsExecutionHelper.get().logout(iosDriver.get(), defaultTestData.get(), iosHomeScreen.get(),
                iosMoreScreen.get());

        // Handle location permission and address setup
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        // Act - Navigate to login and perform login
        Reporter.log("Navigating to login screen after logout", 3, true);
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().pressLoginBtn();

        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen should be displayed after pressing login button");

        Reporter.log("Performing login after logout", 3, true);
        iosTestsExecutionHelper.get().login(defaultTestData.get(), testExecutionHelper.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Assert - Validate successful re-login
        Reporter.log("Validating successful re-login navigation to home screen", 3, true);
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed(),
                "Home page should be displayed after successful re-login");
    }

    /**
     * Test login validation with invalid phone number format
     *
     * This negative test validates that the system properly handles and rejects
     * invalid phone number formats, displaying appropriate error messages.
     *
     * Test Steps:
     * 1. Navigate to login screen
     * 2. Enter invalid phone number format (too short)
     * 3. Unfocus from phone number field
     * 4. Verify error message is displayed
     *
     * Expected Result: Invalid phone number error message is displayed
     */
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void loginWithInvalidPhoneNumber() {
        Reporter.log("Starting loginWithInvalidPhoneNumber test");

        // Arrange - Navigate to login screen
        Reporter.log("Navigating to login screen", 3, true);
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        // Handle permissions
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        // Act - Enter invalid phone number
        Reporter.log("Entering invalid phone number format", 3, true);
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen should be displayed");

        iosPhoneNumberScreen.get().enterPhoneNumber("1234");
        iosPhoneNumberScreen.get().unFocusFromMobileNumberField();

        // Assert - Validate error message is displayed
        Reporter.log("Validating invalid phone number error message", 3, true);
        Assert.assertTrue(iosPhoneNumberScreen.get().isPhoneErrorValidationMsgDisplayed(),
                "Invalid phone number error message should be displayed for short phone number");
    }

    /**
     * Test login validation with empty phone number field
     *
     * This negative test validates that the system prevents progression when
     * the phone number field is left empty, ensuring required field validation.
     *
     * Test Steps:
     * 1. Navigate to login screen
     * 2. Attempt to proceed without entering phone number
     * 3. Verify that user remains on phone number screen
     *
     * Expected Result: User cannot proceed with empty phone number field
     */
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void loginWithEmptyPhoneNumber() {
        Reporter.log("Starting loginWithEmptyPhoneNumber test");

        // Arrange - Navigate to login screen
        Reporter.log("Navigating to login screen", 3, true);
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        // Handle permissions
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        // Act - Try to proceed without entering phone number
        Reporter.log("Attempting to proceed with empty phone number field", 3, true);
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen should be displayed");

        // Try to press next without entering phone number
        iosPhoneNumberScreen.get().pressNextBtn();

        // Assert - Validate that we remain on phone number screen (next button should be disabled or validation should prevent progression)
        Reporter.log("Validating that empty phone number prevents progression", 3, true);
        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Should remain on phone number screen when no phone number is entered");
    }

    /**
     * Test login validation with invalid OTP code
     *
     * This negative test validates that the system properly handles invalid OTP
     * codes and displays appropriate error messages during the verification process.
     *
     * Test Steps:
     * 1. Register user via API for test data
     * 2. Navigate to OTP verification screen
     * 3. Enter invalid OTP code
     * 4. Verify error message is displayed
     *
     * Expected Result: Invalid OTP error message is displayed
     */
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void loginWithInvalidOTP() {
        Reporter.log("Starting loginWithInvalidOTP test");

        // Arrange - Register user using API and navigate to OTP screen
        Reporter.log("Setting up test data using API registration", 3, true);
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        Reporter.log("Navigating to OTP verification screen", 3, true);
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        // Handle permissions
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        // Enter valid phone number to reach OTP screen
        iosPhoneNumberScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());
        iosPhoneNumberScreen.get().pressNextBtn();

        // Act - Enter invalid OTP
        Reporter.log("Entering invalid OTP code", 3, true);
        Assert.assertTrue(iosOTPVerificationScreen.get().isPageHeaderDisplayed(),
                "OTP verification screen should be displayed");

        iosOTPVerificationScreen.get().enterOTP("0000");

        // Assert - Validate error message for invalid OTP
        Reporter.log("Validating invalid OTP error message", 3, true);
        Assert.assertTrue(iosOTPVerificationScreen.get().isErrorMsgDisplayed(),
                "Invalid OTP error message should be displayed");
    }

    /**
     * Test comprehensive phone number field input validation
     *
     * This test validates various invalid phone number formats to ensure
     * the phone number field properly rejects invalid input and displays
     * appropriate error messages for different validation scenarios.
     *
     * Test Steps:
     * 1. Navigate to login screen
     * 2. Test phone number with mixed letters and numbers
     * 3. Test phone number with special characters
     * 4. Verify error messages for each invalid format
     *
     * Expected Result: Error messages displayed for all invalid formats
     */
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validatePhoneNumberFieldInput() {
        Reporter.log("Starting validatePhoneNumberFieldInput test");

        // Arrange - Navigate to login screen
        Reporter.log("Navigating to login screen", 3, true);
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        // Handle permissions
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        Assert.assertTrue(iosPhoneNumberScreen.get().isPageHeaderDisplayed(),
                "Phone number screen should be displayed");

        // Act & Assert - Test various invalid phone number formats
        Reporter.log("Testing phone number field validation with various invalid formats", 3, true);

        // Test with letters and numbers mixed
        iosPhoneNumberScreen.get().enterPhoneNumber("123abc456");
        iosPhoneNumberScreen.get().unFocusFromMobileNumberField();
        Assert.assertTrue(iosPhoneNumberScreen.get().isPhoneErrorValidationMsgDisplayed(),
                "Error message should be displayed for phone number with letters");

        // Test with special characters
        iosPhoneNumberScreen.get().enterPhoneNumber("123-456-789");
        iosPhoneNumberScreen.get().unFocusFromMobileNumberField();
        Assert.assertTrue(iosPhoneNumberScreen.get().isPhoneErrorValidationMsgDisplayed(),
                "Error message should be displayed for phone number with special characters");

        Reporter.log("Phone number field validation test completed successfully", 3, true);
    }
}
