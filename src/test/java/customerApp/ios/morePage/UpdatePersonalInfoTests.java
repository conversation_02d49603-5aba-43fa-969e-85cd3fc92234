package customerApp.ios.morePage;

import base.BaseTest;
import helpers.factories.dataFactories.OtpFactory;
import helpers.factories.dataFactories.UserDataFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.annotations.Test;

@Test
public class UpdatePersonalInfoTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateTheUpdateUserInfoIsWorkingCorrectly(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        iosHomeScreen.get().isHomePageDisplayed();

        //Go To account settings page
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();
        iosMoreScreen.get().pressAccountSettingsBtn();

        iosAccountSettingsScreen.get().pressUpdatePersonalInfoBtn();

        // Change user's first and last name
        defaultTestData.get().setRandomTestUser(new UserDataFactory(configs.get())
                .generateRandomUserDetails(defaultTestData.get().getRandomTestUser()));

        iosUpdateAccountSettingScreen.get().enterFirstName(defaultTestData.get().getRandomTestUser().getFirstName());
        iosUpdateAccountSettingScreen.get().enterLastName(defaultTestData.get().getRandomTestUser().getLastName());

        iosUpdateAccountSettingScreen.get().pressSaveChangesBtn();

        //ToDo: Add assertions (After information update, logout & login again then validate info is added correctly
        // to the screen as previously entered.)
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateTheUpdatePhoneNumberIsWorkingCorrectly(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        iosHomeScreen.get().isHomePageDisplayed();

        //Go To account settings page
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();
        iosMoreScreen.get().pressAccountSettingsBtn();

        iosAccountSettingsScreen.get().pressUpdatePersonalInfoBtn();

        //Change phone Number
        defaultTestData.get().setRandomTestUser(new UserDataFactory(configs.get())
                .generateRandomUserMobileNumber(defaultTestData.get().getRandomTestUser()));

        iosUpdateAccountSettingScreen.get().enterPhoneNumber(defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());
        iosUpdateAccountSettingScreen.get().pressSaveChangesBtn();

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get()).enterOtpInTextField(
                iosUpdatePhoneNumberOTPVerficationScreen.get().getOtpTxtField(),
                defaultTestData.get(),
                "updateAccount",
                defaultTestData.get().getRandomTestUser()));

        iosUpdatePhoneNumberOTPVerficationScreen.get().pressUpdatePhoneNumberBtn();

        //ToDo: Add assertions (After updating the phone number -> Logout then login using the new phone number)
    }
}
