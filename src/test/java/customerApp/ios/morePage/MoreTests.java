package customerApp.ios.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;

public class MoreTests extends BaseTest
{
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void moreScreensNavigation() {
        Reporter.log("Logging with known admin user and accept popups for location and notification");
        //Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //Press the Login or signup button
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(), iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Accept the notifications/tracking permissions request
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Accept location permission alert
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        iosTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Navigate to home screen
        iosHomeScreen.get().isHomePageDisplayed();
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        Reporter.log("Navigating to more screen");
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();
        Assert.assertTrue(iosMoreScreen.get().isPageDisplayed(), "More screen is not displayed!");

        Reporter.log("Navigating to activity history screen");
        iosMoreScreen.get().pressActivityHistoryBtn();
        Assert.assertTrue(iosActivityHistoryScreen.get().isPageDisplayed(), "Activity history screen is not displayed!");
        iosActivityHistoryScreen.get().pressBackBtn();
        Assert.assertTrue(iosMoreScreen.get().isPageDisplayed(), "More screen is not displayed!");

        Reporter.log("Navigating to favorites screen");
        iosMoreScreen.get().pressFavoritesBtn();
        Assert.assertFalse(iosMoreScreen.get().isPageDisplayed(), "Favorites Screen is not displayed!");
        iosFavoritesScreen.get().pressCloseBtn();
        Assert.assertTrue(iosMoreScreen.get().isPageDisplayed(), "More screen is not displayed!");

        Reporter.log("Navigating to free credit screen");
        iosMoreScreen.get().pressFreeCreditBtn();
        Assert.assertTrue(iosFreeCreditScreen.get().isPageDisplayed(), "Free Credit Screen is not displayed!");
        iosFreeCreditScreen.get().pressBackBtn();
        Assert.assertTrue(iosMoreScreen.get().isPageDisplayed(), "More screen is not displayed!");

        Reporter.log("Navigating to account settings screen");
        iosMoreScreen.get().pressAccountSettingsBtn();
        Assert.assertTrue(iosAccountSettingsScreen.get().isPageDisplayed(), "Account settings is not displayed!");
        iosAccountSettingsScreen.get().pressBackBtn();
        Assert.assertTrue(iosMoreScreen.get().isPageDisplayed(), "More screen is not displayed!");

        Reporter.log("Navigating to help screen");
        iosMoreScreen.get().pressHelpBtn();
        Assert.assertTrue(iosHelpScreen.get().isPageDisplayed(), "Help screen is not displayed!");
        iosHelpScreen.get().pressCloseBtn();
        Assert.assertTrue(iosMoreScreen.get().isPageDisplayed(), "More screen is not displayed!");

        Reporter.log("Navigating to chat screen");
        iosMoreScreen.get().pressTalkToUsBtn();
        Assert.assertTrue(iosFreshChatScreen.get().isPageDisplayed(), "Fresh chat screen is not displayed!");
        iosFreshChatScreen.get().pressCloseBtn();
        Assert.assertTrue(iosMoreScreen.get().isPageDisplayed(), "More screen is not displayed!");

        Reporter.log("Navigating to breadfast rewards screen");
        iosMoreScreen.get().pressBreadfastRewardsBtn();
        Assert.assertFalse(iosMoreScreen.get().isPageDisplayed(), "Favorites Screen is not displayed!");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void changeLanguage() {
        Reporter.log("Logging with known admin user and accept popups for location and notification");
        //Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //Press the Login or signup button
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(), iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Accept the notifications/tracking permissions request
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Accept location permission alert
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        iosTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Navigate to home screen
        iosHomeScreen.get().isHomePageDisplayed();
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        Reporter.log("Change app language to Arabic");
        iosTestsExecutionHelper.get().changeAppLanguage(iosHomeScreen.get(), iosMoreScreen.get(),
                iosChooseLanguageModal.get(), "Ar");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void changeCountry() {
        Reporter.log("Logging with known admin user and accept popups for location and notification");
        //Register using API
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        //Press the Login or signup button
        iosCountriesSelectionScreen.get().chooseCountryAndSubmit(defaultTestData.get().getTestCountryCode());
        iosLandingScreen.get().pressAuthHyperLink();

        iosTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(), iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(), iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Accept the notifications/tracking permissions request
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
        iosNotificationsPermissionsAlert.get().takeActionIfAlertIsDisplayed("accept");

        //Accept location permission alert
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        iosTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosPhoneNumberScreen.get(),
                iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(),
                defaultTestData.get().getRandomTestUser().getPhoneCountry());

        //Navigate to home screen
        iosHomeScreen.get().isHomePageDisplayed();
        iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");

        Reporter.log("Changing app country to ksa");
        iosTestsExecutionHelper.get().changeAppCountry(iosHomeScreen.get(), iosLandingScreen.get(),
                iosMoreScreen.get(), iosChooseCountryModal.get(), "ksa");
    }
}
