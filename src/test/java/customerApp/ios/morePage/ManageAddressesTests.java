package customerApp.ios.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class ManageAddressesTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateThatAddAddressesIsWorkingCorrectly(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get()
                , iosNotificationsPermissionsAlert.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , iosCreateAccountScreen.get()
                , iosRegisterSuccessScreen.get()
                , iosTrackingPermissionAlert.get()
                , iosHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().isPageDisplayed();

        iosMoreScreen.get().pressAccountSettingsBtn();
        iosAccountSettingsScreen.get().isPageDisplayed();

        iosAccountSettingsScreen.get().pressManageAddressesBtn();
        iosMyAddressesScreen.get().pressAddNewAddressBtn();
        iosDisabledLocationModal.get().dismissModalIfDisplayed();

        //Add address as current location
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        //Validate that address is submitted correctly
        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        //My Addresses List validation
        Assert.assertEquals(iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount(), 1
                , "Expecting number of addresses to be 1 but found "
                        + iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateThatUpdateAddressIsWorkingCorrectly(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get()
                , iosNotificationsPermissionsAlert.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , iosCreateAccountScreen.get()
                , iosRegisterSuccessScreen.get()
                , iosTrackingPermissionAlert.get()
                , iosHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().isPageDisplayed();

        iosMoreScreen.get().pressAccountSettingsBtn();
        iosAccountSettingsScreen.get().isPageDisplayed();

        iosAccountSettingsScreen.get().pressManageAddressesBtn();
        iosMyAddressesScreen.get().pressAddNewAddressBtn();
        iosDisabledLocationModal.get().dismissModalIfDisplayed();

        //Add address as current location
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        //Validate that address is submitted correctly
        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        //Update the created address with new information
        iosMyAddressesScreen.get().pressFirstAvailableAddress();
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());

        //Change address Info
        defaultTestData.get().getRandomTestUser().getAddress().setAddressLabel("Address"
                + testExecutionHelper.get().getCurrentTimeStamp("yyyyMMddHHmmss"));

        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().updateAddressLabel(
                defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        //Validate that address is submitted correctly
        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        //Validate that addresses list has only 1 address after updating label
        Assert.assertEquals(iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount(), 1
                , "Expecting number of addresses to be 1 but found "
                        + iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount());

        //Validate that address contains the new label only and not the old one
        Assert.assertTrue(iosMyAddressesScreen.get().getFirstAvailableAddressDisplayedTxt().substring(0
                        , iosMyAddressesScreen.get().getFirstAvailableAddressDisplayedTxt().indexOf(" "))
                .contains(defaultTestData.get().getRandomTestUser().getAddress().getAddressLabel()));
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateThatDeleteAddressIsWorkingCorrectly(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get()
                , iosNotificationsPermissionsAlert.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , iosCreateAccountScreen.get()
                , iosRegisterSuccessScreen.get()
                , iosTrackingPermissionAlert.get()
                , iosHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().isPageDisplayed();

        iosMoreScreen.get().pressAccountSettingsBtn();
        iosAccountSettingsScreen.get().isPageDisplayed();

        iosAccountSettingsScreen.get().pressManageAddressesBtn();
        iosMyAddressesScreen.get().pressAddNewAddressBtn();
        iosDisabledLocationModal.get().dismissModalIfDisplayed();

        //Add address as current location
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        //Validate that address is submitted correctly
        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        //My Addresses List validation
        Assert.assertEquals(iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount(), 1
                , "Expecting number of addresses to be 1 but found "
                        + iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount());

        iosMyAddressesScreen.get().pressAddNewAddressBtn();
        iosDisabledLocationModal.get().dismissModalIfDisplayed();

        //Add address as current location
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        //Validate that address is submitted correctly
        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        //My Addresses List validation
        Assert.assertEquals(iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount(), 2
                , "Expecting number of addresses to be 2 but found "
                        + iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount());

        // Go to secondly added address and press the delete button
        iosMyAddressesScreen.get().pressAddressByIndex(2);
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getDeleteAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressDeleteAddressBtn();
        Assert.assertTrue(iosDeleteAddressConfirmationModal.get().isModalDisplayed());

        iosDeleteAddressConfirmationModal.get().pressYesBtnAndDismissModal();
        //My Addresses List validation
        Assert.assertEquals(iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount(), 1
                , "Expecting number of addresses to be 1 but found "
                        + iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateThatDeleteDefaultAddressIsShowingAnError(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get()
                , iosNotificationsPermissionsAlert.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , testExecutionHelper.get()
                , defaultTestData.get()
                , iosCreateAccountScreen.get()
                , iosRegisterSuccessScreen.get()
                , iosTrackingPermissionAlert.get()
                , iosHomeScreen.get()
                , defaultTestData.get().getTestCountryCode());

        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().isPageDisplayed();

        iosMoreScreen.get().pressAccountSettingsBtn();
        iosAccountSettingsScreen.get().isPageDisplayed();

        iosAccountSettingsScreen.get().pressManageAddressesBtn();
        iosMyAddressesScreen.get().pressAddNewAddressBtn();
        iosDisabledLocationModal.get().dismissModalIfDisplayed();

        //Add address as current location
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        // Enter address details and submit the form
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosCreateAddressScreen.get().enterAddressDetails(defaultTestData.get().getRandomTestUser().getAddress());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getSaveAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressSaveAddressBtn();

        //Validate that address is submitted correctly
        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        //My Addresses List validation
        Assert.assertEquals(iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount(), 1
                , "Expecting number of addresses to be 1 but found "
                        + iosMyAddressesScreen.get().getCurrentlyDisplayedAddressesCount());

        iosMyAddressesScreen.get().pressAddNewAddressBtn();
        iosDisabledLocationModal.get().dismissModalIfDisplayed();

        //Add address as current location
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Validate that address is submitted correctly
        Assert.assertTrue(iosAddressCreateSuccessModal.get().isModalDisplayed());
        iosAddressCreateSuccessModal.get().dismissModal();

        // Go to secondly added address and press the delete button
        iosMyAddressesScreen.get().pressAddressByIndex(1);
        Assert.assertTrue(iosCreateAddressScreen.get().isPageDisplayed());
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosCreateAddressScreen.get().getScrollableContentContainer()
                , iosCreateAddressScreen.get().getDeleteAddressBtnNameSelector());
        iosCreateAddressScreen.get().pressDeleteAddressBtn();
        Assert.assertTrue(iosDeleteAddressErrorModal.get().isModalDisplayed());
    }
}
