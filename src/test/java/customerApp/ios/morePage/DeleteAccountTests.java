package customerApp.ios.morePage;

import base.BaseTest;
import helpers.factories.dataFactories.OtpFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class DeleteAccountTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void deleteAccountWithValidOtp(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Go To account settings page
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();

        //Go To account settings page
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();
        iosMoreScreen.get().pressAccountSettingsBtn();

        //Check validation of deleting account
        iosAccountSettingsScreen.get().pressDeleteAccount();
        iosDeleteAccountScreen.get().selectFirstReasonInList();
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosDeleteAccountScreen.get().getScrollableContentContainer()
                ,iosDeleteAccountScreen.get().getContinueBtnContentDescription());

        iosDeleteAccountScreen.get().pressContinueBtn();

        iosVerifyDeleteAccountScreen.get().pressVerifyMobileNumberBtn();

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get()).enterOtpInTextField(
                iosDeleteAccountOTPVerificationScreen.get().getOtpTextField(),
                defaultTestData.get(),
                "deleteAccount",
                defaultTestData.get().getRandomTestUser()));

        iosDeleteAccountOTPVerificationScreen.get().checkTermsAndConditionsCheckbox();

        iosDeleteAccountOTPVerificationScreen.get().pressDeleteAccountBtn();

        Assert.assertTrue(iosDeleteRequestScreen.get().isPageHeaderDisplayed()
                ,"Your delete request is sent.");

        iosDeleteRequestScreen.get().pressCloseBtn();

        //Location Permission
        iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");

        //Set address Screen -> Skip it
        iosSetAddressScreen.get().confirmLocationIfDisplayed();

        //Validate the redirection and that More tab is displayed
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().pressLoginBtn();

        //Login
        iosTestsExecutionHelper.get().login(defaultTestData.get()
                , testExecutionHelper.get()
                , iosPhoneNumberScreen.get()
                , iosCountriesListScreen.get()
                , iosOTPVerificationScreen.get()
                , defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosOTPVerificationScreen.get().isGreetingAfterDeletedAccountLoginDisplayed()
                ,"Greeting message is not displayed ");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void deleteAccountWithInValidOtp(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Validate that Home page is displayed right away
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());

        //Go To account settings page
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();

        //Go To account settings page
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();
        iosMoreScreen.get().pressAccountSettingsBtn();

        //Check validation of deleting account
        iosAccountSettingsScreen.get().pressDeleteAccount();
        iosDeleteAccountScreen.get().selectFirstReasonInList();
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosDeleteAccountScreen.get().getScrollableContentContainer()
                ,iosDeleteAccountScreen.get().getContinueBtnContentDescription());

        iosDeleteAccountScreen.get().pressContinueBtn();

        iosVerifyDeleteAccountScreen.get().pressVerifyMobileNumberBtn();

        iosDeleteAccountOTPVerificationScreen.get().enterOtp("1234");

        iosDeleteAccountOTPVerificationScreen.get().checkTermsAndConditionsCheckbox();

        iosDeleteAccountOTPVerificationScreen.get().pressDeleteAccountBtn();

        Assert.assertTrue(iosDeleteAccountOTPVerificationScreen.get().isErrorAlertDisplayed()
                , "Error message or alert is not displayed");
    }
}
