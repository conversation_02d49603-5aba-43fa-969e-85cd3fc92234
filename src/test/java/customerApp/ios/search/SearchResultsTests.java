package customerApp.ios.search;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class SearchResultsTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void SearchResultsAreRelevantToValidSearchKeyword(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();

        iosHomeScreen.get().pressOnSearchBtn();
        iosSearchScreen.get().isPageDisplayed();
        iosSearchScreen.get().enterKeywordForSearch(defaultTestData.get().getCustomerAppTestSession()
                .getNowOnlyProduct().getName());
        Assert.assertFalse(iosSearchResultScreen.get().ifNoResultFoundLabelReturned(),
                "No Results Found Page Is Returned");
        Assert.assertTrue(iosSearchResultScreen.get().checkProductResultsMatchKeyword(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowOnlyProduct().getName())
                , "There is an item doesn't match search keyword");
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void NoSearchResultsAreDisplayedForNotValidSearchKeyword(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();

        iosHomeScreen.get().pressOnSearchBtn();
        iosSearchScreen.get().isPageDisplayed();
        iosSearchScreen.get().enterKeywordForSearch("TTTT");
        Assert.assertTrue(iosSearchResultScreen.get().ifNoResultFoundLabelReturned()
                ,"Some products are returned");
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void ValidateAddToFavoriteOption() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pressOnSearchBtn();
        iosSearchScreen.get().isPageDisplayed();
        iosSearchScreen.get().enterKeywordForSearch(defaultTestData.get().getCustomerAppTestSession()
                .getNowOnlyProduct().getName());
        iosSearchResultScreen.get().pressFavIconOfItem(1);
        // open favorite view list
        iosSearchResultScreen.get().pressViewListBtn();

        // validate that favorite list is not empty
        Assert.assertFalse(iosFavoritesScreen.get().isFavListEmpty()
                ,"Item doesn't exist in favorites list");
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateProductIsDisplayedInSearchResults() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();

        iosHomeScreen.get().pressOnSearchBtn();
        iosSearchScreen.get().isPageDisplayed();
        iosSearchScreen.get().enterKeywordForSearch(defaultTestData.get().getCustomerAppTestSession()
                .getNowOnlyProduct().getName());
        Assert.assertTrue(iosSearchResultScreen.get().isProductDisplayedInSearchResults(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMysqlId()
        ),"product not returned");
    }
}
