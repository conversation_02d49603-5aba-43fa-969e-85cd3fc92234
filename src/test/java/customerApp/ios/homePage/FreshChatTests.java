package customerApp.ios.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class FreshChatTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateFreshChatIconFunctionality(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosHomeScreen.get().isFreshDeskBtnDisplayed(), "Fresh chat icon isn't displayed " +
                "and it should be");

        //Press the freshDesk icon
        iosHomeScreen.get().pressFreshDeskBtn();

        //Validate that page is opened normally
        Assert.assertTrue(iosFreshChatScreen.get().isPageDisplayed(), "FreshChat page isn't opened.");

        //Go To home page
        iosFreshChatScreen.get().pressCloseBtn();
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
    }
}
