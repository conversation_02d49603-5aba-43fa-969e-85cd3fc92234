package customerApp.ios.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class NowAndTomorrowTests extends BaseTest {

        @Test
        @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
        public void validateNowProductIsDisplayedForRegisteredUsers(){
                //Register
                iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                        iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                        iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                        iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                        defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                        iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                        defaultTestData.get().getTestCountryCode());

                //Add address as current location
                iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                        iosAddressSelectionScreen.get(),
                        iosLocationPermissionAlert.get(),
                        iosSetAddressScreen.get(),
                        iosDriver.get());

                iosHomeScreen.get().isHomePageDisplayed();

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosHomeScreen.get().getScrollableContentContainer()
                        , iosHomeScreen.get().getCategoryNameSelector(
                                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

                iosHomeScreen.get().pressCategory(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

                iosCategoryScreen.get().isPageDisplayed();

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "right"
                        , iosCategoryScreen.get().getScrollableContentContainer("subCategory")
                        , iosCategoryScreen.get().getSubCategoryNameSelector(
                                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

                iosCategoryScreen.get().pressSubCategory(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosCategoryScreen.get().getScrollableContentContainer("product")
                        , iosCategoryScreen.get().getProductCardNameSelector(
                                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));

                Assert.assertTrue(iosCategoryScreen.get().isProductCardDisplayed(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));
        }

        @Test
        @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
        public void validateTomorrowProductIsDisplayedForRegisteredUsers() {
                //Register
                iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                        iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                        iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                        iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                        defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                        iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                        defaultTestData.get().getTestCountryCode());

                //Add address as current location
                iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                        iosAddressSelectionScreen.get(),
                        iosLocationPermissionAlert.get(),
                        iosSetAddressScreen.get(),
                        iosDriver.get());

                iosHomeScreen.get().isHomePageDisplayed();

                // Change from Now to Tomorrow
                iosHomeScreen.get().pressChangeNowAndTomorrowBar();
                iosNowAndTomorrowModal.get().changeServeType("tomorrow");

                iosHomeScreen.get().isHomePageDisplayed();

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosHomeScreen.get().getScrollableContentContainer()
                        , iosHomeScreen.get().getCategoryNameSelector(
                                defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId()));

                iosHomeScreen.get().pressCategory(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId());

                iosCategoryScreen.get().isPageDisplayed();

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "right"
                        , iosCategoryScreen.get().getScrollableContentContainer("subCategory")
                        , iosCategoryScreen.get().getSubCategoryNameSelector(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getTomorrowOnlySubcategory().getId()));

                iosCategoryScreen.get().pressSubCategory(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlySubcategory().getId());

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosCategoryScreen.get().getScrollableContentContainer("product")
                        , iosCategoryScreen.get().getProductCardNameSelector(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getTomorrowOnlyProduct().getMongoId()));

                Assert.assertTrue(iosCategoryScreen.get().isProductCardDisplayed(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyProduct().getMongoId()));
        }
        @Test
        @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
        public void validateNowProductIsDisplayedForGuestUsers(){
                //Guest user
                iosTestsExecutionHelper.get().enterAsGuest(iosCountriesSelectionScreen.get(),
                        iosLandingScreen.get(),
                        iosLocationPermissionAlert.get(),
                        iosNotificationsPermissionsAlert.get(),
                        iosTrackingPermissionAlert.get(),
                        iosHomeScreen.get(),
                        iosSetAddressScreen.get(),
                        defaultTestData.get());

                // Scroll down until the category is shown
                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosHomeScreen.get().getScrollableContentContainer()
                        , iosHomeScreen.get().getCategoryNameSelector(
                                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

                iosHomeScreen.get().pressCategory(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());

                iosCategoryScreen.get().isPageDisplayed();

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "right"
                        , iosCategoryScreen.get().getScrollableContentContainer("subCategory")
                        , iosCategoryScreen.get().getSubCategoryNameSelector(
                                defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId()));

                iosCategoryScreen.get().pressSubCategory(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlySubCategory().getId());

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosCategoryScreen.get().getScrollableContentContainer("product")
                        , iosCategoryScreen.get().getProductCardNameSelector(
                                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));

                Assert.assertTrue(iosCategoryScreen.get().isProductCardDisplayed(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));
        }

        @Test
        @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
        public void validateTomorrowProductIsDisplayedForGuestUsers(){
                //Guest user
                iosTestsExecutionHelper.get().enterAsGuest(iosCountriesSelectionScreen.get(),
                        iosLandingScreen.get(),
                        iosLocationPermissionAlert.get(),
                        iosNotificationsPermissionsAlert.get(),
                        iosTrackingPermissionAlert.get(),
                        iosHomeScreen.get(),
                        iosSetAddressScreen.get(),
                        defaultTestData.get());

                // Change from Now to Tomorrow
                iosHomeScreen.get().pressChangeNowAndTomorrowBar();
                iosNowAndTomorrowModal.get().changeServeType("tomorrow");

                iosHomeScreen.get().isHomePageDisplayed();

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosHomeScreen.get().getScrollableContentContainer()
                        , iosHomeScreen.get().getCategoryNameSelector(
                                defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId()));

                iosHomeScreen.get().pressCategory(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyCategory().getId());

                iosCategoryScreen.get().isPageDisplayed();

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "right"
                        , iosCategoryScreen.get().getScrollableContentContainer("subCategory")
                        , iosCategoryScreen.get().getSubCategoryNameSelector(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getTomorrowOnlySubcategory().getId()));

                iosCategoryScreen.get().pressSubCategory(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlySubcategory().getId());

                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosCategoryScreen.get().getScrollableContentContainer("product")
                        , iosCategoryScreen.get().getProductCardNameSelector(
                                defaultTestData.get().getCustomerAppTestSession()
                                        .getTomorrowOnlyProduct().getMongoId()));

                Assert.assertTrue(iosCategoryScreen.get().isProductCardDisplayed(
                        defaultTestData.get().getCustomerAppTestSession().getTomorrowOnlyProduct().getMongoId()));
        }
        @Test
        @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping"),@Tag("ksa")})
        public void validateSelectDeliveryOptionsWillBeOpenedAfterLaunchingTheApp(){
                //Register
                iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                        iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                        iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                        iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                        defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                        iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                        defaultTestData.get().getTestCountryCode());

                Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
        }
        @Test
        @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping"),@Tag("ksa")})
        public void validateSelectDeliveryOptionsWillBeOpenedAndCanSelectTomorrow(){
                //Register
                iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                        iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                        iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                        iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                        defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                        iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                        defaultTestData.get().getTestCountryCode());

                Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
                iosAddressSelectionScreen.get().pressTomorrowOptionBtn();
                Assert.assertTrue(iosHomeScreen.get().isTomorrowTxtDisplayed(),
                        "Tomorrow Mode is Opened");
        }
        @Test
        @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
        public void validateLastModeSelectionStoresAfterLogoutAndLoginAgain(){
                //Register
                iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                        iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                        iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                        iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                        defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                        iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                        defaultTestData.get().getTestCountryCode());

                Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
                iosAddressSelectionScreen.get().pressTomorrowOptionBtn();
                Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
                iosHomeScreen.get().pressMoreTabBtn();
                iosMoreScreen.get().pressLogoutBtn();
                Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
                iosHomeScreen.get().pressMoreTabBtn();
                iosMoreScreen.get().pressLoginBtn();
                iosTestsExecutionHelper.get().login(defaultTestData.get()
                        , testExecutionHelper.get()
                        , iosPhoneNumberScreen.get()
                        , iosCountriesListScreen.get()
                        , iosOTPVerificationScreen.get()
                        , defaultTestData.get().getTestCountryCode());
                iosLocationPermissionAlert.get().takeActionIfAlertIsDisplayed("whileUsing");
                iosTrackingPermissionAlert.get().takeActionIfAlertIsDisplayed("accept");
                iosSetAddressScreen.get().confirmLocationIfDisplayed();
                Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
                Assert.assertTrue(iosHomeScreen.get().isTomorrowTxtDisplayed());
        }

        @Test
        @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("web"), @Tag("delivery-capacity-management")})
        public void validateThatNowSelectedByDefaultAfterFreshInstall(){
                //add capacity to an instant slot
                webLoginPage.get().goToLoginPage();
                webLoginPage.get().changeCountryCode();
                webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                        , defaultTestData.get().getAdminUser().getBypassScriptPassword());
                Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
                webDeliveryCapacityManagementPage.get().goToPage();
                webDeliveryCapacityManagementPage.get().choosefp();
                webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
                Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
                webDeliveryCapacityManagementPage.get().setCapacityValues(webDeliveryCapacityManagementPage.get().getNextTimeSlot24Hours(), "5");
                Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getCurrentTimeslotCapacity(), 5);
                Assert.assertTrue(deliveryCapacityManagementApiClient.get().getAllSlotsData(
                        defaultTestData.get().getAdminUser(),
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getInstantFlag());
                //Register
                iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                        iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                        iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                        iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                        defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                        iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                        defaultTestData.get().getTestCountryCode());

                Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
                iosAddressSelectionScreen.get().pressAddressOptionsCloseBtn();
                Assert.assertTrue(iosHomeScreen.get().isNowTxtDisplayed());
        }

        @Test
        @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("web"), @Tag("delivery-capacity-management")})
        public void validateTheBusyModalWithoutDismissingWhenFpIsBusyInKsa() {
//                // DCM For KSA not migrated yet but it will be migrated soon, so I added it for the future
//                webLoginPage.get().goToLoginPage();
//                webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
//                        , defaultTestData.get().getAdminUser().getBypassScriptPassword());
//
//                Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
//                webDeliveryCapacityManagementPage.get().goToPage();
//                webDeliveryCapacityManagementPage.get().choosefp();
//                webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
//                Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
//                Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(
//                                defaultTestData.get().getAdminUser(),
//                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getWarehouseStatus()
//                        , "BUSY");
                //Register
                iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                        iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                        iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                        iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                        defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                        iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                        defaultTestData.get().getTestCountryCode());
                Assert.assertTrue(iosHomeScreen.get().isBusyModalDisplayed());
                iosHomeScreen.get().pressSwitchToTomorrowInBusyModal();
                Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
                Assert.assertTrue(iosAddressSelectionScreen.get().isTomorrowOnlyIsAvailableOptionTxtDisplayed());
                Assert.assertTrue(iosAddressSelectionScreen.get().isTomorrowOptionBtnClickable());
        }
        @Test
        @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("web"), @Tag("delivery-capacity-management")})
        public void validateTheBusyModalAfterDismissingWhenFpIsBusyInKsa() {
//         //        DCM For KSA not migrated yet but it will be migrated soon, so I added it for the future
//                webLoginPage.get().goToLoginPage();
//                webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
//                        , defaultTestData.get().getAdminUser().getBypassScriptPassword());
//
//                Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
//                webDeliveryCapacityManagementPage.get().goToPage();
//                webDeliveryCapacityManagementPage.get().choosefp();
//                webDeliveryCapacityManagementPage.get().chooseFpByName(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
//                Assert.assertEquals(webDeliveryCapacityManagementPage.get().enableExtendedHrsToggle(), "ENABLED");
//                Assert.assertEquals(deliveryCapacityManagementApiClient.get().getAllSlotsData(
//                                defaultTestData.get().getAdminUser(),
//                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId()).getFirst().getWarehouseStatus()
//                        , "BUSY");
                //Register
                iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                        iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                        iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                        iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                        defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                        iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                        defaultTestData.get().getTestCountryCode());
                Assert.assertTrue(iosHomeScreen.get().isBusyModalDisplayed());
                iosHomeScreen.get().pressCloseBusyModalIcon();
                Assert.assertTrue(iosAddressSelectionScreen.get().isModalDisplayed());
                Assert.assertTrue(iosAddressSelectionScreen.get().isTomorrowOnlyIsAvailableOptionTxtDisplayed());
                iosAddressSelectionScreen.get().pressTomorrowOptionBtn();
                Assert.assertTrue(iosHomeScreen.get().isTomorrowTxtDisplayed());
        }
}
