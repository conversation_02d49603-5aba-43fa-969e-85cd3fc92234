package customerApp.ios.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class ReferralTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateReferralIconIsNotDisplayedInGuestMode(){
        iosTestsExecutionHelper.get().enterAsGuest(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(),
                iosLocationPermissionAlert.get(),
                iosNotificationsPermissionsAlert.get(),
                iosTrackingPermissionAlert.get(),
                iosHomeScreen.get(),
                iosSetAddressScreen.get(),
                defaultTestData.get());

        Assert.assertFalse(iosHomeScreen.get().isReferralBtnDisplayed());
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateReferralProgressBarsAreDisplayedForNewUsers(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Go To Referral screen
        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().isReferralBtnDisplayed();
        iosHomeScreen.get().pressReferralBtn();

        //Check progress bars
        Assert.assertTrue(iosReferralScreen.get().isProgressBarsDisplayed());
    }
}
