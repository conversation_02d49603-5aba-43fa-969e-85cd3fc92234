package customerApp.ios.homePage;
import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class CategoriesAndProductsTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatAllNowCategoriesRetrievedFromApiAreDisplayed() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();

        validationResults.set(iosTestsExecutionHelper.get().areAllCategoriesDisplayed(iosDriver.get()
                , iosHomeScreen.get()
                , iosCategoryScreen.get()
                , defaultTestData.get().getCustomerAppTestSession().getNowCategoriesInWarehouse()
                , "now"
                , validationResults.get()));

        Assert.assertTrue(validationResults.get().isResult(), String.join("\n"
                , validationResults.get().getValidationResults()));
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatAddProductToFavoriteListIsAddedFromProductDetails(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();
        //Scroll until category displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));
        // Select category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());
        iosCategoryScreen.get().isPageDisplayed();
        iosCategoryScreen.get().pressProductCard((
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));
        iosProductDetailsScreen.get().pressFavoriteIcon();
        iosProductDetailsScreen.get().pressBackBtn();
        iosCategoryScreen.get().pressBackBtn();
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();
        iosMoreScreen.get().pressFavoritesBtn();
        Assert.assertFalse(iosFavoritesScreen.get().isFavListEmpty()
                ,"Item doesn't exist in favorites list");
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatProductAddedToFavoriteCanBeRemovedFromTheList(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();
        //Scroll until category displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));
        // Select category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());
        iosCategoryScreen.get().isPageDisplayed();
        // Select Product
        iosCategoryScreen.get().pressProductCard((
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));
        iosProductDetailsScreen.get().pressFavoriteIcon();
        iosProductDetailsScreen.get().pressBackBtn();
        iosCategoryScreen.get().pressBackBtn();
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().dismissCoachMarksIfDisplayed();
        iosMoreScreen.get().pressFavoritesBtn();
        // make sure that product added to favorite list
        Assert.assertFalse(iosFavoritesScreen.get().isFavListEmpty()
                ,"Item doesn't exist in favorites list");
        // go back to remove product from favorite list
        iosFavoritesScreen.get().pressFavoriteBackBtn();
        iosMoreScreen.get().pressHomeNavBarIcon();
        iosHomeScreen.get().isHomePageDisplayed();
        //Scroll until category displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));
        // Select category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());
        iosCategoryScreen.get().isPageDisplayed();
        // Select product
        iosCategoryScreen.get().pressProductCard((
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));
        iosProductDetailsScreen.get().pressFavoriteIcon();
        iosProductDetailsScreen.get().pressBackBtn();
        iosCategoryScreen.get().pressBackBtn();
        iosHomeScreen.get().pressMoreTabBtn();
        iosMoreScreen.get().pressFavoritesBtn();
        Assert.assertTrue(iosFavoritesScreen.get().isFavListEmpty());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatProductAddedToTheCartFromProductDetails() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();
        //Scroll until category displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosHomeScreen.get().getScrollableContentContainer()
                , iosHomeScreen.get().getCategoryNameSelector(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));
        // Select category
        iosHomeScreen.get().pressCategory(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId());
        iosCategoryScreen.get().isPageDisplayed();
        // Select Product
        iosCategoryScreen.get().pressProductCard((
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyProduct().getMongoId()));
        iosProductDetailsScreen.get().pressAddToCartBtn();
        iosProductDetailsScreen.get().pressGoToCartBtn();
        Assert.assertFalse(iosCartScreen.get().isCartEmpty());
    }
}
