package customerApp.ios.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class RecommendationsTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateRecommendationsSectionIsNotShownForGuestUsers(){
        iosTestsExecutionHelper.get().enterAsGuest(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(),
                iosLocationPermissionAlert.get(),
                iosNotificationsPermissionsAlert.get(),
                iosTrackingPermissionAlert.get(),
                iosHomeScreen.get(),
                iosSetAddressScreen.get(),
                defaultTestData.get());

        Assert.assertFalse(iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosHomeScreen.get().getScrollableContentContainer()
                        , "home_recommendations_sectionTitle")
                , "Scrolled and found the recommendations title text and it shouldn't be displayed");

        Assert.assertFalse(iosHomeScreen.get().isRecommendationsSectionDisplayed(), "Recommendations section " +
                "is displayed and it shouldn't");
    }

    @Test
    @Tags({@Tag("ios"), @Tag("customer-app")})
    public void validateRecommendationsSectionIsDisplayedForRegisteredUsers(){
        //Register with A valid Phone number
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(), defaultTestData.get().getTestCountryCode());

        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();

        Assert.assertTrue(iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosHomeScreen.get().getScrollableContentContainer()
                        , "home_recommendations_sectionTitle")
                , "Scrolled and didn't find the recommendations title text and it should be displayed");

        Assert.assertTrue(iosHomeScreen.get().isRecommendationsSectionDisplayed(), "Recommendations section " +
                "isn't displayed and it should");

        iosHomeScreen.get().pressViewAllRecommendationsBtn();

        Assert.assertTrue(iosRecommendationsScreen.get().isPageDisplayed(),
                "Recommendations Page is not displayed and it should");
    }
}
