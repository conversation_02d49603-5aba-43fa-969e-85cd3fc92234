package customerApp.ios.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class BannersTests extends BaseTest {
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatBannerAppearsOnHomeScreen(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();
        Assert.assertTrue(iosHomeScreen.get().isHomeScreenFirstBannerDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void validateThatBannerAppearsOnHomeScreenAfterReload(){
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();
        iosHomeScreen.get().pullToRefresh();
        Assert.assertTrue(iosHomeScreen.get().isHomeScreenFirstBannerDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void clickingOnFirstBannerToOpen () {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();
        Assert.assertTrue(iosHomeScreen.get().isHomeScreenFirstBannerDisplayed());
        iosHomeScreen.get().clickFirstBanner();
        iosOpenedBannerModalScreen.get().getOpenedBannerAction();
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void takeActionBasedOnTheOpenedBanner() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();
        Assert.assertTrue(iosHomeScreen.get().isHomeScreenFirstBannerDisplayed());
        iosHomeScreen.get().clickFirstBanner();
        iosOpenedBannerModalScreen.get().getOpenedBannerAction();
        Assert.assertTrue(iosHomeScreen.get().isHomePageDisplayed());
    }
    @Test
    @Tags({@Tag("ios"), @Tag("customer-app"), @Tag("mobile-shopping")})
    public void changeFPAndCheckBanners() {
        //Register
        iosTestsExecutionHelper.get().register(iosCountriesSelectionScreen.get(),
                iosLandingScreen.get(), iosNotificationsPermissionsAlert.get(),
                iosPhoneNumberScreen.get(), iosCountriesListScreen.get(),
                iosOTPVerificationScreen.get(), testExecutionHelper.get(),
                defaultTestData.get(), iosCreateAccountScreen.get(), iosRegisterSuccessScreen.get(),
                iosTrackingPermissionAlert.get(), iosHomeScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Add address as current location
        iosTestsExecutionHelper.get().addNewAddressAsCurrentLocation(iosHomeScreen.get(),
                iosAddressSelectionScreen.get(),
                iosLocationPermissionAlert.get(),
                iosSetAddressScreen.get(),
                iosDriver.get());

        iosHomeScreen.get().isHomePageDisplayed();
        Assert.assertTrue(iosHomeScreen.get().isHomeScreenFirstBannerDisplayed());
    }
}
