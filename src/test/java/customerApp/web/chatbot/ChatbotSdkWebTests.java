package customerApp.web.chatbot;

import base.BaseTest;
import helpers.dataProviders.ThreaderDataProviderSource;
import helpers.dataProviders.UsersChatbotTokensProviderSource;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class ChatbotSdkWebTests extends BaseTest {
    @Test
    @Tags({@Tag("web"), @Tag("Database")})
    public void validateChatbotSessionCreationFlow(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                chatbotApiClient.get().generateChatbotJwt(
                        defaultTestData.get().getRandomTestUser()));

        webChatbotSdkHostPage.get().goToUrl("https://wqcw93.csb.app/");

        webChatbotSdkHostPage.get().pressProceedAnywayBtn();

        Assert.assertTrue(webChatbotSdkHostPage.get().isChatbotIconDisplayed());

        webChatbotSdkHostPage.get().fillInHostPageFormAndSubmit(
                defaultTestData.get().getRandomTestUser().getChatbotJwtToken()
                , defaultTestData.get().getRandomTestUser().getFullName()
                , defaultTestData.get().getRandomTestUser().getId()
                , "en");

        webChatbotSdkHostPage.get().pressChatbotBtn();

        webChatbotSdk.get().enterMessageIntoChatTextField("Hi");

        Assert.assertTrue(webChatbotSdk.get().isBreadfastSupportDisplayed());
    }

    @Test(dataProvider = "threadCountsProvider", dataProviderClass = ThreaderDataProviderSource.class)
    @Tags({@Tag("web"), @Tag("Database")})
    public void validateChatbotSessionCreationFlowForStressTesting(int threadCount){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                chatbotApiClient.get().generateChatbotJwt(
                        defaultTestData.get().getRandomTestUser()));

        webChatbotSdkHostPage.get().goToUrl("https://wqcw93.csb.app/");

        webChatbotSdkHostPage.get().pressProceedAnywayBtn();

        Assert.assertTrue(webChatbotSdkHostPage.get().isChatbotIconDisplayed());

        webChatbotSdkHostPage.get().fillInHostPageFormAndSubmit(
                defaultTestData.get().getRandomTestUser().getChatbotJwtToken()
                , defaultTestData.get().getRandomTestUser().getFullName()
                , defaultTestData.get().getRandomTestUser().getId()
                , "en");

        webChatbotSdkHostPage.get().pressChatbotBtn();

        webChatbotSdk.get().enterMessageIntoChatTextField("Hi");

        Assert.assertTrue(webChatbotSdk.get().isBreadfastSupportDisplayed());
    }

    @Test(dataProvider = "chatbotTokensProvider", dataProviderClass = UsersChatbotTokensProviderSource.class)
    @Tags({@Tag("web")})
    public void validateChatbotSessionCreationFlowUsingDataProviderCsvSheet(String id,
                                                                            String token,
                                                                            String fullName,
                                                                            String lang){
        webChatbotSdkHostPage.get().goToUrl("https://wqcw93.csb.app/");

        webChatbotSdkHostPage.get().pressProceedAnywayBtn();

        Assert.assertTrue(webChatbotSdkHostPage.get().isChatbotIconDisplayed());

        webChatbotSdkHostPage.get().fillInHostPageFormAndSubmit(
                token,
                fullName,
                id,
                lang);

        webChatbotSdkHostPage.get().pressChatbotBtn();

        webChatbotSdk.get().enterMessageIntoChatTextField("Hi");

        Assert.assertTrue(webChatbotSdk.get().isBreadfastSupportDisplayed());
    }
}
