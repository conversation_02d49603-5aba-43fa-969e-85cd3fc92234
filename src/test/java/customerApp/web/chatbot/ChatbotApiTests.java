package customerApp.web.chatbot;

import base.BaseTest;
import helpers.dataProviders.ThreaderDataProviderSource;
import helpers.dataProviders.UsersChatbotTokensProviderSource;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;

@Test
public class ChatbotApiTests extends BaseTest {
    @Test(dataProvider = "threadCountsProvider", dataProviderClass = ThreaderDataProviderSource.class)
    @Tags({@Tag("database")})
    public void createFreshChatConversation(int threadsCount){
        Reporter.log("Initiating test run #: " + threadsCount, 3, true);
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                chatbotApiClient.get().generateChatbotJwt(
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().setRandomTestUser(
                freshChatApiClient.get().createUserOnFreshChat(
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().setTestConversation(
                freshChatApiClient.get().createConversationOnFreshChat(
                        defaultTestData.get().getRandomTestUser()
                        , defaultTestData.get().getTestConversation()
                        , "Hi!"));

        defaultTestData.get().setTestConversation(
                freshChatApiClient.get().listMessagesInConversation(
                        defaultTestData.get().getTestConversation()));

        Assert.assertFalse(defaultTestData.get().getTestConversation().getMessages().isEmpty()
                , "No reply to the Hi! after 10 seconds. Conversation ID: "
                        + defaultTestData.get().getTestConversation().getConversationId());

        Assert.assertTrue(defaultTestData.get().getTestConversation().getMessages().getFirst().getTextContent()
                        .contains("! Thank you for using Breadfast! How can I help you?")
                , "The reply to the Hi is not containing the expected message. Conversation ID: "
                        + defaultTestData.get().getTestConversation().getConversationId());
    }

    @Test(dataProvider = "chatbotTokensProvider", dataProviderClass = UsersChatbotTokensProviderSource.class)
    @Tags({@Tag("SkipAdminAuthorizationStep")})
    public void createFreshChatConversationForPreGeneratedTokens(String id,
                                                                 String token,
                                                                 String fullName,
                                                                 String lang){
        Reporter.log("Initiating test run for user with ID: " + id, 3, true);
        defaultTestData.get().getRandomTestUser().setId(id);
        defaultTestData.get().getRandomTestUser().setChatbotJwtToken(token);

        defaultTestData.get().setRandomTestUser(
                freshChatApiClient.get().createUserOnFreshChat(
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().setTestConversation(
                freshChatApiClient.get().createConversationOnFreshChat(
                        defaultTestData.get().getRandomTestUser()
                        , defaultTestData.get().getTestConversation()
                        , "Hi!"));

        defaultTestData.get().setTestConversation(
                freshChatApiClient.get().listMessagesInConversation(
                        defaultTestData.get().getTestConversation()));

        Assert.assertFalse(defaultTestData.get().getTestConversation().getMessages().isEmpty()
                , "No reply to the Hi! after 10 seconds. Conversation ID: "
                        + defaultTestData.get().getTestConversation().getConversationId());

        Assert.assertTrue(defaultTestData.get().getTestConversation().getMessages().getFirst().getTextContent()
                .contains("! Thank you for using Breadfast! How can I help you?")
                , "The reply to the Hi is not containing the expected message. Conversation ID: "
                        + defaultTestData.get().getTestConversation().getConversationId());
    }
}
