package customerApp.iosNative.morePage;

import base.BaseTest;
import helpers.factories.dataFactories.UserDataFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class EmailAddressScreenTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void CheckLoggedUserCanViewAndUpdateHisEmailAddress(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        iosNativeMoreScreen.get().pressMoreTabBtn();
        iosNativeMoreScreen.get().pressAccountSettingsBtn();

        //Assert user is on the account settings screen
        Assert.assertTrue(iOSNativeAccountSettingsScreen.get().isPageDisplayed()
                ,"User is not on the account settings page");

        iOSNativeAccountSettingsScreen.get().pressMailAddress();

        // Assert the user is on the mail address screen
        Assert.assertTrue(iosNativeEmailAddressScreen.get().isEmailPageDisplayed());

        defaultTestData.get().setRandomTestUser(new UserDataFactory(configs.get())
                .generateRandomUserEmailAndPassword(defaultTestData.get().getRandomTestUser()));
        iosNativeEmailAddressScreen.get().updateUserMail(defaultTestData.get().getRandomTestUser().getEmailAddress());
        iosNativeEmailAddressScreen.get().pressOnUpdateMailBtn();

        // Assert the mail is updated normally
        Assert.assertTrue(iosNativeEmailAddressScreen.get().isMailUpdatedTagDisplayed()
                ,"Mail is not updated");
    }
}
