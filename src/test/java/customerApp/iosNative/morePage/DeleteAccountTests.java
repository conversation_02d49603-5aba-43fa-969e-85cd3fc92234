package customerApp.iosNative.morePage;

import base.BaseTest;
import helpers.factories.dataFactories.OtpFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class DeleteAccountTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void CheckUserCanDeleteAccount() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        iosNativeMoreScreen.get().pressMoreTabBtn();
        iosNativeMoreScreen.get().pressAccountSettingsBtn();
        iOSNativeAccountSettingsScreen.get().pressDeleteAccountTab();

        //Assert user is On the delete account page
        Assert.assertTrue(iosNativeDeleteAccountScreen.get().isPageDisplayed()
                ,"Delete Account page is not displayed.");

        // Fetch delete reasons before pressing on them
        testExecutionHelper.get().fetchDeleteReasons(defaultTestData.get().getRandomTestUser(),defaultTestData.get());

      iosNativeDeleteAccountScreen.get().pressDeleteFirstReason(defaultTestData.get().getDeleteReasons().getFirst());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeDeleteAccountScreen.get().getScrollableContentContainer()
                ,iosNativeDeleteAccountScreen.get().getContinueBtnNameSelector());

        iosNativeDeleteAccountScreen.get().pressContinueBtnToDelete();

        //Assert user is on the last step to delete his account
        Assert.assertTrue(iosNativeDeleteAccountScreen.get().isDeleteAccountConfirmationScreenDisplayed()
                ,"User is not on the last step to delete");

        iosNativeDeleteAccountScreen.get().pressVerifyMobileNumber();

        defaultTestData.get().setRandomTestUser(new OtpFactory(configs.get()).enterOtpInTextField(
                iosNativeDeleteAccountOTPVerificationScreen.get().getOtpTextField(),
                defaultTestData.get(),
                "deleteAccount",
                defaultTestData.get().getRandomTestUser()));

        iosNativeDeleteAccountOTPVerificationScreen.get().pressTermsAndConditionsCheckbox();

        Assert.assertTrue(iosNativeDeleteAccountOTPVerificationScreen.get().isDeleteAccountConfirmOtpBtnEnabled()
                , "Delete button is disabled even after the checkbox is marked.");

        iosNativeDeleteAccountOTPVerificationScreen.get().pressOnDeleteAccount();

        Assert.assertTrue(iosNativeDeleteSuccessScreen.get().isPageDisplayed()
                , "Delete account success screen is not displayed");

        iosNativeDeleteSuccessScreen.get().pressCloseBtn();

        Assert.assertTrue(iosNativeHomeScreen.get().isHomeScreenDisplayed(),
                "Home screen is not displayed after deleting the account");
        iosNativeHomeScreen.get().pressMoreBtn();

        iosNativeMoreScreen.get().pressContinueBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Assert after login again with the same deleted user , the user is deleted and welcome back msg is shown
        Assert.assertTrue( iosNativeDeleteAccountScreen.get().isWelcomeBackToBreadfastDisplayed()
                ,"Welcome back msg is not displayed");
    }
}
