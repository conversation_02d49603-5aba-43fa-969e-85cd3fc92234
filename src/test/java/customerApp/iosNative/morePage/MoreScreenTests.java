package customerApp.iosNative.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.time.Duration;
import java.util.stream.Collectors;

public class MoreScreenTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void CheckUserCanLogoutProperly () {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        iosNativeMoreScreen.get().pressMoreTabBtn();

        iosNativeMoreScreen.get().pressLogoutBtn();

        //Assert user is logged out and the guest mood is displayed

        iosNativeMoreScreen.get().pressMoreTabBtn();
        Assert.assertTrue(iosNativeMoreScreen.get().isGetStartedTxtDisplayed()
                ,"The welcome text is not displayed,User is not logged out!");
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void CheckUserCanChangeLanguageFromMoreScreen () {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isHomeScreenDisplayed());

        iosNativeMoreScreen.get().pressMoreTabBtn();

        iosTestsExecutionHelper.get().changeAppLanguage(configs.get(), iosDriver.get(), "ar");

        // Assert the lang has been changed to AR
        Assert.assertTrue(iosNativeMoreScreen.get().isLangChangedToAr(),"Language wasn't changed to Ar");
    }
}
