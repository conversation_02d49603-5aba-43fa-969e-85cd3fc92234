package customerApp.iosNative.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class SavedAddressesScreenTests extends BaseTest {

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void CheckLoggedUseCanViewHisAddresses(){
        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        iosNativeMoreScreen.get().pressMoreTabBtn();
        iosNativeMoreScreen.get().pressAccountSettingsBtn();
        iOSNativeAccountSettingsScreen.get().pressManageAddressesTab();

        // Assert the user is on the saved addresses screen
        Assert.assertTrue(iosNativeSavedAddressScreen.get().isAddressesPageDisplayed()
                ,"User is not on the saved addresses screen");
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void CheckLoggedUseCanNotDeleteHisDefaultAddress(){
        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        iosNativeMoreScreen.get().pressMoreTabBtn();
        iosNativeMoreScreen.get().pressAccountSettingsBtn();
        iOSNativeAccountSettingsScreen.get().pressManageAddressesTab();

        //Assert the default address of the user is shown normally
        Assert.assertTrue(iosNativeSavedAddressScreen.get().isDefaultAddressDisplayed()
                ,"Default address is not displayed");

        iosNativeSavedAddressScreen.get().pressOnDefaultAddress();

        //Click to delete the user default address
        iosNativeAddressDetailsScreen.get().pressOnDeleteBtn();

        //Assert the error message of you can't delete a default address is displayed
        Assert.assertTrue(iosNativeAddressDetailsScreen.get().isDeleteTagDisplayed()
                ,"Error message is not displayed");
    }
}
