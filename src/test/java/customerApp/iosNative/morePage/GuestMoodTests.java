package customerApp.iosNative.morePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class GuestMoodTests extends BaseTest {
    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void ValidateGuestUserOpensHelpSection() {
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());

      iosNativeLandingScreen.get().pressExploreBtn();

        Assert.assertTrue(iosNativeHomeScreen.get().isHomeScreenDisplayed());

        iosNativeMoreScreen.get().pressMoreTabBtn();

        //Assert if the user is on the More screen in Guest mood
        Assert.assertTrue(iosNativeMoreScreen.get().isGetStartedTxtDisplayed()
                ,"User is not landed on the Guest mood more screen!");

        iosNativeMoreScreen.get().pressHelpBtn();

        // Assert user in guest mood can open help screen section
        Assert.assertTrue(iosNativeMoreScreen.get().isHelpPageDisplayed()
                ,"Help page didn't display!");
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void ValidateGuestUserCanChangeLang() {
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());

        iosNativeLandingScreen.get().pressExploreBtn();

        Assert.assertTrue(iosNativeHomeScreen.get().isHomeScreenDisplayed());

        iosNativeMoreScreen.get().pressMoreTabBtn();

        iosTestsExecutionHelper.get().changeAppLanguage(configs.get(), iosDriver.get(), "ar");

        // Assert the lang has been changed to AR
        Assert.assertTrue(iosNativeMoreScreen.get().isLangChangedToAr(),"Language wasn't changed to Ar");
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void ValidateGuestUserCanChaneCountry() {
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressExploreBtn();

        Assert.assertTrue(iosNativeHomeScreen.get().isHomeScreenDisplayed());

        iosNativeMoreScreen.get().pressMoreTabBtn();

        //Assert if the user is on the More screen in Guest mood
        Assert.assertTrue(iosNativeMoreScreen.get().isGetStartedTxtDisplayed());
        iosNativeMoreScreen.get().pressCountryBtn();

        //Assert if the bottom sheet of countries is displayed
        Assert.assertTrue(iosNativeMoreScreen.get().isCountrySheetDisplayed()
                ,"County bottom sheet is not displayed,Can't change the country");
        iosNativeMoreScreen.get().pressToSelectKSA();

        iosNativeMoreScreen.get().pressChangeBtn();

        //Assert if the change country modal is displayed
       Assert.assertTrue(iosNativeChangeCountryModalScreen.get().isModalDisplayed()
               , "Change Country Confirmation modal is not displayed");
       iosNativeChangeCountryModalScreen.get().pressChangeBtn();

        iosNativeMoreScreen.get().pressMoreTabBtn();

        //Assert the country changed to KSA
        Assert.assertEquals(iosNativeMoreScreen.get().getCurrentlySelectedCountryName()
                , "Saudi Arabia"
                ,"Country wasn't changed to KSA");
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void ValidateGuestUserCanClickOnTalkToUS() {
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressExploreBtn();

        Assert.assertTrue(iosNativeHomeScreen.get().isHomeScreenDisplayed());

        iosNativeMoreScreen.get().pressMoreTabBtn();

        //Assert if the user is on the More screen in Guest mood
        Assert.assertTrue(iosNativeMoreScreen.get().isGetStartedTxtDisplayed());

        iosNativeMoreScreen.get().pressOnTalkToUs();

        // Assert the user is on talk to us screen
       iosNativeChatBotScreen.get().isChatBotScreenDisplayed();
    }
}
