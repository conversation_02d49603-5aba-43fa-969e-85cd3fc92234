package customerApp.iosNative.morePage;

import base.BaseTest;
import helpers.factories.dataFactories.UserDataFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class UpdatePersonalInfoTests extends BaseTest {

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void ValidateUSerCanUpdateHisPersonalInfo() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

         iosNativeMoreScreen.get().pressMoreTabBtn();
         iosNativeMoreScreen.get().pressAccountSettingsBtn();
        iOSNativeAccountSettingsScreen.get().pressPersonalInfoTab();

         //Assert user is on the personal information screen
        Assert.assertTrue(iosNativePersonalInfoScreen.get().isPersonalPageDisplayed());

        defaultTestData.get().setRandomTestUser(new UserDataFactory(configs.get())
                .generateRandomUserDetails(defaultTestData.get().getRandomTestUser()));

        iosNativePersonalInfoScreen.get().enterNameAndPressSaveBtn(
                defaultTestData.get().getRandomTestUser().getFirstName()
                , defaultTestData.get().getRandomTestUser().getLastName());

        //Assert the changes have been saved successfully
        Assert.assertTrue(iosNativePersonalInfoScreen.get().isPersonalInfoSaved()
                ,"User personal info is not updated,Success message is not displayed");
    }
}
