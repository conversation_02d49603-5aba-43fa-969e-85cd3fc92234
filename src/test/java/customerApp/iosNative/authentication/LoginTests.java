package customerApp.iosNative.authentication;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test(groups = {"smoke"})
public class LoginTests extends BaseTest {
    @Test(groups = {"B10-12345", "smoke"})
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void loginWithValidLocalPhoneNumber(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
    }

    @Test(groups = {"smoke"})
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void enterWrongOtpCodeDuringLogin(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativePhoneNumberScreen.get().enterPhoneNumberAndPresNext(
                defaultTestData.get().getRandomTestUser().getLocalPhoneNumber());

        Assert.assertTrue(iosNativeOtpVerificationScreen.get().isPageDisplayed());

        iosNativeOtpVerificationScreen.get().isWrongOtpErrorMsgDisplayed();

        iosNativeOtpVerificationScreen.get().enterOtp("0002");
        Assert.assertTrue(iosNativeOtpVerificationScreen.get().isWrongOtpErrorMsgDisplayed());
        Assert.assertTrue(iosNativeOtpVerificationScreen.get().getWrongOtpErrorMsgText().contains("2"));

        // submit OTP wrong for 3 times to validate the error message
        iosNativeOtpVerificationScreen.get().pressFirstOtpDigitField();
        iosNativeOtpVerificationScreen.get().enterOtp("0001");
        Assert.assertTrue(iosNativeOtpVerificationScreen.get().isWrongOtpErrorMsgDisplayed());
        Assert.assertTrue(iosNativeOtpVerificationScreen.get().getWrongOtpErrorMsgText().contains("1"));

        iosNativeOtpVerificationScreen.get().pressFirstOtpDigitField();
        iosNativeOtpVerificationScreen.get().enterOtp("0000");
        Assert.assertTrue(iosNativeOtpVerificationScreen.get().isWrongOtpErrorMsgDisplayed());
        Assert.assertTrue(iosNativeOtpVerificationScreen.get().getWrongOtpErrorMsgText().contains("0"));

        iosNativeOtpVerificationScreen.get().pressFirstOtpDigitField();
        iosNativeOtpVerificationScreen.get().enterOtp("0003");
        Assert.assertTrue(iosNativeOtpVerificationScreen.get().isOutOfVerificationAttemptsErrorMsgDisplayed());
    }

    @Test(groups = {"smoke"})
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void validateRegexOfPhoneNumberField(){
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(configs.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativePhoneNumberScreen.get().enterPhoneNumber(" ");
        iosNativePhoneNumberScreen.get().pressNextBtnWithoutRetry();
        Assert.assertTrue(iosNativePhoneNumberScreen.get().isRequiredMobileNumberErrorMsgDisplayed());

        iosNativePhoneNumberScreen.get().enterPhoneNumber("111abcdefg");
        Assert.assertEquals(iosNativePhoneNumberScreen.get().getCurrentValueInTxtField()
                .replace(" ", "")
                , "111");
        iosNativePhoneNumberScreen.get().pressNextBtnWithoutRetry();
        Assert.assertTrue(iosNativePhoneNumberScreen.get().isInvalidFormatErrorMsgDisplayed());

        iosNativePhoneNumberScreen.get().enterPhoneNumber("4567891");
        Assert.assertTrue(iosNativePhoneNumberScreen.get().isNextBtnEnabled());
    }
}
