package customerApp.iosNative.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class RecommendationsTests extends BaseTest {
    @Test
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void validateRecommendationsSectionIsNotShownForGuestUsers(){
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressExploreBtn();

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        Assert.assertFalse(iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosNativeHomeScreen.get().getScrollableContentContainer()
                        , iosNativeHomeScreen.get().getTopPicksTitleNameSelector())
                , "Scrolled and found the recommendations title text and it shouldn't be displayed");

        Assert.assertFalse(iosNativeHomeScreen.get().isTopPicksDisplayed()
                , "Recommendations section is displayed and it shouldn't");
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void validateRecommendationsSectionIsDisplayedForRegisteredUsers(){
        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        Assert.assertTrue(iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                        , "down"
                        , iosNativeHomeScreen.get().getScrollableContentContainer()
                        , iosNativeHomeScreen.get().getTopPicksTitleNameSelector())
                , "Scrolled and didn't find the recommendations title text and it should be displayed");

        Assert.assertTrue(iosNativeHomeScreen.get().isTopPicksDisplayed()
                , "Recommendations section is not displayed and it should");
    }
}
