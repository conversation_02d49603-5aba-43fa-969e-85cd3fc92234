package customerApp.iosNative.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class CategoriesTests extends BaseTest {
    @Test(groups = {"B10-12345-scroll", "smoke"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void validateThatCategoriesAreDisplayedWhenUserLogsInWithAddress(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        //Scroll until category displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().getScrollableContentContainer().isEnabled());
    }

    @Test(groups = {"B10-12345", "smoke"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void validateThatCategoriesAreScrollable(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        //Scroll until category displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().getScrollableContentContainer().isEnabled());
    }

    @Test(groups = {"sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void validateThatCategoriesIsClickable(){
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        //Scroll until category displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        // Select category
        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        Assert.assertTrue(iosNativeCategoryDetailsScreen.get().isPageDisplayed());
    }

    @Test
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void validateThatUserCanGetBack(){
        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        //Scroll until category displayed
        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        // Select category
        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId()));

        Assert.assertTrue(iosNativeCategoryDetailsScreen.get().isPageDisplayed());

        iosNativeCategoryDetailsScreen.get().pressBackBtn();

        //check categories menu appear
        Assert.assertTrue(iosNativeHomeScreen.get().isCategoriesSectionDisplayed());
    }
}
