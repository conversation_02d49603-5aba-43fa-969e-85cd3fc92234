package customerApp.iosNative.homePage;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class HomeGridTests extends BaseTest {

    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void validateHomeGridIsDisplayed(){

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        Assert.assertTrue(
                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                        iosDriver.get(),
                        "down",
                        iosNativeHomeScreen.get().getScrollableContentContainer(),
                        iosNativeHomeScreen.get().getHomeGridTitleLocator()
                ),
                "Scrolled and found Home Grid"
        );
        Assert.assertTrue(iosNativeHomeScreen.get().isHomeGridDisplayed());
    }
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void validateSupermarketInHomeGridAndCategoriesBottomSheetAreDisplayed(){

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        Assert.assertTrue(
                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                        iosDriver.get(),
                        "down",
                        iosNativeHomeScreen.get().getScrollableContentContainer(),
                        iosNativeHomeScreen.get().getHomeGridTitleLocator()
                ),
                "Scrolled and found Home Grid"
        );
        Assert.assertTrue(iosNativeHomeScreen.get().isSupermarketInHomeGridDisplayed());
        iosNativeHomeScreen.get().pressSuperMarketGridEntryPoint();
        Assert.assertTrue(iosNativeHomeScreen.get().isCategoriesBottomSheetDisplayed());
    }
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void validateShopsInHomeGridIsDisplayedAndShopsLandingPageOpened(){

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        Assert.assertTrue(
                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                        iosDriver.get(),
                        "down",
                        iosNativeHomeScreen.get().getScrollableContentContainer(),
                        iosNativeHomeScreen.get().getHomeGridTitleLocator()
                ),
                "Scrolled and found Home Grid"
        );
        Assert.assertTrue(iosNativeHomeScreen.get().isShopsInHomeGridDisplayed());
        iosNativeHomeScreen.get().pressShopsGridEntryPoint();
        Assert.assertTrue(iosNativeHomeScreen.get().isShopsLandingPageDisplayed());
    }
    @Tags({@Tag("customer-app-native"), @Tag("ios")})
    public void validateRestaurantInHomeGridIsDisplayedAndRestaurantLandingPageOpened(){

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        Assert.assertTrue(
                iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                        iosDriver.get(),
                        "down",
                        iosNativeHomeScreen.get().getScrollableContentContainer(),
                        iosNativeHomeScreen.get().getHomeGridTitleLocator()
                ),
                "Scrolled and found Home Grid"
        );
        Assert.assertTrue(iosNativeHomeScreen.get().isRestaurantInHomeGridDisplayed());
        iosNativeHomeScreen.get().pressRestaurantGridEntryPoint();
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());
    }
}
