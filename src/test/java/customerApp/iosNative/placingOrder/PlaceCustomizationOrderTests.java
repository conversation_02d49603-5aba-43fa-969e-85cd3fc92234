package customerApp.iosNative.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.stream.Collectors;

public class PlaceCustomizationOrderTests extends BaseTest {

    @Test(groups = {"place-order"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationProduct() {

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                ));
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // instant is displayed
        Assert.assertTrue(iosNativeHomeScreen.get().instantDisplay());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getCustomizableOnlyProducts().getFirst().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMongoId());

        // CustomizationLabel is displayed
        Assert.assertTrue(iosNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeInternalCustomizedProductScreen.get().getScrollableContentContainer()
                , iosNativeInternalCustomizedProductScreen.get().getRelatedProductsContentDescription());

        iosNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        iosNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        //click on goto check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        // ScheduleBtn is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isScheduleBtnDisplayed());

        // click on Cash On Delivery
        iosNativeCheckoutScreen.get().pressCashOnDeliveryBtn();

        // click on place order
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");

        Assert.assertEquals(iosNativeOrderSuccessScreen.get().getOrderStatus(), "Order placed successfully");
    }

    @Test(groups = {"place-order"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationAndNonCustomizationProductsWithTipping() {

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // instant is displayed
        Assert.assertTrue(iosNativeHomeScreen.get().instantDisplay());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowProductWithPositiveStock().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMongoId());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getCustomizableOnlyProducts().getFirst().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMongoId());

        // CustomizationLabel is displayed
        Assert.assertTrue(iosNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeInternalCustomizedProductScreen.get().getScrollableContentContainer()
                , iosNativeInternalCustomizedProductScreen.get().getRelatedProductsContentDescription());

        iosNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        iosNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        //click on goto check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        // ScheduleBtn is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isScheduleBtnDisplayed());

        // click on Cash On Delivery
        iosNativeCheckoutScreen.get().pressCashOnDeliveryBtn();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCheckoutScreen.get().getScrollableContentContainer()
                , iosNativeCheckoutScreen.get().getTipValueSelector("5.0"));

        iosNativeCheckoutScreen.get().pressTipValueBtn();

        // click on place order
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");

        Assert.assertEquals(iosNativeOrderSuccessScreen.get().getOrderStatus(), "Order placed successfully");
    }

    @Test(groups = {"place-order"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationProductWithFixedCartCoupon() {

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Create fixed cart coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "fixed_cart",
                "supermarket",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                false,
                false,
                "now"));

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // instant is displayed
        Assert.assertTrue(iosNativeHomeScreen.get().instantDisplay());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getCustomizableOnlyProducts().getFirst().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMongoId());

        // Customization Label is displayed
        Assert.assertTrue(iosNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeInternalCustomizedProductScreen.get().getScrollableContentContainer()
                , iosNativeInternalCustomizedProductScreen.get().getRelatedProductsContentDescription());

        iosNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        iosNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        //click on go to check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        // ScheduleBtn is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isScheduleBtnDisplayed());

        // click on Cash On Delivery
        iosNativeCheckoutScreen.get().pressCashOnDeliveryBtn();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCheckoutScreen.get().getScrollableContentContainer()
                , iosNativeCheckoutScreen.get().getPromoCodeText());

        // Apply the coupon created by the API
        iosNativeCheckoutScreen.get().enterPromoCode(defaultTestData.get().getTestCoupon().getCouponCode());

        iosNativeCheckoutScreen.get().pressPromoCodeApplyBtn();

        // click on place order
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");

        Assert.assertEquals(iosNativeOrderSuccessScreen.get().getOrderStatus(), "Order placed successfully");
    }

    @Test(groups = {"place-order"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationAndNonCustomizationProductsWithPercentageCartCoupon() {

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Create Percentage Cart coupon
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "percent",
                "supermarket",
                "commercial",
                0,
                0,
                10,
                "active",
                true,
                false,
                true,
                "now"));

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // instant is displayed
        Assert.assertTrue(iosNativeHomeScreen.get().instantDisplay());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getNowProductWithPositiveStock().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getMongoId());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getCustomizableOnlyProducts().getFirst().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMongoId());

        // CustomizationLabel is displayed
        Assert.assertTrue(iosNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeInternalCustomizedProductScreen.get().getScrollableContentContainer()
                , iosNativeInternalCustomizedProductScreen.get().getRelatedProductsContentDescription());

        iosNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        iosNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        //click on go to check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        // ScheduleBtn is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isScheduleBtnDisplayed());

        // click on Cash On Delivery
        iosNativeCheckoutScreen.get().pressCashOnDeliveryBtn();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCheckoutScreen.get().getScrollableContentContainer()
                , iosNativeCheckoutScreen.get().getPromoCodeText());

        // Apply the coupon created by the API
        iosNativeCheckoutScreen.get().enterPromoCode(defaultTestData.get().getTestCoupon().getCouponCode());

        iosNativeCheckoutScreen.get().pressPromoCodeApplyBtn();

        // click on place order
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");

        Assert.assertEquals(iosNativeOrderSuccessScreen.get().getOrderStatus(), "Order placed successfully");
    }

    @Test(groups = {"place-order"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationProductWithBackToWalletPercentageCoupon() {

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Create Back to wallet coupon with percentage value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "supermarket",
                "commercial",
                10,
                0,
                0,
                "active",
                true,
                false,
                true,
                "now"));

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // instant is displayed
        Assert.assertTrue(iosNativeHomeScreen.get().instantDisplay());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getCustomizableOnlyProducts().getFirst().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMongoId());

        // Customization Label is displayed
        Assert.assertTrue(iosNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeInternalCustomizedProductScreen.get().getScrollableContentContainer()
                , iosNativeInternalCustomizedProductScreen.get().getRelatedProductsContentDescription());

        iosNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        iosNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        //click on go to check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        // ScheduleBtn is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isScheduleBtnDisplayed());

        // click on Cash On Delivery
        iosNativeCheckoutScreen.get().pressCashOnDeliveryBtn();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCheckoutScreen.get().getScrollableContentContainer()
                , iosNativeCheckoutScreen.get().getPromoCodeText());

        // Apply the coupon created by the API
        iosNativeCheckoutScreen.get().enterPromoCode(defaultTestData.get().getTestCoupon().getCouponCode());

        iosNativeCheckoutScreen.get().pressPromoCodeApplyBtn();

        // click on place order
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");

        Assert.assertEquals(iosNativeOrderSuccessScreen.get().getOrderStatus(), "Order placed successfully");
    }

    @Test(groups = {"place-order"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void createOrderWithCustomizationProductWithBackToWalletCoupon() {

        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "supermarket",
                "commercial",
                1000,
                0,
                0,
                "active",
                true,
                false,
                false,
                "now"));

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // instant is displayed
        Assert.assertTrue(iosNativeHomeScreen.get().instantDisplay());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        Assert.assertTrue(iosNativeHomeScreen.get().isCategoryDisplayed(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));

        iosNativeHomeScreen.get().pressCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowCategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "right"
                , iosNativeCategoryDetailsScreen.get().getSubCategoriesScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getSubCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId())));

        iosNativeCategoryDetailsScreen.get().pressSubCategoryById(String.valueOf(
                defaultTestData.get().getCustomerAppTestSession().getNowSubcategoryWithPositiveStock().getId()));

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCategoryDetailsScreen.get().getProductsScrollableContentContainer()
                , iosNativeCategoryDetailsScreen.get().getProductImgNameSelectorById(
                        defaultTestData.get().getCustomerAppTestSession()
                                .getCustomizableOnlyProducts().getFirst().getMongoId()));

        iosNativeCategoryDetailsScreen.get().pressAddToCartBtnByProductId(
                defaultTestData.get().getCustomerAppTestSession().getCustomizableOnlyProducts().getFirst().getMongoId());

        // Customization Label is displayed
        Assert.assertTrue(iosNativeInternalCustomizedProductScreen.get().isCustomizationLabelDisplayed());

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeInternalCustomizedProductScreen.get().getScrollableContentContainer()
                , iosNativeInternalCustomizedProductScreen.get().getRelatedProductsContentDescription());

        iosNativeInternalCustomizedProductScreen.get().clickOnOptionBtn();

        iosNativeInternalCustomizedProductScreen.get().clickOnAddProductBtn();

        iosNativeCategoryDetailsScreen.get().pressCartBtn();

        //click on go to check out button
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());

        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        // Check if checkout page is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        // ScheduleBtn is displayed
        Assert.assertTrue(iosNativeCheckoutScreen.get().isScheduleBtnDisplayed());

        // click on Cash On Delivery
        iosNativeCheckoutScreen.get().pressCashOnDeliveryBtn();

        iosTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCheckoutScreen.get().getScrollableContentContainer()
                , iosNativeCheckoutScreen.get().getPromoCodeText());

        // Apply the coupon created by the API
        iosNativeCheckoutScreen.get().enterPromoCode(defaultTestData.get().getTestCoupon().getCouponCode());

        iosNativeCheckoutScreen.get().pressPromoCodeApplyBtn();

        // click on place order
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();
        //OrderSuccessScreen display
        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        Assert.assertEquals(defaultTestData.get().getTestOrder().getActualDeliveryTime()
                , defaultTestData.get().getTestOrder().getExpectedDeliveryTime()
                , "The actual is \"" + defaultTestData.get().getTestOrder().getActualDeliveryTime()
                        + "\" while the expected is \""
                        + defaultTestData.get().getTestOrder().getExpectedDeliveryTime() + "\"");

        Assert.assertEquals(iosNativeOrderSuccessScreen.get().getOrderStatus(), "Order placed successfully");
    }

}
