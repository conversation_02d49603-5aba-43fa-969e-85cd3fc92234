package customerApp.iosNative.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.stream.Collectors;

@Test
public class CollectionsTests extends BaseTest {
    @Test(groups = {"collections", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void validateCreateOrderThroughHomeCollectionIsWorkingCorrectly(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        iosNativeHomeScreen.get().pressFirstAvailableCollection();
        iosNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        iosNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }

    @Test(groups = {"collections", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void validateCreateOrderAsGuestThroughHomeCollectionIsWorkingCorrectly(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressExploreBtn();

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        iosNativeHomeScreen.get().pressFirstAvailableCollection();
        iosNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        iosNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(iosNativePhoneNumberScreen.get().isPageDisplayed());
    }

    @Test(groups = {"collections", "regression", "deep-regression", "sanity"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void validateCreateOrderWithSufficientBalanceThroughHomeCollectionIsWorkingCorrectly(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1000.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        iosNativeHomeScreen.get().pressFirstAvailableCollection();
        iosNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        iosNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }

    @Test(groups = {"collections", "regression", "deep-regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void validateCreateOrderWithInSufficientBalanceThroughHomeCollectionIsWorkingCorrectly(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        iosNativeHomeScreen.get().pressFirstAvailableCollection();
        iosNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        iosNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }

    @Test(groups = {"collections", "regression", "deep-regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void validateCreateOrderWithDueBalanceThroughHomeCollectionIsWorkingCorrectly(){
        // Make sure capacity is added to FP
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                100);

        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("-100.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        iosNativeHomeScreen.get().pressFirstAvailableCollection();
        iosNativeCollectionDetailsScreen.get().pressFirstAddToCartBtn();
        iosNativeCollectionDetailsScreen.get().pressCartBtn();

        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();

        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setAllOrders(orderApiClient.get().listAllOrdersUser(
                defaultTestData.get().getRandomTestUser()));

        Assert.assertEquals(defaultTestData.get().getRandomTestUser().getAllOrders().size(), 1
                , "Count of all orders endpoint is not correct. Expecting 1 and it's currently "
                        + defaultTestData.get().getRandomTestUser().getAllOrders().size());
    }
}
