package customerApp.iosNative.placingOrder;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.time.Duration;

@Test
public class MiniTrackerTests extends BaseTest {
    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void ValidateMiniTrackerBeforeAndAfterPreparing() {
        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        // place order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));
        //Login with the same user we created an order for
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());
        //before preparing
        //Home page
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        //mini tracker displayed place order
        Assert.assertTrue(iosNativeHomeScreen.get().placeOrderMiniTrackingIsDisplayed());

        //after preparing
        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        //mini tracker displayed
        Assert.assertTrue(iosNativeHomeScreen.get().preparingYourOrderMiniTrackingIsDisplayed());
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void MiniTrackerMirrorOrderDetails(){
        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // place order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Login with the same user we created an order for
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Home page
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        //mini tracker displayed place order
        Assert.assertTrue(iosNativeHomeScreen.get().placeOrderMiniTrackingIsDisplayed());
        iosNativeHomeScreen.get().pressExpandMiniTracking();
        iosNativeHomeScreen.get().pressViewOrderDetails();
        Assert.assertEquals(iosNativeOrderSuccessScreen.get().getOrderStatus(), "Order placed successfully");

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);
        //back to home page
        iosNativeOrderSuccessScreen.get().pressBackBtn();
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        //mini tracker displayed
        Assert.assertTrue(iosNativeHomeScreen.get().preparingYourOrderMiniTrackingIsDisplayed());
        iosNativeHomeScreen.get().pressExpandMiniTracking();
        iosNativeHomeScreen.get().pressViewOrderDetails();
        Assert.assertEquals(iosNativeOrderSuccessScreen.get().getOrderStatus(), "Preparing your order");

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);
        //back to home page
        iosNativeOrderSuccessScreen.get().pressBackBtn();
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        //mini tracker displayed
        Assert.assertTrue(iosNativeHomeScreen.get().readyForDeliveryMiniTrackingIsDisplayed());
        iosNativeHomeScreen.get().pressExpandMiniTracking();
        iosNativeHomeScreen.get().pressViewOrderDetails();
        Assert.assertEquals(iosNativeOrderSuccessScreen.get().getOrderStatus(), "Ready for delivery");

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //back to home page
        iosNativeOrderSuccessScreen.get().pressBackBtn();
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        //mini tracker displayed
        Assert.assertTrue(iosNativeHomeScreen.get().orderIsOnTheWayMiniTrackingIsDisplayed());
        iosNativeHomeScreen.get().pressExpandMiniTracking();
        iosNativeHomeScreen.get().pressViewOrderDetails();
        Assert.assertEquals(iosNativeOrderSuccessScreen.get().getOrderStatus(), "Order is on the way");
    }

    @Test(groups = {"regression"})
    @Tags({@Tag("customer-app-native"), @Tag("ios"), @Tag("mobile-shopping")})
    public void MiniTrackerOnlyVisibleWhileOrderProcessing() throws InterruptedException {
        //Register using API , Create address for user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        // place order
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        ,false,false,false,false));

        //Login with the same user we created an order for
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        //Home page
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        //mini tracker displayed place order
        Assert.assertTrue(iosNativeHomeScreen.get().placeOrderMiniTrackingIsDisplayed());

        //Mark order as packed
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                0.0f,
                false);

        //Mark order as picked up
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                0.0f,
                false);

        //Mark order as in-route
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                0.0f,
                false);

        //Mark order as delivering, call & parse list orders to set new values after order status change
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                0.0f,
                false);

        //Mark order as delivered
        controlRoomV2ApiClient.get().changeOrderStatus(defaultTestData.get().getAdminUser(),
                defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                "delivered", 20.0f, false);

        //sleep
        Thread.sleep(Duration.ofSeconds(400));

        //mini tracker displayed place order
        Assert.assertFalse(iosNativeHomeScreen.get().orderDeliveredMiniTrackingIsDisplayed());
    }
}
