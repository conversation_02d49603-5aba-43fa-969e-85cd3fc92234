package customerApp.iosNative.foodAggregators;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class CartScreenTests extends BaseTest {
    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping"), @Tag("food-aggregator")})
    public void validateRestaurantsCartFunctionality() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed();

        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(
                        String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().getFirst().getYeloId())));

        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(String.valueOf(
                defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getFirst().getYeloId()));

        iosNativeFoodAggregatorResturantDetailsScreen.get().isPageDisplayed();

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get().isFirstDecreaseQtyBtnEnabled());
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get()
                .isFirstProductAddedToCartCorrectly("1"));

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get().isFirstDecreaseQtyBtnEnabled());
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get()
                .isFirstProductAddedToCartCorrectly("2"));

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstDecreaseQtyBtn();
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get().isFirstDecreaseQtyBtnEnabled());
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get()
                .isFirstProductAddedToCartCorrectly("1"));

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstDecreaseQtyBtn();
        Assert.assertFalse(iosNativeFoodAggregatorResturantDetailsScreen.get().isFirstDecreaseQtyBtnEnabled());

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get()
                .isFirstProductAddedToCartCorrectly("1"));

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressBackBtn();

        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(
                        String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().get(1).getYeloId())));

        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(String.valueOf(
                defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().get(1).getYeloId()));
        iosNativeFoodAggregatorResturantDetailsScreen.get().isPageDisplayed();

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get().isCartTypeErrorMsgDisplayed());

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressKeepCurrentCartBtn();
        Assert.assertFalse(iosNativeFoodAggregatorResturantDetailsScreen.get().isCartTypeErrorMsgDisplayed());
        Assert.assertFalse(iosNativeFoodAggregatorResturantDetailsScreen.get().isFirstDecreaseQtyBtnEnabled());

        iosNativeFoodAggregatorResturantDetailsScreen.get().goToCartScreen();
        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        Assert.assertEquals(iosNativeCartScreen.get().getCountOfProductsInCart(), 1);
    }
}
