package customerApp.iosNative.foodAggregators;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class PlaceOrderTests extends BaseTest {
    @Test(groups = {"aggregators-regression", "B10-11223", "aggregators-smoke"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping"), @Tag("food-aggregator")})
    public void validateOrderIsPlacedSuccessfully() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed();

        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(
                        String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().getFirst().getYeloId())));

        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(String.valueOf(
                defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getFirst().getYeloId()));

        iosNativeFoodAggregatorResturantDetailsScreen.get().isPageDisplayed();

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get().isFirstDecreaseQtyBtnEnabled());
        iosNativeFoodAggregatorResturantDetailsScreen.get().goToCartScreen();

        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        iosNativeCheckoutScreen.get().pressChangeCardBtn();
        Assert.assertTrue(iosNativeCheckoutScreen.get().isCardSelectionModalDisplayed());
        iosNativeCheckoutScreen.get().pressChangeCardBtnInCardSelectionModal();
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosAddCardInfoScreen.get().isPageDisplayed());
        iosAddCardInfoScreen.get().fillCardInfoForm(
                defaultTestData.get().getTestCreditCard(),
                defaultTestData.get().getTestExpiryDate(),
                defaultTestData.get().getTestCVC(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                defaultTestData.get().getRandomTestUser().getFullName()
        );
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());
    }

    @Test(groups = {"aggregators-regression", "B10-11223", "aggregators-smoke"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping"), @Tag("food-aggregator")})
    public void validateOrderIsPlacedSuccessfullyUsingCODOnly() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed();

        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(
                        String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().getFirst().getYeloId())));

        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(String.valueOf(
                defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getFirst().getYeloId()));

        iosNativeFoodAggregatorResturantDetailsScreen.get().isPageDisplayed();

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get().isFirstDecreaseQtyBtnEnabled());
        iosNativeFoodAggregatorResturantDetailsScreen.get().goToCartScreen();

        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        iosNativeCheckoutScreen.get().pressCashOnDeliveryBtn();
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());
    }

    @Test(groups = {"aggregators-regression", "B10-11223", "aggregators-smoke"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping"), @Tag("food-aggregator")})
    public void validateOrderIsPlacedSuccessfullyUsingWalletOnly() {
        // Register and create address for a new test user
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()
                ));

        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("5000.00"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());
        // Log in and place an order (UI steps)
        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(
                defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode()
        );

        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer(),
                iosNativeHomeScreen.get().getCategoryNameSelector(
                        String.valueOf(defaultTestData.get().getCustomerAppTestSession()
                                .getNowOnlyCategory().getId()))
        );

        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed();

        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer(),
                iosNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(
                        String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().getFirst().getYeloId()))
        );

        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(
                String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                        .getRestaurantsList().getFirst().getYeloId())
        );

        iosNativeFoodAggregatorResturantDetailsScreen.get().isPageDisplayed();
        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get().isFirstDecreaseQtyBtnEnabled());
        iosNativeFoodAggregatorResturantDetailsScreen.get().goToCartScreen();

        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        iosNativeCheckoutScreen.get().pressCashOnDeliveryBtn();
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()
                )
                .getCurrentBalance());

        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(
                        defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()
                )
                .getCurrentBalance());

        Assert.assertTrue(
                Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()) < 5000.00f,
                String.format(
                        "User balance should decrease after placing order. Initial: %f, Current: %f",
                        5000.00f, Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())));

        Assert.assertTrue(
                5000.00f - Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()) > 0,
                String.format(
                        "Balance deduction should be greater than 0. Initial: %f, Current: %f",
                        5000.00f, Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())));
    }

    @Test(groups = {"aggregators-regression", "B10-11223", "aggregators-smoke"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping"), @Tag("food-aggregator")})
    public void validateOrderIsPlacedSuccessfullyUsingCCWithBalance() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        defaultTestData.get().getRandomTestUser().setCurrentBalance(
                switcherApiClient.get().updateUserBalanceByPhoneNumber("1"
                        , defaultTestData.get().getAdminUser()
                        , defaultTestData.get().getRandomTestUser().getLocalPhoneNumber()).getCurrentBalance());

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed();

        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(
                        String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().getFirst().getYeloId())));

        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(String.valueOf(
                defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getFirst().getYeloId()));

        iosNativeFoodAggregatorResturantDetailsScreen.get().isPageDisplayed();

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        Assert.assertTrue(iosNativeFoodAggregatorResturantDetailsScreen.get().isFirstDecreaseQtyBtnEnabled());
        iosNativeFoodAggregatorResturantDetailsScreen.get().goToCartScreen();

        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        iosNativeCheckoutScreen.get().pressChangeCardBtn();
        Assert.assertTrue(iosNativeCheckoutScreen.get().isCardSelectionModalDisplayed());
        iosNativeCheckoutScreen.get().pressChangeCardBtnInCardSelectionModal();
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosAddCardInfoScreen.get().isPageDisplayed());
        iosAddCardInfoScreen.get().fillCardInfoForm(
                defaultTestData.get().getTestCreditCard(),
                defaultTestData.get().getTestExpiryDate(),
                defaultTestData.get().getTestCVC(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                defaultTestData.get().getRandomTestUser().getFullName()
        );
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());

        //Update test data with current user balance
        defaultTestData.get().getRandomTestUser().setCurrentBalance(switcherApiClient.get()
                .getBalanceByUserId(defaultTestData.get().getRandomTestUser().getId(),
                        defaultTestData.get().getAdminUser()).getCurrentBalance());

        // Assert that the current balance is less than the initial balance
        Assert.assertTrue(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()) < 1.0f,
                String.format("User balance should decrease after placing order. Initial: %f, Current: %f",
                        1.0f, Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance())));

        Assert.assertEquals(Float.parseFloat(defaultTestData.get().getRandomTestUser().getCurrentBalance()), 0.0, 0.0);
    }
}
