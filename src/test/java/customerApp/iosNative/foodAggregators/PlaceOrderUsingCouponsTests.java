package customerApp.iosNative.foodAggregators;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

@Test
public class PlaceOrderUsingCouponsTests extends BaseTest {
    @Test(groups = {"B10-45941"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("mobile-shopping"), @Tag("food-aggregator")})
    public void validateOrderIsPlacedSuccessfullyUsingValidCoupon() {

        //Create Back to wallet coupon with fixed value
        defaultTestData.get().setTestCoupon(couponsApiClient.get().createCouponUsingCouponCode(
                defaultTestData.get().getAdminUser()
                , defaultTestData.get().getTestCoupon().getCouponCode(),
                "back to wallet",
                "restaurant",
                "commercial",
                100,
                0,
                0,
                "active",
                true,
                false,
                false,
                "now"));

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()
                )
        );

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();

        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantEntryPointContentDescription());

        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();
        iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed();

        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(
                        String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsList().getFirst().getYeloId())));

        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(String.valueOf(
                defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList().getFirst().getYeloId()));

        iosNativeFoodAggregatorResturantDetailsScreen.get().isPageDisplayed();

        iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstAddToCartBtn();
        //iosNativeFoodAggregatorResturantDetailsScreen.get().pressFirstItemAddToCartBtn();
        iosNativeFoodAggregatorResturantDetailsScreen.get().goToCartScreen();

        Assert.assertTrue(iosNativeCartScreen.get().isPageDisplayed());
        iosNativeCartScreen.get().pressGoToCheckoutBtn();
        Assert.assertTrue(iosNativeCheckoutScreen.get().isPageDisplayed());

        iosNativeCheckoutScreen.get().pressChangeCardBtn();
        Assert.assertTrue(iosNativeCheckoutScreen.get().isCardSelectionModalDisplayed());
        iosNativeCheckoutScreen.get().pressChangeCardBtnInCardSelectionModal();
        //Apply the coupon created by the API
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeCheckoutScreen.get().getScrollableContentContainer()
                ,iosNativeCheckoutScreen.get().getPromoCodeTxtFieldContentDescription());
        iosNativeCheckoutScreen.get().enterCouponCode(defaultTestData.get().getTestCoupon().getCouponCode());
        iosNativeCheckoutScreen.get().pressApplyCouponCodeBtn();
        iosNativeCheckoutScreen.get().pressPlaceOrderBtn();

        Assert.assertTrue(iosAddCardInfoScreen.get().isPageDisplayed());
        iosAddCardInfoScreen.get().fillCardInfoForm(
                defaultTestData.get().getTestCreditCard(),
                defaultTestData.get().getTestExpiryDate(),
                defaultTestData.get().getTestCVC(),
                defaultTestData.get().getRandomTestUser().getEmailAddress(),
                defaultTestData.get().getRandomTestUser().getFullName()
        );
        iosAddCardInfoScreen.get().pressAddCardBtn();

        Assert.assertTrue(iosNativeOrderSuccessScreen.get().isPageDisplayed());
    }
}
