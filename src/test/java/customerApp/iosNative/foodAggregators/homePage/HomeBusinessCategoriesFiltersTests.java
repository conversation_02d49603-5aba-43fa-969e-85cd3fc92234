package customerApp.iosNative.foodAggregators.homePage;

import base.BaseTest;
import helpers.factories.dataFactories.foodAggregatorDataFactories.BusinessCategoriesDataFactory;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.annotations.Test;

public class HomeBusinessCategoriesFiltersTests extends BaseTest {

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("database"), @Tag("foodaggregator")})
    public void TC_729VerifyAllExistingCategoriesAreLinkedToRestaurantsInSpecificLocation(){

        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        //Assert Category filter exists
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowDisplayed());

        //Assert category filter is horizontally scrollable
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowScrollable());

        //Assert All filters existing - after scrolling through all categories - are all linked to restaurants in this location
        validationResults.set(iosNativeAggregatorTestsExecutionHelper.get().areExpectedCategoryFiltersDisplayed(iosDriver.get(),
                iosNativeFoodAggregatorHomeScreen.get(),defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsBusinessCategories()
                , validationResults.get()));
        Assert.assertTrue(validationResults.get().isResult()
                , String.join("\n", validationResults.get().getValidationResults()));

    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("database"), @Tag("foodaggregator")})
    public void TC_730VerifyCategorySelectionFiltersRestaurantsAndUpdatesUI() {

        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        //Assert Category filter exists
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowDisplayed());

        //Assert category filter is horizontally scrollable
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowScrollable());

        //Assert First Category Returning from BE is visible
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));

        //Click on the first category
        iosNativeFoodAggregatorHomeScreen.get().pressCategoryFilterById(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId()));

        //Assert Header has changed into the category name
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterHeaderDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));
        Assert.assertEquals(iosNativeFoodAggregatorHomeScreen.get().extractCategoryHeaderName(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())),defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getName());

        //Assert the list returning from filter are of restaurants that are all linked to category
        Assert.assertTrue(new BusinessCategoriesDataFactory(configs.get()).extractUniqueBusinessCategoriesOfRestaurantList(
                                foodAggregatorRestaurantsApiClient.get().getRestaurantsDataByCategoryId(
                                        defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsBusinessCategories().getFirst().getYeloId()))
                        .contains(defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsBusinessCategories().getFirst().getName())
                , "Expected business category " +
                        defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsBusinessCategories().getFirst().getName()
                        + "is not linked to the returning restaurants");

    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("database"), @Tag("foodaggregator")})
    public void TC_732VerifyCategoryFilterSelectionPersistsAfterUserScrolls() {

        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        //Assert Category filter exists
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowDisplayed());

        //Assert First Category Returning from BE is visible
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));

        //Click on the first category
        iosNativeFoodAggregatorHomeScreen.get().pressCategoryFilterById(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId()));

        //Assert that the filter header is displayed
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterHeaderDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));
        Assert.assertEquals(iosNativeFoodAggregatorHomeScreen.get().extractCategoryHeaderName(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())),defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getName());

        //Scroll Through the restaurant List returning till the last restaurant
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "down"
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantCardIdSelector(String.valueOf(defaultTestData.get()
                        .getFoodAggregatorTestSession().getRestaurantsList().getLast().getYeloId())));

        //Scroll Back Up to the top until the first filter is visible
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "up"
                , iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , iosNativeFoodAggregatorHomeScreen.get().getCategoryFilterIdSelector(String.valueOf(defaultTestData.get()
                        .getFoodAggregatorTestSession()
                        .getRestaurantsBusinessCategories().getFirst().getYeloId())));

        //Assert that the filter header is displayed
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterHeaderDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));
        Assert.assertEquals(iosNativeFoodAggregatorHomeScreen.get().extractCategoryHeaderName(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())),defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getName());

    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("database"), @Tag("foodaggregator")})
    public void TC_733VerifyCategoryFilterSelectionPersistsAfterUserOpenRestaurantPageAndReturns() {

        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        //Assert Restaurants Screen is displayed
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        //Assert Category filter exists
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowDisplayed());

        //Assert First Category Returning from BE is visible
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));

        //Click on the first category
        iosNativeFoodAggregatorHomeScreen.get().pressCategoryFilterById(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId()));

        //Click on first restaurant card
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantCardById(String.valueOf(foodAggregatorRestaurantsApiClient.get()
                .getRestaurantsDataByCategoryId(defaultTestData.get()
                        .getFoodAggregatorTestSession()
                        .getRestaurantsBusinessCategories().getFirst().getYeloId()).getFirst().getYeloId()));

        //Click Back Button native to ios returning to Home Aggregator home
        iosDriver.get().navigate().back();

        //Assert filter header is still showing
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterHeaderDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));
        Assert.assertEquals(iosNativeFoodAggregatorHomeScreen.get().extractCategoryHeaderName(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())),defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getName());

        //Assert restaurant list showing in the UI is still filtered
        validationResults.set(iosNativeAggregatorTestsExecutionHelper.get().areExpectedRestaurantsDisplayed(iosDriver.get(),iosNativeFoodAggregatorHomeScreen.get()
                , foodAggregatorRestaurantsApiClient.get()
                        .getRestaurantsDataByCategoryId(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsBusinessCategories().getFirst().getYeloId()),validationResults.get()));
        Assert.assertTrue(validationResults.get().isResult()
                , String.join("\n", validationResults.get().getValidationResults()));

        //Scroll back to the top so filters are visible
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "up"
                ,iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                ,iosNativeFoodAggregatorHomeScreen.get().getCategoryFilterIdSelector(String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                        .getRestaurantsBusinessCategories().getFirst().getYeloId())));

        //Click on the filter to deselect it
        iosNativeFoodAggregatorHomeScreen.get().deselectCategoryFilterById(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId()));

        //Assert Visibilty of All restaurants header instead of category filter header
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        //Assert All restaurants List returns Normally without filtration
        validationResults.set(iosNativeAggregatorTestsExecutionHelper.get().areExpectedRestaurantsDisplayed(iosDriver.get(),
                iosNativeFoodAggregatorHomeScreen.get(),defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList()
                ,validationResults.get()));

        Assert.assertTrue(validationResults.get().isResult()
                , String.join("\n", validationResults.get().getValidationResults()));
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("database"),@Tag("foodaggregator")})
    public void TC_734VerifyCategoryFilterSelectionPersistsPullToRefresh() {

        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        //Assert Category filter exists
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowDisplayed());

        //Assert First Category Returning from BE is visible
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));

        //Click on the first category
        iosNativeFoodAggregatorHomeScreen.get().pressCategoryFilterById(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId()));

        //Assert that the filter header is displayed
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterHeaderDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));
        Assert.assertEquals(iosNativeFoodAggregatorHomeScreen.get().extractCategoryHeaderName(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())),defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getName());

        //Pull To Refresh
        iosNativeTestsExecutionHelper.get().pullToRefresh(iosDriver.get(),iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer());

        //Assert that filter header is still displayed
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterHeaderDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));
        Assert.assertEquals(iosNativeFoodAggregatorHomeScreen.get().extractCategoryHeaderName(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())),defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getName());

        //Asser that restaurant List is still filtered
        validationResults.set(iosNativeAggregatorTestsExecutionHelper.get().areExpectedRestaurantsDisplayed(iosDriver.get(),iosNativeFoodAggregatorHomeScreen.get()
                , foodAggregatorRestaurantsApiClient.get()
                        .getRestaurantsDataByCategoryId(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsBusinessCategories().getFirst().getYeloId()),validationResults.get()));
        Assert.assertTrue(validationResults.get().isResult()
                , String.join("\n", validationResults.get().getValidationResults()));
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("database"),@Tag("foodaggregator")})
    public void TC_735VerifyExitingAggregatorHomeWillResetTheCategoryFilter() {

        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        //Assert Category filter exists
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowDisplayed());

        //Assert category filter is horizontally scrollable
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowScrollable());

        //Assert First Category Returning from BE is visible
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));

        //Click on the first category
        iosNativeFoodAggregatorHomeScreen.get().pressCategoryFilterById(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId()));

        //Assert Header has changed into the category name
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterHeaderDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));
        Assert.assertEquals(iosNativeFoodAggregatorHomeScreen.get().extractCategoryHeaderName(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())),defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getName());

        //Click on back button to return to home app
        iosNativeFoodAggregatorHomeScreen.get().pressBackBtn();

        //Assert app home screen is displayed
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());

        //Navigate to restaurant home again
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        //Assert Visibilty of All restaurants header instead of category filter header
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        //Assert Restaurants Showing are not filtered
        validationResults.set(iosNativeAggregatorTestsExecutionHelper.get().areExpectedRestaurantsDisplayed(iosDriver.get(),
                iosNativeFoodAggregatorHomeScreen.get(),defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList()
                ,validationResults.get()));
        Assert.assertTrue(validationResults.get().isResult()
                , String.join("\n", validationResults.get().getValidationResults()));
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"), @Tag("database"), @Tag("foodaggregator")})
    public void TC_736VerifyClickingOnSelectedFilterAgainWillResetTheCategoryFilter() {

        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        //Assert Category filter exists
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowDisplayed());

        //Assert First Category Returning from BE is visible
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));

        //Click on the first category
        iosNativeFoodAggregatorHomeScreen.get().pressCategoryFilterById(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId()));

        //Assert that the filter header is displayed
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterHeaderDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));
        Assert.assertEquals(iosNativeFoodAggregatorHomeScreen.get().extractCategoryHeaderName(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())),defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getName());

        //Assert the list returning from filter are of restaurants that are all linked to category -BE Validation
        Assert.assertTrue(new BusinessCategoriesDataFactory(configs.get()).extractUniqueBusinessCategoriesOfRestaurantList(
                                foodAggregatorRestaurantsApiClient.get().getRestaurantsDataByCategoryId(
                                        defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsBusinessCategories().getFirst().getYeloId()))
                        .contains(defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsBusinessCategories().getFirst().getName())
                , "Expected business category " +
                        defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsBusinessCategories().getFirst().getName()
                        + "is not linked to the returning restaurants");

        //Assert The list returning in the UI are all linked to the applied filter
        validationResults.set(iosNativeAggregatorTestsExecutionHelper.get().areExpectedRestaurantsDisplayed(iosDriver.get(),iosNativeFoodAggregatorHomeScreen.get()
                , foodAggregatorRestaurantsApiClient.get()
                        .getRestaurantsDataByCategoryId(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsBusinessCategories().getFirst().getYeloId()),validationResults.get()));
        Assert.assertTrue(validationResults.get().isResult()
                , String.join("\n", validationResults.get().getValidationResults()));

        //Scroll back up until the filters are visible again
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(iosDriver.get()
                , "up"
                ,iosNativeFoodAggregatorHomeScreen.get().getRestaurantsScrollableContainer()
                , iosNativeFoodAggregatorHomeScreen.get().getCategoryFilterIdSelector(String.valueOf(defaultTestData.get().getFoodAggregatorTestSession()
                        .getRestaurantsBusinessCategories().getFirst().getYeloId())));

        //Click on the same category Filter Again
        iosNativeFoodAggregatorHomeScreen.get().deselectCategoryFilterById(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId()));

        //Assert Visibilty of All restaurants header instead of category filter header
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isRestaurantHomeScreenDisplayed());

        //Assert All restaurants List returns Normally without filtration
        validationResults.set(iosNativeAggregatorTestsExecutionHelper.get().areExpectedRestaurantsDisplayed(iosDriver.get(),
                iosNativeFoodAggregatorHomeScreen.get(),defaultTestData.get().getFoodAggregatorTestSession().getRestaurantsList()
                ,validationResults.get()));
        Assert.assertTrue(validationResults.get().isResult()
                , String.join("\n", validationResults.get().getValidationResults()));
    }

    @Test(groups = {"aggregators-regression"})
    @Tags({@Tag("ios"), @Tag("customer-app-native"),@Tag("database"), @Tag("foodaggregator")})
    public void TC_737VerifyUserCanOnlySelectOneCategoryFilterAtaTime(){

        // Login to the customer app
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(),
                        defaultTestData.get().getRandomTestUser()));

        iosNativeCountriesSelectionScreen.get().selectCountryAndProceed(defaultTestData.get().getTestCountryCode());
        iosNativeLandingScreen.get().pressAuthBtn();
        iosNativeTestsExecutionHelper.get().login(defaultTestData.get(),
                testExecutionHelper.get(),
                iosNativePhoneNumberScreen.get(),
                iosNativePhoneCountrySelectionDropdownScreen.get(),
                iosNativeOtpVerificationScreen.get(),
                defaultTestData.get().getTestCountryCode());

        // Verify home screen is displayed and navigate to restaurants
        Assert.assertTrue(iosNativeHomeScreen.get().isPageDisplayed());
        iosNativeTestsExecutionHelper.get().scrollUntilACertainElementIsFound(
                iosDriver.get(),
                "down",
                iosNativeHomeScreen.get().getScrollableContentContainer()
                , iosNativeHomeScreen.get().getCategoryNameSelector(String.valueOf(
                        defaultTestData.get().getCustomerAppTestSession().getNowOnlyCategory().getId())));
        iosNativeFoodAggregatorHomeScreen.get().pressRestaurantsEntryPoint();

        //Assert Category filter exists
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterRowDisplayed());

        //Assert First Category Returning from BE is visible
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));

        //Click on the first category
        iosNativeFoodAggregatorHomeScreen.get().pressCategoryFilterById(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId()));

        //Assert that the filter header is displayed
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterHeaderDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())));
        Assert.assertEquals(iosNativeFoodAggregatorHomeScreen.get().extractCategoryHeaderName(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getYeloId())),defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().getFirst().getName());

        //Click On the next Category
        iosNativeFoodAggregatorHomeScreen.get().pressCategoryFilterById(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().get(1).getYeloId()));

        //Assert The filter header Name has changed to the new selected Filter
        Assert.assertTrue(iosNativeFoodAggregatorHomeScreen.get().isCategoryFilterHeaderDisplayed(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().get(1).getYeloId())));
        Assert.assertEquals(iosNativeFoodAggregatorHomeScreen.get().extractCategoryHeaderName(String.valueOf(defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().get(1).getYeloId())),defaultTestData.get()
                .getFoodAggregatorTestSession()
                .getRestaurantsBusinessCategories().get(1).getName());

        //Assert That the list returning contains the relevant restaurants of the new category and not the first
        validationResults.set(iosNativeAggregatorTestsExecutionHelper.get().areExpectedRestaurantsDisplayed(iosDriver.get(),iosNativeFoodAggregatorHomeScreen.get()
                , foodAggregatorRestaurantsApiClient.get()
                        .getRestaurantsDataByCategoryId(defaultTestData.get().getFoodAggregatorTestSession()
                                .getRestaurantsBusinessCategories().get(1).getYeloId()),validationResults.get()));
        Assert.assertTrue(validationResults.get().isResult()
                , String.join("\n", validationResults.get().getValidationResults()));

    }
}
