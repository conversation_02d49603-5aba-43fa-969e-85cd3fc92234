package controlRoom;

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;

import java.util.stream.Collectors;

@Test
public class ControlRoomTests extends BaseTest {
    @Test(groups = {"smoke", "supply-demand"})
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("database")})
    public void validateControlRoomOpensCorrectly() {
        Reporter.log("Going to Login Page", 3, true);
        webLoginPage.get().goToLoginPage();

        Reporter.log("Logging in with the admin user", 3, true);
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        // Validate that the more page is displayed
        Reporter.log("Login is successful and checking isf more button is displayed");
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        Reporter.log("More Button is displayed");

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        Assert.assertTrue(webControlRoomV2AdminPage.get().isFpModuleOpen(), "Control room is not open");
        Reporter.log("Control room is open");

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(configs.get().getTestFpName());
        webControlRoomV2AdminPage.get().chooseTargetDate(
                testExecutionHelper.get().getCurrentTimeStamp("dd-MM-yyyy"), "dd-MM-yyyy");
        webControlRoomV2AdminPage.get().clickShowButton();

        //validate that selected FP and date are displayed
        Assert.assertTrue(webControlRoomV2AdminPage.get().isSelectedFPAndDateDisplayed()
                , " FP and date are not displayed");

        Assert.assertTrue(webControlRoomV2AdminPage.get().getDisplayedFPAndDateText()
                .contains(configs.get().getTestFpName()), "FP name is not correct");
        Assert.assertTrue(webControlRoomV2AdminPage.get().getDisplayedFPAndDateText()
                .contains(testExecutionHelper.get().getCurrentTimeStamp("dd-MM-yyyy"))
                , "Displayed Date is not correct");
        Reporter.log("FP and date are displayed correctly");
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("database")})
    public void validateMultipleFPsSelection() {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();
        webControlRoomV2AdminPage.get().selectMultipleFPs(
                defaultTestData.get().getWarehousesList().getFirst().getName()
                , defaultTestData.get().getWarehousesList().get(1).getName());
        webControlRoomV2AdminPage.get().clickShowButton();
        Assert.assertEquals(webControlRoomV2AdminPage.get().getDisplayedFPAndDateText(),
                defaultTestData.get().getWarehousesList().getFirst().getName() + " & 1 more" +
                        "|" + testExecutionHelper.get().getCurrentTimeStamp("dd-MM-yyyy"));
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("mobile-shopping"), @Tag("database")})
    public void validateFPDimmed() {

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getFpManagerUser().getLocalPhoneNumber(),
                defaultTestData.get().getFpManagerUser().getBypassScriptPassword());
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isPageDisplayed();
        Assert.assertTrue(webControlRoomV2AdminPage.get().isSelectedFPAndDateDisplayed()
                , " FP and date are not displayed");
        webControlRoomV2AdminPage.get().clickDisplayedFPAndDate();
        webControlRoomV2AdminPage.get().isFpModuleOpen();
        Assert.assertTrue(webControlRoomV2AdminPage.get().isFPDisabled(), "Dimmed FP is not disabled");
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("mobile-shopping"), @Tag("database")})
    public void validateSearchResultsWhenSearchingWithOrderNumber() {

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        webHomePage.get().isMoreBtnDisplayed();

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isPageDisplayed();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().chooseTargetDate(
                testExecutionHelper.get().getCurrentTimeStamp("dd-MM-yyyy"), "dd-MM-yyyy");
        webControlRoomV2AdminPage.get().clickShowButton();

        //validate that selected FP and date are displayed
        Assert.assertTrue(webControlRoomV2AdminPage.get().isSelectedFPAndDateDisplayed()
                , " FP and date are not displayed");

        webControlRoomV2AdminPage.get().searchForOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        webControlRoomV2AdminPage.get().getSearchResult();

        //Validate that the targeted order is displayed in the search results dropdown
        Assert.assertTrue(webControlRoomV2AdminPage.get().
                getSearchResult().contains(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber()));

        Assert.assertTrue(webControlRoomV2AdminPage.get().getOrderNumberFromDetails()
                .contains(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber()));
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("mobile-shopping"), @Tag("database")})
    public void validateSearchResultsWhenSearchingWithCustomerName() {
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        webHomePage.get().isMoreBtnDisplayed();

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isPageDisplayed();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().chooseTargetDate(
                testExecutionHelper.get().getCurrentTimeStamp("dd-MM-yyyy"), "dd-MM-yyyy");
        webControlRoomV2AdminPage.get().clickShowButton();

        //validate that selected FP and date are displayed
        Assert.assertTrue(webControlRoomV2AdminPage.get().isSelectedFPAndDateDisplayed()
                , " FP and date are not displayed");

        webControlRoomV2AdminPage.get().searchForOrder(defaultTestData.get()
                .getRandomTestUser().getTestOrder().getOrderNumber());

        webControlRoomV2AdminPage.get().getSearchResult();

        defaultTestData.get().getRandomTestUser().getTestOrder().setCustomerName( controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder()).getCustomerName());

        //Validate that the targeted order is displayed in the search results dropdown
        Assert.assertTrue(webControlRoomV2AdminPage.get().
                getSearchResult().contains(defaultTestData.get().getRandomTestUser().getTestOrder().getCustomerName()));
        Assert.assertTrue(webControlRoomV2AdminPage.get().getCustomerNameFromDetails()
                .contains(defaultTestData.get().getRandomTestUser().getTestOrder().getCustomerName()));
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("database")})
    public void validateSearchResultsWhenSearchingWithWrongValues() {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        webHomePage.get().isMoreBtnDisplayed();

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isPageDisplayed();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().chooseTargetDate(
                testExecutionHelper.get().getCurrentTimeStamp("dd-MM-yyyy"), "dd-MM-yyyy");
        webControlRoomV2AdminPage.get().clickShowButton();
        Assert.assertTrue(webControlRoomV2AdminPage.get().isSelectedFPAndDateDisplayed()
                , " FP and date are not displayed");
        webControlRoomV2AdminPage.get().searchForOrder("25-1113789");
        webControlRoomV2AdminPage.get().wrongSearchResult();
        //Validate that the search result is empty/not correct
        Assert.assertTrue(webControlRoomV2AdminPage.get().
                wrongSearchResult().contains("No results found"));
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("database")})
    public void validateShowBtnIsDisabledWhenNoFpIsSelected() {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        webHomePage.get().isMoreBtnDisplayed();

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isPageDisplayed();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        Assert.assertTrue(webControlRoomV2AdminPage.get().isShowBtnDisabled());
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("database")})
    public void validateFiltersPage() {
        Reporter.log("Going to Login Page", 3, true);
        webLoginPage.get().goToLoginPage();

        Reporter.log("Logging in with the admin user", 3, true);
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        //Validate that the more page is displayed
        Reporter.log("Login is successful and checking isf more button is displayed");
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        Reporter.log("More Button is displayed");

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();
        Assert.assertTrue(webControlRoomV2AdminPage.get().isFpModuleOpen(), "Control room is not open");
        Reporter.log("Control room is open");

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(configs.get().getTestFpName());
        webControlRoomV2AdminPage.get().chooseTargetDate(testExecutionHelper.get()
                .getCurrentTimeStamp("dd-MM-yyyy"), "dd-MM-yyyy");
        webControlRoomV2AdminPage.get().clickShowButton();
        webControlRoomV2AdminPage.get().isSelectedFPAndDateDisplayed();

        webControlRoomV2AdminPage.get().clickFiltersBtn();
        webControlRoomV2AdminPage.get().isFiltersDrawerDisplayed();

        Assert.assertTrue(webControlRoomV2AdminPage.get().isFiltersDrawerDisplayed());
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("database")})
    public void validateGroupByPage() {
        Reporter.log("Going to Login Page", 3, true);
        webLoginPage.get().goToLoginPage();

        Reporter.log("Logging in with the admin user", 3, true);
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        //Validate that the more page is displayed
        Reporter.log("Login is successful and checking isf more button is displayed");
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        Reporter.log("More Button is displayed");

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();
        Assert.assertTrue(webControlRoomV2AdminPage.get().isFpModuleOpen(), "Control room is not open");
        Reporter.log("Control room is open");

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(configs.get().getTestFpName());
        webControlRoomV2AdminPage.get().chooseTargetDate(testExecutionHelper.get()
                .getCurrentTimeStamp("dd-MM-yyyy"), "dd-MM-yyyy");
        webControlRoomV2AdminPage.get().clickShowButton();
        webControlRoomV2AdminPage.get().isSelectedFPAndDateDisplayed();

        webControlRoomV2AdminPage.get().clickGroupByBtn();
        webControlRoomV2AdminPage.get().isGroupByDrawerDisplayed();

        Assert.assertTrue(webControlRoomV2AdminPage.get().isGroupByDrawerDisplayed());
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("database")})
    public void validateGroupByTimeslot() {
        Reporter.log("Going to Login Page", 3, true);
        webLoginPage.get().goToLoginPage();

        Reporter.log("Logging in with the admin user", 3, true);
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        //Validate that the more page is displayed
        Reporter.log("Login is successful and checking isf more button is displayed");
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());
        Reporter.log("More Button is displayed");

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();
        Assert.assertTrue(webControlRoomV2AdminPage.get().isFpModuleOpen(), "Control room is not open");
        Reporter.log("Control room is open");

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(configs.get().getTestFpName());
        webControlRoomV2AdminPage.get().chooseTargetDate(testExecutionHelper.get()
                .getCurrentTimeStamp("dd-MM-yyyy"), "dd-MM-yyyy");
        webControlRoomV2AdminPage.get().clickShowButton();
        webControlRoomV2AdminPage.get().isSelectedFPAndDateDisplayed();

        webControlRoomV2AdminPage.get().clickGroupByBtn();
        webControlRoomV2AdminPage.get().isGroupByDrawerDisplayed();

        Assert.assertTrue(webControlRoomV2AdminPage.get().isGroupByDrawerDisplayed());

        webControlRoomV2AdminPage.get().selectGroupByTimeSlot();
        webControlRoomV2AdminPage.get().clickApplyGroupByBtn();
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("database")})
    public void validateAllOrdersAreDisplayed() {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        webHomePage.get().isMoreBtnDisplayed();

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isPageDisplayed();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().chooseTargetDate(testExecutionHelper.get()
                .getCurrentTimeStamp("dd-MM-yyyy"), "dd-MM-yyyy");
        webControlRoomV2AdminPage.get().clickShowButton();

        Assert.assertTrue(webControlRoomV2AdminPage.get().isAllOrdersDisplayed(defaultTestData.get().getOrdersList())
                , "1 or more orders are missing from the UI.");
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("database")})
    public void validateThatAssigningDaIsWorkingCorrectly() {
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        webHomePage.get().isMoreBtnDisplayed();

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isPageDisplayed();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        // call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().chooseTargetDate(testExecutionHelper.get()
                .getCurrentTimeStamp("dd-MM-yyyy"), "dd-MM-yyyy");
        webControlRoomV2AdminPage.get().clickShowButton();

        //call methods to assign DA
        webControlRoomV2AdminPage.get().clickFirstOrderActionsBtn();
        webControlRoomV2AdminPage.get().clickAssignDaBtn();
        webControlRoomV2AdminPage.get().selectFirstAvailableDa();
        webControlRoomV2AdminPage.get().clickAssignBtn();

        // validate the alert text
        Assert.assertEquals(webControlRoomV2AdminPage.get().getSuccessToasterText(), "DA assigned successfully");
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("mobile-shopping"), @Tag("database")})
    public void checkOrderPlacedFromApiAppearsInSearch() {

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        //Validate that the more page is displayed
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().clickShowButton();

        webControlRoomV2AdminPage.get().searchForOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());

        webControlRoomV2AdminPage.get().getSearchResult();

        Assert.assertTrue(webControlRoomV2AdminPage.get().getSearchResult()
                .contains(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber()));

        defaultTestData.get().getRandomTestUser().getTestOrder().setCustomerName( controlRoomV2ApiClient.get().getOrderDetailsById(defaultTestData.get().getAdminUser()
                ,defaultTestData.get().getRandomTestUser().getTestOrder().getOrderId(),
                defaultTestData.get().getTestOrder()).getCustomerName());

        Assert.assertTrue(webControlRoomV2AdminPage.get().getSearchResult()
                .contains(defaultTestData.get().getRandomTestUser().getTestOrder().getCustomerName()));

    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("mobile-shopping"), @Tag("database")})
    public void checkApiOrderTimeSlotInDetails() {

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false,false,false,false));

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        //Validate that the more page is displayed
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList((defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName()));
        webControlRoomV2AdminPage.get().clickShowButton();
        webControlRoomV2AdminPage.get().searchForOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        Assert.assertTrue(webControlRoomV2AdminPage.get().getOrderTimeslotFromDetails()
                .contains(defaultTestData.get().getRandomTestUser().getTestOrder().getTimeSlot()));
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("mobile-shopping"), @Tag("database")})
    public void checkApiTimeslotInListing() {

        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false,false,false,false));

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        //Validate that the more page is displayed
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().clickShowButton();
        webControlRoomV2AdminPage.get().getRowTextWithOrderNumber(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        Assert.assertTrue(webControlRoomV2AdminPage.get().getRowTextWithOrderNumber(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber())
                .contains(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber()));
        Assert.assertTrue(webControlRoomV2AdminPage.get().getRowTextWithOrderNumber(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber())
                .contains(defaultTestData.get().getRandomTestUser().getTestOrder().getTimeSlot()));
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("mobile-shopping"), @Tag("database")})
    public void checkAreaInListing() {

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false,false,false,false));
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        //Validate that the more page is displayed
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().clickShowButton();
        Assert.assertTrue(webControlRoomV2AdminPage.get().getRowTextWithOrderNumber(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber())
                .contains(defaultTestData.get().getRandomTestUser().getTestOrder().getArea()));

    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("mobile-shopping"), @Tag("database")})
    public void checkProductShownInDetails() {

        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false,false,false,false));

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        //Validate that the more page is displayed
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().clickShowButton();
        webControlRoomV2AdminPage.get().searchForOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        Assert.assertTrue(webControlRoomV2AdminPage.get().getOrderProductsFromDetails()
                .contains(defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock().getName()));
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("mobile-shopping"), @Tag("database")})
    public void checkCompletingOrderFromDetails() {

        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false,false,false,false));

        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());

        //Validate that the more page is displayed
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().clickShowButton();
        webControlRoomV2AdminPage.get().searchForOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        webControlRoomV2AdminPage.get().getOrderTimeslotFromDetails();
        webControlRoomV2AdminPage.get().assignDaFromDetails();
        Assert.assertEquals(webControlRoomV2AdminPage.get().packageOrderStatus(), "Packed");
        Assert.assertEquals(webControlRoomV2AdminPage.get().pickupOrderStatus(), "Picked up");
        Assert.assertTrue(webControlRoomV2AdminPage.get().inrouteOrderStatus().contains("Starting"));
        Assert.assertTrue(webControlRoomV2AdminPage.get().deliveringOrderStatus().contains("Arriving"));
        Assert.assertTrue(webControlRoomV2AdminPage.get().deliveredOrderStatus().contains("Order completed"));
    }

    @Test
    @Tags({@Tag("web"), @Tag("controlroom"), @Tag("mobile-shopping"), @Tag("database")})
    public void validateAssigningPickerToAnOrderIsWorkingCorrectly() {
        //place order from api
        deliveryCapacityManagementApiClient.get().updateTimeslotCapacity(
                defaultTestData.get().getAdminUser(),
                deliveryCapacityManagementApiClient.get().getAllSlotsData(defaultTestData.get().getAdminUser(),
                                defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getId())
                        .stream()
                        .filter(timeslot -> timeslot.getTimeslotIds() != null)
                        .flatMap(timeslot -> timeslot.getTimeslotIds().stream())
                        .collect(Collectors.toList()),
                1007);
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().registerUsingApi(
                        defaultTestData.get(), 
                        defaultTestData.get().getRandomTestUser()
                )
        );
        defaultTestData.get().setRandomTestUser(
                testExecutionHelper.get().createAddressUsingApi(
                        defaultTestData.get().getCustomerAppTestSession().getTestWarehouse(),
                        defaultTestData.get().getRandomTestUser()));
        defaultTestData.get().getRandomTestUser().setTestOrder(
                orderApiClient.get().CreateOrderUsingApi(defaultTestData.get().getRandomTestUser()
                        , "20"
                        , "0"
                        , "cod"
                        , defaultTestData.get().getCustomerAppTestSession().getNowProductWithPositiveStock()
                        ,""
                        , defaultTestData.get().getCustomerAppTestSession().getTestWarehouse()
                        , testExecutionHelper.get().getCurrentTimeStamp("d MMMM, yyyy")
                        , false, false,false,false));
        //login with admin user
        webLoginPage.get().goToLoginPage();
        webLoginPage.get().loginWithByPassScript(defaultTestData.get().getAdminUser().getPhoneNumber()
                , defaultTestData.get().getAdminUser().getBypassScriptPassword());
        //Validate that the more page is displayed
        Assert.assertTrue(webHomePage.get().isMoreBtnDisplayed());

        // navigate to control room and validate that FP module is open
        webControlRoomV2AdminPage.get().goToPage();
        webControlRoomV2AdminPage.get().isFpModuleOpen();

        //call methods to select FP and date and click show button
        webControlRoomV2AdminPage.get().selectFpFromList(defaultTestData.get().getCustomerAppTestSession().getTestWarehouse().getName());
        webControlRoomV2AdminPage.get().clickShowButton();
        webControlRoomV2AdminPage.get().searchForOrder(defaultTestData.get().getRandomTestUser().getTestOrder().getOrderNumber());
        webControlRoomV2AdminPage.get().openThePlacedOrder();

        webControlRoomV2AdminPage.get().switchToTheOtherTab();

        //open assigning picker modal
        webControlRoomV2AdminPage.get().clickAssignPickerButton();
        webControlRoomV2AdminPage.get().searchForPicker("Rawan");
        webControlRoomV2AdminPage.get().clickPickerRadioBtn();
        webControlRoomV2AdminPage.get().submitSelectedPickerToOrderBtn();
    }
}
