{"$schema": "http://json-schema.org/draft-06/schema#", "$ref": "#/definitions/Welcome3", "definitions": {"Welcome3": {"type": "object", "additionalProperties": false, "properties": {"code": {"type": "string"}, "message": {"$ref": "#/definitions/Message"}, "status": {"type": "integer"}}, "required": ["code", "message", "status"], "title": "Welcome3"}, "Message": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "order_number": {"type": "string"}, "delivery_date": {"type": "string"}, "status": {"type": "string"}, "timeslot": {"type": "string"}, "last4": {"type": "null"}, "now": {"type": "boolean"}, "card_type": {"type": "null"}, "fees": {"type": "array", "items": {"$ref": "#/definitions/Fee"}}, "delivery_fees": {"type": "integer"}, "customer": {"type": "string", "format": "integer"}, "customer_name": {"type": "string"}, "event": {"type": "null"}, "type": {"type": "string"}, "coupon": {"type": "array", "items": {}}, "gift_receipt": {"type": "boolean"}, "delivery_person": {"type": "boolean"}, "platform": {"type": "string"}, "statuses": {"$ref": "#/definitions/Statuses"}, "subscription": {"type": "null"}, "topup": {"type": "null"}, "collected": {"type": "integer"}, "notes": {"type": "string"}, "products": {"type": "array", "items": {"$ref": "#/definitions/Product"}}, "address": {"$ref": "#/definitions/Address"}, "payment_method": {"type": "string"}, "payment_title": {"type": "string"}, "marked_as_free": {"type": "boolean"}, "subtotal": {"type": "integer"}, "discount": {"type": "integer"}, "delivery_type": {"type": "string", "format": "integer"}, "warehouse": {"$ref": "#/definitions/Warehouse"}, "total": {"type": "integer"}, "total_invoice": {"type": "integer"}, "balance_used": {"type": "integer"}, "total_due": {"type": "integer"}, "old_balance": {"type": "integer"}, "cancellable": {"type": "boolean"}, "rate": {"type": "null"}, "created_at": {"type": "integer"}, "created_at_utc": {"type": "string", "format": "date-time"}, "language": {"type": "string"}, "card_id": {"type": "null"}, "gratuity": {"type": "null"}, "handle_with_care": {"type": "string", "format": "integer"}, "gameball_rewarded_points": {"type": "null"}, "negative_experience": {"type": "boolean"}, "eligibleGameballPoints": {"type": "null"}, "image": {"type": "null"}, "firebase_order_id": {"type": "null"}, "gratuity_details": {"$ref": "#/definitions/GratuityDetails"}, "isTimeSlotShifted": {"type": "boolean"}, "receipt_url": {"type": "null"}, "capture_method": {"type": "null"}, "scheduled_express": {"type": "boolean"}, "is_scheduled": {"type": "boolean"}, "eta": {"type": "null"}, "promised_time": {"type": "string"}, "service_fees": {"type": "integer"}}, "required": ["address", "balance_used", "cancellable", "capture_method", "card_id", "card_type", "collected", "coupon", "created_at", "created_at_utc", "customer", "customer_name", "delivery_date", "delivery_fees", "delivery_person", "delivery_type", "discount", "eligibleGameballPoints", "eta", "event", "fees", "firebase_order_id", "gameball_rewarded_points", "gift_receipt", "gratuity", "gratuity_details", "handle_with_care", "id", "image", "isTimeSlotShifted", "is_scheduled", "language", "last4", "marked_as_free", "negative_experience", "notes", "now", "old_balance", "order_number", "payment_method", "payment_title", "platform", "products", "promised_time", "rate", "receipt_url", "scheduled_express", "service_fees", "status", "statuses", "subscription", "subtotal", "timeslot", "topup", "total", "total_due", "total_invoice", "type", "warehouse"], "title": "Message"}, "Address": {"type": "object", "additionalProperties": false, "properties": {"address_id": {"type": "string", "format": "integer"}, "label": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "phone": {"type": "string"}, "area": {"type": "string"}, "area_id": {"type": "string", "format": "integer"}, "address": {"type": "string"}, "delivery_instructions": {"type": "string"}, "location": {"$ref": "#/definitions/Location"}}, "required": ["address", "address_id", "area", "area_id", "delivery_instructions", "first_name", "label", "last_name", "location", "phone"], "title": "Address"}, "Location": {"type": "object", "additionalProperties": false, "properties": {"lat": {"type": "number"}, "lng": {"type": "number"}}, "required": ["lat", "lng"], "title": "Location"}, "Fee": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "total": {"type": "integer"}}, "required": ["name", "total"], "title": "Fee"}, "GratuityDetails": {"type": "object", "additionalProperties": false, "properties": {"origin": {"type": "string"}, "status": {"type": "string"}, "gratuity_amount": {"type": "string", "format": "integer"}, "gratuityFundsUsed": {"type": "integer"}}, "required": ["gratuityFundsUsed", "gratuity_amount", "origin", "status"], "title": "GratuityDetails"}, "Product": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "name_ar": {"type": "string"}, "quantity": {"type": "integer"}, "subtotal": {"type": "integer"}, "image": {"type": "string", "format": "uri", "qt-uri-protocols": ["https"], "qt-uri-extensions": [".jpg", ".png"]}, "total": {"type": "integer"}, "free": {"type": "boolean"}, "internalCateogry": {"$ref": "#/definitions/InternalCateogry"}, "outOfBags": {"type": "boolean"}, "top_up": {"type": "boolean"}}, "required": ["free", "id", "image", "internalCateogry", "name", "name_ar", "outOfBags", "quantity", "subtotal", "top_up", "total"], "title": "Product"}, "InternalCateogry": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "id": {"type": "integer"}}, "required": ["id", "name"], "title": "InternalCateogry"}, "Statuses": {"type": "object", "additionalProperties": false, "properties": {"printed": {"type": "null"}, "packaged": {"type": "null"}, "received": {"type": "null"}, "started": {"type": "null"}, "arrived": {"type": "null"}, "completed": {"type": "null"}}, "required": ["arrived", "completed", "packaged", "printed", "received", "started"], "title": "Statuses"}, "Warehouse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "shift_id": {"type": "string"}, "timeslot_id": {"type": "string"}}, "required": ["id", "name", "shift_id", "timeslot_id"], "title": "Warehouse"}}}