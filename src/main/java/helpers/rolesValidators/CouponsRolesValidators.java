package helpers.rolesValidators;

import modals.wordpressAdmin.CouponsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CouponsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(CouponsRolesValidators.class);

    public ValidationResults validateCouponsAccessPerRole(ValidationResults validationResults
            , CouponsAdminPage couponsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, couponsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , CouponsAdminPage couponsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to coupons page");
            if (couponsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to coupons page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to coupons page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to coupons page");
            if (!couponsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to coupons page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "coupons page and he shouldn't");
            }
        }
        return validationResults;
    }
}
