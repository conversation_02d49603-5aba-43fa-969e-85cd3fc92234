package helpers.rolesValidators;

import modals.wordpressAdmin.BrandsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BrandsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(BrandsRolesValidators.class);

    public ValidationResults validateBrandsAccessPerRole(ValidationResults validationResults
            , BrandsAdminPage brandsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, brandsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , BrandsAdminPage brandsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to brands page");
            if (brandsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to brands page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to brands page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to brands page");
            if (!brandsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to brands page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "brands page and he shouldn't");
            }
        }
        return validationResults;
    }
}
