package helpers.rolesValidators;

import modals.mainAdminPortal.BannersAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BannersRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(BannersRolesValidators.class);

    public ValidationResults validateBannersPageAccessPerRole(ValidationResults validationResults,
                                                              BannersAdminPage bannersAdminPage, String roleName,
                                                              boolean pageAccess, boolean createBanners,
                                                              boolean sortBanners, boolean editBanners,
                                                              boolean deleteBanners){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, bannersAdminPage, roleName,
                pageAccess);
        validationResults = validateCreateBannersPermission(validationResults, bannersAdminPage, roleName,
                pageAccess, createBanners);
        validationResults = validateSortBannersPermission(validationResults, bannersAdminPage, roleName,
                pageAccess, sortBanners);
        validationResults = validateEditBannersPermission(validationResults,bannersAdminPage, roleName,
                pageAccess, editBanners);
        validationResults = validateDeleteBannersPermission(validationResults, bannersAdminPage, roleName,
                pageAccess, deleteBanners);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults,
                                                           BannersAdminPage bannersAdminPage,
                                                           String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to Banners page");
            if (bannersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to banners page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to banners page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to banners page");
            if (!bannersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to banners page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "banners page and he shouldn't");
            }
        }
        return validationResults;
    }

    private ValidationResults validateCreateBannersPermission(ValidationResults validationResults,
                                                              BannersAdminPage bannersAdminPage, String roleName,
                                                              boolean pageAccess, boolean createBanners){
        if (pageAccess) {
            if (createBanners) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to add new banners");
                if (bannersAdminPage.isAddBannerBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to add new banners");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to add new banners");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to add new banners");
                if (!bannersAdminPage.isAddBannerBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to add new banners");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "add new banners and he shouldn't");
                }
            }
        } else if (createBanners){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (createBanners) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "createBanners validations");
        } else {
            validationResults.addALogToValidationResults("\n[CreateBanners] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateSortBannersPermission(ValidationResults validationResults,
                                                            BannersAdminPage bannersAdminPage, String roleName,
                                                            boolean pageAccess, boolean sortBanners){
        if (pageAccess) {
            if (sortBanners) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to sort banners");
                if (bannersAdminPage.isSortBannersBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to sort banners");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to sort banners");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to sort banners");
                if (!bannersAdminPage.isSortBannersBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to sort banners");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "sort banners and he shouldn't");
                }
            }
        } else if (sortBanners){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (sortBanners) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "sortBanners validations");
        } else {
            validationResults.addALogToValidationResults("\n[sortBanners] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateEditBannersPermission(ValidationResults validationResults,
                                                            BannersAdminPage bannersAdminPage, String roleName,
                                                            boolean pageAccess, boolean editBanners){
        if (pageAccess) {
            if (editBanners) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to edit banners");
                if (bannersAdminPage.isEditBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to edit banners");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to edit banners");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to edit banners...");
                if (!bannersAdminPage.isEditBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to edit banners");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "edit banners and he shouldn't");
                }
            }
        } else if (editBanners){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (editBanners) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "editBanners validations");
        } else {
            validationResults.addALogToValidationResults("\n[editBanners] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateDeleteBannersPermission(ValidationResults validationResults,
                                                              BannersAdminPage bannersAdminPage, String roleName,
                                                              boolean pageAccess, boolean deleteBanners){
        if (pageAccess) {
            if (deleteBanners) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to delete banners");
                if (bannersAdminPage.isDeleteBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to delete banners");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to delete banners");
                }
            } else {
                logger.info("\nUser with role " + roleName + "shouldn't have access to delete banners...");
                if (!bannersAdminPage.isDeleteBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to delete banners");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "delete banners and he shouldn't");
                }
            }
        } else if (deleteBanners){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (deleteBanners) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "deleteBanners validations");
        } else {
            validationResults.addALogToValidationResults("\n[deleteBanners] No validations required.");
        }
        return validationResults;
    }
}
