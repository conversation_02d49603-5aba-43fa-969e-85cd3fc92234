package helpers.rolesValidators;

import modals.mainAdminPortal.SignatureAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SignaturesRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(SignaturesRolesValidators.class);

    public ValidationResults validateSignatureAccessPerRole(ValidationResults validationResults
            , SignatureAdminPage signatureAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, signatureAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , SignatureAdminPage signatureAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to signature page");
            if (signatureAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to signature page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to signature page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to signature page");
            if (!signatureAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to signature page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "signature page and he shouldn't");
            }
        }
        return validationResults;
    }
}
