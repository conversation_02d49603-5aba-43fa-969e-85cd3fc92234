package helpers.rolesValidators;

import modals.wordpressAdmin.CareersSettingsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CareersSettingsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(CareersSettingsRolesValidators.class);

    public ValidationResults validateCareersSettingsAccessPerRole(ValidationResults validationResults
            , CareersSettingsAdminPage careersSettingsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, careersSettingsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , CareersSettingsAdminPage careersSettingsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to careersSettings page");
            if (careersSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to careersSettings page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to careersSettings page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to careersSettings page");
            if (!careersSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to careersSettings page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "careersSettings page and he shouldn't");
            }
        }
        return validationResults;
    }
}
