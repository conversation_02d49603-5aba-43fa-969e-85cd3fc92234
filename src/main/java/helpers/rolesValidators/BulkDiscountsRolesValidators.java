package helpers.rolesValidators;

import modals.mainAdminPortal.BulkDiscountsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BulkDiscountsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(BulkDiscountsRolesValidators.class);

    public ValidationResults validateBulkDiscountsAccessPerRole(ValidationResults validationResults
            , BulkDiscountsAdminPage bulkDiscountsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, bulkDiscountsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , BulkDiscountsAdminPage bulkDiscountsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to bulk discounts admin page");
            if (bulkDiscountsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to bulk discounts admin page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to bulk discounts admin page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to bulk discounts admin page");
            if (!bulkDiscountsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to bulk discounts admin page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "bulk discounts admin page and he shouldn't");
            }
        }
        return validationResults;
    }
}
