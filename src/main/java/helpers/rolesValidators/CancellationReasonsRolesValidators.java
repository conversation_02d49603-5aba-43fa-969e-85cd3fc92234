package helpers.rolesValidators;

import modals.mainAdminPortal.CancellationReasonsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CancellationReasonsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(CancellationReasonsRolesValidators.class);

    public ValidationResults validateCancellationReasonsAccessPerRole(ValidationResults validationResults,
                                                                      CancellationReasonsAdminPage cancellationReasonsAdminPage,
                                                                      String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, cancellationReasonsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults,
                                                           CancellationReasonsAdminPage cancellationReasonsAdminPage,
                                                           String roleName,
                                                           boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to cancellation reasons admin page");
            if (cancellationReasonsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to cancellation reasons admin page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to cancellation reasons admin page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to pages admin page");
            if (!cancellationReasonsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to cancellation reasons admin page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "cancellation reasons admin page and he shouldn't");
            }
        }
        return validationResults;
    }
}
