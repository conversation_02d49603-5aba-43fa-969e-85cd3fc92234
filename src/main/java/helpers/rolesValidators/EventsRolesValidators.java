package helpers.rolesValidators;

import modals.wordpressAdmin.EventsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EventsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(EventsRolesValidators.class);

    public ValidationResults validateEventsAccessPerRole(ValidationResults validationResults
            , EventsAdminPage eventsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, eventsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , EventsAdminPage eventsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to events page");
            if (eventsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to events page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to events page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to events page");
            if (!eventsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to events page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "events page and he shouldn't");
            }
        }
        return validationResults;
    }
}
