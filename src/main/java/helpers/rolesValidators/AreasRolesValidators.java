package helpers.rolesValidators;

import modals.wordpressAdmin.AreasAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AreasRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(AreasRolesValidators.class);

    public ValidationResults validateAreasAccessPerRole(ValidationResults validationResults
            , AreasAdminPage areasAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, areasAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , AreasAdminPage areasAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to areas page");
            if (areasAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to areas page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to areas page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to areas page");
            if (!areasAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to areas page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "areas page and he shouldn't");
            }
        }
        return validationResults;
    }
}
