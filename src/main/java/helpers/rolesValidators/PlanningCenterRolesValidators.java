package helpers.rolesValidators;

import modals.mainAdminPortal.PlanningCenterAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PlanningCenterRolesValidators extends BaseRolesValidator{

    Logger logger = LoggerFactory.getLogger(PlanningCenterRolesValidators.class);

    public ValidationResults validatePlanningCenterPageAccessPerRole(ValidationResults validationResults,
                                                                     PlanningCenterAdminPage planningCenterAdminPage,
                                                                     String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, planningCenterAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults,
                                                           PlanningCenterAdminPage planningCenterAdminPage,
                                                           String roleName,
                                                           boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to planning center");
            if (planningCenterAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to planning center page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to planning center");
            if (!planningCenterAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to planning center page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "planning center page and he shouldn't");
            }
        }
        return validationResults;
    }
}
