package helpers.rolesValidators;

import modals.mainAdminPortal.AttendanceAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AttendanceRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(AttendanceRolesValidators.class);

    public ValidationResults validateAttendanceAccessPerRole(ValidationResults validationResults
            , AttendanceAdminPage attendanceAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, attendanceAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , AttendanceAdminPage attendanceAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to attendance page");
            if (attendanceAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to attendance page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to attendance page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to attendance page");
            if (!attendanceAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to attendance page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "attendance page and he shouldn't");
            }
        }
        return validationResults;
    }
}
