package helpers.rolesValidators;

import modals.wordpressAdmin.GeneralSettingsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GeneralSettingsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(GeneralSettingsRolesValidators.class);

    public ValidationResults validateGeneralSettingsAccessPerRole(ValidationResults validationResults
            , GeneralSettingsAdminPage generalSettingsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, generalSettingsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , GeneralSettingsAdminPage generalSettingsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to generalSettings page");
            if (generalSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to generalSettings page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to generalSettings page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to generalSettings page");
            if (!generalSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to generalSettings page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "generalSettings page and he shouldn't");
            }
        }
        return validationResults;
    }
}
