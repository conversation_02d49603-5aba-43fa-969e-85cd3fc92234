package helpers.rolesValidators;

import modals.wordpressAdmin.AppMessagesAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AppMessagesRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(PostsRolesValidators.class);

    public ValidationResults validateAppMessagesAccessPerRole(ValidationResults validationResults
            , AppMessagesAdminPage appMessagesAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, appMessagesAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , AppMessagesAdminPage appMessagesAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to appMessages page");
            if (appMessagesAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to appMessages page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to appMessages page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to appMessages page");
            if (!appMessagesAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to appMessages page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "appMessages page and he shouldn't");
            }
        }
        return validationResults;
    }
}
