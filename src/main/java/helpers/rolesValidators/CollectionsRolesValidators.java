package helpers.rolesValidators;

import modals.mainAdminPortal.CollectionsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CollectionsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(CollectionsRolesValidators.class);

    public ValidationResults validateCollectionsPageAccessPerRole(ValidationResults validationResults,
                                                                  CollectionsAdminPage collectionsAdminPage,
                                                                  String roleName, boolean pageAccess,
                                                                  boolean createCollections,
                                                                  boolean sortCollections, boolean editCollections,
                                                                  boolean deleteCollections){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, collectionsAdminPage, roleName,
                pageAccess);
        validationResults = validateCreateCollectionsPermission(validationResults, collectionsAdminPage,
                roleName, pageAccess, createCollections);
        validationResults = validateSortCollectionsPermission(validationResults, collectionsAdminPage,
                roleName, pageAccess, sortCollections);
        validationResults = validateEditCollectionsPermission(validationResults,collectionsAdminPage,
                roleName, pageAccess, editCollections);
        validationResults = validateDeleteCollectionsPermission(validationResults, collectionsAdminPage,
                roleName, pageAccess, deleteCollections);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults,
                                                           CollectionsAdminPage collectionsAdminPage,
                                                           String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to collections page");
            if (collectionsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to collections page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to collections page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to collections page");
            if (!collectionsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to collections page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "collections page and he shouldn't");
            }
        }
        return validationResults;
    }

    private ValidationResults validateCreateCollectionsPermission(ValidationResults validationResults,
                                                                  CollectionsAdminPage collectionsAdminPage,
                                                                  String roleName, boolean pageAccess,
                                                                  boolean createCollections){
        if (pageAccess) {
            if (createCollections) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to add new collections");
                if (collectionsAdminPage.isAddButtonsDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to add new collections");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to add new collections");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to add new collections");
                if (!collectionsAdminPage.isAddButtonsDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to add new collections");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "add new collections and he shouldn't");
                }
            }
        } else if (createCollections){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (createCollections) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "createCollections validations");
        } else {
            validationResults.addALogToValidationResults("\n[createCollections] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateSortCollectionsPermission(ValidationResults validationResults,
                                                                CollectionsAdminPage collectionsAdminPage,
                                                                String roleName, boolean pageAccess,
                                                                boolean sortCollections){
        if (pageAccess) {
            if (sortCollections) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to sort collections");
                if (collectionsAdminPage.isSortCollectionsBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to sort collections");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to sort collections");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to sort collections");
                if (!collectionsAdminPage.isSortCollectionsBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to sort collections");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "sort collections and he shouldn't");
                }
            }
        } else if (sortCollections){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (sortCollections) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "sortCollections validations");
        } else {
            validationResults.addALogToValidationResults("\n[SortCollections] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateEditCollectionsPermission(ValidationResults validationResults,
                                                                CollectionsAdminPage collectionsAdminPage,
                                                                String roleName, boolean pageAccess,
                                                                boolean editCollections){
        if (pageAccess) {
            if (editCollections) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to edit collections");
                if (collectionsAdminPage.isEditBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to edit collections");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to edit collections");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to edit collections...");
                if (!collectionsAdminPage.isEditBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to edit collections");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "edit collections and he shouldn't");
                }
            }
        } else if (editCollections){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (editCollections) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "editCollections validations");
        } else {
            validationResults.addALogToValidationResults("\n[editCollections] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateDeleteCollectionsPermission(ValidationResults validationResults,
                                                                  CollectionsAdminPage collectionsAdminPage,
                                                                  String roleName, boolean pageAccess,
                                                                  boolean deleteCollections){
        if (pageAccess) {
            if (deleteCollections) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to delete collections");
                if (collectionsAdminPage.isDeleteBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to delete collections");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to delete collections");
                }
            } else {
                logger.info("\nUser with role " + roleName + "shouldn't have access to delete collections...");
                if (!collectionsAdminPage.isDeleteBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to delete collections");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "delete collections and he shouldn't");
                }
            }
        } else if (deleteCollections){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (deleteCollections) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "deleteCollections validations");
        } else {
            validationResults.addALogToValidationResults("\n[deleteCollections] No validations required.");
        }
        return validationResults;
    }
}
