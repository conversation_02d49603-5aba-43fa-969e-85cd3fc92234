package helpers.rolesValidators;

import modals.wordpressAdmin.ProductsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ProductsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(ProductsAdminPage.class);

    public ValidationResults validateProductsAccessPerRole(ValidationResults validationResults
            , ProductsAdminPage productsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, productsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , ProductsAdminPage productsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to products page");
            if (productsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to products page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to products page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to products page");
            if (!productsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to products page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "products page and he shouldn't");
            }
        }
        return validationResults;
    }
}
