package helpers.rolesValidators;

import modals.wordpressAdmin.InternalCategoriesAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class InternalCategoriesRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(InternalCategoriesRolesValidators.class);

    public ValidationResults validateInternalCategoriesAccessPerRole(ValidationResults validationResults
            , InternalCategoriesAdminPage internalCategoriesAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, internalCategoriesAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , InternalCategoriesAdminPage internalCategoriesAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to internal categories page");
            if (internalCategoriesAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to internal categories page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to internal categories page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to internal categories page");
            if (!internalCategoriesAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to internal categories page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "internal categories page and he shouldn't");
            }
        }
        return validationResults;
    }
}
