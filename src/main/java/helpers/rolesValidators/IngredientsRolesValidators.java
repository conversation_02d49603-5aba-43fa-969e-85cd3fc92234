package helpers.rolesValidators;

import modals.wordpressAdmin.IngredientsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IngredientsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(IngredientsRolesValidators.class);

    public ValidationResults validateIngredientsAccessPerRole(ValidationResults validationResults
            , IngredientsAdminPage ingredientsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, ingredientsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , IngredientsAdminPage ingredientsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to ingredients page");
            if (ingredientsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to ingredients page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to ingredients page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to ingredients page");
            if (!ingredientsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to ingredients page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "ingredients page and he shouldn't");
            }
        }
        return validationResults;
    }
}
