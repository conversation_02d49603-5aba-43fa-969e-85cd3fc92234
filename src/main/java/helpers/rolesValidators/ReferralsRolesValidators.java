package helpers.rolesValidators;

import modals.wordpressAdmin.ReferralsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ReferralsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(ReferralsRolesValidators.class);

    public ValidationResults validateReferralsAccessPerRole(ValidationResults validationResults
            , ReferralsAdminPage referralsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, referralsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , ReferralsAdminPage referralsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to referrals page");
            if (referralsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to referrals page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to referrals page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to referrals page");
            if (!referralsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to referrals page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "referrals page and he shouldn't");
            }
        }
        return validationResults;
    }
}
