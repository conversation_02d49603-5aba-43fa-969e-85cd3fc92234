package helpers.rolesValidators;

import modals.mainAdminPortal.CategoriesSortingAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ProductsSortingRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(ProductsSortingRolesValidators.class);

    public ValidationResults validateProductsSortingAccessPerRole(ValidationResults validationResults
            , CategoriesSortingAdminPage categoriesSortingAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, categoriesSortingAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , CategoriesSortingAdminPage categoriesSortingAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to categories sorting page");
            if (categoriesSortingAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to categories sorting page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to categories sorting page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to categories sorting page");
            if (!categoriesSortingAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to categories sorting page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "categories sorting page and he shouldn't");
            }
        }
        return validationResults;
    }
}
