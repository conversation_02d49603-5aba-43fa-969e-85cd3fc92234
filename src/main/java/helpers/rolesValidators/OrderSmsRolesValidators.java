package helpers.rolesValidators;

import modals.wordpressAdmin.OrderSmsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrderSmsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(OrderSmsRolesValidators.class);

    public ValidationResults validateOrderSmsAccessPerRole(ValidationResults validationResults
            , OrderSmsAdminPage orderSmsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, orderSmsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , OrderSmsAdminPage orderSmsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to orderSms page");
            if (orderSmsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to orderSms page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to orderSms page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to orderSms page");
            if (!orderSmsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to orderSms page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "orderSms page and he shouldn't");
            }
        }
        return validationResults;
    }
}
