package helpers.rolesValidators;

import modals.wordpressAdmin.AppFaqsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AppFaqsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(AppFaqsRolesValidators.class);

    public ValidationResults validateAppFaqsAccessPerRole(ValidationResults validationResults,
                                                          AppFaqsAdminPage appFaqsAdminPage,
                                                          String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, appFaqsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults,
                                                           AppFaqsAdminPage appFaqsAdminPage,
                                                           String roleName,
                                                           boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to App FAQs admin page");
            if (appFaqsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to App FAQs admin page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to App FAQs admin page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to App FAQs admin page");
            if (!appFaqsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to App FAQs admin page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "App FAQs admin page and he shouldn't");
            }
        }
        return validationResults;
    }
}
