package helpers.rolesValidators;

import modals.mainAdminPortal.ScheduledOrdersAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ScheduledOrdersRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(ScheduledOrdersRolesValidators.class);

    public ValidationResults validateScheduledOrdersAccessPerRole(ValidationResults validationResults
            , ScheduledOrdersAdminPage scheduledOrdersAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, scheduledOrdersAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , ScheduledOrdersAdminPage scheduledOrdersAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to scheduled orders page");
            if (scheduledOrdersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to scheduled orders page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to scheduled orders page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to scheduled orders page");
            if (!scheduledOrdersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to scheduled orders page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "scheduled orders page and he shouldn't");
            }
        }
        return validationResults;
    }
}
