package helpers.rolesValidators;

import modals.wordpressAdmin.OrdersAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OrdersRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(OrdersRolesValidators.class);

    public ValidationResults validateOrdersAccessPerRole(ValidationResults validationResults
            , OrdersAdminPage ordersAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, ordersAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , OrdersAdminPage ordersAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to orders page");
            if (ordersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to orders page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to orders page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to orders page");
            if (!ordersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to orders page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "orders page and he shouldn't");
            }
        }
        return validationResults;
    }
}
