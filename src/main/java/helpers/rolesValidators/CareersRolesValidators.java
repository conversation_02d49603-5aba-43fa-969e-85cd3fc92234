package helpers.rolesValidators;

import modals.wordpressAdmin.CareersAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CareersRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(CareersRolesValidators.class);

    public ValidationResults validateCareersAccessPerRole(ValidationResults validationResults
            , CareersAdminPage careersAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, careersAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , CareersAdminPage careersAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to careers page");
            if (careersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to careers page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to careers page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to careers page");
            if (!careersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to careers page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "careers page and he shouldn't");
            }
        }
        return validationResults;
    }
}
