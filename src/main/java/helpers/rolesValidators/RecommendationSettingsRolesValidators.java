package helpers.rolesValidators;

import modals.wordpressAdmin.RecommendationSettingsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RecommendationSettingsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(RecommendationSettingsRolesValidators.class);

    public ValidationResults validateRecommendationSettingsAccessPerRole(ValidationResults validationResults
            , RecommendationSettingsAdminPage recommendationSettingsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, recommendationSettingsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , RecommendationSettingsAdminPage recommendationSettingsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to recommendationSettings page");
            if (recommendationSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to recommendationSettings page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to recommendationSettings page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to recommendationSettings page");
            if (!recommendationSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to recommendationSettings page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "recommendationSettings page and he shouldn't");
            }
        }
        return validationResults;
    }
}
