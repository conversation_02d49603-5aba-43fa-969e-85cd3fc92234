package helpers.rolesValidators;

import modals.wordpressAdmin.VodafoneCouponAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class VodafoneCouponRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(VodafoneCouponRolesValidators.class);

    public ValidationResults validateVodafoneCouponAccessPerRole(ValidationResults validationResults
            , VodafoneCouponAdminPage VodafoneCouponAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, VodafoneCouponAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , VodafoneCouponAdminPage VodafoneCouponAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to VodafoneCoupon page");
            if (VodafoneCouponAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to VodafoneCoupon page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to VodafoneCoupon page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to VodafoneCoupon page");
            if (!VodafoneCouponAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to VodafoneCoupon page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "VodafoneCoupon page and he shouldn't");
            }
        }
        return validationResults;
    }
}
