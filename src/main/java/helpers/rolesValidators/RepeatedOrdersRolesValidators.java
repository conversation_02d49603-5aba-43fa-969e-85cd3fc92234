package helpers.rolesValidators;

import modals.wordpressAdmin.RepeatedOrdersAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RepeatedOrdersRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(RepeatedOrdersRolesValidators.class);

    public ValidationResults validateRepeatedOrdersAccessPerRole(ValidationResults validationResults
            , RepeatedOrdersAdminPage repeatedOrdersAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, repeatedOrdersAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , RepeatedOrdersAdminPage repeatedOrdersAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to repeated orders page");
            if (repeatedOrdersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to repeated orders page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to repeated orders page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to repeated orders page");
            if (!repeatedOrdersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to repeated orders page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "repeated orders page and he shouldn't");
            }
        }
        return validationResults;
    }
}
