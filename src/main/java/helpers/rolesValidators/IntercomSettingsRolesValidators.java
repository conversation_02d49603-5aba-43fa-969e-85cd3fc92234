package helpers.rolesValidators;

import modals.wordpressAdmin.IntercomSettingsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IntercomSettingsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(IntercomSettingsRolesValidators.class);

    public ValidationResults validateIntercomSettingsAccessPerRole(ValidationResults validationResults
            , IntercomSettingsAdminPage intercomSettingsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, intercomSettingsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , IntercomSettingsAdminPage intercomSettingsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to intercomSettings page");
            if (intercomSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to intercomSettings page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to intercomSettings page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to intercomSettings page");
            if (!intercomSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to intercomSettings page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "intercomSettings page and he shouldn't");
            }
        }
        return validationResults;
    }
}
