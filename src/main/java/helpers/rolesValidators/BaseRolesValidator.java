package helpers.rolesValidators;

import java.util.Map;

public class BaseRolesValidator {
    public boolean isRoleAllowedForFunction(Map<String, Map<String, Boolean>> rolesFunctionMap,
                                            String roleName, String function) {
        Map<String, Boolean> roleAccess = rolesFunctionMap.get(function);
        return roleAccess != null && roleAccess.containsKey(roleName) && roleAccess.get(roleName);
    }
}
