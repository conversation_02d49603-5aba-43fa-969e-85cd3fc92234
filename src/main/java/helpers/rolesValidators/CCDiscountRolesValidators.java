package helpers.rolesValidators;

import modals.wordpressAdmin.CCDiscountAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CCDiscountRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(CCDiscountRolesValidators.class);

    public ValidationResults validateCCDiscountAccessPerRole(ValidationResults validationResults
            , CCDiscountAdminPage ccDiscountAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, ccDiscountAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , CCDiscountAdminPage ccDiscountAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to ccDiscount page");
            if (ccDiscountAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to ccDiscount page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to ccDiscount page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to CCDiscount page");
            if (!ccDiscountAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to ccDiscount page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "ccDiscount page and he shouldn't");
            }
        }
        return validationResults;
    }
}
