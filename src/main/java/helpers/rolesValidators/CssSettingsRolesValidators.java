package helpers.rolesValidators;

import modals.wordpressAdmin.CssSettingsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CssSettingsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(CssSettingsRolesValidators.class);

    public ValidationResults validateCssSettingsAccessPerRole(ValidationResults validationResults
            , CssSettingsAdminPage cssSettingsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, cssSettingsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , CssSettingsAdminPage cssSettingsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to cssSettings page");
            if (cssSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to cssSettings page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to cssSettings page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to cssSettings page");
            if (!cssSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to cssSettings page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "cssSettings page and he shouldn't");
            }
        }
        return validationResults;
    }
}
