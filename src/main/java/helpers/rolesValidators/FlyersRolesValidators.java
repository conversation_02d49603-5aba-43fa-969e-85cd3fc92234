package helpers.rolesValidators;

import modals.wordpressAdmin.FlyersAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FlyersRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(FlyersRolesValidators.class);

    public ValidationResults validateFlyersAccessPerRole(ValidationResults validationResults
            , FlyersAdminPage flyersAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, flyersAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , FlyersAdminPage flyersAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to flyers page");
            if (flyersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to flyers page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to flyers page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to flyers page");
            if (!flyersAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to flyers page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "flyers page and he shouldn't");
            }
        }
        return validationResults;
    }
}
