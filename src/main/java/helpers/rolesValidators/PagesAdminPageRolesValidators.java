package helpers.rolesValidators;

import modals.wordpressAdmin.PagesAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PagesAdminPageRolesValidators extends BaseRolesValidator{

    Logger logger = LoggerFactory.getLogger(PagesAdminPageRolesValidators.class);

    public ValidationResults validatePagesAdminPageAccessPerRole(ValidationResults validationResults,
                                                                 PagesAdminPage pagesAdminPage,
                                                                 String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, pagesAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults,
                                                           PagesAdminPage pagesAdminPage,
                                                           String roleName,
                                                           boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to pages admin page");
            if (pagesAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to pages admin page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to pages admin page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to pages admin page");
            if (!pagesAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to pages admin page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "pages admin page and he shouldn't");
            }
        }
        return validationResults;
    }
}
