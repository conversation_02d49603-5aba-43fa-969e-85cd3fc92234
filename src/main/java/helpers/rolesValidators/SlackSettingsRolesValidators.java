package helpers.rolesValidators;

import modals.wordpressAdmin.SlackSettingsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SlackSettingsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(SlackSettingsRolesValidators.class);

    public ValidationResults validateSlackSettingsAccessPerRole(ValidationResults validationResults
            , SlackSettingsAdminPage slackSettingsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, slackSettingsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , SlackSettingsAdminPage slackSettingsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to slackSettings page");
            if (slackSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to slackSettings page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to slackSettings page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to slackSettings page");
            if (!slackSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to slackSettings page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "slackSettings page and he shouldn't");
            }
        }
        return validationResults;
    }
}
