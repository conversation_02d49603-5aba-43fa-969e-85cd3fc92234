package helpers.rolesValidators;

import modals.wordpressAdmin.ProductCategoriesAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CategoriesRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(CategoriesRolesValidators.class);

    public ValidationResults validateProductsCategoriesAccessPerRole(ValidationResults validationResults
            , ProductCategoriesAdminPage productCategoriesAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, productCategoriesAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , ProductCategoriesAdminPage productCategoriesAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to product categories page");
            if (productCategoriesAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to product categories page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to product categories page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to product categories page");
            if (!productCategoriesAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to product categories page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "product categories page and he shouldn't");
            }
        }
        return validationResults;
    }
}
