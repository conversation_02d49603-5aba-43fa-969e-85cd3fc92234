package helpers.rolesValidators;

import modals.wordpressAdmin.CXSettingsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CXSettingsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(CXSettingsRolesValidators.class);

    public ValidationResults validateCXSettingsAccessPerRole(ValidationResults validationResults
            , CXSettingsAdminPage cxSettingsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, cxSettingsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , CXSettingsAdminPage cxSettingsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to cxSettings page");
            if (cxSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to cxSettings page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to cxSettings page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to cxSettings page");
            if (!cxSettingsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to cxSettings page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "cxSettings page and he shouldn't");
            }
        }
        return validationResults;
    }
}
