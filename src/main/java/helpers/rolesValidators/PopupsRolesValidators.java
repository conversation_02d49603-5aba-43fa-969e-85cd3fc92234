package helpers.rolesValidators;

import modals.mainAdminPortal.PopUpsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PopupsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(PopupsRolesValidators.class);

    public ValidationResults validatePopupsPageAccessPerRole(ValidationResults validationResults,
                                                             PopUpsAdminPage popUpsAdminPage, String roleName,
                                                             boolean pageAccess, boolean createPopups,
                                                             boolean editPopups, boolean deletePopups){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, popUpsAdminPage, roleName,
                pageAccess);
        validationResults = validateCreatePopupsPermission(validationResults, popUpsAdminPage, roleName,
                pageAccess, createPopups);
        validationResults = validateEditPopupsPermission(validationResults,popUpsAdminPage, roleName,
                pageAccess, editPopups);
        validationResults = validateDeletePopupsPermission(validationResults, popUpsAdminPage, roleName,
                pageAccess, deletePopups);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults,
                                                           PopUpsAdminPage popUpsAdminPage,
                                                           String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to popups page");
            if (popUpsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to popups page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to popups page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to popups page");
            if (!popUpsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to popups page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "popups page and he shouldn't");
            }
        }
        return validationResults;
    }

    private ValidationResults validateCreatePopupsPermission(ValidationResults validationResults,
                                                             PopUpsAdminPage popUpsAdminPage, String roleName,
                                                             boolean pageAccess, boolean createPopups){
        if (pageAccess) {
            if (createPopups) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to add new popup");
                if (popUpsAdminPage.isAddNewPopupBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to add new popup");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to add new popup");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to add new popup");
                if (!popUpsAdminPage.isAddNewPopupBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to add new popup");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "add new popup and he shouldn't");
                }
            }
        } else if (createPopups){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (createPopups) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "createPopups validations");
        } else {
            validationResults.addALogToValidationResults("\n[createPopups] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateEditPopupsPermission(ValidationResults validationResults,
                                                           PopUpsAdminPage popUpsAdminPage, String roleName,
                                                           boolean pageAccess, boolean editPopups){
        if (pageAccess) {
            if (editPopups) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to edit popups");
                if (popUpsAdminPage.isEditBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to edit popups");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to edit popups");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to edit popups...");
                if (!popUpsAdminPage.isEditBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to edit popups");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "edit popups and he shouldn't");
                }
            }
        } else if (editPopups){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (editPopups) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "editPopups validations");
        } else {
            validationResults.addALogToValidationResults("\n[editPopups] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateDeletePopupsPermission(ValidationResults validationResults,
                                                             PopUpsAdminPage popUpsAdminPage, String roleName,
                                                             boolean pageAccess, boolean deletePopups){
        if (pageAccess) {
            if (deletePopups) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to delete popups");
                if (popUpsAdminPage.isDeleteBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to delete popups");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to delete popups");
                }
            } else {
                logger.info("\nUser with role " + roleName + "shouldn't have access to delete popups...");
                if (!popUpsAdminPage.isDeleteBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to delete popups");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "delete popups and he shouldn't");
                }
            }
        } else if (deletePopups){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (deletePopups) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "deletePopups validations");
        } else {
            validationResults.addALogToValidationResults("\n[deletePopups] No validations required.");
        }
        return validationResults;
    }
}
