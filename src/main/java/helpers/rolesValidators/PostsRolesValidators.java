package helpers.rolesValidators;

import modals.wordpressAdmin.PostsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PostsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(PostsRolesValidators.class);

    public ValidationResults validatePostsAccessPerRole(ValidationResults validationResults
            , PostsAdminPage postsAdminPage, String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, postsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults
            , PostsAdminPage postsAdminPage, String roleName, boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to posts page");
            if (postsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to posts page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to posts page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to posts page");
            if (!postsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to posts page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "posts page and he shouldn't");
            }
        }
        return validationResults;
    }
}
