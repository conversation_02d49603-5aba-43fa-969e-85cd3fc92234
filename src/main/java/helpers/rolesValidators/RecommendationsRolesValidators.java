package helpers.rolesValidators;

import modals.mainAdminPortal.RecommendationsAdminPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RecommendationsRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(RecommendationsRolesValidators.class);

    public ValidationResults validateRecommendationsPageAccessPerRole(ValidationResults validationResults
            , RecommendationsAdminPage recommendationsAdminPage
            , String roleName, boolean pageAccess){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, recommendationsAdminPage,
                roleName, pageAccess);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults,
                                                           RecommendationsAdminPage recommendationsAdminPage,
                                                           String roleName,
                                                           boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to recommendations");
            if (recommendationsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to recommendations admin page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to recommendations admin page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to recommendations admin page");
            if (!recommendationsAdminPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to recommendations admin page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "recommendations admin page and he shouldn't");
            }
        }
        return validationResults;
    }
}
