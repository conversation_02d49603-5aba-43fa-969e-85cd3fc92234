package helpers.rolesValidators;

import modals.mainAdminPortal.SwitcherPage;
import models.ValidationResults;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SwitcherRolesValidators extends BaseRolesValidator{
    Logger logger = LoggerFactory.getLogger(SwitcherRolesValidators.class);

    public ValidationResults validateSwitcherPageAccessPerRole(ValidationResults validationResults,
                                                               SwitcherPage switcherPage, String roleName,
                                                               boolean pageAccess, boolean updateBalance,
                                                               boolean changePassword, boolean blockUser, boolean deleteUser,
                                                               boolean updateRoles, boolean updateUserData,
                                                               boolean updateAddress, boolean viewUserInfo,
                                                               boolean addNewUser){
        validationResults.setResult(true);
        validationResults = validatePageAccessPermission(validationResults, switcherPage, roleName,
                pageAccess);
        validationResults = validateAddNewUserPermission(validationResults, switcherPage, roleName,
                pageAccess, addNewUser);
        validationResults = validateBlockUserPermission(validationResults, switcherPage, roleName,
                pageAccess, blockUser);
        validationResults = validateUpdateBalancePermission(validationResults,switcherPage, roleName,
                pageAccess, updateBalance);
        validationResults = validateChangePwdPermission(validationResults, switcherPage, roleName,
                pageAccess, changePassword);
        validationResults = validateDeleteUserPermission(validationResults, switcherPage, roleName,
                pageAccess, deleteUser);
        validationResults = validateUpdateRolesPermission(validationResults, switcherPage, roleName,
                pageAccess, updateRoles);
        validationResults = validateUpdateUserDataPermission(validationResults, switcherPage, roleName,
                pageAccess, updateUserData);
        validationResults = validateUpdateAddressPermission(validationResults, switcherPage, roleName,
                pageAccess, updateAddress);
        validationResults = validateViewUserInfoPermission(validationResults, switcherPage, roleName,
                pageAccess, viewUserInfo);

        return validationResults;
    }

    private ValidationResults validatePageAccessPermission(ValidationResults validationResults,
                                                           SwitcherPage switcherPage, String roleName,
                                                           boolean pageAccess){
        if (pageAccess) {
            logger.info("\nUser with role " + roleName + " should be having access to switcher");
            if (switcherPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                        "to switcher page");
            else{
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                        "access to page and he should");
            }
        } else {
            logger.info("User with role " + roleName + "shouldn't have access to switcher");
            if (!switcherPage.isPageDisplayed())
                validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                        "access to switcher page");
            else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                        "switcher page and he shouldn't");
            }
        }
        return validationResults;
    }

    private ValidationResults validateAddNewUserPermission(ValidationResults validationResults,
                                                           SwitcherPage switcherPage, String roleName,
                                                           boolean pageAccess, boolean addNewUser){
        if (pageAccess) {
            if (addNewUser) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to add new users");
                if (switcherPage.isAddNewUserBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to add new users");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to add new users");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to add new users");
                if (!switcherPage.isAddNewUserBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to add new users");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "add new users and he shouldn't");
                }
            }
        } else if (addNewUser){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (addNewUser) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "addNewUser validations");
        } else {
            validationResults.addALogToValidationResults("\n[AddNewUser] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateBlockUserPermission(ValidationResults validationResults,
                                                          SwitcherPage switcherPage, String roleName,
                                                          boolean pageAccess, boolean blockUser){
        if (pageAccess) {
            if (blockUser) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to block users");
                if (switcherPage.isBlockUserBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to block users");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to block users");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to block users");
                if (!switcherPage.isBlockUserBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to block users");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "block users and he shouldn't");
                }
            }
        } else if (blockUser){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (blockUser) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "blockUser validations");
        } else {
            validationResults.addALogToValidationResults("\n[BlockUser] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateDeleteUserPermission(ValidationResults validationResults,
                                                           SwitcherPage switcherPage, String roleName,
                                                           boolean pageAccess, boolean deleteUser){
        if (pageAccess) {
            if (deleteUser) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to delete users");
                if (switcherPage.isDeleteUserBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to delete users");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to delete users");
                }
            } else {
                logger.info("User with role " + roleName + "shouldn't have access to delete users...");
                if (!switcherPage.isDeleteUserBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to delete users");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "delete users and he shouldn't");
                }
            }
        } else if (deleteUser){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (deleteUser) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "deleteUser validations");
        } else {
            validationResults.addALogToValidationResults("\n[deleteUser] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateUpdateBalancePermission(ValidationResults validationResults,
                                                              SwitcherPage switcherPage, String roleName,
                                                              boolean pageAccess, boolean updateBalance){
        if (pageAccess) {
            if (updateBalance) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to update user's balance");
                if (switcherPage.isUpdateBalanceBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to update user's balance");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to update user's balance");
                }
            } else {
                logger.info("\nUser with role " + roleName + "shouldn't have access to update user's balance...");
                if (!switcherPage.isUpdateBalanceBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to update user's balance");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "update user's balance and he shouldn't");
                }
            }
        } else if (updateBalance){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (updateBalance) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "updateBalance validations");
        } else {
            validationResults.addALogToValidationResults("\n[updateBalance] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateChangePwdPermission(ValidationResults validationResults,
                                                          SwitcherPage switcherPage, String roleName,
                                                          boolean pageAccess, boolean changePassword){
        if (pageAccess) {
            if (changePassword) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to change user's password...");
                if (switcherPage.isChangePwdBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to change user's password");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to change user's password");
                }
            } else {
                logger.info("\nUser with role " + roleName + "shouldn't have access to change user's password...");
                if (!switcherPage.isChangePwdBtnDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to change user's password");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "change user's password and he shouldn't");
                }
            }
        } else if (changePassword){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (changePassword) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "changePassword validations");
        } else {
            validationResults.addALogToValidationResults("\n[changePassword] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateViewUserInfoPermission(ValidationResults validationResults,
                                                             SwitcherPage switcherPage, String roleName,
                                                             boolean pageAccess, boolean viewUserInfo){
        if (pageAccess) {
            if (viewUserInfo) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to view user's info...");
                if (switcherPage.isFirstNameTxtFieldDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to view user's info");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to view user's info");
                }
            } else {
                logger.info("\nUser with role " + roleName + "shouldn't have access to view user's info...");
                if (!switcherPage.isFirstNameTxtFieldDisplayed())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to view user's info");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "view user's info and he shouldn't");
                }
            }
        } else if (viewUserInfo){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (viewUserInfo) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "viewUserInfo validations");
        } else {
            validationResults.addALogToValidationResults("\n[viewUserInfo] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateUpdateRolesPermission(ValidationResults validationResults,
                                                            SwitcherPage switcherPage, String roleName,
                                                            boolean pageAccess, boolean updateRoles){
        if (pageAccess) {
            if (updateRoles) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to update roles...");
                if (!switcherPage.isRolesDropdownDisabled())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to update roles");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to update roles");
                }
            } else {
                logger.info("\nUser with role " + roleName + "shouldn't have access to update roles...");
                if (switcherPage.isRolesDropdownDisabled())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to update roles");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "update roles and he shouldn't");
                }
            }
        } else if (updateRoles){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (updateRoles) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "updateRoles validations");
        } else {
            validationResults.addALogToValidationResults("\n[updateRoles] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateUpdateAddressPermission(ValidationResults validationResults,
                                                              SwitcherPage switcherPage, String roleName,
                                                              boolean pageAccess, boolean updateAddress){
        if (pageAccess) {
            if (updateAddress) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to update address...");
                if (switcherPage.isAddressTxtFieldDisplayed()
                        && !switcherPage.isAddressTxtFieldDisabled())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to update address");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to update address");
                }
            } else {
                logger.info("\nUser with role " + roleName + "shouldn't have access to update address...");
                if (switcherPage.isAddressTxtFieldDisabled())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to update address");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "update address and he shouldn't");
                }
            }
        } else if (updateAddress){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (updateAddress) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "updateAddress validations");
        } else {
            validationResults.addALogToValidationResults("\n[updateAddress] No validations required.");
        }
        return validationResults;
    }

    private ValidationResults validateUpdateUserDataPermission(ValidationResults validationResults,
                                                               SwitcherPage switcherPage, String roleName,
                                                               boolean pageAccess, boolean updateUserData){
        if (pageAccess) {
            if (updateUserData) {
                logger.info("\nUser with role " + roleName + " should be having access " +
                        "to update user data...");
                if (switcherPage.isFirstNameTxtFieldDisplayed()
                        && !switcherPage.isFirstNameTxtFieldDisabled())
                    validationResults.addALogToValidationResults("\n[TRUE] User has access " +
                            "to update user data");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User doesn't have " +
                            "access to update user data");
                }
            } else {
                logger.info("\nUser with role " + roleName + "shouldn't have access to update user data...");
                if (switcherPage.isFirstNameTxtFieldDisabled())
                    validationResults.addALogToValidationResults("\n[TRUE] User doesn't have " +
                            "access to update user data");
                else {
                    validationResults.setResult(false);
                    validationResults.addALogToValidationResults("\n[FALSE] User has access to " +
                            "update user data and he shouldn't");
                }
            }
        } else if (updateUserData){
            logger.error("\n[DATA ERROR], pageAccess is FALSE while a sub permission (updateUserData) is TRUE");
            validationResults.addALogToValidationResults("\nUser shouldn't have access to page. Skipping " +
                    "updateUserData validations");
        } else {
            validationResults.addALogToValidationResults("\n[updateUserData] No validations required.");
        }
        return validationResults;
    }

}
