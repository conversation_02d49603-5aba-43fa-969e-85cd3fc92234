package helpers.factories;

import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;

public class WebVideoRecorderFactory {
    private static final Logger logger = LoggerFactory.getLogger(WebVideoRecorderFactory.class);

    public Process startRecording(Process ffmpegProcess, String videoFileName){
        if (isHeadlessEnvironment()){
            logger.error("Can't detect display attached to the operating system. " +
                    "Recording of the webTests might not function.");
        } else {
            logger.info("Found a display attached to the system and will proceed with recording initialization...");
        }

        String[] command = null;
        if (System.getProperty("os.name").toLowerCase().contains("win")){
            logger.info("Starting the video recorder for windows OS");
            command = new String[]{
                    "cmd", "/c"
                    , "ffmpeg"
                    , "-f", "gdigrab"
                    , "-i", "\"desktop\""
                    , "-f", "mp4"
                    , "-r", "5"
                    , "-c:v", "libx264"
                    , "-c:a", "aac"
                    , videoFileName};
        } else if (System.getProperty("os.name").toLowerCase().contains("mac")){
            logger.info("Starting the video recorder for macOS");
            command = new String[]{
                    "ffmpeg"
                    , "-f", "avfoundation"
                    ,"-i", "1"
                    , "-f", "mp4"
                    , "-r", "5"
                    , "-c:v", "libx264"
                    , "-c:a", "aac"
                    , videoFileName};
        } else if (System.getProperty("os.name").toLowerCase().contains("nix")
                || System.getProperty("os.name").toLowerCase().contains("nux")) {
            logger.info("Starting the video recorder for linux OS");
            command = new String[]{
                    "ffmpeg"
                    , "-f", "x11grab"
                    ,"-i", "\"0:0\""
                    , "-f", "mp4"
                    , "-r", "5"
                    , "-c:v", "libx264"
                    , "-c:a", "aac"
                    , videoFileName};
        } else {
            logger.error("An unsupported OS is found installed. Current OS is: " + System.getProperty("os.name"));
        }

        if (command == null){
            return null;
        }
        else {
            logger.info("Executing the command \"" + String.join(" ", command) + "\"");
            try {
                ffmpegProcess = new ProcessBuilder(command).redirectErrorStream(true).start();

                getThread(ffmpegProcess).start();

                logger.info("Recording has started");
            } catch (Exception e){
                logger.error("Starting the recording process crashed or didn't run as expected.", e);
            }
        }
        return ffmpegProcess;
    }

    @NotNull
    private static Thread getThread(Process ffmpegProcess) {
        Process process = ffmpegProcess;

        // Create a separate thread to capture and print the output asynchronously
        return new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                logger.info("Recording has started.\nPrinting the recording command output.");
                while ((line = reader.readLine()) != null) {
                    logger.info(line); // Print each line of the output
                }
            } catch (Exception e) {
                logger.error("Error while reading and printing FFmpeg output.", e);
            }
        });
    }

    public void stopRecording(Process ffmpegProcess){
        logger.info("Stopping the web recording...");
        if (ffmpegProcess != null) {
            logger.info("Found that recording has been running fine. Initiating the stop operation...");
            try {
                // Send the 'q' key to FFmpeg to stop the recording gracefully
                ffmpegProcess.getOutputStream().write("q".getBytes());
                ffmpegProcess.getOutputStream().flush();
                ffmpegProcess.getOutputStream().close();

                if (ffmpegProcess.waitFor() == 0){
                    logger.info("Recording has stopped successfully and saved in it's destination file.");
                } else {
                    logger.error("Recording didn't get terminated as expected and exit code is: "
                            + ffmpegProcess.exitValue());
                }
            } catch (Exception e) {
                logger.error("Terminating the recording failed.", e);
            }
        } else {
            logger.error("The ffmpegProcess is currently set to null. Ignoring the stop recording request");
        }
    }

    public boolean isHeadlessEnvironment(){
        return System.getenv("DISPLAY") == null
                || System.getenv("DISPLAY").isEmpty();
    }
}
