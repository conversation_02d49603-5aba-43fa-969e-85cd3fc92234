package helpers.factories;

import io.qameta.allure.Attachment;
import org.slf4j.MDC;
import org.testng.ITestContext;
import org.testng.ITestListener;
import org.testng.ITestResult;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class AllureLogListener implements ITestListener {
    @Override
    public void onTestStart(ITestResult result) {
        MDC.put("testMethodName", result.getMethod().getMethodName());
    }

    @Override
    public void onTestSuccess(ITestResult result) {
        attachLogs();
        MDC.remove("testMethodName");
    }

    @Override
    public void onTestFailure(ITestResult result) {
        attachLogs();
        MDC.remove("testMethodName");
    }

    @Override
    public void onTestSkipped(ITestResult result) {
        attachLogs();
    }

    @Override
    public void onTestFailedButWithinSuccessPercentage(ITestResult result) {
        attachLogs();
    }

    @Override
    public void onStart(ITestContext context) {
        // No action needed
    }

    @Override
    public void onFinish(ITestContext context) {
        // No action needed
    }

    @Attachment(value = "Test Logs", type = "text/plain")
    public String attachLogs() {
        try {
            return new String(Files.readAllBytes(Paths.get("logs/test.log")));
        } catch (IOException e) {
            e.printStackTrace();
            return "Failed to read logs";
        }
    }
}
