package helpers.factories;

import com.github.alexdlaird.ngrok.NgrokClient;
import com.github.alexdlaird.ngrok.protocol.CreateTunnel;
import com.github.alexdlaird.ngrok.protocol.Tunnel;
import helpers.BaseHelper;
import helpers.DataHelper;
import models.Configs;
import models.TunnelServerConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

@RestController
@SpringBootApplication
public class ServerFactory extends BaseHelper {

    private static final Logger logger = LoggerFactory.getLogger(ServerFactory.class);
    private final Configs configs = new DataHelper().readConfigs(new Configs());
    private final static ConcurrentLinkedQueue<String> webhookQueue = new ConcurrentLinkedQueue<>();

    public TunnelServerConnection createTunnel(String userIdentifier
            , TunnelServerConnection tunnelServerConnection){
        NgrokClient client = new NgrokClient.Builder().build();
        client.setAuthToken(configs.getNgrokAuthToken());

        int portNumber = generateRandomPortNumber();

        logger.info("Creating Ngrok tunnel ...");

        CreateTunnel createTunnel = new CreateTunnel.Builder()
                .withMetadata(userIdentifier)
                .withAddr(portNumber)
                .build();

        Tunnel tunnel = client.connect(createTunnel);

        tunnelServerConnection.setClient(client);
        tunnelServerConnection.setTunnel(tunnel);

        logger.info("Tunnel created with URL: " + tunnel.getPublicUrl());
        logger.info("Tunnel local address: " + tunnel.getConfig().getAddr());
        logger.info("Tunnel port number: " + tunnel.getConfig().getAddr().substring(
                tunnel.getConfig().getAddr().lastIndexOf(":") + 1));

        return tunnelServerConnection;
    }
    public TunnelServerConnection createWebhooksListener(TunnelServerConnection tunnelServerConnection){
        SpringApplication app = new SpringApplication(ServerFactory.class);
        int portNumber = Integer.parseInt(tunnelServerConnection.getTunnel().getConfig().getAddr()
                .substring(tunnelServerConnection.getTunnel().getConfig().getAddr()
                        .lastIndexOf(":") + 1));
        app.setDefaultProperties(Collections.singletonMap("server.port", portNumber));
        tunnelServerConnection.setServer(app);
        tunnelServerConnection.setContext(tunnelServerConnection.getServer().run());
        logger.info("Server started on port " + portNumber);
        return tunnelServerConnection;
    }

    @RequestMapping(value = "/", method = {RequestMethod.GET, RequestMethod.POST})
    public ResponseEntity<String> handleWebhooks(@RequestBody(required = false) String requestBody){
        // Log the request
        logger.info("Received request");
        if(requestBody != null) {
            logger.info("Request body: " + requestBody);
            webhookQueue.offer(requestBody);
        }
        // Send a response
        String response = "<html><body><h1>Webhook received</h1></body></html>";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_HTML);
        return new ResponseEntity<>(response, headers, HttpStatus.OK);
    }

    public void stopWebhookListener(TunnelServerConnection tunnelServerConnection){
        logger.info("Stopping server ....");
        ConfigurableApplicationContext context =
                tunnelServerConnection.getContext();
        context.close();
        logger.info("server stopped");
    }

    public void tearDownNgrokTunnel(TunnelServerConnection tunnelServerConnection){
        logger.info("Stopping Ngrok Client ...");
        tunnelServerConnection.getClient().disconnect(tunnelServerConnection.getTunnel().getPublicUrl());
        tunnelServerConnection.getClient().kill();
        logger.info("Ngrok client stopped.");
    }

    public List<String> getWebhooks() {
        return new ArrayList<>(webhookQueue);
    }
}
