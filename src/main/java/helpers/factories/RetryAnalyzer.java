package helpers.factories;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.IRetryAnalyzer;
import org.testng.ITestResult;

public class RetryAnalyzer implements IRetryAnalyzer {
    private int retryCount = 0;
    private static final int maxRetryCount = 2;
    Logger logger = LoggerFactory.getLogger(RetryAnalyzer.class);

    @Override
    public boolean retry(ITestResult result) {
        if (retryCount < maxRetryCount) {
            retryCount++;
            logger.info("Retrying the test named \"{}\" and the current retry index is {}"
                    , result.getTestName(), retryCount);
            return true;
        }
        logger.info("No need to retry the test named \"{}\".", result.getTestName());
        return false;
    }
}
