package helpers.factories.dataFactories.customerAppDataFactories;

import helpers.BaseHelper;
import helpers.apiClients.mobileApiClients.MobileWarehousesApiClient;
import models.*;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class WarehousesDataFactory extends BaseHelper {
    public WarehousesDataFactory(Configs configs) {
        this.configs = configs;
        mobileWarehousesApiClient = new MobileWarehousesApiClient(this.configs);
    }

    private final Configs configs;
    private final Logger logger = LoggerFactory.getLogger(WarehousesDataFactory.class);
    MobileWarehousesApiClient mobileWarehousesApiClient;

    public Warehouse buildTestWarehouseForCustomerAppTestSession() {
        Warehouse targetWarehouse = mobileWarehousesApiClient
                .getWarehouseByLocation(String.valueOf(configs.getTestLatitude())
                        , String.valueOf(configs.getTestLongitude()));
        targetWarehouse.setArea(getAreaByCertainCriteria(
                mobileWarehousesApiClient.getAreas(),
                "name", targetWarehouse.getAreaName()));
        return targetWarehouse;
    }

    public Area getAreaByCertainCriteria(List<Area> areasList, String criteriaName, String criteriaValue) {
        logger.info("Searching the list areas with count of " + areasList.size()
                + " to find an area with the criteriaName \"" + criteriaName + "\" and criteriaValue \""
                + criteriaValue + "\"");
        boolean isAreaFound;
        for (Area al : areasList) {
            isAreaFound = false;
            switch (criteriaName.toLowerCase()) {
                case "name", "enname" -> {
                    if (al.getDefaultName().equalsIgnoreCase(criteriaValue) || al.getEnName().equalsIgnoreCase(criteriaValue))
                        isAreaFound = true;
                    break;
                }
                case "arName" -> {
                    if (al.getArName().equalsIgnoreCase(criteriaValue))
                        isAreaFound = true;
                    break;
                }
                case "id" -> {
                    if (al.getId().equalsIgnoreCase(criteriaValue))
                        isAreaFound = true;
                    break;
                }
                default -> {
                    logger.error("Invalid Area search criteriaName. Searching for \"" + criteriaName +
                            "\" and the available values are (arName, enName, id, defaultName)");
                }
            }
            if (isAreaFound) {
                logger.info("Found area with ID: " + al.getId() + " and name \"" + al.getEnName() + "\"");
                return al;
            }
        }
        logger.error("Didn't find any area with \"" + criteriaName + "\" matching \"" + criteriaValue + "\"");
        return null;
    }

    public Warehouse pointWarehouseObjectToShopsFp(Warehouse warehouse){
        if (warehouse.getSupportedFps() != null && !warehouse.getSupportedFps().isEmpty()) {
            for (JSONObject fp : warehouse.getSupportedFps()){
                if (fp.optString("type").equals("shop")) {
                    warehouse.setId(fp.optString("id"));
                    warehouse.setName(fp.optString("name"));
                    warehouse.setType(fp.optString("type"));
                    warehouse.setShiftId(fp.optString("shiftId"));
                    warehouse.setStatus(fp.optString("status"));
                    break;
                }
            }
        }
        return warehouse;
    }
}
