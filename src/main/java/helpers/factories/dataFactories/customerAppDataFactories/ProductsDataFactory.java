package helpers.factories.dataFactories.customerAppDataFactories;

import helpers.BaseHelper;
import models.Configs;
import models.OptionSets;
import models.Options;
import models.Product;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class ProductsDataFactory extends BaseHelper {
    public ProductsDataFactory(Configs configs) {
        this.configs = configs;
    }

    private final Configs configs;
    private final Logger logger = LoggerFactory.getLogger(ProductsDataFactory.class);

    public List<Product> identifyNowOnlyProducts(List<Product> nowProductsInclusive,
                                                 List<Product> laterProductsInclusive) {
        List<Product> nowOnlyProducts = new ArrayList<>();
        for (Product np : nowProductsInclusive) {
            boolean productExists = false;
            for (Product lp : laterProductsInclusive) {
                if (productExists)
                    break;
                if (np.getMysqlId() == lp.getMysqlId()) {
                    productExists = true;
                }
            }
            if (!productExists)
                nowOnlyProducts.add(np);
        }
        return nowOnlyProducts;
    }

    public List<Product> identifyLaterOnlyProducts(List<Product> laterProductsInclusive,
                                                   List<Product> nowProductsInclusive) {
        List<Product> nowOnlyProducts = new ArrayList<>();
        for (Product lp : laterProductsInclusive) {
            boolean productExists = false;
            for (Product np : nowProductsInclusive) {
                if (productExists)
                    break;
                if (lp.getMysqlId() == np.getMysqlId()) {
                    productExists = true;
                }
            }
            if (!productExists)
                nowOnlyProducts.add(lp);
        }
        return nowOnlyProducts;
    }

    public List<Product> identifyProductsWithPositiveStock(List<Product> productsPool) {
        List<Product> productsWithPositiveStock = new ArrayList<>();
        for (Product p : productsPool) {
            if (p.getStock() > (double) 1 || p.getNowStock() > (double) 1) {
                productsWithPositiveStock.add(p);
            }
        }
        return productsWithPositiveStock;
    }

    public List<Product> identifyBundleOnlyProducts(List<Product> bundleProducts) {
        List<Product> bundleOnlyProducts = new ArrayList<>();
        for (Product bp : bundleProducts) {
            if (bp.getProductType() != null && bp.getProductType().equals("bundle")) {
                bundleOnlyProducts.add(bp);
            }
        }
        return bundleOnlyProducts;
    }

    public List<Product> identifyCustomizableOnlyProducts(List<Product> customizableProducts) {
        List<Product> customizableOnlyProducts = new ArrayList<>();

        for (Product cp : customizableProducts) {
            if (cp.isCustomizable() && cp.getOptionSetsCount() == 1) {
                customizableOnlyProducts.add(cp);

                // Log full product detail for debugging
                logger.info("Selected Product: ID={}, Name={}, Stock={}, OptionSetsCount={}",
                        cp.getMysqlId(), cp.getName(), cp.getStock(), cp.getOptionSetsCount());

                // Log full details of the first OptionSet
                if (cp.getOptionSet() != null && !cp.getOptionSet().isEmpty()) {
                    OptionSets os = cp.getOptionSet().get(0);
                    logger.info("OptionSet -> ID={}, TypeID={}", os.getOptionSetId(), os.getOptionSetTypeId());
                }

                if (cp.getOption() != null && !cp.getOption().isEmpty()) {
                    for (Options option : cp.getOption()) {
                        logger.info("Option -> ID={}, Name={}, Price={}, Type={}, Stock={}",
                                option.getId(), option.getName(), option.getPrice(),
                                option.getType(), option.getStock());
                    }
                }
            } else {
                // Log skipped customizable products
                if (cp.isCustomizable()) {
                    logger.info("Skipped Product (more than one OptionSet): ID={}, Name={}, OptionSetsCount={}",
                            cp.getMysqlId(), cp.getName(), cp.getOptionSetsCount());
                }
            }
        }

        return customizableOnlyProducts;
    }

    public List<Product> identifyCustomizableAndNonCustomizableProducts(List<Product> customizableProducts,
                                                         List<Product> singleProducts) {
        List<Product> customizableAndNonCustomizableProducts = new ArrayList<>();

        List<Product> customizableOnlyProducts = identifyCustomizableOnlyProducts(customizableProducts);
        List<Product> singleOnlyProducts = identifySingleOnlyProducts(singleProducts);

        Iterator<Product> customizableIterator = customizableOnlyProducts.iterator();
        Iterator<Product> singleIterator = singleOnlyProducts.iterator();

        // Alternate between customizable and non customizable products list
        while (customizableIterator.hasNext() || singleIterator.hasNext()) {
            if (customizableIterator.hasNext()) {
                customizableAndNonCustomizableProducts.add(customizableIterator.next());
            }
            if (singleIterator.hasNext()) {
                customizableAndNonCustomizableProducts.add(singleIterator.next());
            }
        }
        return customizableAndNonCustomizableProducts;
    }

    public List<Product> identifySingleOnlyProducts(List<Product> singleProducts) {
        List<Product> singleOnlyProducts = new ArrayList<>();
        for (Product bp : singleProducts) {
            if (bp.getProductType() != null && bp.getProductType().equals("single")) {
                singleOnlyProducts.add(bp);
            }
        }
        return singleOnlyProducts;
    }

    public List<Product> identifySingleAndBundleProducts(List<Product> bundleProducts,
                                                         List<Product> singleProducts) {
        List<Product> singleAndBundleOnlyProducts = new ArrayList<>();

        List<Product> bundleOnlyProducts = identifyBundleOnlyProducts(bundleProducts);
        List<Product> singleOnlyProducts = identifySingleOnlyProducts(singleProducts);

        Iterator<Product> bundleIterator = bundleOnlyProducts.iterator();
        Iterator<Product> singleIterator = singleOnlyProducts.iterator();

        // Alternate between single and bundle products list
        while (bundleIterator.hasNext() || singleIterator.hasNext()) {
            if (bundleIterator.hasNext()) {
                singleAndBundleOnlyProducts.add(bundleIterator.next());
            }
            if (singleIterator.hasNext()) {
                singleAndBundleOnlyProducts.add(singleIterator.next());
            }
        }
        return singleAndBundleOnlyProducts;
    }

    public List<Product> identifyProductsWithoutDiscount(List<Product> products , String fpId){
        List<Product> productsWithoutDiscount = new ArrayList<>();

        for(Product p : products){
            if (p.getSalePrice() == 0
                    && p.getExtraSalesPrice().isEmpty()
                    && (p.getAutomatedDiscounts() == null || p.getAutomatedDiscounts().isEmpty()
                    || p.getAutomatedDiscounts().stream().noneMatch(d -> d.getFpId().equals(fpId)))){
                productsWithoutDiscount.add(p);
            }
        }
        return productsWithoutDiscount;
    }

    public List<Product> identifyProductsWithSalePrice(List<Product> products,String fpId){
        List<Product> productsWithSalePrice = new ArrayList<>();

        for(Product p: products) {
            if (p.getSalePrice() != 0
                    && p.getExtraSalesPrice().isEmpty()
                    && (p.getAutomatedDiscounts() == null || p.getAutomatedDiscounts().isEmpty()
                    || p.getAutomatedDiscounts().stream().noneMatch(d -> d.getFpId().equals(fpId)))) {
                productsWithSalePrice.add(p);
            }
        }
        return productsWithSalePrice;
    }

    public List<Product> identifyProductsWithExtraSalePrice (List<Product> products,String fpId){
        List<Product> productsWithExtraSalePrice = new ArrayList<>();

        for(Product p: products){
            if(!p.getExtraSalesPrice().isEmpty()
                    && (p.getAutomatedDiscounts() == null || p.getAutomatedDiscounts().isEmpty()
                    || p.getAutomatedDiscounts().stream().noneMatch(d -> d.getFpId().equals(fpId)))){
                productsWithExtraSalePrice.add(p);
            }
        }
        return productsWithExtraSalePrice;
    }
}
