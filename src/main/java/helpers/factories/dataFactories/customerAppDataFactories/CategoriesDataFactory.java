package helpers.factories.dataFactories.customerAppDataFactories;

import helpers.BaseHelper;
import helpers.apiClients.mobileApiClients.MobileWarehousesApiClient;
import models.Category;
import models.Configs;
import models.Warehouse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class CategoriesDataFactory extends BaseHelper {
    public CategoriesDataFactory(Configs configs) {
        this.configs = configs;
        mobileWarehousesApiClient = new MobileWarehousesApiClient(this.configs);
    }

    private final Configs configs;
    private final MobileWarehousesApiClient mobileWarehousesApiClient;
    private final Logger logger = LoggerFactory.getLogger(CategoriesDataFactory.class);

    public List<Category> buildCategoriesListInWarehouse(Warehouse warehouse, String nowTomorrowType) {
        switch (nowTomorrowType.toLowerCase()){
            case "later", "tomorrow" -> {
                return mobileWarehousesApiClient.getCategoriesByWarehouse(warehouse.getId(), "later");
            }
            default -> {
                return mobileWarehousesApiClient.getCategoriesByWarehouse(warehouse.getId(), "now");
            }
        }
    }

    public List<Category> buildSharedFacilityCategoriesList(List<Category> categoriesPool,
                                                            List<Integer> sharedFacilityCategoryIds) {
        logger.info("Building a list of shared facility categories from the categories pool with count of {}",
                categoriesPool.size());
        List<Category> sharedFacilityCategories = sharedFacilityCategoryIds.stream()
                .map(id -> getCategoryByCertainCriteria(categoriesPool, "id", String.valueOf(id)))
                .filter(java.util.Objects::nonNull)
                .toList();
        logger.info("A list of shared facility categories built with count of {}", sharedFacilityCategories.size());
        return sharedFacilityCategories;
    }

    public Category getCategoryByCertainCriteria(List<Category> categoriesPool, String criteriaName, String criteriaValue) {
        logger.info("Searching the list of categories with count of {} to find a category with the criteriaName \"{}\" and criteriaValue \"{}\"",
                categoriesPool.size(), criteriaName, criteriaValue);

        Category result = null;

        switch (criteriaName.toLowerCase()) {
            case "name", "enname" -> {
                result = categoriesPool.stream()
                        .filter(category -> category.getName().equalsIgnoreCase(criteriaValue))
                        .findFirst()
                        .orElse(null);
            }
            case "id" -> {
                result = categoriesPool.stream()
                        .filter(category -> String.valueOf(category.getId()).equalsIgnoreCase(criteriaValue))
                        .findFirst()
                        .orElse(null);
            }
            default -> {
                logger.error("Invalid Category search criteriaName. Searching for \"{}\" and the available values are: name, id", criteriaName);
                return null;
            }
        }

        if (result != null) {
            logger.info("A category found matching criteria \"{}\" with value \"{}\". Category ID: {}",
                    criteriaName, criteriaValue, result.getId());
        } else {
            logger.error("No categories found matching criteria \"{}\" with value \"{}\". Available criteria names are: name, id",
                    criteriaName, criteriaValue);
        }
        return result;
    }
}
