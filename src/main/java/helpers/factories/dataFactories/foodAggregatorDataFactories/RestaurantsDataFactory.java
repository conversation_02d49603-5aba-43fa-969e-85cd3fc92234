package helpers.factories.dataFactories.foodAggregatorDataFactories;

import helpers.factories.dataFactories.OtpFactory;
import models.Configs;
import models.Restaurant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class RestaurantsDataFactory {

    public RestaurantsDataFactory(Configs configs) {
        this.configs = configs;
    }
    private final Configs configs;
    private final Logger logger = LoggerFactory.getLogger(RestaurantsDataFactory.class);

    public boolean compareTwoListsOfRestaurants(List<Restaurant> yeloRestaurantsList, List<Restaurant> aggregatorRestaurantsList) {
        logger.info("Comparing the two lists of restaurants..");

        // Handle null lists
        if (yeloRestaurantsList == null && aggregatorRestaurantsList == null) {
            logger.warn("Both Lists are null");
            return true;
        }
        if (yeloRestaurantsList == null || aggregatorRestaurantsList == null) {
            logger.warn(yeloRestaurantsList == null ? "Yelo Restaurants List is null" : "Aggregator Restaurants List is null");
            return false;
        }

        // Check if lists have same size
        if (yeloRestaurantsList.size() != aggregatorRestaurantsList.size()) {
            logger.warn("The two lists do not have the same size, yelo restaurant size is: " + yeloRestaurantsList.size()
                    + " while aggregator list size is: " + aggregatorRestaurantsList.size());
            return false;
        }

        Map<Integer, Restaurant> yeloMap = yeloRestaurantsList.stream()
                .collect(Collectors.toMap(Restaurant::getYeloId, r -> r));

        Map<Integer, Restaurant> aggregatorMap = aggregatorRestaurantsList.stream()
                .collect(Collectors.toMap(Restaurant::getYeloId, r -> r));

        // Check if both lists have the same restaurant IDs
        if (!yeloMap.keySet().equals(aggregatorMap.keySet())) {
            Set<Integer> onlyInYelo = new HashSet<>(yeloMap.keySet());
            onlyInYelo.removeAll(aggregatorMap.keySet());
            Set<Integer> onlyInAggregator = new HashSet<>(aggregatorMap.keySet());
            onlyInAggregator.removeAll(yeloMap.keySet());

            logger.warn("ID mismatch - Only in Yelo: " + onlyInYelo + ", Only in Aggregator: " + onlyInAggregator);
            return false;
        }

        // Compare each restaurant by matching IDs
        boolean allRestaurantsMatch = true;

        for (Integer id : yeloMap.keySet()) {
            Restaurant yelo = yeloMap.get(id);
            Restaurant aggregator = aggregatorMap.get(id);

            if (!compareRestaurants(yelo, aggregator)) {
                allRestaurantsMatch = false;
            }
        }

        if (allRestaurantsMatch) {
            logger.info("All restaurants match successfully");
            return true;
        } else {
            logger.warn("Some restaurants have differences - comparison failed");
            return false;
        }
    }

    public boolean compareRestaurants(Restaurant yeloRestaurant, Restaurant aggregatorRestaurant) {
        List<String> differences = new ArrayList<>();

        // Compare all fields and collect differences
        if (!Objects.equals(yeloRestaurant.getName(), aggregatorRestaurant.getName())) {
            differences.add("Name: yelo→'" + yeloRestaurant.getName() + "' aggregator→'" + aggregatorRestaurant.getName() + "'");
        }
        if (!Objects.equals(yeloRestaurant.getBusinessCategories(), aggregatorRestaurant.getBusinessCategories())) {
            differences.add("Business Categories: yelo→'" + yeloRestaurant.getBusinessCategories() + "' aggregator→'" + aggregatorRestaurant.getBusinessCategories() + "'");
        }
        if (!Objects.equals(yeloRestaurant.getLogo(), aggregatorRestaurant.getLogo())) {
            differences.add("Logo: yelo→'" + yeloRestaurant.getLogo() + "' aggregator→'" + aggregatorRestaurant.getLogo() + "'");
        }
        if (!Objects.equals(yeloRestaurant.getCover(), aggregatorRestaurant.getCover())) {
            differences.add("Cover: yelo→'" + yeloRestaurant.getCover() + "' aggregator→'" + aggregatorRestaurant.getCover() + "'");
        }
        if (!Objects.equals(yeloRestaurant.getOperatingStatus(), aggregatorRestaurant.getOperatingStatus())) {
            differences.add("Operating Status: yelo→'" + yeloRestaurant.getOperatingStatus() + "' aggregator→'" + aggregatorRestaurant.getOperatingStatus() + "'");
        }
        if (!Objects.equals(yeloRestaurant.getDeliveryFee(), aggregatorRestaurant.getDeliveryFee())) {
            differences.add("Delivery Fee: yelo→'" + yeloRestaurant.getDeliveryFee() + "' aggregator→'" + aggregatorRestaurant.getDeliveryFee() + "'");
        }
        if (!Objects.equals(yeloRestaurant.getDeliveryTime(), aggregatorRestaurant.getDeliveryTime())) {
            differences.add("Delivery Time: yelo→'" + yeloRestaurant.getDeliveryTime() + "' aggregator→'" + aggregatorRestaurant.getDeliveryTime() + "'");
        }
        if (!Objects.equals(yeloRestaurant.getRating(), aggregatorRestaurant.getRating())) {
            differences.add("Rating: yelo→'" + yeloRestaurant.getRating() + "' aggregator→'" + aggregatorRestaurant.getRating() + "'");
        }
        if (!Objects.equals(yeloRestaurant.getReviewCount(), aggregatorRestaurant.getReviewCount())) {
            differences.add("Review Count: yelo→'" + yeloRestaurant.getReviewCount() + "' aggregator→'" + aggregatorRestaurant.getReviewCount() + "'");
        }

        // Log all differences together
        if (!differences.isEmpty()) {
            logger.warn("Restaurant ID " + yeloRestaurant.getYeloId() + " has differences: " + differences);
            return false;
        }

        return true;
    }

    public List<Integer> extractAllRestaurantsId(List<Restaurant> restaurants) {
        logger.info("Extracting restaurant IDs from a list of {} restaurants", restaurants.size());

        List<Integer> restaurantIds = new ArrayList<>();

        for (Restaurant restaurant : restaurants) {
            if (restaurant.getYeloId() > 0) {
                restaurantIds.add(restaurant.getYeloId());
            } else {
                logger.warn("Found restaurant with invalid Yelo ID: {} - Name: '{}'",
                        restaurant.getYeloId(), restaurant.getName());
            }
        }

        logger.info("Successfully extracted {} restaurant IDs from {} restaurants",
                restaurantIds.size(), restaurants.size());

        return restaurantIds;
    }

    public List<Integer> extractBusyRestaurantIds(List<Restaurant> restaurants){
        return restaurants.stream()
                .filter(restaurant -> restaurant.getOperatingStatus() != null &&
                        "BUSY".equals(restaurant.getOperatingStatus()))
                .map(Restaurant::getYeloId)
                .collect(Collectors.toList());
    }

    public List<Integer> extractClosedRestaurantsIds(List<Restaurant> restaurants){
        return restaurants.stream()
                .filter(restaurant -> restaurant.getOperatingStatus() != null &&
                        "CLOSED".equals(restaurant.getOperatingStatus()))
                .map(Restaurant::getYeloId)
                .collect(Collectors.toList());
    }

}
