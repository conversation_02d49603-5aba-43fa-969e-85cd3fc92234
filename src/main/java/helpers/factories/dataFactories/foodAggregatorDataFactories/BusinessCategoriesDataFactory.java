package helpers.factories.dataFactories.foodAggregatorDataFactories;

import models.BusinessCategory;
import models.Configs;
import models.Restaurant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class BusinessCategoriesDataFactory {

    public BusinessCategoriesDataFactory(Configs configs) {
        this.configs = configs;
    }

    private final Configs configs;
    private final Logger logger = LoggerFactory.getLogger(BusinessCategoriesDataFactory.class);

    public List<String> extractUniqueBusinessCategoriesOfRestaurantList(List<Restaurant> restaurants) {
        logger.info("Extracting Business Categories from a list of {} restaurants", restaurants.size());

        Set<String> businessCategoriesSet = new HashSet<>();

        for (Restaurant restaurant : restaurants) {
            if (restaurant.getYeloId() > 0) {
                businessCategoriesSet.addAll(restaurant.getBusinessCategories());
            } else {
                logger.warn("Found restaurant with invalid Yelo ID: {} - Name: '{}'",
                        restaurant.getYeloId(), restaurant.getName());
            }
        }

        List<String> businessCategories = new ArrayList<>(businessCategoriesSet);

        logger.info("Successfully extracted {} unique business categories from {} restaurants",
                businessCategories.size(), restaurants.size());

        return businessCategories;
    }

    public static Map<String, Integer> mapCategoryNamesToIds(List<String> categoryNames, List<Integer> categoryIds) {
        Map<String, Integer> mapping = new HashMap<>();

        // Use the minimum size of both lists
        int minSize = Math.min(categoryNames.size(), categoryIds.size());

        for (int i = 0; i < minSize; i++) {
            String name = categoryNames.get(i);
            Integer id = categoryIds.get(i);

            if (name != null && id != null) {
                mapping.put(name.trim(), id);
            }
        }

        return mapping;
    }

    public List<Integer> extractAllBusinessCategoryIds(List<BusinessCategory> businessCategories) {
        logger.info("Extracting business category IDs from a list of {} business categories", businessCategories.size());

        List<Integer> categoryIds = new ArrayList<>();

        for (BusinessCategory category : businessCategories) {
            if (category.getYeloId() > 0) {
                categoryIds.add(category.getYeloId());
            } else {
                logger.warn("Found business category with invalid Yelo ID: {} - Name: '{}'",
                        category.getYeloId(), category.getName());
            }
        }

        logger.info("Successfully extracted {} business category IDs from {} business categories",
                categoryIds.size(), businessCategories.size());

        return categoryIds;
    }

}
