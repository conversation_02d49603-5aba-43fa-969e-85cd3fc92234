package helpers.factories.dataFactories;

import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import helpers.BaseHelper;
import models.Address;
import models.Configs;
import models.TestData;
import models.User;
import net.datafaker.Faker;
import net.datafaker.providers.base.Country;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

public class UserDataFactory extends BaseHelper {
    public UserDataFactory(Configs configs) {
        this.configs = configs;
    }

    private final Configs configs;
    PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
    private final Logger logger = LoggerFactory.getLogger(UserDataFactory.class);
    Faker faker = new Faker();

    public User generateRandomUserDetails(User user){
        user.setFirstName(faker.name().firstName().replaceAll("[^a-zA-Z]", ""));
        user.setLastName(faker.name().lastName().replaceAll("[^a-zA-Z]", ""));
        user.setFullName(user.getFirstName() + " "
                + user.getLastName());
        return user;
    }

    public User generateRandomUserMobileNumber(User user){
        user.setPhoneNumber(generateRandomPhoneNumber(configs.getTestCountryCode(),
                configs.getTestMobileNumber(),
                configs.getTestMobileCompany()
        ));
        user.setLocalPhoneNumber(user.getPhoneNumber().replace(configs.getTestMobileNumber(), ""));
        user.setPhoneCountry(configs.getTestCountryCode());
        return user;
    }

    public String generateRandomPhoneNumber(String countryCode, String phoneCode, String mobileCompany){
        String mobileNumber = phoneCode;
        switch (countryCode.toUpperCase()) {
            case "EG" -> {
                mobileNumber = switch (configs.getTestMobileCompany()) {
                    case "Etisalat" -> mobileNumber.concat("11");
                    case "Vodafone" -> mobileNumber.concat("10");
                    case "Orange" -> mobileNumber.concat("12");
                    case "WE" -> mobileNumber.concat("15");
                    default -> mobileNumber.concat(String.valueOf(new Random().nextInt(3)));
                };
                mobileNumber = mobileNumber
                        .concat(String.valueOf(new Random().nextInt(90000000) + 10000000));
            }
            case "KSA" -> {
                mobileNumber = switch (mobileCompany) {
                    case "STC" -> mobileNumber.concat("0");
                    case "Mobily" -> mobileNumber.concat("3");
                    case "Zain" -> mobileNumber.concat("1");
                    case "Lebara" -> mobileNumber.concat("6");
                    case "Virgin" -> mobileNumber.concat("9");
                    default -> mobileNumber.concat(new String[]{"0", "3", "1", "6", "9"}
                            [new Random().nextInt(5)]);
                };
                mobileNumber = mobileNumber
                        .concat(String.valueOf(new Random().nextInt(9000000) + 1000000));
            }
        }

        return mobileNumber;
    }

    public User generateRandomUserEmailAndPassword(User user){
        user.setEmailAddress(configs.getTestEmail().replace("@", "_"
                + getCurrentTimeStamp("yyyyMMddHHmmss")
                + "_" + new Random().nextInt(1000000)
                + "_" + user.getLocalPhoneNumber() + "@"));
        user.setEmailPassword(configs.getTestPassword());

        return user;
    }

    public User buildForeignCountryData(User user) {
        Country randomCountry = faker.country();
        user.setForeignPhoneCountry(null);
        user.setForeignPhoneCountryCode(null);

        while ((user.getForeignPhoneCountry() == null || user.getForeignPhoneCountry().isEmpty())) {
            user.setForeignPhoneCountryCode(randomCountry.countryCode2().toUpperCase());
            user.setForeignPhoneCountry(getCountryNameByCountryCode(user.getForeignPhoneCountryCode()));
        }

        logger.info("Country name is: \n{}", user.getForeignPhoneCountry());
        logger.info("Generating random foreign phone number for country with code:\n{}"
                , user.getForeignPhoneCountryCode());

        try {
            Phonenumber.PhoneNumber phoneNumber =
                    phoneNumberUtil.getExampleNumberForType(user.getForeignPhoneCountryCode(),
                            PhoneNumberUtil.PhoneNumberType.MOBILE);

            String rawPhoneNumber = phoneNumberUtil.format(phoneNumber, PhoneNumberUtil.PhoneNumberFormat.E164);
            String modifiedPhoneNumber = replaceLongestConsecutiveWithRandomDigits(rawPhoneNumber);
            phoneNumber = phoneNumberUtil.parse(modifiedPhoneNumber, String.valueOf(phoneNumber.getCountryCode()));

            String mobileNumber = phoneNumberUtil.format(phoneNumber, PhoneNumberUtil.PhoneNumberFormat.E164);
            String localMobileNumber = phoneNumberUtil.getNationalSignificantNumber(phoneNumber);
            logger.info("\nGenerated random phone number for country code:\n{}", user.getForeignPhoneCountryCode());
            logger.info("\nFull Mobile Number is:\n{}", mobileNumber);
            logger.info("\nLocal Mobile Number is:\n{}", localMobileNumber);

            user.setForeignPhoneNumber(mobileNumber);
            user.setForeignLocalPhoneNumber(localMobileNumber);
        } catch (Exception e) {
            logger.error("Generating valid foreign number failed for country with code:\n{}"
                    , user.getForeignPhoneCountryCode(), e);
            user.setForeignPhoneNumber(String.valueOf(new Random().nextInt(90000000) + 10000000));
        }
        return user;
    }

    public String fetchLongestConsecutiveSequence(String rawPhoneNumber) {
        if (rawPhoneNumber == null || rawPhoneNumber.isEmpty()) {
            return "";
        }

        int longestStart = 0; // Starting index of the longest consecutive sequence
        int longestLength = 0; // Length of the longest consecutive sequence

        int currentStart = 0; // Starting index of the current consecutive sequence
        int currentLength = 0; // Length of the current consecutive sequence

        for (int i = 1; i < rawPhoneNumber.length(); i++) {
            char currentDigit = rawPhoneNumber.charAt(i);
            char previousDigit = rawPhoneNumber.charAt(i - 1);

            if (Character.isDigit(currentDigit) && Character.isDigit(previousDigit)
                    && currentDigit == previousDigit + 1) {
                // Continue the current consecutive sequence
                if (currentLength == 0) {
                    // If this is the start of a new consecutive sequence, update the current start index
                    currentStart = i - 1;
                }
                currentLength++;
                if (currentLength > longestLength) {
                    // Update the longest consecutive sequence found so far
                    longestStart = currentStart;
                    longestLength = currentLength;
                }
            } else {
                // Start a new consecutive sequence
                currentLength = 0;
            }
        }

        if (longestLength > 0) {
            // Extract and return the longest consecutive sequence
            return rawPhoneNumber.substring(longestStart, longestStart + longestLength + 1);
        } else {
            // No consecutive sequence found, return an empty string
            return "";
        }
    }

    public String replaceLongestConsecutiveWithRandomDigits(String rawPhoneNumber) {
        // Check if the original phone number had a leading '+'
        boolean hasLeadingPlus = rawPhoneNumber != null && rawPhoneNumber.startsWith("+");

        String sequenceToReplace = fetchLongestConsecutiveSequence(rawPhoneNumber);
        int digits = sequenceToReplace.length();

        if (sequenceToReplace.isEmpty() || digits == 0) {
            // No consecutive sequence found, or the sequence has no digits
            return rawPhoneNumber;
        }

        // Generate a random number with the same number of digits as the sequence
        StringBuilder randomReplacement = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < digits; i++) {
            randomReplacement.append(random.nextInt(10));
        }

        // Replace the sequence with the random number
        String newNumber = Objects.requireNonNull(rawPhoneNumber).replace(sequenceToReplace, randomReplacement.toString());

        // Add the '+' sign back to the new phone number, if it was present in the original number
        if (hasLeadingPlus) {
            newNumber = "+" + newNumber;
        }

        return newNumber;
    }

    public String getCountryNameByCountryCode(String countryCode) {
        logger.info("Trying to fetch the country name for country code {}", countryCode);
        String apiUrl = "https://restcountries.com/v3.1/alpha/" + countryCode;

        // Configure RestTemplate with timeout
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(Duration.ofSeconds(10)); // 5 seconds connection timeout
        requestFactory.setReadTimeout(Duration.ofSeconds(10));    // 5 seconds read timeout

        RestTemplate restTemplate = new RestTemplate(requestFactory);
        int maxRetries = 10; // Maximum number of retries
        int retryCount = 0; // Current retry count
        int retryDelay = 2; // Delay between retries in milliseconds

        while (retryCount < maxRetries) {
            logger.info("getCountryNameByCountryCode request number: #{} and country {}", retryCount + 1, countryCode);
            try {
                String response = restTemplate.getForObject(apiUrl, String.class);

                // Get the common name of the country retrieved using that country code
                if (response != null){
                    JSONArray jsonArray = new JSONArray(response);
                    if (!jsonArray.isEmpty()) {
                        JSONObject countryInfo = jsonArray.getJSONObject(0);
                        JSONObject nameObject = countryInfo.getJSONObject("name");
                        return nameObject.optString("common", null);
                    }
                }
            } catch (Exception e) {
                logger.error("Fetching the country name using the country code failed with exception", e);
                retryCount++;
                if (retryCount < maxRetries) {
                    try {
                        Thread.sleep(Duration.ofSeconds(retryDelay)); // Wait before retrying
                    } catch (Exception ee) {
                        logger.error("Trying to wait before retrying failed with exception", ee);
                    }
                }
            }
        }

        return null;
    }

    public User generateRandomEgyptianNationalID(User user) {
        // 1st digit: Century of birth
        int century = (ThreadLocalRandom.current().nextInt(0, 2) == 0) ? 2 : 3; // 2 for 1900s, 3 for 2000s

        // Random birthdate
        LocalDate startDate = (century == 2) ? LocalDate.of(1900, 1, 1) : LocalDate.of(2000, 1, 1);
        LocalDate endDate = LocalDate.now().minusYears(18); // At least 18 years old
        long daysBetween = endDate.toEpochDay() - startDate.toEpochDay();
        LocalDate birthDate = startDate.plusDays(ThreadLocalRandom.current().nextLong(0, daysBetween + 1));

        // Birthdate in YYMMDD format
        String birthDateFormatted = String.format("%02d%02d%02d",
                birthDate.getYear() % 100,
                birthDate.getMonthValue(),
                birthDate.getDayOfMonth());

        // 8th–9th digits: Random governorate code (valid range 01–29)
        int governorateCode = ThreadLocalRandom.current().nextInt(1, 30);

        // 10th–13th digits: Serial number
        int serialNumber = ThreadLocalRandom.current().nextInt(1000, 10000);

        // Combine all parts (without the check digit)
        String withoutCheckDigit = century + birthDateFormatted +
                String.format("%02d", governorateCode) +
                String.format("%04d", serialNumber);

        // 14th digit: Check digit (Luhn algorithm)
        int checkDigit = calculateLuhnCheckDigit(withoutCheckDigit);

        // Return the user with complete ID
        user.setNationalId(withoutCheckDigit + checkDigit);
        return user;
    }

    public static String extractDateOfBirthFromNationalID(String nationalID) {
        if (nationalID == null || nationalID.length() != 14) {
            throw new IllegalArgumentException("Invalid National ID");
        }

        char centuryDigit = nationalID.charAt(0);
        String yearPart = nationalID.substring(1, 3);
        String monthPart = nationalID.substring(3, 5);
        String dayPart = nationalID.substring(5, 7);

        int year;
        if (centuryDigit == '2') {
            year = 1900 + Integer.parseInt(yearPart);
        } else if (centuryDigit == '3') {
            year = 2000 + Integer.parseInt(yearPart);
        } else {
            throw new IllegalArgumentException("Invalid century digit in National ID");
        }

        LocalDate birthDate = LocalDate.of(year, Integer.parseInt(monthPart), Integer.parseInt(dayPart));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
        return birthDate.format(formatter);
    }

    private int calculateLuhnCheckDigit(String number) {
        int sum = 0;
        boolean alternate = false;

        for (int i = number.length() - 1; i >= 0; i--) {
            int n = Character.getNumericValue(number.charAt(i));
            if (alternate) {
                n *= 2;
                if (n > 9) n -= 9;
            }
            sum += n;
            alternate = !alternate;
        }

        return (10 - (sum % 10)) % 10;
    }

    public User buildRandomAddressDetails(User user) {
        user.setAddress(new Address());
        user.getAddress().setAddressLabel("Address"
                + getCurrentTimeStamp("yyyyMMddHHmmss"));
        user.getAddress().setFullAddress(faker.address().fullAddress());
        user.getAddress().setFloorNumber(faker.number().digits(1));
        user.getAddress().setFlatNumber(faker.number().digits(1));
        return user;
    }
}
