package helpers.factories.dataFactories;

import helpers.BaseHelper;
import helpers.apiClients.SlackApiClient;
import helpers.factories.DatabaseConnectionFactory;
import io.restassured.path.json.JsonPath;
import models.Configs;
import models.TestData;
import models.User;
import org.openqa.selenium.WebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class OtpFactory extends BaseHelper {
    public OtpFactory(Configs configs) {
        this.configs = configs;
    }

    private final Configs configs;
    private final Logger logger = LoggerFactory.getLogger(OtpFactory.class);

    public User fetchOtp(TestData testData, String method, User user) {
        SlackApiClient slackApiClient = new SlackApiClient(configs);
        String phoneNumberToUse =
                configs.isUseForeignCountryData() ? user.getForeignPhoneNumber() : user.getPhoneNumber();

        // Fetching OTP from DB if there is a valid connection
        if (testData.getDbConnection() != null){
            int maxRetryCount = 5;
            int indicator = 1;
            do{
                logger.info("Attempting to fetch OTP from DB trial #: {}", indicator);
                if (!method.equalsIgnoreCase("deleteAccount")){
                    user.setOtp(JsonPath.given(new DatabaseConnectionFactory(configs).executeQuery(testData.getDbConnection()
                                    , "select otp from bf_phone_otp_verification where phone = \""
                                            + phoneNumberToUse + "\" order by id desc limit 1"))
                            .getString("[0].otp"));
                } else {
                    user.setOtp(JsonPath.given(new DatabaseConnectionFactory(configs).executeQuery(testData.getDbConnection()
                                    , "select meta_value from bf_usermeta where user_id " +
                                            "= (select id from bf_breadfast_customers where phone = \""
                                            + phoneNumberToUse + "\") " +
                                            "and meta_key = \"user_confirmation_code\" limit 1"))
                            .getString("[0].meta_value"));
                }

                logger.info("OTP value fetched from DB is: {}", user.getOtp());

                if (user.getOtp() == null || user.getOtp().isBlank()){
                    logger.info("Query returned an empty string. Retrying in 2 seconds...");
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (Exception e){
                        logger.error("Trying to sleep for 2 seconds crashed with exception,", e);
                    }
                }

                indicator++;
            } while ((Objects.isNull(user.getOtp()) || user.getOtp().isBlank()) && indicator <= maxRetryCount);

            return user;
        }

        // Fetching OTP from Slack API if there is no valid connection
        String originalOtp = user.getOtp();
        if (originalOtp == null) {
            user.setOtp(slackApiClient.findMessageForOTP(phoneNumberToUse,method));
        } else {
            final long startTime = System.currentTimeMillis(); // Store start time
            final long timeout = 360000; // Timeout set for 3 minutes in milliseconds
            String latestOtp = "";

            while (!originalOtp.equalsIgnoreCase(latestOtp)
                    && ((System.currentTimeMillis() - startTime) < timeout)) {
                latestOtp = slackApiClient.findMessageForOTP(phoneNumberToUse,method);
                if (!originalOtp.equalsIgnoreCase(latestOtp)
                        && !latestOtp.isBlank()) {
                    user.setOtp(latestOtp);
                    break;
                } else {
                    latestOtp = "";
                }
                // Wait for a short duration before trying again
                try {
                    Thread.sleep(1000); // Wait for 1 second before trying again
                } catch (Exception e) {
                    // do nothing
                }
            }
        }
        logger.info("OTP value is added to user with phoneNumber {} as: {}", phoneNumberToUse, user.getOtp());
        return user;
    }

    /**
     * Enters the one-time password (OTP) into the provided text field. If the original OTP is null,
     * it retrieves a new OTP and inputs it directly. If an original OTP exists, the method waits for
     * a new OTP different from the original, periodically checking until a timeout is reached.
     *
     * @param textField    The WebElement representing the text field where the OTP should be entered.
     * @param testData     TestData object containing the current test data, including any previously known OTP.
     * @param method       A string specifying the context that this method is used in ("login", "updatePhoneNumber",
     *                     "deleteAccount"), Any other value will get you the first OTP available in the response.
     * @return Returns the updated TestData object with the latest OTP set.
     */
    public User enterOtpInTextField(WebElement textField, TestData testData, String method, User user) {
        user = fetchOtp(testData, method, user);
        //Fetch and Enter the OTP
        if (textField.isDisplayed()) {
            textField.sendKeys(user.getOtp());
        }
        return user;
    }
}
