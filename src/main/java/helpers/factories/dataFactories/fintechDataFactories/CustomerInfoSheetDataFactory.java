package helpers.factories.dataFactories.fintechDataFactories;

import models.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Simple CSV reader
 * Expected columns (order): test_tag,phone_number,national id,first_name,last_name
 * Location: resources/dataSets/bcard-pre-coded-users.csv
 */
public final class CustomerInfoSheetDataFactory {

    private static final Logger logger = LoggerFactory.getLogger(CustomerInfoSheetDataFactory.class);
    private static final String DEFAULT_CSV_PATH = "resources/dataSets/bcard-pre-coded-users.csv";

    /**
     * Reads the given CSV path (file system) and returns a map keyed by test_tag.
     * @param csvPath file system path
     */
    public static Map<String, User> readUsers(String csvPath) {
        Map<String, User> users = new LinkedHashMap<>();
        int lineNum = 0;
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(new FileInputStream(DEFAULT_CSV_PATH), StandardCharsets.UTF_8))) {

            String line;
            boolean header = true;
            while ((line = br.readLine()) != null) {
                lineNum++;
                line = line.trim();
                if (line.isEmpty()) continue;
                if (header) { header = false; continue; }

                String[] parts = line.split(",", -1);
                if (parts.length < 5) {
                    logger.warn("Skipping malformed line {}: {}", lineNum, line);
                    continue;
                }

                String testTag    = parts[0].trim();
                String phone      = parts[1].trim();
                String nationalId = parts[2].trim();
                String firstName  = parts[3].trim();
                String lastName   = parts[4].trim();

                if (testTag.isEmpty()) {
                    logger.warn("Skipping line {} (empty test_tag)", lineNum);
                    continue;
                }

                User user = new User();
                user.setFirstName(firstName);
                user.setLastName(lastName);
                user.setFullName((firstName + " " + lastName).trim());
                user.setLocalPhoneNumber(phone);
                user.setPhoneNumber(phone.startsWith("+") ? phone : "+" + phone);
                user.setNationalId(nationalId);

                if (users.put(testTag, user) != null) {
                    logger.debug("Overwriting duplicate test_tag '{}' at line {}", testTag, lineNum);
                }
            }
            logger.info("Loaded {} users from {}", users.size(), csvPath);
        } catch (Exception e) {
            logger.error("Failed reading CSV {}: {}", csvPath, e.getMessage());
        }
        return users;
    }

    public User updateUserBasedOnSpecificTestTag(User existingUser, String testTag) {
        if (existingUser == null || testTag == null || testTag.isEmpty()) {
            logger.warn("Invalid input for updating user: existingUser={}, testTag={}", existingUser, testTag);
            return existingUser;
        }

        User updatedUser = readUsers(DEFAULT_CSV_PATH).get(testTag);

        String countryCode = "+20";

        if (updatedUser != null) {
            if (updatedUser.getPhoneNumber().toString().startsWith("+966"))
                countryCode = "+966";
            existingUser.setFirstName(updatedUser.getFirstName());
            existingUser.setLastName(updatedUser.getLastName());
            existingUser.setFullName(updatedUser.getFullName());
            existingUser.setPhoneNumber(updatedUser.getPhoneNumber());
            existingUser.setLocalPhoneNumber(updatedUser.getPhoneNumber().substring(countryCode.length()));
            existingUser.setNationalId(updatedUser.getNationalId());
        } else {
            logger.warn("No user found for test_tag '{}'", testTag);
        }
        return existingUser;
    }
}
