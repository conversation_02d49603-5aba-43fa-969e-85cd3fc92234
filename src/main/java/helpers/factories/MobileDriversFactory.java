package helpers.factories;

import helpers.BaseHelper;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.android.options.UiAutomator2Options;
import io.appium.java_client.ios.options.XCUITestOptions;
import io.appium.java_client.ios.options.simulator.Permissions;
import io.appium.java_client.service.local.AppiumDriverLocalService;
import io.appium.java_client.service.local.AppiumServiceBuilder;
import models.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URI;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class MobileDriversFactory extends BaseHelper {
    public MobileDriversFactory(Configs configs) {
        this.configs = configs;
    }

    public enum Platform {
        IOS,
        ANDROID
    }

    public enum RemoteProvider {
        BROWSERSTACK,
        LAMBDATEST
    }

    private String buildName, projectName, remoteAppUrl = "";

    private final String browserStackDomain = "@hub-cloud.browserstack.com/wd/hub";
    private final String lambdaTestDomain = "@mobile-hub.lambdatest.com/wd/hub";

    Logger logger = LoggerFactory.getLogger(MobileDriversFactory.class);
    private static final Duration maxTimeOut = Duration.ofMinutes(10);
    Configs configs;

    public AppiumServer buildLocalAppiumServer(AppiumServer appiumServer){
        logger.info("Building appium server...");
        String localAppiumJSPath = getLocalAppiumPath();
        String localNodeJSPath = getLocalNodeJSPath();

        AppiumServiceBuilder builder = new AppiumServiceBuilder();
        builder.usingPort(generateRandomPortNumber());

        if (localAppiumJSPath != null){
            builder.withAppiumJS(new File(localAppiumJSPath));
        } else {
            logger.warn("Fetching appiumJS path failed. Trying to create an appium service without it...");
        }

        if (localNodeJSPath != null){
            builder.usingDriverExecutable(new File(localNodeJSPath));
        } else {
            logger.warn("Fetching NodeJS path failed. Trying to create an appium service without it...");
        }
        builder.withTimeout(maxTimeOut);
        AppiumDriverLocalService localService = builder.build();

        logger.info("Appium local service built on URL: " + localService.getUrl());

        appiumServer.setAppiumLocalService(localService);

        return appiumServer;
    }

    public AppiumServer startAppiumServer(AppiumServer appiumServer){
        logger.info("Starting appium server...");
        try {
            appiumServer.getAppiumLocalService().start();
            logger.info("Appium server should be started now.");
        } catch (Exception e){
            logger.error("Starting appium server crashed with exception: ", e);
        }
        if (appiumServer.getAppiumLocalService().isRunning()) {
            logger.info("Appium Server started on URL: " + appiumServer.getAppiumLocalService().getUrl());
        } else {
            // Handle failure of appium server starting
            logger.error("Appium server couldn't be started");
        }
        return appiumServer;
    }

    public String getLocalNodeJSPath() {
        logger.info("Getting local Node.js path...");
        String nodeJSPath = null;
        String osName = System.getProperty("os.name").toLowerCase();
        logger.info("Current OS Value is: " + osName);
        String[] command = (osName.contains("win")) ?
                new String[]{"cmd", "/c", "where node"}
                : new String[]{"/bin/bash", "-c", "command -v node"};
        logger.info("Executing command \"" + String.join(" ", command) + "\"");
        try {
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                nodeJSPath = line.trim();
                break; // Only retrieve the first path if multiple paths are returned
            }

            logger.info("Result of the command \"" + String.join(" ", command) +"\" is: " + nodeJSPath);

            if (nodeJSPath != null) {
                logger.info("Node.js path: " + nodeJSPath);
            } else {
                logger.error("Node.js is not installed on the machine.");
            }
        } catch (IOException e) {
            logger.error("Failed to get Node.js path.", e);
        }
        return nodeJSPath;
    }

    public String getLocalAppiumPath(){
        logger.info("Getting local appium path...");
        String appiumJSPath = null;
        String appiumPath = null;
        String osName = System.getProperty("os.name").toLowerCase();
        logger.info("Current OS Value is: " + osName);

        // Getting the appium path to find out if appium is installed or not
        String[] command = (osName.contains("win")) ?
                new String[]{"cmd", "/c", "where appium"}
                : new String[]{"/bin/bash", "-c", "command -v appium"};
        logger.info("Executing command \"" + String.join(" ", command) + "\"");
        try {
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;

            while ((line = reader.readLine()) != null) {
                appiumPath = line.trim();
                break; // Only retrieve the first path if multiple paths are returned
            }
            logger.info("Result of the command \"" + String.join(" ", command) +"\" is: " + appiumPath);
        } catch (IOException e) {
            logger.error("Getting appium path failed with Exception.", e);
        }

        // If appium path found, that means that appium is installed correctly on machine
        if (appiumPath != null && appiumPath.contains(File.separator + "appium")){
            logger.info("Getting the npm root path...");
            String[] npmRootCommand = (osName.contains("win")) ?
                    new String[]{"cmd", "/c", "npm", "root", "--location=global"} :
                    new String[]{"npm", "root", "--location=global"};
            logger.info("Executing command \"" + String.join(" ", npmRootCommand) + "\"");

            try {
                Process process = Runtime.getRuntime().exec(npmRootCommand);
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String npmRootPath = reader.readLine();

                logger.info("Result of the command \"" + String.join(" ", npmRootCommand) +"\" is: " + npmRootPath);

                if (npmRootPath != null && npmRootPath.contains(File.separator + "node_modules")) {
                    appiumJSPath = npmRootPath.trim() + File.separator
                            + "appium" + File.separator
                            + "build" + File.separator
                            + "lib" + File.separator
                            + "main.js";
                } else {
                    logger.info("Value of the command: \"" + Arrays.toString(npmRootCommand)
                            + "\" is empty or not a valid path");
                }
            } catch (IOException e) {
                logger.error("Getting the appium JS path failed with exception: ", e);
            }
        } else {
            logger.error("Appium is not installed correctly on machine. Try running \"where appium\" command");
        }
        logger.info("Final Appium JS path is: " + appiumJSPath);
        return appiumJSPath;
    }

    public AndroidDriver buildAndroidDriver(AppiumServer appiumServer, AndroidDevice androidDevice){
        logger.info("Building android driver...");
        UiAutomator2Options options = new UiAutomator2Options();

        //App related desiredCapabilities
        switch (configs.getTargetApp()){
            case "customerAppReactNative" ->{
                logger.info("Targeting android customerApp react-native version");
                options.setAppWaitActivity("com.breadfast.MainActivity");
                if (configs.getMobileRunMode().equalsIgnoreCase("remote")) {
                    projectName = "CustomerApp - ReactNative Android";
                    buildName = configs.getbStackAndroidCustomerAppReactNativeBuildNumber();
                } else {
                    options.setApp("resources/builds/app.apk");
                }
            }
            case "customerAppNative" -> {
                logger.info("Targeting android customerApp native version");
                options.setAppActivity("com.breadfast.main.MainActivity");
                options.setCapability("appium:disableIdLocatorAutocompletion",true);
                if (configs.getMobileRunMode().equalsIgnoreCase("remote")) {
                    projectName = "CustomerApp - Native Android";
                    buildName = configs.getbStackAndroidCustomerAppNativeAppBuildNumber();
                } else {
                    options.setApp("resources/builds/app-native.apk");
                }
            }
            case "midMileApp" -> {
                logger.info("Targeting android midMile app");
                options.setAppWaitActivity("com.breadfast.dispatchers.MainActivity");
                options.setCapability("appium:disableIdLocatorAutocompletion", true);
                if (configs.getMobileRunMode().equalsIgnoreCase("remote")) {
                    projectName = "MidMileApp - Android";
                    buildName = configs.getbStackAndroidMidMileAppBuildNumber();
                } else {
                    options.setApp("resources/builds/midMileApp.apk");
                }
            }
            case "fleetApp" -> {
                logger.info("Targeting android fleet app");
                options.setAppWaitActivity("com.breadfast.fleet.MainActivity");
                options.setCapability("appium:disableIdLocatorAutocompletion", true);
                if (configs.getMobileRunMode().equalsIgnoreCase("remote")) {
                    projectName = "FleetApp - Android";
                    buildName = configs.getbStackAndroidFleetAppBuildNumber();
                } else {
                    options.setApp("resources/builds/fleetApp.apk");
                }
            }
            default ->
                    logger.error("An unknown android targetApp name is used. Current value is \"{}\" " +
                            "and supported values are (customerAppReactNative, customerAppNative, midMileApp or fleetApp)"
                            , configs.getTargetApp());
        }

        //Assign a port listener for the device
        options.setSystemPort(appiumServer.getListenerPort());

        //Device related capabilities
        options.setAutoGrantPermissions(true);
        options.setPlatformName(androidDevice.getPlatformName());
        options.setPlatformVersion(androidDevice.getOsVersion());
        options.setDeviceName(configs.getMobileRunMode().equalsIgnoreCase("remote")
                ? androidDevice.getName()
                : androidDevice.getAdbName());
        options.setAutomationName(androidDevice.getAutomationName());
        options.setFullReset(false);

        if (configs.getMobileRunMode().equals("localHeadless"))
            options.setIsHeadless(true);

        //Increase setup timeouts to reduce flakiness of tests
        options.setUiautomator2ServerLaunchTimeout(maxTimeOut);
        options.setUiautomator2ServerInstallTimeout(maxTimeOut);
        options.setUiautomator2ServerReadTimeout(maxTimeOut);
        options.setAndroidInstallTimeout(maxTimeOut);
        options.setAvdLaunchTimeout(maxTimeOut);
        options.setAvdReadyTimeout(maxTimeOut);
        options.setAdbExecTimeout(maxTimeOut);
        options.setNewCommandTimeout(maxTimeOut);
        options.setAppWaitDuration(maxTimeOut);

        options.setAppWaitForLaunch(true);
        options.setIgnoreHiddenApiPolicyError(true);
        options.setDisableWindowAnimation(true);
        options.setGpsEnabled(true);

        options.setMockLocationApp("io.appium.settings");
        options.setCapability("locationContextEnabled", true);
        options.setEventTimings(true);

        //AVD capabilities
        if (!configs.getMobileRunMode().equalsIgnoreCase("remote")){
            options.setAvd(androidDevice.getName().replace(" ", "_"));
            options.setAvdArgs("-port " + androidDevice.getPortNumber()
                    + " -read-only"
                    + " -no-boot-anim"
                    + " -no-snapshot"
                    + " -no-audio");
        }

        //Increase appium xml depth to be able to get the deeply nested UI elements
        options.setCapability("appium:settings[snapshotMaxDepth]", 62);
        logger.info("Attempting to create a driver instance with capabilities: {}", options);

        logger.info("Initiating the android driver on the below info:");
        logger.info("SystemPort: {}", options.getSystemPort());
        logger.info("avdArgs: {}", options.getAvdArgs());

        //Remote driver Setup
        if(configs.getMobileRunMode().equalsIgnoreCase("remote")){
            logger.info("Initiating remote android driver with remote provider: {}", configs.getRemoteProviderName());

            options.setApp(getRemoteAppUrlBasedOnTargetVars(
                    androidDevice.getPlatformName(), configs.getRemoteProviderName(), configs.getTargetApp()));
            logger.info("Android app value for the target app: {} as {}",
                    configs.getTargetApp(), options.getApp());

            if (configs.getRemoteProviderName().equalsIgnoreCase("browserstack")){
                options.setCapability("bstack:options",
                        buildExternalMobileDriverOptions(Platform.ANDROID, RemoteProvider.BROWSERSTACK));
                options.setDisableSuppressAccessibilityService(false);

                logger.info("Final capabilities for android Driver for browserstack: {}", options);

                try {
                    return new AndroidDriver(
                            new URI("https://"
                                    + configs.getBStackUserName()
                                    + ":"
                                    + configs.getBStackAccessKey()
                                    + browserStackDomain).toURL()
                            , options);
                } catch (Exception e) {
                    logger.error("Initiating BrowserStack session failed with exception.", e);
                }
            } else if (configs.getRemoteProviderName().equalsIgnoreCase("lambdatest")) {
                options.setCapability("lt:options", buildExternalMobileDriverOptions(Platform.ANDROID, RemoteProvider.LAMBDATEST));
                options.setDisableSuppressAccessibilityService(false);

                logger.info("Final capabilities for android Driver for lambdaTest: {}", options);
                try {
                    return new AndroidDriver(
                            new URI("https://"
                                    + configs.getLambdaTestUserName()
                                    + ":"
                                    + configs.getLambdaTestAccessKey()
                                    + lambdaTestDomain).toURL()
                            , options);
                } catch (Exception e) {
                    logger.error("Initiating lambdaTest session failed with exception.", e);
                }
            }
        }
        return new AndroidDriver(appiumServer.getAppiumLocalService(), options);
    }

    public IOSDriver buildIosDriver(AppiumServer appiumServer, IosDevice iosDevice){
        logger.info("Building the iOS driver...");
        XCUITestOptions options = new XCUITestOptions();
        Permissions appPermissions = new Permissions();
        Map<String, String> permissions = new HashMap<>();
        Map<String, Object> appiumSettings = new HashMap<>();

        appiumSettings.put("animationCoolOffTimeout", 0);
        appiumSettings.put("acceptAlertButtonSelector", "**/XCUIElementTypeButton[`label CONTAINS[c] 'Allow While Using App'`]");

        permissions.put("notifications", "YES");
        permissions.put("location-always", "YES");
        permissions.put("userTracking", "YES");

        switch (configs.getTargetApp()){
            case "customerAppReactNative" ->{
                logger.info("Targeting iOS customerApp react-native version");
                if (configs.getMobileRunMode().equalsIgnoreCase("remote")) {
                    projectName = "CustomerApp - ReactNative iOS";
                    buildName = configs.getbStackIosCustomerAppReactNativeBuildNumber();
                } else {
                    options.setApp("resources/builds/app.zip");
                }

                logger.info("Granting permissions to the reactNative-customerApp (notifications, location-always, userTracking)");
                appPermissions.withAppPermissions("com.breadfast.breadfast", permissions);
                options.setPermissions(appPermissions);
            }
            case "customerAppNative" -> {
                logger.info("Targeting customerApp native version");
                if (configs.getMobileRunMode().equalsIgnoreCase("remote")) {
                    projectName = "CustomerApp - Native iOS";
                    buildName = configs.getbStackIosCustomerAppNativeAppBuildNumber();
                } else {
                    options.setApp("resources/builds/app-native.zip");
                }

                logger.info("Granting permissions to the native-customerApp (notifications, location-always, userTracking)");
                appPermissions.withAppPermissions("com.breadfast.testing", permissions);
                options.setPermissions(appPermissions);
            }
            case "midMileApp" -> {
                logger.info("Targeting midMile app");
                if (configs.getMobileRunMode().equalsIgnoreCase("remote")) {
                    projectName = "MidMileApp - iOS";
                    buildName = configs.getbStackIosMidMileAppBuildNumber();
                } else {
                    options.setApp("resources/builds/midMileApp.zip");
                }

                logger.info("Granting permissions to the midMileApp (notifications, location-always, userTracking)");
                appPermissions.withAppPermissions("com.breadfast.dispatchers", permissions);
                options.setPermissions(appPermissions);
            }
            case "fleetApp" -> {
                logger.info("Targeting fleetApp app");
                if (configs.getMobileRunMode().equalsIgnoreCase("remote")) {
                    projectName = "FleetApp - iOS";
                    buildName = configs.getbStackIosFleetAppBuildNumber();
                } else {
                    options.setApp("resources/builds/fleetApp.zip");
                }

                logger.info("Granting permissions to the fleetApp (notifications, location-always, userTracking)");
                appPermissions.withAppPermissions("com.breadfast.fleet", permissions);
                options.setPermissions(appPermissions);
            }

            default ->
                    logger.error("An unknown targetApp name is used. Current value is \"{}\" and supported values " +
                            "are (customerAppReactNative, customerAppNative, midMileApp or fleetApp)"
                            , configs.getTargetApp());
        }

        options.setPlatformName(iosDevice.getPlatformName());
        options.setPlatformVersion(iosDevice.getOsVersion());
        options.setDeviceName(iosDevice.getDeviceName());
        options.setAutomationName(iosDevice.getAutomationName());
        options.setWdaLocalPort(appiumServer.getListenerPort());
        options.setUdid(iosDevice.getDeviceUdid());
        options.setAutoAcceptAlerts(true);

        options.setNewCommandTimeout(maxTimeOut);
        options.setWdaLaunchTimeout(maxTimeOut);
        options.setWaitForIdleTimeout(maxTimeOut);

        options.setUseNewWDA(true);
        options.setFullReset(true);
        options.setCapability("appium:settings", appiumSettings);

        if (configs.getMobileRunMode().equals("localHeadless"))
            options.setIsHeadless(true);

        //Increase appium xml depth to be able to get the deeply nested UI elements
        options.setCapability("appium:settings[snapshotMaxDepth]", 62);

        //Determines how long the driver wait after actions for animation to cooldown and screen to freeze
        options.setCapability("appium:settings[animationCoolOffTimeout]", 3);

        //Change the finding strategy to cache the findBy query instead of caching element reference
        //this shall resolve the staleElementReference Exception that is thrown during animation happening
        options.setCapability("appium:settings[boundElementsByIndex]", true);

        logger.info("Initiating the iOS driver on the below info.");
        logger.info("WDA local port: {}", options.getWdaLocalPort());
        logger.info("Platform version: {}", options.getPlatformVersion());
        logger.info("Simulator/device UDID: {}", options.getUdid());

        //Remote driver setup Setup
        if(configs.getMobileRunMode().equalsIgnoreCase("remote")){
            logger.info("Initiating remote iOS driver with remote provider: {}", configs.getRemoteProviderName());

            options.setApp(getRemoteAppUrlBasedOnTargetVars(
                    iosDevice.getPlatformName(), configs.getRemoteProviderName(), configs.getTargetApp()));
            logger.info("iOS app value for the target app: {} as {}",
                    configs.getTargetApp(), options.getApp());

            if (configs.getRemoteProviderName().equalsIgnoreCase("browserstack")){
                options.setCapability("bstack:options"
                        , buildExternalMobileDriverOptions(Platform.IOS, RemoteProvider.BROWSERSTACK));
                logger.info("Final capabilities for iOS driver on browserstack: {}", options);

                try {
                    return new IOSDriver(
                            new URI("https://"
                                    + configs.getBStackUserName()
                                    + ":"
                                    + configs.getBStackAccessKey()
                                    + browserStackDomain).toURL()
                            , options);
                } catch (Exception e) {
                    logger.error("Initiating BrowserStack failed with exception.", e);
                }

            } else if (configs.getRemoteProviderName().equalsIgnoreCase("lambdatest")){
                options.setCapability("lt:options", buildExternalMobileDriverOptions(Platform.IOS, RemoteProvider.LAMBDATEST));
                logger.info("Final capabilities for iOS driver on lambdaTest: {}", options);
            }
        }

        return new IOSDriver(appiumServer.getAppiumLocalService(), options);
    }

    public AppiumServer generateRandomDriverPortNumber(AppiumServer appiumServer){
        appiumServer.setDriverPort(generateRandomPortNumber());
        return appiumServer;
    }

    public AppiumServer generateListenerPortNumber(AppiumServer appiumServer){
        appiumServer.setListenerPort(generateRandomPortNumber());
        return appiumServer;
    }

    public AndroidDevice runAndroidEmulator(AndroidDevice androidDevice){
        System.setProperty("user.dir", System.getProperty("user.home"));

        int emulatorPortNumber = generateRandomPortNumber();
        androidDevice.setName(configs.getAndroidDeviceName());
        androidDevice.setAdbName("emulator-"+emulatorPortNumber);
        androidDevice.setPortNumber(String.valueOf(emulatorPortNumber));
        androidDevice.setOsVersion(configs.getAndroidPlatformVersion());

        try {
            Runtime.getRuntime().exec(new String[]{"emulator", "@" + configs.getAndroidDeviceName()
                    , "-port", androidDevice.getPortNumber(), "-read-only"});
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        waitUntilDeviceIsEnabled("Android", androidDevice.getAdbName());

        return androidDevice;
    }

    public void runIosSimulator(){
        System.setProperty("user.dir", System.getProperty("user.home"));

        try {
            String deviceName = "\"" + configs.getiOSDeviceName() + "\"";
            String[] command = {"xcrun", "simctl", "boot", deviceName};
            Runtime.getRuntime().exec(command);
        } catch (IOException e){
            throw new RuntimeException(e);
        }
        waitUntilDeviceIsEnabled("iOS", configs.getiOSDeviceName());

        if (configs.getMobileRunMode().equals("local"))
            try{
                Runtime.getRuntime().exec(new String[]{"open", "-a", "Simulator"});
            } catch (Exception e){
                logger.error("Couldn't start simulator app..,");
            }
    }

    public void killIosSimulator(IosDevice iosDevice){
        ProcessBuilder processBuilder = new ProcessBuilder("xcrun", "simctl", "shutdown"
                , iosDevice.getDeviceUdid());
        try {
            Process process = processBuilder.start();
            process.waitFor();

            // Check the exit value of the process
            int exitCode = process.exitValue();
            if (exitCode == 0) {
                logger.info("Simulator with Name: " + iosDevice.getDeviceName()
                        + " and UDID " + iosDevice.getDeviceUdid() + " is terminated.");
            } else {
                logger.info("Simulator failed to exit.\nName:" + iosDevice.getDeviceName()
                        + "\nUDID: " + iosDevice.getDeviceUdid());
            }
        } catch (IOException | InterruptedException e){
            logger.error("Killing iOS simulator failed with an exception.");
        }
    }

    public void killAndroidEmulator(AndroidDevice androidDevice){
        ProcessBuilder processBuilder = new ProcessBuilder("adb", "-s", androidDevice.getAdbName()
                , "emu", "kill");
        try {
            Process process = processBuilder.start();
            process.waitFor();

            // Check the exit value of the process
            int exitCode = process.exitValue();
            if (exitCode == 0) {
                logger.info("Emulator with Name: " + androidDevice.getName()
                        + " and ADBName " + androidDevice.getAdbName() + " is terminated.");
            } else {
                logger.info("Emulator failed to exit.\nName:" + androidDevice.getName()
                        + "\nADBName: " + androidDevice.getAdbName());
            }
        } catch (IOException | InterruptedException e){
            logger.error("Killing android emulator failed with an exception.");
        }
    }

    //deviceName should be the ADBName in case of Android
    public void waitUntilDeviceIsEnabled(String deviceType, String deviceName){
        boolean emulatorStarted = false;
        boolean simulatorStarted = false;
        if (configs.getMobileRunMode().equals("local") || configs.getMobileRunMode().equals("localHeadless")) {
            long startTime;
            System.setProperty("user.dir", System.getProperty("user.home"));
            switch (deviceType) {
                case "Android" -> {
                    startTime = System.currentTimeMillis();
                    while (!emulatorStarted) {
                        try {
                            Process process = Runtime.getRuntime().
                                    exec(new String[]{"adb", "-s", deviceName, "shell", "getprop"
                                            , "sys.boot_completed"});
                            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                            String output = reader.readLine();
                            if (output != null) {
                                if (output.equals("1"))
                                    emulatorStarted = true;
                            }
                            reader.close();
                        } catch (IOException e) {
                            logger.error("Not able to check the android emulator status.", e);
                        }

                        // Sleep for 1 second to reduce resources usage in pooling
                        try {
                            Thread.sleep(Duration.ofSeconds(1).toMillis());
                        } catch (Exception e) {
                            logger.error("Thread sleep interrupted", e);
                        }

                        // Check if the waiting time exceeds the maximum wait time
                        if (System.currentTimeMillis() - startTime > maxTimeOut.toMillis()) {
                            logger.error("Timeout: Android emulator did not start within the specified time.");
                            break;
                        }
                    }
                }
                case "iOS" -> {
                    startTime = System.currentTimeMillis();
                    while (!simulatorStarted) {
                        try {
                            Process process = Runtime.getRuntime()
                                    .exec(new String[]{"xcrun simctl list devices booted | grep \""
                                            + deviceName + "\""});
                            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                            if (reader.readLine().contains(configs.getiOSDeviceName()
                                    + " (" + configs.getIosUDID() + ") (Booted)"))
                                simulatorStarted = true;
                            reader.close();
                        } catch (Exception e) {
                            logger.error("Not able to check the iOS simulator status.", e);
                        }

                        // Sleep for 1 second to reduce resources usage in pooling
                        try {
                            Thread.sleep(Duration.ofSeconds(1).toMillis());
                        } catch (Exception e) {
                            logger.error("Thread sleep interrupted", e);
                        }

                        // Check if the waiting time exceeds the maximum wait time
                        if (System.currentTimeMillis() - startTime > maxTimeOut.toMillis()) {
                            logger.error("Timeout: iOS simulator did not start within the specified time.");
                            break;
                        }
                    }
                }
            }
        } else {
            logger.warn("Remote Mobile drivers are not supported yet.");
        }
    }

    public IosDevice createStandAloneSimulatorImage(IosDevice iosDevice, TestData testData){
        String simulatorNameIdentifier = testData.getRandomTestUser().getEmailAddress().replaceAll("\\D", "");
        ProcessBuilder processBuilder = new ProcessBuilder("xcrun", "simctl", "clone", configs.getIosUDID()
                , simulatorNameIdentifier);

        try {
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.start();
            InputStream inputStream = process.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            String udid = null;
            // Read the command output
            String line;
            while ((line = reader.readLine()) != null) {
                // Assuming the UDID is the last line of the output
                udid = line.trim();
                logger.info("Cloned device from UDID: " + configs.getIosUDID() + "And Created a new simulator." +
                        "\nName: " + simulatorNameIdentifier + "\nUDID: " + udid);
            }
            if (udid != null){
                iosDevice.setDeviceUdid(udid);
                iosDevice.setDeviceName(simulatorNameIdentifier);
            }
            int exitCode = process.waitFor();
            logger.info("Cloning process completed with exitCode: " + exitCode);
        } catch (IOException | InterruptedException e){
            logger.error("Creating an iOS Simulator failed with an exception: ", e);
        }

        return iosDevice;
    }

    public void deleteIosSimulator(IosDevice iosDevice){
        if (iosDevice.getDeviceUdid().equals(configs.getIosUDID())){
            logger.warn("Not Deleting simulator as creating a clone of the simulator failed." +
                    "\nNot Deleting master image with UDID: " + iosDevice.getDeviceUdid());
        }
        else {
            ProcessBuilder processBuilder = new ProcessBuilder("xcrun", "simctl", "delete"
                    , iosDevice.getDeviceUdid());
            try {
                Process process = processBuilder.start();
                process.waitFor();

                int exitCode = process.exitValue();
                if (exitCode == 0){
                    logger.info("Created simulator deleted successfully." +
                            "\nName: " + iosDevice.getDeviceName() + "\nUDID: " + iosDevice.getDeviceUdid());
                } else {
                    logger.error("Deleting simulator failed. MANUAL ACTION REQUIRED TO RUN THIS COMMAND:" +
                            "\nxcrun simctl delete " + iosDevice.getDeviceUdid());
                }
            } catch (IOException | InterruptedException e){
                logger.error("Deleting simulator failed with an exception. MANUAL ACTION REQUIRED TO RUN THIS COMMAND:" +
                        "\nxcrun simctl delete " + iosDevice.getDeviceUdid(), e);
            }
        }
    }

    public HashMap<String, Object> buildExternalMobileDriverOptions(Platform platform, RemoteProvider remoteProvider){
        logger.info("Building external mobile driver options for platform: {}, remote provider: {}",
                platform, remoteProvider);
        HashMap<String, Object> remoteDriverOptions = new HashMap<String, Object>();
        HashMap<String, Object> networkLogsOptions = new HashMap<String, Object>();

        switch (remoteProvider) {
            case remoteProvider.BROWSERSTACK -> {
                logger.info("Building remote driver options for BrowserStack");
                remoteDriverOptions.put("userName", configs.getBStackUserName());
                remoteDriverOptions.put("accessKey", configs.getBStackAccessKey());
                remoteDriverOptions.put("local", configs.getBStackLocal());
                remoteDriverOptions.put("debug", configs.getBStackDebug());
                remoteDriverOptions.put("networkLogs", configs.getBStackNetworkLogs());
                networkLogsOptions.put("captureContent", configs.getBStackNetworkLogs());
                remoteDriverOptions.put("networkLogsOptions", networkLogsOptions);
                remoteDriverOptions.put("interactiveDebugging", "true");
                networkLogsOptions.put("captureContent", configs.getBStackNetworkLogs());
                remoteDriverOptions.put("networkLogsOptions", networkLogsOptions);
                remoteDriverOptions.put("selfHeal", configs.getBStackSelfHeal());
                remoteDriverOptions.put("projectName", projectName);
                remoteDriverOptions.put("buildName", buildName);
                switch (platform) {
                    case platform.ANDROID -> {
                        remoteDriverOptions.put("appiumVersion", configs.getbStackAppiumVersionForAndroidTests());
                    }
                    case platform.IOS -> {
                        remoteDriverOptions.put("appiumVersion", configs.getbStackAppiumVersionForIosTests());
                    }
                }
                logger.info("Completed building remote driver options for BrowserStack: {}", remoteDriverOptions);
            }
            case remoteProvider.LAMBDATEST -> {
                logger.info("Building remote driver options for LambdaTest");
                HashMap<String, Object> locationObject = new HashMap<String, Object>();

                remoteDriverOptions.put("user", configs.getLambdaTestUserName());
                remoteDriverOptions.put("accessKey", configs.getLambdaTestAccessKey());
                remoteDriverOptions.put("visual", configs.getLambdaTestVisualKey());
                remoteDriverOptions.put("network", configs.getLambdaTestNetworkKey());
                remoteDriverOptions.put("video", configs.getLambdaTestVideoKey());
                remoteDriverOptions.put("isRealMobile", configs.getLambdaTestIsRealMobileKey());
                remoteDriverOptions.put("console", configs.getLambdaTestConsoleKey());
                locationObject.put("lat", configs.getTestLatitude());
                locationObject.put("long", configs.getTestLongitude());
                remoteDriverOptions.put("location", locationObject);
                remoteDriverOptions.put("project", projectName);
                remoteDriverOptions.put("build", buildName);
                switch (platform) {
                    case platform.ANDROID -> {
                        remoteDriverOptions.put("appiumVersion", "2.16.2");
                    }
                    case platform.IOS -> {
                        remoteDriverOptions.put("appiumVersion", "2.16.2");
                    }
                }
                logger.info("Completed building remote driver options for LambdaTest: {}", remoteDriverOptions);
            }
        }
        return remoteDriverOptions;
    }

    private String getRemoteAppUrlBasedOnTargetVars(String platformName, String remoteProviderName, String targetAppName) {
        logger.info("Getting remote app URL for provider: {}, target app: {}", remoteProviderName, targetAppName);

        if (remoteProviderName == null || remoteProviderName.isEmpty()) {
            logger.error("Remote provider name is null or empty.");
            return null;
        }

        if (targetAppName == null || targetAppName.isEmpty()) {
            logger.error("Target app name is null or empty.");
            return null;
        }

        switch (remoteProviderName.toLowerCase()) {
            case "browserstack" -> {
                return switch (targetAppName.toLowerCase()) {
                    case "customerappreactnative" -> platformName.toLowerCase().equalsIgnoreCase("android")
                            ? configs.getbStackAndroidCustomerAppReactNativeId()
                            : configs.getbStackIosCustomerAppReactNativeId();
                    case "customerappnative" -> platformName.toLowerCase().equalsIgnoreCase("android")
                            ? configs.getbStackAndroidCustomerAppNativeAppId()
                            : configs.getbStackIosCustomerAppNativeAppId();
                    case "midmileapp" -> platformName.toLowerCase().equalsIgnoreCase("android")
                            ? configs.getbStackAndroidMidMileAppId()
                            : configs.getbStackIosMidMileAppId();
                    case "fleetapp" -> platformName.toLowerCase().equalsIgnoreCase("android")
                            ? configs.getbStackAndroidFleetAppId()
                            : configs.getbStackIosFleetAppId();
                    default -> {
                        logger.error("Unknown target app name \"{}\" for BrowserStack. Supported values: " +
                                "customerAppReactNative, customerAppNative, midMileApp, fleetApp.", targetAppName);
                        yield null;
                    }
                };
            }
            case "lambdatest" -> {
                return switch (targetAppName.toLowerCase()) {
                    case "customerappnative" -> platformName.toLowerCase().equalsIgnoreCase("android")
                            ? configs.getLambdaTestAndroidCustomerAppNativeAppId()
                            : configs.getLambdaTestIosCustomerAppNativeAppId();
                    default -> {
                        logger.error("Unknown target app name \"{}\" for LambdaTest. Supported values: " +
                                "customerAppNative.", targetAppName);
                        yield null;
                    }
                };
            }
            default -> {
                logger.error("Unsupported remote provider name \"{}\". Supported values: browserstack, lambdatest."
                        , remoteProviderName);
                return null;
            }
        }
    }
}
