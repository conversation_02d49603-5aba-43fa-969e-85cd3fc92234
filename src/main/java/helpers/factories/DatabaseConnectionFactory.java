package helpers.factories;

import helpers.BaseHelper;
import models.Configs;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.create.table.CreateTable;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Arrays;

public class DatabaseConnectionFactory extends BaseHelper {
    Configs configs;
    Logger logger = LoggerFactory.getLogger(DatabaseConnectionFactory.class);

    public DatabaseConnectionFactory(Configs configs) {
        this.configs = configs;
    }

    public Connection setupSshConnection(){
        JSch.setLogger(new DatabaseConnectionLogger());
        logger.info("""
                Initiating DB connection to DB with the following properties: \
                
                DB Host: {}\

                DB UserName: {}\
                
                DB UserPass: {}\

                Database Name: {}\
                
                Mysql Server Port Number: {}\

                SSH UserName: {}\

                SSH Host: {}\
                
                SSH PortNumber: {}\
                
                SSH KeyPath: {}"""
                , configs.getMysqlHost()
                , configs.getMysqlUserName()
                , configs.getMysqlUserPassword()
                , configs.getMysqlDatabaseName()
                , configs.getMysqlServerPort()
                , configs.getSshUserName()
                , configs.getSshHost()
                , configs.getSshPortNumber()
                , configs.getSshKeyPath());

        String targetMysqlHost = configs.getMysqlHost();
        int assignedPort = configs.getMysqlServerPort();
        try {
            if (configs.isSshConnectionRequired()){
                logger.info("Initiating SSH connection setup...");
                // Setup SSH tunnel
                JSch jsch = new JSch();
                targetMysqlHost = "localhost";
                logger.info("Fetched the current SSH key value successfully.");
                Session session = configs.getSshPortNumber() != 0
                        ? jsch.getSession(configs.getSshUserName(), configs.getSshHost(), configs.getSshPortNumber())
                        : jsch.getSession(configs.getSshUserName(), configs.getSshHost());

                if (configs.isSshKeyProtected()){
                    logger.info("SSH key is protected with a passphrase.");
                    jsch.addIdentity(configs.getSshKeyPath(), configs.getSshPassphrase());
                } else {
                    logger.info("SSH Key is not protected with a passphrase.");
                    jsch.addIdentity(configs.getSshKeyPath());
                }

                session.setConfig("StrictHostKeyChecking", "no");

                logger.info("Added the SSH connection identity for the SSH Key in: {}", configs.getSshKeyPath());
                session.connect(20000);

                if (session.isConnected()){
                    logger.info("SSH session is connected successfully.");
                } else {
                    logger.error("SSH connection is not created.");
                }

                assignedPort = session.setPortForwardingL(0, configs.getMysqlHost(), configs.getMysqlServerPort());
                logger.info("Current assigned port is: {}", Arrays.toString(session.getPortForwardingL()));
            } else {
                logger.info("SSH is not required to connect to DB. Connecting to the database server directly...");
            }

            // Connect to MySQL database through SSH tunnel
            String url = "jdbc:mysql://" + targetMysqlHost + ":" + assignedPort + "/" + configs.getMysqlDatabaseName()
                    + "?"
                    + "&autoReconnect=true"
                    + "&failOverReadOnly=false"
                    + "&sessionVariables=wait_timeout=31536000,interactive_timeout=31536000"
                    + "&maxReconnects=10";
            logger.info("DB connection URL is: {}", url);

            Connection connection =
                    DriverManager.getConnection(url, configs.getMysqlUserName(), configs.getMysqlUserPassword());
            logger.info("Connected to DB successfully.");
            return connection;
        } catch (Exception e) {
            logger.error("Failed to create connection to MySQL database: ", e);
        }
        return null;
    }

    public void closeConnection(Connection connection) {
        if (connection != null) {
            logger.info("Starting to close DB connection to DB");
            try {
                connection.close();
                logger.info("Closed DB connection successfully.");
            } catch (Exception e){
                logger.error("Couldn't close the DB connection with error: {}", e.getMessage());
            }

            logger.info("SSH session closed successfully.");
        } else {
            logger.error("Current connection object is null. Skipping connection closing...");
        }
    }

    public String executeQuery(Connection connection, String sqlQuery) {
        try {
            logger.info("Starting to run query: {}", sqlQuery);
            // Validate SQL query
            net.sf.jsqlparser.statement.Statement stmt = CCJSqlParserUtil.parse(sqlQuery);

            if (stmt instanceof Select) {
                // SELECT query
                Statement statement = connection.createStatement();
                ResultSet resultSet = statement.executeQuery(sqlQuery);

                JSONArray jsonArray = new JSONArray();
                while (resultSet.next()) {
                    JSONObject row = new JSONObject();
                    for (int i = 1; i <= resultSet.getMetaData().getColumnCount(); i++) {
                        row.put(resultSet.getMetaData().getColumnLabel(i), resultSet.getObject(i));
                    }
                    jsonArray.put(row);
                }

                resultSet.close();
                statement.close();

                logger.info("Result of query is: {}", jsonArray);
                return jsonArray.toString();
            }

            // UPDATE, INSERT, DELETE, or DDL query
            else if (stmt instanceof Update || stmt instanceof Insert || stmt instanceof Delete || stmt instanceof CreateTable) {
                Statement statement = connection.createStatement();
                int affectedRows = statement.executeUpdate(sqlQuery);

                statement.close();

                logger.info("Query executed successfully, affected rows: {}", affectedRows);
                return "{\"affectedRows\":" + affectedRows + "}";
            }

            else {
                throw new UnsupportedOperationException("Unsupported SQL statement: " + sqlQuery);
            }
        } catch (Exception e) {
            logger.error("Failed to execute SQL query with error: ", e);
            return null;
        }
    }
}
