package helpers.factories;

import com.jcraft.jsch.JSch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DatabaseConnectionLogger implements com.jcraft.jsch.Logger {
    private final Logger logger;

    public DatabaseConnectionLogger() {
        logger = LoggerFactory.getLogger(JSch.class);
    }

    @Override
    public boolean isEnabled(int i) {
        return true;
    }

    @Override
    public void log(int level, String message) {
        // Map JSch log levels to SLF4J log levels
        switch (level) {
            case DEBUG:
                logger.debug(message);
                break;
            case INFO:
                logger.info(message);
                break;
            case WARN:
                logger.warn(message);
                break;
            case ERROR:
                logger.error(message);
                break;
            case FATAL:
                logger.error("[FATAL] " + message);
                break;
            default:
                logger.debug(message);
        }
    }

    @Override
    public void log(int level, String message, Throwable cause) {
        com.jcraft.jsch.Logger.super.log(level, message, cause);
    }
}
