package helpers.factories;

import org.testng.IAlterSuiteListener;
import org.testng.xml.XmlInclude;
import org.testng.xml.XmlSuite;
import org.testng.xml.XmlTest;

import java.util.ArrayList;
import java.util.List;

public class TestTypeListeners implements IAlterSuiteListener {
    @Override
    public void alter(List<XmlSuite> suites) {
        String testType = System.getProperty("testType", "create_order");

        for (XmlSuite suite : suites) {
            for (XmlTest test : suite.getTests()) {
                List<XmlInclude> includedMethods = new ArrayList<>();
                if (testType.equals("create_order") && "Create Order Performance Test".equals(test.getName())) {
                    includedMethods.add(new XmlInclude("createOrderWithOneProductFromApiForStressTesting"));
                } else if (testType.equals("chatbot") && "Chatbot Performance Test".equals(test.getName())) {
                    includedMethods.add(new XmlInclude("validateChatbotSessionCreationFlowForStressTesting"));
                } else if (testType.equals("chatbot_with_predefined_tokens")
                        && "Chatbot Performance Test".equals(test.getName())){
                    includedMethods.add(new XmlInclude("validateChatbotSessionCreationFlowUsingDataProviderCsvSheet"));
                } else if (testType.equals("freshchat_api_performance_test")
                        && "Chatbot API Performance Test".equals(test.getName())) {
                    includedMethods.add(new XmlInclude("createFreshChatConversation"));
                } else if (testType.equals("freshchat_api_performance_test_with_predefined_tokens")
                        && "Chatbot API Performance Test".equals(test.getName())) {
                    includedMethods.add(new XmlInclude("createFreshChatConversationForPreGeneratedTokens"));
                }
                if (!includedMethods.isEmpty()) {
                    test.getClasses().getFirst().setIncludedMethods(includedMethods);
                } else {
                    test.getClasses().clear();
                }
            }
        }
    }
}
