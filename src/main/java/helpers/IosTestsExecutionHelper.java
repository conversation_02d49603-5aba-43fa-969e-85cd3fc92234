package helpers;

import io.appium.java_client.AppiumBy.ByIosNsPredicate;
import io.appium.java_client.Location;
import io.appium.java_client.ios.IOSDriver;
import modals.customerApp.ios.*;
import modals.customerApp.ios.iosAlerts.IosLocationPermissionAlert;
import modals.customerApp.ios.iosAlerts.IosNotificationsPermissionsAlert;
import modals.customerApp.ios.iosAlerts.IosTrackingPermissionAlert;
import modals.customerApp.ios.iosHomePage.IosAddressSelectionScreen;
import modals.customerApp.ios.iosHomePage.IosHomeScreen;
import modals.customerApp.ios.iosMoreScreen.IosChooseCountryModal;
import modals.customerApp.ios.iosMoreScreen.IosChooseLanguageModal;
import modals.customerApp.ios.iosMoreScreen.IosMoreScreen;
import models.*;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.Point;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class IosTestsExecutionHelper{
    private static final Logger logger = LoggerFactory.getLogger(IosTestsExecutionHelper.class);
    private Map<String, String> selectorTypes = Map.of(
            "id=", "id",
            "xpath=", "xpath",
            "name=", "name",
            "accessibility-id=", "accessibility-id"
    );

    public void register(IosCountriesSelectionScreen iosCountriesSelectionScreen,
                         IosLandingScreen iosLandingScreen,
                         IosNotificationsPermissionsAlert iosNotificationsPermissionsAlert,
                         IosPhoneNumberScreen iosPhoneNumberScreen, IosCountriesListScreen iosCountriesListScreen,
                         IosOTPVerificationScreen iosOTPVerificationScreen,
                         TestExecutionHelper testExecutionHelper, TestData testData,
                         IosCreateAccountScreen iosCreateAccountScreen,
                         IosRegisterSuccessScreen iosRegisterSuccessScreen,
                         IosTrackingPermissionAlert iosTrackingPermissionAlert, IosHomeScreen iosHomeScreen,
                         String phoneCountry){

        // Choose a country and press login
        iosCountriesSelectionScreen.chooseCountryAndSubmit(testData.getTestCountryCode());

        //Press the Login or signup button
        iosLandingScreen.pressAuthHyperLink();

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosPhoneNumberScreen, iosOTPVerificationScreen,
                        "register", phoneCountry);
            }
            default -> {
                changeCountry(iosPhoneNumberScreen, iosCountriesListScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosPhoneNumberScreen, iosOTPVerificationScreen,
                        "register", phoneCountry);
            }
        }

        //Enter Account Information
        iosCreateAccountScreen.fillInAccountInformationForm(testData);
        iosCreateAccountScreen.pressSubmitBtn();

        //Proceed from the success screen
        if (iosRegisterSuccessScreen.isConfirmationMessageDisplayed())
            iosRegisterSuccessScreen.pressProceedBtn();

        //Validate that home screen is displayed
        iosHomeScreen.isHomePageDisplayed();
    }

    public void login(TestData testData, TestExecutionHelper testExecutionHelper,
                      IosPhoneNumberScreen iosPhoneNumberScreen, IosCountriesListScreen iosCountriesListScreen,
                      IosOTPVerificationScreen iosOTPVerificationScreen, String phoneCountry){

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosPhoneNumberScreen, iosOTPVerificationScreen,
                        "login", phoneCountry);
            }
            default -> {
                changeCountry(iosPhoneNumberScreen, iosCountriesListScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosPhoneNumberScreen, iosOTPVerificationScreen,
                        "login", phoneCountry);
            }
        }
    }

    private void enterPhoneNumberAndOTP(TestData testData, TestExecutionHelper testExecutionHelper,
                                       IosPhoneNumberScreen iosPhoneNumberScreen,
                                       IosOTPVerificationScreen iosOTPVerificationScreen,
                                       String method, String phoneCountry){
        //Enter phone number
        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                if (iosPhoneNumberScreen.isPageHeaderDisplayed()) {
                    iosPhoneNumberScreen.enterPhoneNumber(testData.getRandomTestUser().getLocalPhoneNumber());
                    iosPhoneNumberScreen.pressNextBtn();

                    if (iosOTPVerificationScreen.isPageHeaderDisplayed()
                            && iosOTPVerificationScreen.isPageSubHeaderDisplayed()
                            && iosOTPVerificationScreen.isPhoneNumberDisplayed(
                            testData.getRandomTestUser().getPhoneNumber())) {
                        while (iosOTPVerificationScreen.isPageHeaderDisplayed()) {
                            try {
                                testData.setRandomTestUser(testExecutionHelper.otpFactory
                                        .fetchOtp(testData, method, testData.getRandomTestUser()));
                                if (iosOTPVerificationScreen.isPageHeaderDisplayed())
                                    iosOTPVerificationScreen.enterOTP(testData.getRandomTestUser().getOtp());
                            } catch (Exception e){
                                break;
                            }
                        }
                    }
                }
            }
            default -> {
                if (iosPhoneNumberScreen.isPageHeaderDisplayed()) {
                    iosPhoneNumberScreen.enterPhoneNumber(testData.getRandomTestUser().getForeignLocalPhoneNumber());
                    iosPhoneNumberScreen.pressNextBtn();
                    if (iosOTPVerificationScreen.isPageHeaderDisplayed()
                            && iosOTPVerificationScreen.isPageSubHeaderDisplayed()
                            && iosOTPVerificationScreen
                            .isPhoneNumberDisplayed(testData.getRandomTestUser().getForeignPhoneNumber())) {
                        while (iosOTPVerificationScreen.isPageHeaderDisplayed()) {
                            try {
                                testData.setRandomTestUser(testExecutionHelper.otpFactory
                                        .fetchOtp(testData, method, testData.getRandomTestUser()));
                                if (iosOTPVerificationScreen.isPageHeaderDisplayed())
                                    iosOTPVerificationScreen.enterOTP(testData.getRandomTestUser().getOtp());
                            } catch (Exception e){
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    public void logout(IOSDriver iosDriver, TestData testData, IosHomeScreen iosHomeScreen
            , IosMoreScreen iosMoreScreen){
        //Go to more Tab
        if (iosHomeScreen.isHomePageDisplayed())
            iosHomeScreen.pressMoreTabBtn();

        scrollUntilACertainElementIsFound(iosDriver, "down", iosHomeScreen.getScrollableContentContainer()
                , iosMoreScreen.getLogoutBtnContentDescription());

        //Logout
        if (iosMoreScreen.isFullNameDisplayed(testData.getRandomTestUser().getFirstName()
                , testData.getRandomTestUser().getLastName()))
            iosMoreScreen.pressLogoutBtn();
    }

    //Changing phone number's country code during login/register
    private void changeCountry(IosPhoneNumberScreen iosPhoneNumberScreen, IosCountriesListScreen iosCountriesListScreen,
                              TestData testData){
        iosPhoneNumberScreen.pressCountryCodeDropDownBtn(testData.getRandomTestUser().getPhoneCountry());
        if (iosCountriesListScreen.isCountriesListScreenDisplayed()){
            iosCountriesListScreen.searchForCountry(testData.getRandomTestUser().getForeignPhoneCountryCode());
            iosCountriesListScreen.selectCountry(testData.getRandomTestUser().getForeignPhoneCountryCode());
        }
    }

    public void setIosEmulatorLocation(double latitude, double longitude, IOSDriver iosDriver){
        Location location = new Location(latitude, longitude);
        iosDriver.setLocation(location);
    }

    public void enterAsGuest(IosCountriesSelectionScreen iosCountriesSelectionScreen,
                             IosLandingScreen iosLandingScreen,
                             IosLocationPermissionAlert iosLocationPermissionAlert,
                             IosNotificationsPermissionsAlert iosNotificationsPermissionsAlert,
                             IosTrackingPermissionAlert iosTrackingPermissionAlert,
                             IosHomeScreen iosHomeScreen, IosSetAddressScreen iosSetAddressScreen,
                             TestData testData){
        // Choose a country and press login
        iosCountriesSelectionScreen.chooseCountryAndSubmit(testData.getTestCountryCode());
        iosLandingScreen.pressExploreBtn();

        iosLocationPermissionAlert.takeActionIfAlertIsDisplayed("whileUsing");
        iosTrackingPermissionAlert.takeActionIfAlertIsDisplayed("accept");
        iosNotificationsPermissionsAlert.takeActionIfAlertIsDisplayed("accept");

        //Set address Screen -> Skip it
        iosSetAddressScreen.confirmLocationIfDisplayed();

        iosHomeScreen.isHomePageDisplayed();
    }

    public boolean isElementDisplayedBySelector(IOSDriver iosDriver, String elementSelector, String elementSelectorType){
        try {
            return getElementBySelector(iosDriver, elementSelector, elementSelectorType).isDisplayed();
        } catch (Exception e) {
            return false;
        }
    }

    public WebElement getElementBySelector(IOSDriver iosDriver, String elementSelector, String elementSelectorType){
        try {
            switch (elementSelectorType.toLowerCase()){
                case "id":
                    return iosDriver.findElement(ByIosNsPredicate.id(elementSelector));
                case "xpath":
                    return iosDriver.findElement(ByIosNsPredicate.xpath(elementSelector));
                case "accessibility-id":
                    return iosDriver.findElement(ByIosNsPredicate.accessibilityId(elementSelector));
                default:
                    return iosDriver.findElement(ByIosNsPredicate.name(elementSelector));
            }
        } catch (Exception e) {
            return null;
        }
    }

    public void scrollToDirection(IOSDriver iosDriver, WebElement scrollView, String direction, String elementSelector){
        if (scrollView == null)
            return;
        HashMap<String, Object> scrollObject = new HashMap<>();
        scrollObject.put("direction", direction);
        scrollObject.put("element", scrollView);
        scrollObject.put("predicateString", elementSelector);
        scrollObject.put("toVisible", true);

        iosDriver.executeScript("mobile: scroll", scrollObject);
    }

    public boolean isElementPartiallyDisplayed(IOSDriver iosDriver, WebElement element, String side
            , double marginPercentage) {
        if (iosDriver == null || element == null || side == null)
            return true;

        // Get the location and size of the element
        Point elementLocation = element.getLocation();
        Dimension elementSize = element.getSize();

        // Get the size of the screen
        Dimension screenSize = iosDriver.manage().window().getSize();
        int bottomLimit = (int) (screenSize.getHeight() - (screenSize.getHeight() * marginPercentage));
        int topLimit = (int) (screenSize.getHeight() * marginPercentage);
        int rightLimit = (int) screenSize.getWidth();
        int leftLimit = 0;

        return switch (side.toLowerCase()) {
            case "up" -> elementLocation.getY() < topLimit;
            case "down" -> (elementLocation.getY() + elementSize.getHeight()) > bottomLimit;
            case "right" -> (elementLocation.getX() + elementSize.getWidth()) > rightLimit;
            case "left" -> elementLocation.getX() < leftLimit;
            default -> true;
        };
    }

    public boolean scrollUntilACertainElementIsFound(IOSDriver iosDriver, String direction, WebElement scrollView,
                                                     String elementSelector) {
        String elementSelectorType = getElementSelectorType(elementSelector);
        elementSelector = getSubstringSelector(elementSelector);
        boolean isTargetElementDisplayed = isElementDisplayedBySelector(iosDriver, elementSelector, elementSelectorType);
        long startTime = System.currentTimeMillis();
        long timeout = 180000; // 3 minutes in milliseconds

        //Check if element is partiallyDisplayed
        if (isTargetElementDisplayed){
            // Define your margin values
            double marginPercentage = 0.05; // 5%
            boolean elementAtEdge = (isElementPartiallyDisplayed(iosDriver
                    , getElementBySelector(iosDriver, elementSelector, elementSelectorType)
                    , direction
                    , marginPercentage));
            if (elementAtEdge)
                isTargetElementDisplayed = false;
        }

        while (!isTargetElementDisplayed && !(System.currentTimeMillis() - startTime > timeout)){
            scrollToDirection(iosDriver, scrollView, direction, elementSelector);
            isTargetElementDisplayed = isElementDisplayedBySelector(iosDriver, elementSelector, elementSelectorType);

        }

        return isTargetElementDisplayed;
    }

    public String getElementSelectorType(String elementSelector){
        return selectorTypes.entrySet().stream()
                .filter(entry -> elementSelector.startsWith(entry.getKey()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse("name");
    }

    public String getSubstringSelector(String elementSelector) {
        for (String prefix : selectorTypes.keySet()) {
            if (elementSelector.startsWith(prefix)) {
                return elementSelector.substring(prefix.length());
            }
        }
        return elementSelector;
    }

    public void addNewAddressAsCurrentLocation(IosHomeScreen iosHomeScreen,
                                               IosAddressSelectionScreen iosAddressSelectionScreen,
                                               IosLocationPermissionAlert iosLocationPermissionAlert,
                                               IosSetAddressScreen iosSetAddressScreen,
                                               IOSDriver iosDriver){
        iosHomeScreen.pressChangeAddressBtn();
        if (iosAddressSelectionScreen.isDropDownDisplayed()){
            if (!iosAddressSelectionScreen.isAddNewAddressBtnDisplayed()) {
                scrollUntilACertainElementIsFound(iosDriver,
                        "down",
                        iosAddressSelectionScreen.getAddressesListScrollableContainer(),
                        iosAddressSelectionScreen.getAddNewAddressBtnNameSelector());
            }
            iosAddressSelectionScreen.pressAddNewAddressBtn();
            iosLocationPermissionAlert.takeActionIfAlertIsDisplayed("whileUsing");
            iosSetAddressScreen.confirmLocationIfDisplayed();
        }
    }

    public void changeAppCountry(IosHomeScreen iosHomeScreen,
                                 IosLandingScreen iosLandingScreen,
                                 IosMoreScreen iosMoreScreen,
                                 IosChooseCountryModal iosChooseCountryModal,
                                 String countryCode)
    {
        //Navigate to home screen and assert navigation
        iosHomeScreen.pressHomeTabBtn();
        iosHomeScreen.isHomePageDisplayed();

        //Navigate to more tab and assert navigation
        iosHomeScreen.pressMoreTabBtn();
        iosMoreScreen.dismissCoachMarksIfDisplayed();

        iosMoreScreen.isPageDisplayed();

        //Navigate to country modal and assert navigation
        iosMoreScreen.pressCountryBtn();
        iosChooseCountryModal.isPageDisplayed();

        //Select country and assert navigation to home screen
        iosChooseCountryModal.selectCountry(countryCode);
        iosLandingScreen.isPageDisplayed();
    }

    public void changeAppLanguage(IosHomeScreen iosHomeScreen,
                                  IosMoreScreen iosMoreScreen,
                                  IosChooseLanguageModal iosChooseLanguageModal,
                                  String language)
    {
        //Navigate to home screen and assert navigation
        iosHomeScreen.pressHomeTabBtn();
        iosHomeScreen.isHomePageDisplayed();

        //Navigate to more tab and assert navigation
        iosHomeScreen.pressMoreTabBtn();
        iosMoreScreen.dismissCoachMarksIfDisplayed();
        iosMoreScreen.isPageDisplayed();

        //Navigate to language modal and assert navigation
        iosMoreScreen.pressLanguageBtn();
        iosChooseLanguageModal.isPageDisplayed();

        //Select language and assert navigation to home screen
        iosChooseLanguageModal.selectLanguage(language);
        iosHomeScreen.dismissRatingBottomIfDisplayed();
        iosHomeScreen.isHomePageDisplayed();
    }

    public ValidationResults areAllCategoriesDisplayed(IOSDriver iosDriver
            , IosHomeScreen iosHomeScreen
            , IosCategoryScreen iosCategoryScreen
            , List<Category> allCategories
            , String serveMode
            , ValidationResults validationResults){
        for (Category c : allCategories){
            scrollUntilACertainElementIsFound(iosDriver
                    , "down"
                    , iosHomeScreen.getScrollableContentContainer()
                    , iosHomeScreen.getCategoryNameSelector(c.getId()));
            if (iosHomeScreen.isCategoryDisplayed(c.getId())){
                iosHomeScreen.pressCategory(c.getId());
                iosCategoryScreen.isPageDisplayed();

                // Validate subCategories and products per subCategory
                if (!c.getSubCategories().isEmpty()){
                    for (Category sc : c.getSubCategories()){
                        if (c.getSubCategories().size() == 1){
                            if (!iosCategoryScreen.getSubCategoryProductsTitle().isDisplayed()
                                    && !(iosCategoryScreen.getSubCategoryProductsTitle().getText()
                                    .equalsIgnoreCase(c.getSubCategories().get(0).getName())
                                    || iosCategoryScreen.getSubCategoryProductsTitle().getText()
                                    .equalsIgnoreCase(c.getSubCategories().get(0).getArabicName()))){
                                validationResults.setResult(false);
                                validationResults.addALogToValidationResults("At Category \"" + c.getId()
                                        + "\" with name \"" + c.getName()
                                        + "\". SubCategory with ID: " + c.getSubCategories().get(0).getId()
                                        + " and name \"" + c.getSubCategories().get(0).getName()
                                        + "\" title is not displayed\n");
                            }
                        } else {
                            // Reset scroll position to the first element on the left before starting scroll
                            scrollUntilACertainElementIsFound(iosDriver
                                    , "left"
                                    , iosCategoryScreen.getScrollableContentContainer("subcategory")
                                    , iosCategoryScreen.getSubCategoryNameSelector(
                                            c.getSubCategories().get(0).getId()));

                            // Scroll and find the target subCategory
                            scrollUntilACertainElementIsFound(iosDriver
                                    , "right"
                                    , iosCategoryScreen.getScrollableContentContainer("subcategory")
                                    , iosCategoryScreen.getSubCategoryNameSelector(sc.getId()));

                            // If SubCategory is found, press it
                            if (iosCategoryScreen.isSubCategoryDisplayed(sc.getId())){
                                iosCategoryScreen.pressSubCategory(sc.getId());
                            } else {
                                validationResults.setResult(false);
                                validationResults.addALogToValidationResults("At Category \"" + c.getId()
                                        + "\" with name \"" + c.getName()
                                        + "\". SubCategory with ID: " + sc.getId()
                                        + " and " + "name \"" + sc.getName() + "\" is not displayed\n");
                            }
                        }
                        // Validate each product is displayed inside each subCategory
                        for (Product p : (serveMode.equalsIgnoreCase("now")
                                ? sc.getNowProductsInclusive() : sc.getLaterProductsInclusive())) {
                            scrollUntilACertainElementIsFound(iosDriver
                                    , "down"
                                    , iosCategoryScreen.getScrollableContentContainer("product")
                                    , iosCategoryScreen.getProductCardNameSelector(p.getMongoId()));

                            if (iosCategoryScreen.isProductCardDisplayed(p.getMongoId())){
                                //ToDo: Add internal product details page validations
                            } else {
                                validationResults.setResult(false);
                                validationResults.addALogToValidationResults("At Category \"" + c.getId()
                                        + "\" with name \"" + c.getName()
                                        + "\". SubCategory with ID: " + sc.getId()
                                        + " and " + "name \"" + sc.getName()
                                        + "\". Product with ID \"" + p.getMongoId() + "\" "
                                        + "and name is \"" + p.getName() + "\" is not displayed\n");
                            }

                            // Reset Page scroll position to the first Product Card
                            scrollUntilACertainElementIsFound(iosDriver
                                    , "up"
                                    , iosCategoryScreen.getScrollableContentContainer("product")
                                    , iosCategoryScreen.getProductCardNameSelector(
                                            serveMode.equalsIgnoreCase("now")
                                                    ? sc.getNowProductsInclusive().get(0).getMongoId()
                                                    : sc.getLaterProductsInclusive().get(0).getMongoId()));
                        }
                    }
                }
                iosCategoryScreen.pressBackBtn();
                iosHomeScreen.isHomePageDisplayed();
            } else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("Category with ID: " + c.getId() + " and " +
                        "name \"" + c.getName() + "\" is not displayed\n");
            }
            //Scroll back to first category or top of the page after all validations are done
            scrollUntilACertainElementIsFound(iosDriver
                    , "up"
                    , iosHomeScreen.getScrollableContentContainer()
                    , iosHomeScreen.getCategoryNameSelector(allCategories.get(0).getId()));
        }
        return validationResults;
    }

    public void changeAppLanguage(Configs configs, IOSDriver iosDriver, String language){
        switch (language.toLowerCase()){
            case "en", "english" -> {
                if (configs.getMobileRunMode().equalsIgnoreCase("remote")
                        && configs.getRemoteProviderName().equalsIgnoreCase("browserstack")){
                    iosDriver.executeScript(
                            "browserstack_executor: {\"action\": \"updateAppSettings\"" +
                                    ", \"arguments\": {\"Permission Settings\":" +
                                    "{\"Language\":" +
                                    "{\"OTHER LANGUAGES\": \"English\"}}}}");
                } else {
                    Map<String, Object> args = new HashMap<>();
                    args.put("arguments", new String[]{"-AppleLanguages", "(en)", "-AppleLocale", "en_US"});
                    iosDriver.executeScript("mobile: launchApp", args);
                }
            }
            case "ar", "arabic" -> {
                if (configs.getMobileRunMode().equalsIgnoreCase("remote")
                        && configs.getRemoteProviderName().equalsIgnoreCase("browserstack")){
                    iosDriver.executeScript(
                            "browserstack_executor: {\"action\": \"updateAppSettings\"" +
                                    ", \"arguments\": {\"Permission Settings\":" +
                                    "{\"Language\":" +
                                    "{\"OTHER LANGUAGES\": \"العربية\"}}}}");
                } else {
                    Map<String, Object> args = new HashMap<>();
                    args.put("arguments", new String[]{"-AppleLanguages", "(ar)", "-AppleLocale", "ar_EG"});
                    iosDriver.executeScript("mobile: launchApp", args);
                }
            }
        }
    }

    public void swipeElementInDirection(IOSDriver iosDriver, WebElement element, String direction) {
        if (!element.isDisplayed()){
            logger.error("Can't perform the swipe gesture as element isn't displayed on the screen.");
            return;
        }

        if (iosDriver == null || element == null || direction == null) {
            logger.error("ios driver, element or swipe direction is null.");
            return;
        }

        if (!direction.equalsIgnoreCase("left")
                && !direction.equalsIgnoreCase("right")){
            logger.error("Invalid swipe direction provided. Valid values are: left, right");
            return;
        }

        HashMap<String, Object> swipeObject = new HashMap<>();
        String elementId = ((RemoteWebElement) element).getId();
        swipeObject.put("elementId", elementId);
        swipeObject.put("direction", direction.toLowerCase());
        swipeObject.put("velocity", 2500);

        iosDriver.executeScript("mobile: swipe", swipeObject);
    }

    public void pullToRefresh(IOSDriver iosDriver, WebElement scrollView) {
        if (iosDriver == null || scrollView == null) {
            logger.error("iosDriver or scrollView is null. Cannot perform pull to refresh.");
            return;
        }
        HashMap<String, Object> scrollObject = new HashMap<>();
        scrollObject.put("direction", "up");
        scrollObject.put("element", ((RemoteWebElement) scrollView).getId());
        iosDriver.executeScript("mobile: scroll", scrollObject);
    }

    public void payWithApplePayOnBrowserStack(IOSDriver iosDriver){
        logger.info("Attempting to pay with Apple Pay...");
        try {
            iosDriver.executeScript("browserstack_executor: " +
                    "{\"action\":\"applePay\", \"arguments\": {\"confirmPayment\" : \"true\"}}");
        } catch (Exception e){
            logger.error("Failed to pay with Apple Pay: {}", e.getMessage());
        }
    }
}
