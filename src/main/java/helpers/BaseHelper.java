package helpers;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.ServerSocket;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

public class BaseHelper {
    private static final Logger logger = LoggerFactory.getLogger(BaseHelper.class);

    public int generateRandomPortNumber(){
        int portNumber = 0;
        boolean isValidPortNumber = false;
        while (!isValidPortNumber) {
            portNumber = (int) (Math.random() * 10000);
            if (portNumber <= 0 || portNumber > 65535) {
                logger.warn("Invalid port number: " + portNumber);
                continue;
            }
            try (ServerSocket ignored = new ServerSocket(portNumber)) {
                isValidPortNumber = true;
            } catch (Exception e) {
                logger.warn("Port " + portNumber + " is already in use");
            }
        }
        return portNumber;
    }

    public String changeDateFormatToTargetFormat(String date, String currentFormat, String newFormat){
        logger.info("Changing date format to match format: " + newFormat);
        SimpleDateFormat inputDateFormat = new SimpleDateFormat(currentFormat);
        SimpleDateFormat outputDateFormat = new SimpleDateFormat(newFormat);
        String outputDate = "";
        logger.info("Initiating a date format with current value of: \"" + date
                + "\" and current format of: \"" + currentFormat + "\" to match the format of: \"" + newFormat + "\"");
        try {
            Date currentDate = inputDateFormat.parse(date);
            outputDate = outputDateFormat.format(currentDate);
            logger.info("Converted date value from \"" + date + "\" to \"" + outputDate + "\"");
        } catch (Exception e){
            logger.error(
                    "changeDateFormatToTargetFormat failed to parse current date " +
                            " to it's original format of " + currentFormat);
        }
        return outputDate;
    }

    public String getCurrentTimeStamp(String format){
        return new SimpleDateFormat(format).format(Calendar.getInstance().getTime());
    }

    public String getFutureTimeStamp(String format, int hoursToAdd, int OneHour) {
        LocalTime currentTime = LocalTime.now().withMinute(0).withSecond(0).withNano(0);

        LocalTime futureTime = currentTime.plusHours(hoursToAdd);
        LocalTime futureOneTime = futureTime.plusHours(OneHour);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        String formattedFutureTime = futureTime.format(formatter);
        String formattedOneFutureTime = futureOneTime.format(formatter);

        return formattedFutureTime + " - " + formattedOneFutureTime;
    }

    public String getUTCFutureTimeStamp(String format, int hoursToAdd, int oneHour) {
        ZonedDateTime currentTime = ZonedDateTime.now(ZoneOffset.UTC)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);

        ZonedDateTime futureTime = currentTime.plusHours(hoursToAdd);
        ZonedDateTime futureOneTime = futureTime.plusHours(oneHour);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        String formattedFutureTime = futureTime.format(formatter);
        String formattedOneFutureTime = futureOneTime.format(formatter);

        return formattedFutureTime + " - " + formattedOneFutureTime;
    }

    public String getPastUTCZoneTimeStamp(String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -1);

        return simpleDateFormat.format(calendar.getTime());
    }

    public String getUTCZoneCurrentTimeStamp(String format, int minute) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, minute);

        return simpleDateFormat.format(calendar.getTime());
    }

    public String AddOrSubtractDaysFromTargetDate(String date, String currentFormat, String finalFormat,
                                                  int changeValue){
        logger.info("Changing the date format by adding the value of: " + changeValue);
        SimpleDateFormat inputDateFormat = new SimpleDateFormat(currentFormat);
        SimpleDateFormat outputDateFormat = new SimpleDateFormat(finalFormat);
        String output = "";
        try {
            Calendar calendar = Calendar.getInstance();
            Date currentDate = inputDateFormat.parse(date);
            calendar.setTime(currentDate);
            calendar.add(Calendar.DAY_OF_YEAR, changeValue);
            currentDate = calendar.getTime();
            output = outputDateFormat.format(currentDate);
        } catch (Exception e){
            logger.error("Adding the amount of " + changeValue + " to the date of \"" + date + "\" and the format of \""
                    + currentFormat + "\"");
        }
        return output;
    }
}
