package helpers.dataProviders;

import com.opencsv.CSVReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.DataProvider;

import java.io.FileReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ThreaderDataProviderSource {
    private static final Logger logger = LoggerFactory.getLogger(ThreaderDataProviderSource.class);
    public List<Integer> getThreadCountsFromCSV(String filePath) {
        logger.info("Starting to fetch the threads counter from CSV file from path: {}", filePath);
        List<Integer> threadCounts = new ArrayList<>();

        try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
            List<String[]> rows = reader.readAll();

            // Assuming the first row contains headers
            String[] headers = rows.getFirst();
            int columnIndex = -1;
            for (int i = 0; i < headers.length; i++) {
                if ("threads_count".equals(headers[i])) {
                    columnIndex = i;
                    break;
                }
            }

            if (columnIndex != -1) {
                for (int i = 1; i < rows.size(); i++) {
                    String[] row = rows.get(i);
                    threadCounts.add(Integer.parseInt(row[columnIndex].trim()));
                }
            } else {
                logger.error("Column 'threads_count' not found in CSV file.");
            }
        } catch (Exception e) {
            logger.error("Fetching the CSV contents failed with error: {}", e.getMessage());
        }
        logger.info("Fetched threads count as: {}", threadCounts.size());
        logger.info("Contents of the list is: {}", Arrays.toString(threadCounts.toArray()));
        return threadCounts;
    }

    @DataProvider(name = "threadCountsProvider", parallel = true)
    public Object[][] provideThreadCounts() {
        List<Integer> threadCounts = getThreadCountsFromCSV("resources/dataSets/threads_counter.csv");

        Object[][] data = new Object[threadCounts.size()][1];
        for (int i = 0; i < threadCounts.size(); i++) {
            data[i][0] = threadCounts.get(i);
        }

        // Logging the contents of the 2D array
        logger.info("Contents of the 2D array:");
        StringBuilder sb = new StringBuilder();
        for (Object[] row : data) {
            sb.append(Arrays.toString(row)).append("\n");
        }
        logger.info(sb.toString());

        return data;
    }
}
