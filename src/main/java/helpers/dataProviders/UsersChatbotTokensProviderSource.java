package helpers.dataProviders;

import com.opencsv.CSVReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.DataProvider;

import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;

public class UsersChatbotTokensProviderSource {
    private static final Logger logger = LoggerFactory.getLogger(UsersChatbotTokensProviderSource.class);

    @DataProvider(name = "chatbotTokensProvider", parallel = true)
    public Object[][] csvDataProvider() {
        String csvFile = "resources/dataSets/chatbot_tokens.csv"; // Set your file path here
        List<Object[]> usersTokens = new ArrayList<>();

        try (CSVReader csvReader = new CSVReader(new FileReader(csvFile))) {
            String[] line;
            boolean isFirstRow = true;

            while ((line = csvReader.readNext()) != null) {
                if (isFirstRow) {
                    isFirstRow = false;
                    continue;
                }

                // Add each row from the CSV file as an array to the list
                usersTokens.add(new Object[] { line[0], line[1], line[2], line[3] });
            }

            logger.info("Fetched chatbot tokens with count of: {}", usersTokens.size());
        } catch (Exception e) {
            logger.error("The chatbot_tokens file is either empty or doesn't exist.");
        }

        // Convert List to Object[][]
        return usersTokens.toArray(new Object[0][0]);
    }
}
