package helpers.dataProviders;

import com.opencsv.CSVReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.DataProvider;

import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;

public class RolesDataProviderSource {
    private static final Logger logger = LoggerFactory.getLogger(RolesDataProviderSource.class);

    private static final String SWITCHER_PAGE = "switcher";
    private static final String PLANNING_CENTER_PAGE = "planning_center";
    private static final String PAGES_ADMIN_PAGE = "pages";
    private static final String APP_FAQS_PAGE = "app_faqs";
    private static final String CANCELLATION_REASONS_PAGE = "cancellation_reasons";
    private static final String RECOMMENDATIONS_PAGE = "recommendations";
    private static final String BULK_DISCOUNTS_PAGE = "bulk_discounts";
    private static final String ATTENDANCE_PAGE = "attendance";
    private static final String SIGNATURE_PAGE = "signature";
    private static final String POSTS_PAGE = "posts";
    private static final String FLYERS_PAGE = "flyers";
    private static final String REFERRALS_PAGE = "referrals";
    private static final String APP_MESSAGES_PAGE = "app_messages";
    private static final String PRODUCTS_SORTING_PAGE = "products_sorting";
    private static final String INTERNAL_CATEGORIES_PAGE = "internal_categories";
    private static final String SCHEDULED_ORDERS_PAGE = "scheduled_orders";
    private static final String CATEGORIES_PAGE = "categories";
    private static final String INGREDIENTS_PAGE = "ingredients";
    private static final String PRODUCTS_PAGE = "products";
    private static final String BRANDS_PAGE = "brands";
    private static final String COUPONS_PAGE = "coupons";
    private static final String ORDERS_PAGE = "orders";
    private static final String AREAS_PAGE = "areas";
    private static final String EVENTS_PAGE = "events";
    private static final String CAREERS_PAGE = "careers";
    private static final String REPEATED_ORDERS_PAGE = "repeated_orders";
    private static final String BANNERS_PAGE = "banners";
    private static final String POPUPS_PAGE = "popups";
    private static final String COLLECTIONS_PAGE = "collections";
    private static final String GENERAL_SETTINGS_PAGE = "general_settings";
    private static final String CX_SETTINGS_PAGE = "cx_settings";
    private static final String ORDER_SMS_PAGE = "order_sms";
    private static final String INTERCOM_SETTINGS_PAGE = "intercom_settings";
    private static final String CSS_SETTINGS_PAGE = "css_settings";
    private static final String SLACK_SETTINGS_PAGE = "slack_settings";
    private static final String VODAFONE_SETTINGS_PAGE = "vodafone_settings";
    private static final String CC_DISCOUNT_PAGE = "cc_discount";
    private static final String CAREER_SETTINGS_PAGE = "career_settings";
    private static final String RECOMMENDATION_SETTINGS_PAGE = "recommendation_settings";

    @DataProvider(name = "recommendationSettingsRolesMap", parallel = true)
    public static Object[][] readRecommendationSettingsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(RECOMMENDATION_SETTINGS_PAGE);
    }

    @DataProvider(name = "careerSettingsRolesMap", parallel = true)
    public static Object[][] readCareerSettingsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(CAREER_SETTINGS_PAGE);
    }

    @DataProvider(name = "ccDiscountRolesMap", parallel = true)
    public static Object[][] readCCDiscountRolesMapFromCSV(){
        return fetchMappedDataFromCSV(CC_DISCOUNT_PAGE);
    }

    @DataProvider(name = "vodafoneSettingsRolesMap", parallel = true)
    public static Object[][] readVodafoneSettingsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(VODAFONE_SETTINGS_PAGE);
    }

    @DataProvider(name = "slackSettingsRolesMap", parallel = true)
    public static Object[][] readSlackSettingsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(SLACK_SETTINGS_PAGE);
    }

    @DataProvider(name = "cssSettingsRolesMap", parallel = true)
    public static Object[][] readCssSettingsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(CSS_SETTINGS_PAGE);
    }

    @DataProvider(name = "intercomSettingsRolesMap", parallel = true)
    public static Object[][] readIntercomSettingsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(INTERCOM_SETTINGS_PAGE);
    }

    @DataProvider(name = "orderSmsRolesMap", parallel = true)
    public static Object[][] readOrderSmsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(ORDER_SMS_PAGE);
    }

    @DataProvider(name = "cxSettingsRolesMap", parallel = true)
    public static Object[][] readCXSettingsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(CX_SETTINGS_PAGE);
    }

    @DataProvider(name = "generalSettingsRolesMap", parallel = true)
    public static Object[][] readGeneralSettingsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(GENERAL_SETTINGS_PAGE);
    }

    @DataProvider(name = "collectionsRolesMap", parallel = true)
    public static Object[][] readCollectionsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(COLLECTIONS_PAGE);
    }

    @DataProvider(name = "popupsRolesMap", parallel = true)
    public static Object[][] readPopupsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(POPUPS_PAGE);
    }

    @DataProvider(name = "bannersRolesMap", parallel = true)
    public static Object[][] readBannersRolesMapFromCSV(){
        return fetchMappedDataFromCSV(BANNERS_PAGE);
    }

    @DataProvider(name = "repeatedOrdersRolesMap", parallel = true)
    public static Object[][] readRepeatedOrdersRolesMapFromCSV(){
        return fetchMappedDataFromCSV(REPEATED_ORDERS_PAGE);
    }

    @DataProvider(name = "careersRolesMap", parallel = true)
    public static Object[][] readCareersRolesMapFromCSV(){
        return fetchMappedDataFromCSV(CAREERS_PAGE);
    }

    @DataProvider(name = "eventsRolesMap", parallel = true)
    public static Object[][] readEventsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(EVENTS_PAGE);
    }

    @DataProvider(name = "areasRolesMap", parallel = true)
    public static Object[][] readAreasRolesMapFromCSV(){
        return fetchMappedDataFromCSV(AREAS_PAGE);
    }

    @DataProvider(name = "ordersRolesMap", parallel = true)
    public static Object[][] readOrdersRolesMapFromCSV(){
        return fetchMappedDataFromCSV(ORDERS_PAGE);
    }

    @DataProvider(name = "couponsRolesMap", parallel = true)
    public static Object[][] readCouponsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(COUPONS_PAGE);
    }

    @DataProvider(name = "brandsRolesMap", parallel = true)
    public static Object[][] readBrandsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(BRANDS_PAGE);
    }

    @DataProvider(name = "productsRolesMap", parallel = true)
    public static Object[][] readProductsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(PRODUCTS_PAGE);
    }

    @DataProvider(name = "ingredientsRolesMap", parallel = true)
    public static Object[][] readIngredientsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(INGREDIENTS_PAGE);
    }

    @DataProvider(name = "categoriesRolesMap", parallel = true)
    public static Object[][] readCategoriesRolesMapFromCSV(){
        return fetchMappedDataFromCSV(CATEGORIES_PAGE);
    }

    @DataProvider(name = "scheduledOrdersRolesMap", parallel = true)
    public static Object[][] readScheduledOrdersRolesMapFromCSV(){
        return fetchMappedDataFromCSV(SCHEDULED_ORDERS_PAGE);
    }

    @DataProvider(name = "internalCategoriesRolesMap", parallel = true)
    public static Object[][] readInternalCategoriesRolesMapFromCSV(){
        return fetchMappedDataFromCSV(INTERNAL_CATEGORIES_PAGE);
    }

    @DataProvider(name = "productsSortingRolesMap", parallel = true)
    public static Object[][] readProductsSortingRolesMapFromCSV(){
        return fetchMappedDataFromCSV(PRODUCTS_SORTING_PAGE);
    }

    @DataProvider(name = "appMessagesRolesMap", parallel = true)
    public static Object[][] readAppMessagesRolesMapFromCSV(){
        return fetchMappedDataFromCSV(APP_MESSAGES_PAGE);
    }

    @DataProvider(name = "referralsRolesMap", parallel = true)
    public static Object[][] readReferralsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(REFERRALS_PAGE);
    }

    @DataProvider(name = "flyersRolesMap", parallel = true)
    public static Object[][] readFlyersRolesMapFromCSV(){
        return fetchMappedDataFromCSV(FLYERS_PAGE);
    }

    @DataProvider(name = "postsRolesMap", parallel = true)
    public static Object[][] readPostsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(POSTS_PAGE);
    }

    @DataProvider(name = "signatureRolesMap", parallel = true)
    public static Object[][] readSignatureRolesMapFromCSV(){
        return fetchMappedDataFromCSV(SIGNATURE_PAGE);
    }

    @DataProvider(name = "attendanceRolesMap", parallel = true)
    public static Object[][] readAttendanceRolesMapFromCSV(){
        return fetchMappedDataFromCSV(ATTENDANCE_PAGE);
    }

    @DataProvider(name = "bulkDiscountsRolesMap", parallel = true)
    public static Object[][] readBulkDiscountsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(BULK_DISCOUNTS_PAGE);
    }

    @DataProvider(name = "recommendationsRolesMap", parallel = true)
    public static Object[][] readRecommendationsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(RECOMMENDATIONS_PAGE);
    }

    @DataProvider(name = "cancellationReasonsRolesMap", parallel = true)
    public static Object[][] readCancellationReasonsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(CANCELLATION_REASONS_PAGE);
    }

    @DataProvider(name = "appFaqsRolesMap", parallel = true)
    public static Object[][] readAppFaqsRolesMapFromCSV(){
        return fetchMappedDataFromCSV(APP_FAQS_PAGE);
    }

    @DataProvider(name = "pagesAdminPage", parallel = true)
    public static Object[][] readPagesRolesMapFromCSV(){
        return fetchMappedDataFromCSV(PAGES_ADMIN_PAGE);
    }

    @DataProvider(name = "planningCenterRolesMap", parallel = true)
    public static Object[][] readPlanningCenterRolesMapFromCSV(){
        return fetchMappedDataFromCSV(PLANNING_CENTER_PAGE);
    }

    @DataProvider(name = "switcherRolesMap", parallel = true)
    public static Object[][] readSwitcherRolesMapFromCSV(){
        return fetchMappedDataFromCSV(SWITCHER_PAGE);
    }

    private static Object[][] fetchMappedDataFromCSV(String pageName) {
        List<String[]> csvData = loadCSVData(pageName); // Load the CSV data

        List<Object[]> testDataList = new ArrayList<>();

        for (int i = 1; i < csvData.size(); i++) {
            String[] rowData = csvData.get(i);

            Object[] testData = new Object[rowData.length];
            System.arraycopy(rowData, 0, testData, 0, rowData.length); // Copy the entire row
            testDataList.add(testData);
        }

        Object[][] testDataArray = new Object[testDataList.size()][];
        for (int i = 0; i < testDataList.size(); i++) {
            testDataArray[i] = testDataList.get(i);
        }

        return testDataArray;
    }

    private static List<String[]> loadCSVData(String pageName) {
        List<String[]> csvData = new ArrayList<>();
        String csvFilePath = "";
        switch (pageName.toLowerCase()){
            case "switcher" -> csvFilePath = "resources/dataSets/rolesDataSets/switcherRolesMap.csv";
            case "planning_center" -> csvFilePath = "resources/dataSets/rolesDataSets/planningCenterRolesMap.csv";
            case "pages" -> csvFilePath = "resources/dataSets/rolesDataSets/pagesRolesMap.csv";
            case "app_faqs" -> csvFilePath = "resources/dataSets/rolesDataSets/appFaqsRolesMap.csv";
            case "cancellation_reasons" -> csvFilePath = "resources/dataSets/rolesDataSets/cancellationReasonsRolesMap.csv";
            case "recommendations" -> csvFilePath = "resources/dataSets/rolesDataSets/recommendationsRolesMap.csv";
            case "bulk_discounts" -> csvFilePath = "resources/dataSets/rolesDataSets/bulkDiscountsRolesMap.csv";
            case "attendance" -> csvFilePath = "resources/dataSets/rolesDataSets/attendanceRolesMap.csv";
            case "signature" -> csvFilePath = "resources/dataSets/rolesDataSets/signaturesRolesMap.csv";
            case "posts" -> csvFilePath = "resources/dataSets/rolesDataSets/postsRolesMap.csv";
            case "flyers" -> csvFilePath = "resources/dataSets/rolesDataSets/flyersRolesMap.csv";
            case "referrals" -> csvFilePath = "resources/dataSets/rolesDataSets/referralsRolesMap.csv";
            case "app_messages" -> csvFilePath = "resources/dataSets/rolesDataSets/appMessagesRolesMap.csv";
            case "products_sorting" -> csvFilePath = "resources/dataSets/rolesDataSets/productsSortingRolesMap.csv";
            case "internal_categories" -> csvFilePath = "resources/dataSets/rolesDataSets/internalCategoriesRolesMap.csv";
            case "scheduled_orders" -> csvFilePath = "resources/dataSets/rolesDataSets/scheduledOrdersRolesMap.csv";
            case "categories" -> csvFilePath = "resources/dataSets/rolesDataSets/categoriesRolesMap.csv";
            case "ingredients" -> csvFilePath = "resources/dataSets/rolesDataSets/ingredientsRolesMap.csv";
            case "products" -> csvFilePath = "resources/dataSets/rolesDataSets/productsRolesMap.csv";
            case "brands" -> csvFilePath = "resources/dataSets/rolesDataSets/brandsRolesMap.csv";
            case "coupons" -> csvFilePath = "resources/dataSets/rolesDataSets/couponsRolesMap.csv";
            case "orders" -> csvFilePath = "resources/dataSets/rolesDataSets/ordersRolesMap.csv";
            case "areas" -> csvFilePath = "resources/dataSets/rolesDataSets/areasRolesMap.csv";
            case "events" -> csvFilePath = "resources/dataSets/rolesDataSets/eventsRolesMap.csv";
            case "careers" -> csvFilePath = "resources/dataSets/rolesDataSets/careersRolesMap.csv";
            case "repeated_orders" -> csvFilePath = "resources/dataSets/rolesDataSets/repeatedOrdersRolesMap.csv";
            case "banners" -> csvFilePath = "resources/dataSets/rolesDataSets/bannersRolesMap.csv";
            case "popups" -> csvFilePath = "resources/dataSets/rolesDataSets/popupsRolesMap.csv";
            case "collections" -> csvFilePath = "resources/dataSets/rolesDataSets/collectionsRolesMap.csv";
            case "general_settings" -> csvFilePath = "resources/dataSets/rolesDataSets/generalSettingsRolesMap.csv";
            case "cx_settings" -> csvFilePath = "resources/dataSets/rolesDataSets/cxSettingsRolesMap.csv";
            case "order_sms" -> csvFilePath = "resources/dataSets/rolesDataSets/orderSmsRolesMap.csv";
            case "intercom_settings" -> csvFilePath = "resources/dataSets/rolesDataSets/intercomSettingsRolesMap.csv";
            case "css_settings" -> csvFilePath = "resources/dataSets/rolesDataSets/cssSettingsRolesMap.csv";
            case "slack_settings" -> csvFilePath = "resources/dataSets/rolesDataSets/slackSettingsRolesMap.csv";
            case "vodafone_settings" -> csvFilePath = "resources/dataSets/rolesDataSets/vodafoneSettingsRolesMap.csv";
            case "cc_discount" -> csvFilePath = "resources/dataSets/rolesDataSets/ccDiscountRolesMap.csv";
            case "career_settings" -> csvFilePath = "resources/dataSets/rolesDataSets/careerSettingsRolesMap.csv";
            case "recommendation_settings" -> csvFilePath = "resources/dataSets/rolesDataSets/recommendationSettingsRolesMap.csv";
            default -> logger.error("[ERROR] The provided page name is invalid/not supported");
        }

        try (CSVReader csvReader = new CSVReader(new FileReader(csvFilePath))) {
            String[] record;
            while ((record = csvReader.readNext()) != null) {
                csvData.add(record);
            }
        } catch (Exception e) {
            logger.error("[ERROR] Reading the CSV file for page \"" + pageName + "\" failed with exception.", e);
        }

        return csvData;
    }
}
