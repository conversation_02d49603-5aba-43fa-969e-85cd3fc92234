package helpers.dataProviders;

import org.testng.annotations.DataProvider;

public class TransferReasonDataProvider {

    public enum TransferReason {
        LIVING_EXPENSES,
        BILL_PAYMENT,
        EMERGENCY_ASSISTANCE,
        GIFT,
        FAMILY_SUPPORT,
        POCKET_MONEY,
        ACCOMMODATION_FEES,
        B<PERSON>L_SPLIT,
        SAVINGS,
        DONATION;
    }

    @DataProvider(name = "transferReasons")
    public static Object[][] transferReasonData() {
        TransferReason[] enumValues = TransferReason.values();
        Object[][] data = new Object[enumValues.length + 2][1];

        // Add all enum values
        for (int i = 0; i < enumValues.length; i++) {
            data[i][0] = enumValues[i].name();
        }

        // Add empty string and null
        data[enumValues.length][0] = "";
        data[enumValues.length + 1][0] = null;

        return data;
    }
}
