package helpers.apiClients;

import io.restassured.RestAssured;
import io.restassured.response.Response;
import org.json.JSONArray;
import org.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import helpers.BaseHelper;
import models.Configs;

import java.util.concurrent.TimeUnit;

public class SlackApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(SlackApiClient.class);
    private final String slackAuthToken;

    private final String searchMessagesEndpoint = "/search.messages";

    public SlackApiClient(Configs configs) {
        this.configs = configs;
        this.slackAuthToken = configs.getSlackApiToken();
    }

    public String findMessageForOTP(String phoneNumber, String method){
        logger.info("Searching for the OTP message for phoneNumber: {} and method {}", phoneNumber, method);
        long endTimeMillis = System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(30);
        String otpValue = null;

        // Send API request to search for messages
        while (System.currentTimeMillis() < endTimeMillis && otpValue == null) {
            // Send API request to search for messages
            Response response = RestAssured.given()
                    .header("Authorization", "Bearer " + slackAuthToken)
                    .queryParam("query", "OTP For " + phoneNumber)
                    .queryParam("sort", "timestamp")
                    .queryParam("sort_dir", "desc")
                    .get(configs.getSlackApiBaseURL() + searchMessagesEndpoint)
                    .then()
                    .log().ifValidationFails()
                    .statusCode(200)
                    .extract().response();

            if (response.getStatusCode() == 200) {
                // Extract the OTP value from the response
                JSONObject jsonResponse = new JSONObject(response.getBody().asString());
                JSONArray matchesArray = jsonResponse.getJSONObject("messages").getJSONArray("matches");
                logger.info("Received {} matches for the search query \"{}\""
                        , matchesArray.length()
                        , "OTP For " + phoneNumber);
                switch (method){
                    case "login", "updatePhoneNumber", "deleteAccount":
                        if (matchesArray.length() > 1){
                            JSONObject messageObj = matchesArray.getJSONObject(0);
                            otpValue = extractOtpValue(messageObj.getString("text"));
                        } else {
                            logger.warn("Login code were not found for phoneNumber: {}. Returning NULL", phoneNumber);
                        }
                    break;
                    default:
                        if (!matchesArray.isEmpty()) {
                            logger.info("Found message/s that has the phoneNumber: {}", phoneNumber);
                            JSONObject messageObj = matchesArray.getJSONObject(0);
                            otpValue = extractOtpValue(messageObj.getString("text"));
                            break;
                        }
                        else {
                            logger.warn("No messages were found for phoneNumber: {}. Returning NULL", phoneNumber);
                        }
                        break;
                }
            }

            try {
                TimeUnit.SECONDS.sleep(2);
            } catch (InterruptedException e) {
                logger.error("Trying to sleep for 1 second crashed with exception: ", e);
            }
        }
        return otpValue;
    }

    private static String extractOtpValue(String messageText) {
        // Extract the 4 digits OTP value from the message text
        logger.info("Extracting OTP code from message: \n" + messageText);
        String otpValue = "";
        int startIndex = messageText.indexOf("is") + 3;
        int endIndex = messageText.indexOf("\n", startIndex);
        if (startIndex >= 0 && endIndex <= messageText.length()) {
            otpValue = messageText.substring(startIndex, endIndex);
        }
        logger.info("OTP code is: \n" + otpValue);
        return otpValue;
    }

}
