package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.OrdersDataParser;
import helpers.dataParsers.ProductsParser;
import helpers.dataParsers.WarehousesDataParser;
import io.restassured.RestAssured;
import io.restassured.config.HttpClientConfig;
import io.restassured.config.RestAssuredConfig;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import models.*;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;

public class ControlRoomV2ApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(ControlRoomV2ApiClient.class);
    private final String listWarehousesEndpoint = "/warehouses-service/warehouses/listWarehouses";
    private final String listOrdersEndpoint = "/control-room/orders";
    private final String getUserData="/picker-service/user/get-user-data";
    private final String updatePickersStatus="/picker-service/user/update-user-status";
    private final String loginPicker = "/wp-json/shopper/v1/user/login";
    private final String productStockLogEndpoint ="/warehouses-service/productStockLog/warehouse";
    private final String addStockToProductEndpoint = "/warehouses-service/warehouses/{warehouseId}/batches/add";
    private final String changeOrderStatusEndpoint = "/control-room/order/{orderId}/{actionHandler}";
    private static final int MAX_RETRIES = 10;
    private final static int SMALL_BAG_ID = 2;
    private final static int BIG_BAG_ID = 1;

    public ControlRoomV2ApiClient(Configs configs) {
        this.configs = configs;
    }

    public List<Warehouse> listAllWarehouses(User adminUser){
        // Create the JSON request body manually
        String requestBody = "{\n" +
                "    \"project\": [\n" +
                "        {\n" +
                "            \"key\": \"basicInfo\",\n" +
                "            \"value\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"key\": \"users\",\n" +
                "            \"value\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"key\": \"areas\",\n" +
                "            \"value\": 1\n" +
                "        }\n" +
                "    ]\n" +
                "}";

        Response response = RestAssured.given()
                .header("content-type", "application/json")
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .body(requestBody)
                .post(configs.getBaseURL() + listWarehousesEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONArray retrievedList = new JSONArray(response.getBody().asString());
        List<JSONObject> warehousesList;
        List<Warehouse> allWarehouses = new ArrayList<>();
        if (retrievedList.length() > 0){
            logger.info("Total count of found warehouses is: " + retrievedList.length());
            warehousesList = new WarehousesDataParser().parseJsonArrayToListOfJsonObjects(retrievedList);
            for (JSONObject e : warehousesList){
                allWarehouses.add(new WarehousesDataParser()
                        .parseWarehouseJsonObject(e, new WarehousesDataParser().REQUEST_SOURCE_CONTROL_ROOM));
            }
        } else {
            logger.info("Found 0 warehouses available in DB");
        }
        return allWarehouses;
    }

    public Warehouse getWarehouseByName(User adminUser, String warehouseName){
        logger.info("Starting to search the warehouses using admin user: " + adminUser.getPhoneNumber()
                + " To find a warehouse with the name: " + warehouseName);
        Warehouse targetWarehouse = null;
        List<Warehouse> warehousesList = listAllWarehouses(adminUser);

        if (warehousesList.size() > 0){
            logger.info("Warehouses list has total count of: " + warehousesList.size());
            for (Warehouse warehouse : warehousesList){
                logger.info("Evaluating \"" + warehouse.getName() + "\" against \"" + warehouseName + "\""
                        + " and the result is: " + warehouse.getName().equalsIgnoreCase(warehouseName));
                if (warehouse.getName().equalsIgnoreCase(warehouseName)){
                    targetWarehouse = warehouse;
                    logger.info("Found the target warehouse with ID: " + targetWarehouse.getId());
                    break;
                }
            }
        }
        return targetWarehouse;
    }

    public List<Order> listAllActiveOrdersByWarehouse(User adminUser, Warehouse warehouse, String targetDate){
        logger.info("Sending a getAll active orders to URL {}"
                , configs.getPickerServicesBaseURL() + listOrdersEndpoint);
        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .queryParam("page[limit]", 500)
                .queryParam("page[offset]", 0)
                .queryParam("fps[]", warehouse.getId())
                .queryParam("pending_orders_only", false)
                .queryParam("date", targetDate)
                .get(configs.getPickerServicesBaseURL() + listOrdersEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONArray retrievedList = new JSONObject(response.getBody().asString()).optJSONArray("data");
        List<JSONObject> ordersList;
        List<Order> allOrders = new ArrayList<>();
        if (!retrievedList.isEmpty()){
            logger.info("Total count of found orders in warehouse \"" + warehouse.getName() + "\" are: "
                    + retrievedList.length());
            ordersList = new OrdersDataParser().parseJsonArrayToListOfJsonObjects(retrievedList);
            for (JSONObject e : ordersList){
                allOrders.add(new OrdersDataParser().parseOrderJsonObject(e
                        , new OrdersDataParser().REQUEST_SOURCE_CONTROL_ROOM));
            }
        } else {
            logger.info("Found 0 orders in warehouse \"" + warehouse.getName()
                    + "\" and it's ID is: " + warehouse.getId() );
        }

        return allOrders;
    }

    public Order getOrderById(User adminUser, Warehouse warehouse, String targetDate, String orderId){
        logger.info("Starting to search the orders list using admin user: " + adminUser.getPhoneNumber()
                + " To find an order with the ID: " + orderId);
        Order targetOrder = null;
        List<Order> ordersList = listAllActiveOrdersByWarehouse(adminUser, warehouse, targetDate);

        if (ordersList.size() > 0){
            logger.info("Orders list has total count of: " + ordersList.size());
            for (Order order : ordersList){
                logger.info("Evaluating \"" + order.getOrderId() + "\" against \"" + orderId + "\""
                        + "and the result is: " + order.getOrderId().equalsIgnoreCase(orderId));
                if (order.getOrderId().equalsIgnoreCase(orderId)){
                    targetOrder = order;
                    logger.info("Found the target order with Number: " + order.getOrderNumber());
                    break;
                }
            }
        }

        return targetOrder;
    }

    public List<Order> filterOrdersByValue(User adminUser, Warehouse warehouse, String targetDate, String filterValue,
                                           String filterCriteria){
        logger.info("Starting to filter orders with criteria \"" + filterCriteria + "\" and value: \""
                + filterValue + "\" on warehouse \""
                + warehouse.getName() + "\" and date of \"" + targetDate + "\"");
        List<Order> filteredOrders = new ArrayList<>();
        List<Order> ordersList = listAllActiveOrdersByWarehouse(adminUser, warehouse, targetDate);

        if (ordersList.size() > 0){
            logger.info("Orders list has total count of: " + ordersList.size());
            for (Order order : ordersList) {
                boolean matchingOrderFound = false;
                switch(filterCriteria.toLowerCase()) {
                    case "timeslot" -> {
                        if (order.getTimeSlot().equalsIgnoreCase(filterValue))
                            matchingOrderFound = true;
                        break;
                    }
                    case "status" -> {
                        if (order.getStatus().equalsIgnoreCase(filterValue))
                            matchingOrderFound = true;
                        break;
                    }
                    case "area" -> {
                        if (order.getArea().equalsIgnoreCase(filterValue))
                            matchingOrderFound = true;
                        break;
                    }
                    case "subarea" -> {
                        if (order.getSubArea().equalsIgnoreCase(filterValue))
                            matchingOrderFound = true;
                        break;
                    }
                    case "delivery type" -> {
                        if (order.getNowTomorrowType().equalsIgnoreCase(filterValue))
                            matchingOrderFound = true;
                        break;
                    }
                    default -> {
                        logger.error("Invalid filter criteria \"" + filterCriteria + "\" used. Supported ones are " +
                                "(timeslot, status, area, subarea, delivery type)");
                        break;
                    }
                };
                if (matchingOrderFound)
                    filteredOrders.add(order);
            }
        }
        return filteredOrders;
    }

    public User loginToPickersApp(User picker){
        Response response = RestAssured.given()
                .contentType(ContentType.URLENC)
                .formParam("phone", configs.getPickerPhoneCountryCode().replaceAll("\\D+", "")
                        + picker.getLocalPhoneNumber())
                .formParam("password", picker.getBypassScriptPassword())
                .when()
                .post(configs.getBaseURL() + loginPicker)
                .then()
                .statusCode(200)
                .extract().response();

        //Fetching AuthToken from response json body
        picker.setAuthorizationToken(
                new JSONObject(response.getBody().asString()).optJSONObject("data").optString("token"));

        //Fetch cookies from response headers
        Map<String, String> cookies = response.cookies();
        Map<String, String> pickerCookies = new HashMap<>();
        pickerCookies.put(configs.getTestWpSecCookieName(), cookies.get(configs.getTestWpLoggedInCookieName()));
        pickerCookies.put(configs.getTestWpNodeAuthorizationCookieName(),
                cookies.get(configs.getTestWpNodeAuthorizationCookieName()));

        picker.setAdminAuthorisationCookies(pickerCookies);
        
        logger.info("logged in successfully with picker: {}", picker.getLocalPhoneNumber());

        return picker;
    }

    public void getUserData(User picker){
        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + picker.getAuthorizationToken())
                .header("content-type", "application/json")
                .when()
                .get(configs.getBaseURL() + getUserData)
                .then()
                .statusCode(200)
                .extract().response();
    }

    public void updatePickersUserStatus(User picker, String pickerStatus){
        Response response =  RestAssured.given()
                .header("Authorization", "Bearer " + picker.getAuthorizationToken())
                .header("content-type", "application/json")
                .param("long", configs.getTestLongitude())
                .param("lat", configs.getTestLatitude())
                .param("lang", "en")
                .param("newStatus", pickerStatus)
                .when()
                .put(configs.getBaseURL() + updatePickersStatus)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("Changed Picker status to \"" + pickerStatus + "\" for picker \"" + picker.getLocalPhoneNumber());
    }

    public Order getOrderDetailsById(User adminUser, String orderId, Order userOrder) {

        boolean isRequestSucceeded = false;
        Response response = null;
        int retryCount = 0;
        while (!isRequestSucceeded && retryCount < MAX_RETRIES) {
            logger.info("Trying to get Order Details from control Room. Trial number: {}", retryCount + 1);
            try {
                response = RestAssured.given()
                        .header("Authorization", "Bearer "
                                + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                        .get(configs.getPickerServicesBaseURL() + listOrdersEndpoint + "/" + orderId)
                        .then()
                        .extract()
                        .response();

                if (response.statusCode() == 200)
                    isRequestSucceeded = true;
                retryCount++;
            } catch (Exception e) {
                logger.error("Failed to get Order Details from control Room" + (retryCount + 1) + ". Error: " + e.getMessage());
                retryCount++;
            }
        }

        if (response == null || !isRequestSucceeded) {
            logger.error("Failed to get Order Details from control Room after {} attempts. Giving up.", MAX_RETRIES);
            return userOrder;
        }else {
            response.then()
                    .log().ifValidationFails()
                    .body("$", hasKey("data"))
                    .body("data", notNullValue());
        }

        JSONObject responseJson = new JSONObject(response.getBody().asString());

        JSONObject data = responseJson.optJSONObject("data");
            userOrder.setOrderId(String.valueOf(data.optInt("orderId")));
            userOrder.setOrderNumber(data.optString("orderNumber"));
            userOrder.setTimeSlot(data.optString("timeSlot"));
            userOrder.setTotalToCollect(data.optFloat("totalToCollect"));
            userOrder.setTotalAmount(String.format("%.2f",data.optFloat("totalToAmount")));
            userOrder.setCollectedAmount(String.valueOf(data.optFloat("collectedAmount")));

        JSONObject customer = data.optJSONObject("customer");
            userOrder.setCustomerId(customer.optInt("id"));
            userOrder.setCustomerName(customer.optString("name"));

        JSONObject fp = data.optJSONObject("fp");
            userOrder.setFpName(fp.optString("name"));
            userOrder.setFpId(fp.optString("fpId"));

        return userOrder;
    }

    public Product getProductLogStock(User orderOwner,
                                      String warehouseId,
                                      int productId,
                                      String orderId) {

        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + orderOwner.getAuthorizationToken())
                .contentType("application/json")
                .queryParam("productid",productId)
                .queryParam("warehouseid",warehouseId)
                .queryParam("orderId",orderId)
                .get(configs.getBaseURL() + productStockLogEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONArray productLogStockJsonObject = new JSONArray(response.getBody().asString());
        Product parsedProductLogStock = new ProductsParser().parseProductLogStockJsonObject(productLogStockJsonObject, Integer.parseInt(orderId));
        return parsedProductLogStock;
    }

    public void addStockToProduct(User adminUser, String warehouseId, int productId){
        logger.info("Sending a request to add stock to product with ID: {} in warehouse: {}...", productId, warehouseId);

        // Create the JSON request payload using JSONObject and JSONArray
        JSONObject productJsonOjbect = new JSONObject();
        productJsonOjbect.put("phpId", productId);
        productJsonOjbect.put("delta", 100);
        productJsonOjbect.put("reason", "AutomationRun - " + getCurrentTimeStamp("d MMMM, yyyy - HH:mm"));
        productJsonOjbect.put("productionDate",getCurrentTimeStamp("yyyy-M-dd"));

        JSONArray productsArray = new JSONArray();
        productsArray.put(productJsonOjbect);

        JSONObject requestBody = new JSONObject();
        requestBody.put("products", productsArray);

        try {
            Response response = RestAssured.given()
                    .config(RestAssuredConfig.config()
                            .httpClient(HttpClientConfig.httpClientConfig()
                                    .setParam("http.connection.timeout", 10000)
                                    .setParam("http.socket.timeout", 10000)
                                    ))
                    .header("Authorization", "Bearer "
                            + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                    .contentType("application/json")
                    .pathParam("warehouseId", warehouseId)
                    .body(requestBody.toString())
                    .post(configs.getBaseURL() + addStockToProductEndpoint )
                    .then()
                    .extract()
                    .response();

            response.then()
                    .log().ifValidationFails()
                    .statusCode(200)
                    .body("success", equalTo(true))
                    .body("movementId", notNullValue());

            logger.info("Added stock to product successfully. Product ID: {}, Movement ID: {}",
                    productId,
                    response.jsonPath().getInt("movementId"));
        } catch (Exception e) {
            logger.error("Failed to add stock to product. Product ID: {}, Warehouse ID: {}. Error: {}",
                    productId,
                    warehouseId,
                    e.getMessage());
        }
    }

    public void changeOrderStatus(User adminUser,
                                  String orderId,
                                  String orderStatus,
                                  float collectedAmount,
                                  boolean markAsFree) {
        logger.info("Mark Order As {} Process Started...", orderStatus);
        logger.info("Sending request to mark order as {} for Order ID: {}", orderStatus, orderId);
        String action = "", loggerKey  = "", actionHandler = "change-action";

        if (orderId == null || orderStatus == null){
            logger.error("""
                    A required parameter is null. Current values are:
                    orderId: {}
                    orderStatus: {}""", orderId, orderStatus);
            return;
        }

        switch (orderStatus.toLowerCase()){
            case "packed", "packing" -> {
                action = "packed";
                actionHandler = "packing";
                loggerKey = "packaged";
            }
            case "pickup", "pick-up", "pickedup" -> {
                action = "pickup";
                loggerKey = "received";
            }
            case "in-route", "inroute" -> {
                action = "in-route";
                loggerKey = "started";
            }
            case "delivering", "delivery" -> {
                action = "delivering";
                loggerKey = "arrived";
            }
            case "delivered" -> {
                action = "delivered";
                actionHandler = "delivered";
                loggerKey = "completed";
            }
            default -> {
                logger.error("Invalid order status {} used.", orderStatus);
                return;
            }
        }

        logger.info("Action Handler is: {} and Action is: {}", actionHandler, action);
        JSONObject requestBody = getRequestBodyBasedOnAction(actionHandler, action, collectedAmount, markAsFree);

        boolean isRequestSucceeded = false;
        Response response = null;
        int retryCount = 0;

        while (!isRequestSucceeded && retryCount < MAX_RETRIES) {
            logger.info("Trying to send mark order as {}. Trial number: {}", action, retryCount + 1);
            try {
                response = given()
                        .header("Authorization", "Bearer "
                                + adminUser.getAdminAuthorisationCookies().get(
                                        configs.getTestWpNodeAuthorizationCookieName())
                        )
                        .cookie(configs.getTestWpLoggedInCookieName()
                                , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                        .cookie(configs.getTestWpNodeAuthorizationCookieName()
                                , adminUser.getAdminAuthorisationCookies()
                                        .get(configs.getTestWpNodeAuthorizationCookieName()))
                        .header("content-type", "application/json")
                        .pathParams("orderId", orderId)
                        .pathParam("actionHandler", actionHandler)
                        .body(requestBody.toString())
                        .when()
                        .post(configs.getPickerServicesBaseURL() + changeOrderStatusEndpoint)
                        .then()
                        .extract().response();

                if (response.statusCode() == 201)
                    isRequestSucceeded = true; // If we reach this point, the request succeeded
                else {
                    logger.error("""
                                    Failed to mark order as {}.
                                    Trial {}
                                    Request: {}
                                    Response Status code: {}
                                    response body: {}""",
                            action, retryCount + 1, requestBody, response.statusCode(), response.getBody().asString());
                }

                retryCount++;
            } catch (Exception e) {
                logger.error("Failed to mark order as {} on trial {}. Error: {}", action, retryCount + 1, e.getMessage());
                retryCount++;
            }
        }

        if (response == null || !isRequestSucceeded) {
            logger.error("Failed to mark order as {} after {} attempts. Returning with no actions taken"
                    , action, MAX_RETRIES);
            return;
        } else {
            response.then()
                    .log().ifValidationFails()
                    .body("$", hasKey("data"))
                    .body("data", notNullValue())
                    .body("data.actions", notNullValue());
        }

        JSONObject responseObject = new JSONObject(response.getBody().asString());
        JSONObject actionsObject = responseObject.optJSONObject("data").optJSONObject("actions");

        if (actionsObject != null && !actionsObject.optString(loggerKey).isEmpty()) {
            logger.info("""
                            Order with Id: {} has been marked as {} successfully.
                            Timestamp of {} is: {}"""
                    , orderId, action, loggerKey, actionsObject.optString(loggerKey));
        } else {
            logger.warn("""
                        Received status code of 201. Yet,Changing status of order with Id: {} didn't respond correctly.
                        The current status of the order is: {}
                        Either actions object is null or the {} timestamp is empty.
                        API Response is: {}"""
                    , orderId, actionsObject.optString("current"), loggerKey, responseObject);
        }
    }

    @NotNull
    private JSONObject getRequestBodyBasedOnAction(String actionHandler,
                                                   String action,
                                                   float collectedAmount,
                                                   boolean markAsFree) {
        JSONObject requestBody = new JSONObject();

        switch (actionHandler.toLowerCase()){
            case "packing" -> {
                JSONArray bags = new JSONArray();

                JSONObject bigBag = new JSONObject();
                bigBag.put("bagId", BIG_BAG_ID);
                bigBag.put("expectedQuantity", 0);
                bigBag.put("actualQuantity", 0);

                JSONObject smallBag = new JSONObject();
                smallBag.put("bagId", SMALL_BAG_ID);
                smallBag.put("expectedQuantity", 0);
                smallBag.put("actualQuantity", 0);

                bags.put(bigBag);
                bags.put(smallBag);

                requestBody.put("bags", bags);
                requestBody.put("numberOfBags", 0);
                requestBody.put("shelf", 3);
            }
            case "delivered" -> {
                requestBody.put("collectedAmount", collectedAmount);
                requestBody.put("markAsFree", markAsFree);
            }
            default -> {
                requestBody.put("action", action);
            }
        }
        return requestBody;
    }
}
