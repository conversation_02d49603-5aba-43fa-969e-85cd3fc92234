package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.PlanningCenterDataParser;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import models.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

public class PlanningCenterApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(PlanningCenterApiClient.class);
    private final String internalCategoriesEndpoint = "/internal-categories";
    private final String manualForecastedCategoriesOrderEndpoint = "/planning-center/get-demand-plan";
    private final String automaticallyForecastedCategoriesOrderEndpoint = "/v2/planning-center";
    private final String updateMinimumAndSafetyProductEndpoint = "/planning-center/update-product-demand";
    private final String updateForecastedProductQuantityEndPoint = "/v2/planning-center/update-product-forecast";
    private final String createSchedulersEndPoint  = "/schedulers";
    private final String generateAndResetStatusEndpoint = "/internal-orders/generate";
    private final String processAndCalculateEndpoint = "/internal-orders/process";
    private final String syncToOdooEndpoint = "/internal-orders/sync";
    private final String getInternalOrderIdEndpoint = "/internal-orders/{schedulerId}";
    private final String getInternalOrdersProductsEndpoint = "/internal-orders/{id}/products";
    private final String getSchedulerDetailsEndpoint = "/schedulers/{id}";

    public PlanningCenterApiClient(Configs configs) {
        this.configs = configs;
    }

    public List<PlanningCenter> getInternalCategories(User adminUser) {
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)                
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .when()
                .get(configs.getInternalOrdersBaseURL() + internalCategoriesEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject internalCategoriesObject = new JSONObject(response.getBody().asString());
        List<PlanningCenter> parsedInternalCategories = new PlanningCenterDataParser()
                .parsedInternalCategoriesJsonObject(internalCategoriesObject);

        if (parsedInternalCategories.isEmpty()) {
            logger.error("internal Categories didn't get returned and couldn't find an id in the response below:");
            logger.error(response.getBody().asString());
        } else {
            for (PlanningCenter category : parsedInternalCategories) {
                logger.info("Successfully returned internal Categories with id \""
                        + category.getInternalCategoryId()
                        + "\" and isAutomated: \"" + category.isAutomated() + "\".");
            }
        }

        return parsedInternalCategories;
    }

    public Product getAutomaticallyForecastedCategories(User adminUser, Warehouse warehouse, PlanningCenter category) {
        category = findCategory(adminUser, true);

        logger.info("Category should be automated with : " + category.isAutomated()+
                "\n with category Id : " + category.getInternalCategoryId()+
                "\n with category Name : " +category.getInternalCategoryName());

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)                
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .queryParam("filter[fpIds]", warehouse.getId())
                .queryParam("filter[internalCategoryIds]", category.getInternalCategoryId())
                .queryParam("filter[date]", getCurrentTimeStamp("yyyy-MM-dd"))
                .when()
                .get(configs.getInternalOrdersBaseURL() + automaticallyForecastedCategoriesOrderEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject productsObject = new JSONObject(response.getBody().asString());
        Product parsedproducts = new PlanningCenterDataParser().
                parsedProductsJsonObject(productsObject);

        if(parsedproducts != null){
            logger.info("Successfully returned first internal Product with id \""
                    + parsedproducts.getMysqlId());
        } else {
            logger.error("internal Products didn't get returned and couldn't find an id in the response below:");
            logger.error(response.getBody().asString());
        }
        return parsedproducts;
    }

    public Product getManualForecastedCategories(User adminUser, Warehouse warehouse, PlanningCenter category) {
        category = findCategory(adminUser, false);

        logger.info("Category should be non-automated with : " + category.isAutomated()+
                "\n with category Id : " + category.getInternalCategoryId()+
                "\n with category Name : " +category.getInternalCategoryName());

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)                
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .queryParam("filter[fpIds]", warehouse.getId())
                .queryParam("filter[internalCategoryIds]", category.getInternalCategoryId())
                .when()
                .get(configs.getInternalOrdersBaseURL() + manualForecastedCategoriesOrderEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject productsObject = new JSONObject(response.getBody().asString());
        Product parsedproducts = new PlanningCenterDataParser().
                parsedProductsJsonObject(productsObject);

        if(parsedproducts != null){
            logger.info("Successfully returned first internal Product with id \""
                    + parsedproducts.getMysqlId());
        } else {
            logger.error("internal Products didn't get returned and couldn't find an id in the response below:");
            logger.error(response.getBody().asString());
        }
        return parsedproducts;
    }

    public void updateMinimumAndSafetyProductStock (User adminUser, Warehouse warehouse, PlanningCenter category, Product product, int stock, boolean minimumStock){
        category = findCategory(adminUser, false);

        String requestBody;
        if(minimumStock == true){
            requestBody = "{\n" +
                    "    \"productId\":" + product.getMysqlId() + ",\n" +
                    "    \"minStock\": " + stock+ ",\n" +
                    "    \"internalCategoryId\": " + category.getInternalCategoryId() + ",\n" +
                    "    \"fpId\": \"" + warehouse.getId() + "\"\n" +
                    "}";
        }else {
            requestBody = "{\n" +
                    "    \"productId\":" + product.getMysqlId() + ",\n" +
                    "    \"safetyStock\": " + stock+ ",\n" +
                    "    \"internalCategoryId\": " + category.getInternalCategoryId() + ",\n" +
                    "    \"fpId\": \"" + warehouse.getId() + "\"\n" +
                    "}";
        }

        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .contentType(ContentType.JSON)
                .body(requestBody)
                .when()
                .put(configs.getInternalOrdersBaseURL() + updateMinimumAndSafetyProductEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        if (response.jsonPath().getString("status").equals("success")) {
            logger.info("Stock of Product updated successfully");
        } else {
            logger.error("Failed to Stock");
        }
    }

    public void updateForecastedProductQuantity (User adminUser, Warehouse warehouse, PlanningCenter category, Product product, int forecastQuantity){
        category = findCategory(adminUser, true);

        String requestBody = "{\n" +
                "    \"productId\": " + product.getMysqlId() + ",\n" +
                "    \"fpId\": \"" + warehouse.getId() + "\",\n" +
                "    \"forecastQuantity\": " + forecastQuantity + ",\n" +
                "    \"internalCategoryId\": " + category.getInternalCategoryId() + ",\n" +
                "    \"date\": \"" + getCurrentTimeStamp("yyyy-MM-dd") + "\"\n" +
                "}";

        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .contentType(ContentType.JSON)
                .body(requestBody)
                .when()
                .put(configs.getInternalOrdersBaseURL() + updateForecastedProductQuantityEndPoint)
                .then()
                .statusCode(200)
                .extract().response();

        if (response.jsonPath().getString("status").equals("success")) {
            logger.info("Product forecast updated successfully for product {}", product.getMysqlId());
        } else {
            logger.error("Failed to update Product Quantity");
        }
    }

    public PlanningCenter createSchedulers(User adminUser, Warehouse warehouse, PlanningCenter category,
                                           String restockRule, boolean isAutomatic, String deliveryDay,
                                           boolean everyDay, boolean futureTime) {

        category = findCategory(adminUser, isAutomatic);

        String requestBody = buildRequestBody(warehouse, category, restockRule, deliveryDay, everyDay, futureTime);

        logger.info("Category should be with : " + category.isAutomated() +
                "\n with category Id : " + category.getInternalCategoryId() +
                "\n with category Name : " + category.getInternalCategoryName());

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)                
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .body(requestBody)
                .when()
                .post(configs.getInternalOrdersBaseURL() + createSchedulersEndPoint)
                .then()
                .statusCode(201)
                .extract().response();

        JSONObject responseObject = new JSONObject(response.getBody().asString());
        PlanningCenter schedulerId = new PlanningCenter();

        if ("success".equals(responseObject.optString("status"))) {
            schedulerId.setSchedulerId(responseObject.optInt("payload"));
            logger.info("Scheduler created successfully with {}", schedulerId.getSchedulerId());
        } else {
            logger.info("Failed to create scheduler");
        }

        return schedulerId;
    }

    public void getSchedulerDetails(User adminUser, PlanningCenter scheduler) {

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .pathParams("id", scheduler.getSchedulerId())
                .when()
                .get(configs.getInternalOrdersBaseURL() + getSchedulerDetailsEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject responseJson = new JSONObject(response.getBody().asString());

        if(response.jsonPath().getString("status").equals("success")){
            logger.info("Successfully returned scheduler order details");
            scheduler.setStatusId(
                    responseJson.optJSONObject("payload")
                            .optInt("statusId"));
        } else {
            logger.error("scheduler order details didn't get returned and couldn't find an id in the response below:");
            logger.error(response.getBody().asString());
        }
    }

    public void deleteScheduler(User adminUser, PlanningCenter scheduler) {

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .pathParams("id", scheduler.getSchedulerId())
                .when()
                .delete(configs.getInternalOrdersBaseURL() + getSchedulerDetailsEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        if(response.jsonPath().getString("status").equals("success")){
            logger.info("Successfully deleted scheduler");
        } else {
            logger.error("scheduler order details didn't get deleted :");
            logger.error(response.getBody().asString());
        }
    }

    public PlanningCenter getInternalOrders(User adminUser, int schedulerId) {

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .pathParams("schedulerId", schedulerId)
                .when()
                .get(configs.getInternalOrdersBaseURL() + getInternalOrderIdEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        PlanningCenter parsedObject = new PlanningCenter();
        JSONObject internalOrdersObject = new JSONObject(response.getBody().asString());

        JSONArray payloadArray = internalOrdersObject.optJSONArray("payload");
        if (payloadArray != null) {
            parsedObject.setPayload(payloadArray);
        } else {
            parsedObject.setPayload(new JSONArray());
        }

        if (parsedObject.getPayload() != null && !parsedObject.getPayload().isEmpty()) {
            logger.info("Successfully returned internal orders");
            JSONObject internalOrderObject = parsedObject.getPayload().getJSONObject(0);
            parsedObject.setInternalOrderId(internalOrderObject.optInt("id"));
            parsedObject.setStatusId(internalOrderObject.optInt("statusId"));
            parsedObject.setReadyToSync(internalOrderObject.optInt("readyToSync"));
            parsedObject.setTriedToSendBefore(internalOrderObject.optInt("triedToSendBefore"));
        } else {
            logger.info("internal Orders didn't get created");
        }

        return parsedObject;
    }

    public PlanningCenter getInternalOrdersProducts(User adminUser, int productId) {

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .pathParams("id", productId)
                .when()
                .get(configs.getInternalOrdersBaseURL() + getInternalOrdersProductsEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject internalOrdersProductsObject = new JSONObject(response.getBody().asString());
        PlanningCenter parsedInternalOrdersProducts = new PlanningCenterDataParser().
                parsedInternalOrderProductsJsonObject(internalOrdersProductsObject);

        if(response.jsonPath().getString("status").equals("success")){
            logger.info("Successfully returned internal orders products");
        } else {
            logger.error("internal Orders Products didn't get returned and couldn't find an id in the response below:");
            logger.error(response.getBody().asString());
        }
        return parsedInternalOrdersProducts;
    }

    private String buildRequestBody(Warehouse warehouse, PlanningCenter category,
                                    String restockRule, String deliveryDay,
                                    boolean everyDay, boolean futureTime) {

        int recurrenceTypeId = everyDay ? 1 : 2;
        String placingTime;
        if (futureTime == true){
            placingTime = everyDay ? getUTCZoneCurrentTimeStamp("HH:mm", 3) : null;
        }else{
            placingTime = everyDay ? getPastUTCZoneTimeStamp("HH:mm") : null;
        }
        String customSchedule = everyDay ? "[]" :
                "[\n" +
                        "        {\n" +
                        "            \"day\": " + getNextDayOfWeek() + ",\n" +
                        "            \"time\": \"" + getCurrentTimeStamp("HH:mm") + "\"\n" +
                        "        }\n" +
                        "    ]";
        return "{\n" +
                "    \"name\": \"test\",\n" +
                "    \"internalCategoryId\": " + category.getInternalCategoryId() + ",\n" +
                "    \"fpIds\": [\n" +
                "        \"" + warehouse.getId() + "\"\n" +
                "    ],\n" +
                "    \"restockRuleId\": " + getRestockRule(restockRule) + ",\n" +
                "    \"isActive\": true,\n" +
                "    \"placingTime\": " + (placingTime == null ? "null" : "\"" + placingTime + "\"") + ",\n" +
                "    \"deliveryDay\": " + getDeliveryDay(deliveryDay) + ",\n" +
                "    \"deliveryTime\": \"" + getDeliveryTimeRange("hh:mm a", 1) + "\",\n" +
                "    \"recurrenceTypeId\": " + recurrenceTypeId + ",\n" +
                "    \"custom\": " + customSchedule + ",\n" +
                "    \"internalCategoryTypeId\": 1\n" +
                "}";
    }

    public void generateAndResetStatus(User adminUser){

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .queryParam("reset", "false")
                .when()
                .post(configs.getInternalOrdersBaseURL() + generateAndResetStatusEndpoint)
                .then()
                .statusCode(201)
                .extract().response();

        if (response.jsonPath().getString("status").equals("success")) {
            logger.info("Generate and Reset Status Successfully");
        } else {
            logger.error("Failed to Generate and Reset Status");
        }
    }

    public void processAndCalculate(User adminUser){

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .when()
                .post(configs.getInternalOrdersBaseURL() + processAndCalculateEndpoint)
                .then()
                .statusCode(201)
                .extract().response();

        if (response.jsonPath().getString("status").equals("success")) {
            logger.info("Generate and Reset Status Successfully");
        } else {
            logger.error("Failed to Generate and Reset Status");
        }
    }

    public void syncToOdoo(User adminUser){

        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .when()
                .post(configs.getInternalOrdersBaseURL() + syncToOdooEndpoint)
                .then()
                .statusCode(201)
                .extract().response();

        if (response.jsonPath().getString("status").equals("success")) {
            logger.info("Sync To Odoo Successfully");
        } else {
            logger.error("Failed to Sync To Odoo");
        }
    }

    private PlanningCenter findCategory(User adminUser, boolean isAutomated) {
        List<PlanningCenter> categories = getInternalCategories(adminUser);
        PlanningCenter selectedCategory = null;

        if (!categories.isEmpty()) {
            selectedCategory = categories.get(0);
        }
        return selectedCategory;
    }

    private int getRestockRule(String restockRule) {
        return switch (restockRule.toLowerCase()) {
            case "all safety stock", "all safety" -> 1;
            case "all minimum stock", "all minimum" -> 2;
            case "below safety to safety" -> 3;
            case "below minimum to minimum" -> 4;
            default -> 5;
        };
    }

    private int getDeliveryDay(String deliveryDay) {
        return switch (deliveryDay.toLowerCase()) {
            case "next day" -> 1;
            default -> 0;
        };
    }

    public String getDeliveryTimeRange(String format, int durationInHours) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Calendar calendar = Calendar.getInstance();

        // Start time
        String startTime = sdf.format(calendar.getTime());

        // End time
        calendar.add(Calendar.HOUR, durationInHours);
        String endTime = sdf.format(calendar.getTime());

        return startTime + " - " + endTime;
    }

    private int getNextDayOfWeek() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK); // Get the day of the week

        switch (dayOfWeek) {
            case Calendar.SATURDAY:
                return 1;
            case Calendar.SUNDAY:
                return 2;
            case Calendar.MONDAY:
                return 3;
            case Calendar.TUESDAY:
                return 4;
            case Calendar.WEDNESDAY:
                return 5;
            case Calendar.THURSDAY:
                return 6;
            case Calendar.FRIDAY:
                return 7;
            default:
                return -1;
        }
    }

}
