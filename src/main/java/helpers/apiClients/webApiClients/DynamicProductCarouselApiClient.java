package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import io.restassured.response.Response;
import models.Configs;
import models.Product;
import models.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import static io.restassured.RestAssured.given;

public class DynamicProductCarouselApiClient extends BaseHelper {

    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(DynamicProductCarouselApiClient.class);
    private final String carouselEndpoint = "/dynamic-product-carousel/configs";

    public DynamicProductCarouselApiClient(Configs configs) {
        this.configs = configs;
    }

    private String getCarouselStatus (String status) {
        return switch (status.toLowerCase()) {
            case "active" -> "active";
            case "inactive" -> "inactive";
            default -> "inactive";
        };
    }
public List<Product> createProductCarousel(User adminUser, String sectionEName, String sectionARName, String[] fpsIds, List<Product> productIds,  String status, boolean serveNow, boolean serveLater) {
    logger.info("Starting to create a dynamic product carousel");

    // Extract product IDs from the Product list
    List<Integer> productIdList = productIds.stream()
            .map(Product::getMysqlId)
            .collect(Collectors.toList());
    // Build the request body
    JSONObject requestBody = new JSONObject();
    JSONObject section = new JSONObject();
    JSONObject name = new JSONObject();
    name.put("en", sectionEName);  // English name
    name.put("ar", sectionARName);  // Arabic name

    section.put("name", name);
    section.put("fps", fpsIds);
    section.put("productIds", productIdList);
    section.put("status", getCarouselStatus(status));
    section.put("sortStrategy", "random");
    JSONObject serve = new JSONObject();
    serve.put("now", serveNow);
    serve.put("later", serveLater);
    section.put("serve", serve);
    requestBody.put("section", section);

    // Make the API call
    Response response = given()
            .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
            .header("Content-Type", "application/json")
            .body(requestBody.toString())
            .post(configs.getBaseURL() + carouselEndpoint)
            .then().extract().response();

    // Log the raw API response for debugging
    logger.info("Raw API response: " + response.asString());
    // Extract product IDs from the response
    List<Integer> productIdsFromResponse = new ArrayList<>();
    try {
        JSONObject responseJson = new JSONObject(response.asString());
        JSONArray productIdsArray = responseJson.optJSONObject("section").optJSONArray("productIds");
        if (productIdsArray != null) {
            for (int i = 0; i < productIdsArray.length(); i++) {
                productIdsFromResponse.add(productIdsArray.getInt(i));
            }
        }
    } catch (Exception e) {
        logger.error("Failed to parse product IDs from the API response", e);
        throw new RuntimeException("Error while parsing product IDs: " + e.getMessage());
    }

    // Find the Product objects that match the product IDs from the response
    List<Product> matchingProducts = new ArrayList<>();
    for (Product product : productIds) {
        logger.info("Checking product: " + product.getMysqlId());
        if (productIdsFromResponse.contains(product.getMysqlId())) {
            matchingProducts.add(product);
        }
    }

    if (matchingProducts.isEmpty()) {
        logger.error("No matching products found for the product IDs returned by the API.");
        throw new RuntimeException("No matching products found for the product IDs returned by the API.");
    }

    return matchingProducts;  // Return the list of matching products
}

    public List<String> listProductCarouselIds(User adminUser, int page, int limit) {
        logger.info("Fetching dynamic product carousel configs with page: " + page + " and limit: " + limit);

        Response response = given()
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .header("Content-Type", "application/json")
                .queryParam("page", page)
                .queryParam("limit", limit)
                .when()
                .get(configs.getBaseURL() + carouselEndpoint)
                .then()
                .extract()
                .response();
        return extractIdsFromResponse(response.asString());
    }

    private List<String> extractIdsFromResponse(String responseBody) {
        List<String> ids = new ArrayList<>();
        try {
            JSONObject responseJson = new JSONObject(responseBody);
            JSONArray sectionsArray = responseJson.optJSONArray("sections");

            if (sectionsArray != null) {
                for (int i = 0; i < sectionsArray.length(); i++) {
                    JSONObject section = sectionsArray.getJSONObject(i);
                    if (section.has("_id")) {
                        ids.add(section.getString("_id"));
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Failed to parse _id values from API response", e);
        }
        logger.info("Extracted _id values: " + ids);
        return ids;
    }

    public boolean deleteCarouselConfig(User adminUser) {
        logger.info("Fetching dynamic product carousel configs to get the first _id...");

        // Fetch the list of IDs from the first API (Listing API)
        List<String> ids = listProductCarouselIds(adminUser, 1, 5);

        if (ids.isEmpty()) {
            logger.error("No _id found to delete.");
            return false;
        }

        // Get the first _id from the list
        String firstId = ids.get(0);
        logger.info("Using _id: " + firstId + " for deletion.");

        // Perform DELETE request
        Response response = given()
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .delete(configs.getBaseURL() + "/dynamic-product-carousel/configs/" + firstId)
                .then().extract().response();
        return response.getStatusCode() == 200;
    }
}
