package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.OrdersDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.Order;
import models.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class MidMileOrdersApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(ControlRoomV2ApiClient.class);
    private final String midMileDashboardOrdersEndPoint = "/api/orders";
    private final String midMileAssignOrderEndPoint = "/api/orders/assign";
    public MidMileOrdersApiClient(Configs configs) {
        this.configs = configs;
    }

    public List<Order> listMidMileDashboardOrdersWithFilters(User adminUser, String date) {
        logger.info("Getting the orders list for user with ID \"" + adminUser.getId() + "\"");
        logger.info("Sending a getAllOrdersUser request to endpoint \"" +
                midMileDashboardOrdersEndPoint + "\"");

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .header("content-type", "application/json")
                .param("date", date)
                .get(configs.getMidMileAppBaseURL() + midMileDashboardOrdersEndPoint)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("ListMidMileOrders Request completed and received a response code of 200");

        JSONArray retrievedList = new JSONObject(response.getBody().asString()).optJSONArray("data");
        List<JSONObject> ordersList;
        List<Order> allOrders = new ArrayList<>();
        if (!retrievedList.isEmpty()) {
            ordersList = new OrdersDataParser().parseJsonArrayToListOfJsonObjects(retrievedList);
            logger.info("Total count of found orders is " + retrievedList.length());
            logger.info("Starting to parse each order in the list...");
            for (JSONObject e : ordersList) {
                allOrders.add(new OrdersDataParser().parseOrderJsonObject(e
                        , new OrdersDataParser().REQUEST_SOURCE_MIDMILE_APP));
            }
            logger.info("Orders processing completed and count of processed orders is: " + allOrders.size());
        } else {
            logger.error("Found 0 orders.");
        }

        logger.error("Getting the orders list for user with ID \"" + adminUser.getId() + "\" and phoneNumber \""
                + adminUser.getPhoneNumber() + "\" failed and full API response is below \n"
                + response.getBody().asString());

        return allOrders;
    }
    public Response assignOrderToDispatcher(User midMileUser,Order orderID){
        String body ="{\n" +
                "  \"dispatcherId\": "+midMileUser.getId()+",\n" +
                "  \"ordersIds\": ["+orderID.getOrderId()+"]\n" +
                "}";
        return RestAssured.given()
                .header("Authorization", "Bearer "
                        + midMileUser.getAuthorizationToken())
                .header("content-type", "application/json")
                .body(body)
                .post(configs.getMidMileAppBaseURL() + midMileAssignOrderEndPoint)
                .then()
                .statusCode(200)
                .extract().response();
    }
}
