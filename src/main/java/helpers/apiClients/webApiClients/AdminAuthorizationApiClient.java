package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class AdminAuthorizationApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(AdminAuthorizationApiClient.class);
    private final String loginEndPoint = "/wp-admin/admin-ajax.php";

    public AdminAuthorizationApiClient(Configs configs) {
        this.configs = configs;
    }

    /**
     * Perform admin login and retrieve authorization tokens.
     *
     * @param phoneNumber  The phone number.
     * @param bypassScriptPass The bypass script pass.
     * @param user     The user object.
     * @return The updated test data.
     */
    public User loginAndGetAuthorizationTokens(String phoneNumber, String bypassScriptPass, User user){
        logger.info("Sending a POST request to the admin login endpoint: \"{}\" using admin phone {}",
                loginEndPoint+configs.getBaseURL(),
                phoneNumber);
        // Create a request and send the POST request
        Response response = RestAssured.given()
                .header("content-type", "application/x-www-form-urlencoded; charset=UTF-8")
                .formParam("phone", phoneNumber)
                .formParam("password", bypassScriptPass)
                .param("action", "u_user_login")
                .when()
                .post(configs.getBaseURL() + loginEndPoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        // Get the response cookies
        Map<String, String> cookies = response.cookies();
        Map<String, String> adminCookies = new HashMap<>();

        // Store the needed cookies
        adminCookies.put(configs.getTestWpSecCookieName(), cookies.get(configs.getTestWpSecCookieName()));
        adminCookies.put(configs.getTestWpLoggedInCookieName(), cookies.get(configs.getTestWpLoggedInCookieName()));
        adminCookies.put(configs.getTestWpNodeAuthorizationCookieName(),
                cookies.get(configs.getTestWpNodeAuthorizationCookieName()));

        user.setAdminAuthorisationCookies(adminCookies);
        user.setAuthorizationToken(user.getAdminAuthorisationCookies()
                .get(configs.getTestWpNodeAuthorizationCookieName()));
        logger.info("Admin authorization cookies retrieved and saved in User object.");
        return user;
    }
}
