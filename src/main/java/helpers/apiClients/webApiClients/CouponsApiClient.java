package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.CouponDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.Coupon;
import models.Product;
import models.User;
import org.json.JSONArray;
import org.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class CouponsApiClient extends BaseHelper {
    Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(CouponsApiClient.class);

    private final String addCouponEndpoint = "/wp-json/coupons-api/v1/coupons";

    public CouponsApiClient(Configs configs) {
        this.configs = configs;
    }

    /**
     * Creates a coupon using the provided coupon code, discount type, theme, and other specified parameters.
     * This method is designed for use by administrators to generate coupons with custom configurations.
     *
     * @param adminUser        The admin user object, required for authorization purposes.
     * @param verticalType     The vertical type of the coupon. Valid values are ("supermarket", "grocery", "restaurant", "food aggregator")
     * @param couponCode       The unique code for the new coupon.
     * @param discountType     The type of discount the coupon applies. Valid values are ("percent", "percentage", "%", "fixed", "free_delivery", "free delivery", "back_to_wallet", "back to wallet")
     * @param couponTheme      The theme or category this coupon belongs to. Valid values are "commercial"
     * @param amount           The discount amount. This could represent a percentage or a fixed discount amount, based on the discount type. Importantly noted, this value can be used for Back To Wallet Percentage coupon type for percent value.
     * @param maxAmount        The max amount for maximum value to be deducted in percentage cart only.
     * @param percentValue     The percent value for percentage cart only.
     * @param activationStatus Indicates the coupon's activation status. Valid values are (active, expired).
     * @param isActive         A boolean indicating if the coupon is currently active. Valid values are (true, false).
     * @param isDisplayedInApp A boolean indicating if the coupon should be displayed in the app. Valid values are (true, false).
     * @param orderType        Specifies the type of orders the coupon applies to. Valid values are (tomorrow, later, now, both)
     * @param isPercent        A boolean indicating if the back to wallet coupon is a percentage or a fixed discount amount. Valid values are (true, false)
     * @return Coupon A Coupon object representing the newly created coupon with its properties set as per the arguments.
     */
    public Coupon createCouponUsingCouponCode(User adminUser,
                                              String couponCode,
                                              String discountType,
                                              String verticalType,
                                              String couponTheme,
                                              int amount,
                                              int maxAmount,
                                              int percentValue,
                                              String activationStatus,
                                              boolean isActive,
                                              boolean isDisplayedInApp,
                                              boolean isPercent,
                                              String orderType) {
        logger.info("Starting to create a coupon with the following characteristics:" +
                "\n Coupon Code: {}" +
                "\n Discount Type: {}" +
                "\n Vertical Type: {}" +
                "\n Coupon Theme: {}" +
                "\n Amount: {}" +
                "\n maxAmount: {}" +
                "\n percentValue: {}" +
                "\n Activation Status: {}" +
                "\n Is Active: {}" +
                "\n Is Displayed In App: {}" +
                "\n Is Percent: {}" +
                "\n Order Type: {}" +
                "\n Endpoint is: {}",
                couponCode,discountType, verticalType, couponTheme, amount, maxAmount, percentValue,
                activationStatus, isActive, isDisplayedInApp, isPercent, orderType, addCouponEndpoint);

        JSONObject couponJsonObject = new JSONObject()
                .put("type", getDiscountType(discountType))
                .put("coupon_type", "single_code")
                .put("code", couponCode)
                .put("title", new JSONObject().put("en", "English Title").put("ar", "عنوان الكوبون"))
                .put("vertical", getVerticalType(verticalType))
                .put("description", new JSONObject().put("en", "This is an English Description").put("ar", "هذا وصف للكوبون باللغة العربية"))
                .put("app_display", isDisplayedInApp)
                .put("active", isActive)
                .put("coupon_theme", getCouponTheme(couponTheme))
                .put("constrains", new JSONObject().put("general",
                        new JSONObject()
                                .put("order_type", getOrderType(orderType))
                                .put("start_date", getStartDate())
                                .put("end_date", getEndDate(activationStatus))
                ))
                .put("type_info", new JSONObject()
                        .put("amount", amount)
                        .put("max_amount", maxAmount)
                        .put("percent_value", percentValue)
                        .put("isPercent", isPercent)
                );

        String requestBody = couponJsonObject.toString();

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getBaseURL() + addCouponEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(201)
                .extract().response();

        JSONObject couponObject = new JSONObject(response.getBody().asString());
        Coupon parsedCoupon = new CouponDataParser().parseCouponJsonObject(couponObject);

        if (couponObject.has("id")) {
            logger.info("Successfully created a coupon with code \""
                    + parsedCoupon.getCouponCode()
                    + "\" and id \"" + parsedCoupon.getId() + "\".");
        } else {
            logger.error("Coupon code didn't get created and couldn't find an id in the response below:");
            logger.error(response.getBody().asString());
        }
        return parsedCoupon;
    }

    /**
     * @param adminUser        The admin user object, required for authorization purposes.
     * @param couponCode       The unique code for the new coupon.
     * @param discountType     The type of discount the coupon applies. Valid values are ("free_gift", "free gift")
     * @param couponTheme      The theme or category this coupon belongs to. Valid values are "commercial"
     * @param quantity         Indicates the quantity of the products.
     * @param activationStatus Indicates the coupon's activation status. Valid values are (active, expired).
     * @param isActive         A boolean indicating if the coupon is currently active. Valid values are (true, false)
     * @param isDisplayedInApp A boolean indicating if the coupon should be displayed in the app. Valid values are (true, false).
     * @param product          indicates the product that is on discount or gift in a coupon
     * @param orderType        Specifies the type of orders the coupon applies to. Valid values are (tomorrow, later, now, both)
     * @return Coupon A Coupon object representing the newly created coupon with its properties set as per the arguments.
     */
    public Coupon createDiscountProductOrFreeGiftCoupon(User adminUser,
                                                        String couponCode,
                                                        String discountType,
                                                        String couponTheme,
                                                        int quantity,
                                                        String activationStatus,
                                                        boolean isActive,
                                                        boolean isDisplayedInApp,
                                                        Product product,
                                                        String orderType) {
        logger.info("Starting to create a coupon with the following characteristics:"
                + "\n Coupon Code: " + couponCode
                + "\n Discount Type: " + discountType
                + "\n Coupon Theme: " + couponTheme
                + "\n Quantity: " + quantity
                + "\n Activation Status: " + activationStatus
                + "\n Is Active: " + isActive
                + "\n Is Displayed In App: " + isDisplayedInApp
                + "\n Product: " + product
                + "\n Order Type: " + orderType);

        String requestBody = new JSONObject()
                .put("type", getDiscountType(discountType))
                .put("code", couponCode)
                .put("coupon_type", "single_code")
                .put("vertical", "grocery")
                .put("app_display", isDisplayedInApp)
                .put("active", isActive)
                .put("coupon_theme", getCouponTheme(couponTheme))
                .put("constrains", new JSONObject()
                        .put("general", new JSONObject()
                                .put("start_date", getStartDate())
                                .put("end_date", getEndDate(activationStatus))
                                .put("order_type", getOrderType(orderType))
                        )
                )
                .put("type_info", new JSONObject()
                        .put("products", new JSONArray()
                                .put(new JSONObject()
                                        .put("qty", quantity)
                                        .put("id", product.getMysqlId())
                                )
                        )
                ).toString();

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getBaseURL() + addCouponEndpoint)
                .then()
                .statusCode(201)
                .extract().response();

        JSONObject couponObject = new JSONObject(response.getBody().asString());
        Coupon parsedCoupon = new CouponDataParser().parseCouponJsonObject(couponObject);

        if (couponObject.has("id")) {
            logger.info("Successfully created a coupon with code \""
                    + parsedCoupon.getCouponCode()
                    + "\" and id \"" + parsedCoupon.getId() + "\".");
        } else {
            logger.error("Coupon code didn't get created and couldn't find an id in the response below:");
            logger.error(response.getBody().asString());
        }
        return parsedCoupon;
    }

    // Create a start date that is today - 2days
    private static String getStartDate() {
        String pattern = "yyyy-MM-dd HH:mm:ss";
        LocalDateTime currentDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return currentDateTime.minusDays(2).format(formatter);
    }

    public static String getEndDate(String status) {
        String pattern = "yyyy-MM-dd HH:mm:ss";
        LocalDateTime currentDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);

        switch (status.toLowerCase()) {
            case "expired" -> {
                // Return current date
                return getStartDate();
            }
            default -> {
                // Return current date + 2 days
                LocalDateTime validDateTime = currentDateTime.plusDays(2);
                return validDateTime.format(formatter);
            }
        }
    }

    private String getCouponTheme(String theme) {
        return switch (theme.toLowerCase()) {
            case "commercial" -> "Commercial";
            default -> "Commercial";
        };
    }

    private String getOrderType(String orderType) {
        return switch (orderType.toLowerCase()) {
            case "tomorrow", "later" -> "later";
            case "now" -> "now";
            default -> "both";
        };
    }

    private String getDiscountType(String discountType) {
        return switch (discountType.toLowerCase()) {
            case "percent", "percentage", "%" -> "percent";
            case "free_delivery", "free delivery" -> "free_delivery";
            case "back_to_wallet", "back to wallet" -> "back_to_wallet";
            case "free_gift", "free gift" -> "free_gift";
            default -> "fixed_cart";
        };
    }

    private String getVerticalType(String verticalType) {
        return switch (verticalType.toLowerCase()) {
            case "supermarket", "grocery" -> "grocery";
            case "restaurant", "food aggregator" -> "restaurant";
            default -> "grocery";
        };
    }
}
