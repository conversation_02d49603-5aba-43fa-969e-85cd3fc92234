package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.TrucksParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.Truck;
import models.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class MidMileTrucksApiClient extends BaseHelper {
    private final Configs configs;
    private final String getAllTrucksEndPoint="/api/trucks?lang=en";
    private static final Logger logger = LoggerFactory.getLogger(ControlRoomV2ApiClient.class);
    public MidMileTrucksApiClient(Configs configs) {
        this.configs = configs;
    }
    public List<Truck> listMidMileDashboardTrucks(User adminUser) {
        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .header("content-type", "application/json")
                .get(configs.getMidMileAppBaseURL() + getAllTrucksEndPoint)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("ListMidMileTrucks Request completed and received a response code of 200");
        JSONArray retrievedList = new JSONArray(response.getBody().asString());
        List<JSONObject> trucksJsonList;
        List<Truck> allTrucks = new ArrayList<>();

        if (!retrievedList.isEmpty()) {
            trucksJsonList = new TrucksParser().parseJsonArrayToListOfJsonObjects(retrievedList);
            logger.info("Total count of found trucks: " + retrievedList.length());
            logger.info("Starting to parse each truck in the list...");

            for (JSONObject e : trucksJsonList) {
                allTrucks.add(new TrucksParser().parseTruckJsonObject(e));
            }

            logger.info("Truck processing completed. Total processed trucks: " + allTrucks.size());
        } else {
            logger.error("Found 0 trucks.");
        }
        return allTrucks;
    }
}
