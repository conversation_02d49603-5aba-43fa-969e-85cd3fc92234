package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.OrderPaymentDataParser;
import helpers.dataParsers.OrdersDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.Order;
import models.Role;
import models.User;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import static helpers.dataParsers.OrdersDataParser.convertHtmlToJson;
import static org.hamcrest.Matchers.*;

public class SwitcherApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(SwitcherApiClient.class);
    private final String searchForUserEndpoint = "/wp-json/coupons-api/v1/coupons/users";
    private final String getUserEndpoint = "/wp-admin/admin-ajax.php";
    private final String updateBalanceEndpoint = "/wp-admin/admin-ajax.php";
    private final String getBalanceByUserIdEndpoint = "/wp-json/balance-management/v1/users/%s/balance";
    private final String updateUserReferralStatusEndpoint = "/wp-admin/admin-ajax.php";
    private final String updateUserRoleEndpoint = "/wp-admin/admin-ajax.php?action=save_user_data";
    private final String getUsersOrdersSwitcherEndpoint="/wp-admin/admin-ajax.php?action=get_user_orders";
    private final String refundBalanceEndpoint = "/wp-admin/admin-ajax.php?action=refund_balance";
    private final String refundTransactionEndpoint = "/wp-admin/admin-ajax.php?action=get_refund_transaction";
    private final String getUserOrdersEndpoint ="/wp-json/breadfast/v3/get_orders";

    private static final int MAX_RETRIES = 10;
    private static final int RETRY_DELAY_MS = 5000;

    public SwitcherApiClient(Configs configs) {
        this.configs = configs;
    }

    public User searchForUser(String searchTerm, User adminUser) {
        User foundUser = new User();

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .param("search", searchTerm)
                .when()
                .get(configs.getBaseURL() + searchForUserEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        if (response.getStatusCode() == 200 && new JSONArray(response.getBody().asString()).length() > 0) {
            logger.info("Found 1 or more results matching the term: " + searchTerm
                    + "\nusing admin credentials with phoneNumber: " + adminUser.getPhoneNumber());
            JSONObject matchedUser = new JSONObject(new JSONArray(response.getBody().asString()).get(0).toString());
            foundUser.setId(matchedUser.optString("id"));
            foundUser.setFullName(matchedUser.optString("name"));
            foundUser.setEmailAddress(matchedUser.optString("email"));
            foundUser.setPhoneNumber(matchedUser.optString("phone"));
            logger.info("Stored users info after searching with term: " + searchTerm);
        } else if (response.getStatusCode() == 200 && new JSONArray(response.getBody().asString()).length() == 0) {
            logger.error("Searching for term: " + searchTerm + " and Found zero results"
                    + "\nusing admin credentials with phoneNumber: " + adminUser.getPhoneNumber());
            foundUser = null;
        } else {
            logger.error("Search request for term: " + searchTerm
                    + "\nusing admin credentials with phoneNumber: " + adminUser.getPhoneNumber()
                    + "\nfailed with status code: " + response.getStatusCode());
            foundUser = null;
        }

        return foundUser;
    }

    public User getUserInfoById(String userId, User adminUser) {
        logger.info("Sending request to GET user info for ID: " + userId);
        int getUserByIdRetryCount = 0;
        boolean isGetUserByIdSuccessful = false;
        Response response = null;
        while (!isGetUserByIdSuccessful && getUserByIdRetryCount < MAX_RETRIES) {
            try {
                response = RestAssured.given()
                        .header("Authorization", "Bearer "
                                + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                        .cookie(configs.getTestWpLoggedInCookieName()
                                , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                        .cookie(configs.getTestWpNodeAuthorizationCookieName()
                                , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                        .param("action", "get_user_data")
                        .formParam("user", userId)
                        .when()
                        .post(configs.getBaseURL() + getUserEndpoint)
                        .then()
                        .extract().response();

                if (response.statusCode() == 200) {
                    isGetUserByIdSuccessful = true;
                    logger.info("Request to get User info by ID succeeded for id {} and retry count of {}"
                            , userId, getUserByIdRetryCount + 1);
                    break;
                } else {
                    getUserByIdRetryCount++;
                    logger.error("Request to get User info by ID failed for id {} and retry count of {}"
                            , userId, getUserByIdRetryCount + 1);
                }
            } catch (Exception e) {
                getUserByIdRetryCount++;
                logger.error("Process to fetch the user details with ID" + userId + "failed with exception: ", e);
            }
        }

        if (response != null && isGetUserByIdSuccessful) {
            JSONObject userJson = new JSONObject(response.getBody().asString());
            User user = new User();
            if (!userJson.isNull("id") && userJson.optString("id").equalsIgnoreCase(userId)) {
                logger.info("Data is retrieved for user with ID: " + userId);
                user.setId(userJson.optString("id"));
                user.setFullName(userJson.optString("fname") + " " + userJson.optString("lname"));
                user.setCurrentBalance(userJson.optString("balance"));
                return user;
            } else {
                logger.error("There are no matching records for user with ID: " + userId);
                return null;
            }
        } else {
            logger.error("Saved Response is null. Retrieving order failed in trial # {}", getUserByIdRetryCount);
            return null;
        }
    }

    public User getBalanceByUserId(String userId, User adminUser) {
        logger.info("Sending request to GET Balance by user ID: " + userId);
        String formattedEndpoint = String.format(getBalanceByUserIdEndpoint, userId);
        User user = new User();

        try {
            Response response = RestAssured.given()
                    .header("Authorization", "Bearer "
                            + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                    .cookie(configs.getTestWpLoggedInCookieName(),
                            adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                    .cookie(configs.getTestWpNodeAuthorizationCookieName(),
                            adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                    .get(configs.getBaseURL() + formattedEndpoint)
                    .then()
                    .log().ifValidationFails()
                    .statusCode(200)
                    .body("$", hasKey("balance"))
                    .body("balance", notNullValue())
                    .extract().response();

            if (response != null && response.getBody() != null) {
                JSONObject userBalanceObject = new JSONObject(response.getBody().asString());

                user.setCurrentBalance(userBalanceObject.optString("balance"));
                logger.info("Data is retrieved for user with ID: {} and balance is \"{}\""
                        , userId, user.getCurrentBalance());
            } else {
                logger.error("Response is null or body is empty for user ID: {}", userId);
            }
        } catch (Exception e) {
            logger.error("An error occurred while retrieving balance for user ID: {}", userId, e);
        }

        if (user == null) {
            logger.error("There are no matching records for user with ID: " + userId);
        }

        return user;
    }

    public User getUserInfoOrBalanceById(String userId, User adminUser) {
        User user = null;
        int retryCount = 0;
        boolean isSuccessful = false;

        while (!isSuccessful && retryCount < MAX_RETRIES) {
            try {
                user = getUserInfoById(userId, adminUser);
                if (user != null) {
                    isSuccessful = true;
                    logger.info("Successfully retrieved user info by ID: " + userId + " on attempt " + (retryCount + 1));
                } else {
                    retryCount++;
                    logger.warn("Failed to retrieve user info by ID: " + userId + " on attempt " + retryCount);
                }
            } catch (Exception e) {
                retryCount++;
                logger.error("Exception occurred while retrieving user info by ID: " + userId + " on attempt " + retryCount, e);
            }
        }

        if (!isSuccessful) {
            logger.info("Attempting to retrieve balance by user ID: " + userId);

            while (!isSuccessful && retryCount < MAX_RETRIES) {
                try {
                    user = getBalanceByUserId(userId, adminUser);
                    if (user != null) {
                        isSuccessful = true;
                        logger.info("Successfully retrieved balance by user ID: " + userId + " on attempt " + (retryCount + 1));
                    } else {
                        retryCount++;
                        logger.warn("Failed to retrieve balance by user ID: " + userId + " on attempt " + retryCount);
                    }
                } catch (Exception e) {
                    retryCount++;
                    logger.error("Exception occurred while retrieving balance by user ID: " + userId + " on attempt " + retryCount, e);
                }
            }
        }

        if (!isSuccessful) {
            logger.error("Failed to retrieve user info or balance by user ID: " + userId + " after " + MAX_RETRIES + " attempts");
        }

        return user;
    }

    public User updateUserBalanceByPhoneNumber(String userBalance, User adminUser, String phoneNumber) {
        userBalance = String.valueOf(new DecimalFormat("0.00").format(Double.parseDouble(userBalance)));
        logger.info("Update balance process started");
        logger.info("Sending request to get the userObject for phoneNumber: {}", phoneNumber);
        User user = searchForUser(phoneNumber, adminUser);
        if (user != null) {
            logger.info("Sending request to update User balance for phoneNumber: {}", phoneNumber);
            boolean isRequestSucceeded = false;
            Response response = null;
            int retryCount = 0;
            while (!isRequestSucceeded && retryCount < MAX_RETRIES) {
                logger.info("Trying to send an update balance request. Trial number: {}", retryCount + 1);
                try {
                    response = RestAssured.given()
                            .header("Authorization", "Bearer "
                                    + adminUser.getAdminAuthorisationCookies()
                                    .get(configs.getTestWpNodeAuthorizationCookieName()))
                            .cookie(configs.getTestWpSecCookieName()
                                    , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpSecCookieName()))
                            .cookie(configs.getTestWpLoggedInCookieName()
                                    , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                            .cookie(configs.getTestWpNodeAuthorizationCookieName()
                                    , adminUser.getAdminAuthorisationCookies()
                                            .get(configs.getTestWpNodeAuthorizationCookieName()))
                            .param("action", "update_user_balance")
                            .formParam("id", user.getId())
                            .formParam("balance[newBalance]", userBalance)
                            .when()
                            .post(configs.getBaseURL() + updateBalanceEndpoint)
                            .then()
                            .extract().response();

                    if (response.statusCode() == 200) {
                        isRequestSucceeded = true;
                        logger.info("Received status code of 200 for update balance for phoneNumber: {}", phoneNumber);
                    } else {
                        retryCount++;
                        logger.error("Update balance request of try {} for phoneNumber {}", retryCount + 1, phoneNumber);
                        logger.error("Full response log is below: {}", response.getBody().asString());
                    }
                } catch (Exception e) {
                    logger.error("Sending an update balance request failed with exception: ", e);
                    retryCount++;
                    logger.info("Retrying to update user balance...");
                    try {
                        Thread.sleep(RETRY_DELAY_MS); // Delay before retrying
                    } catch (Exception ee) {
                        logger.error("Trying to delay the thread for 5 seconds failed with exception: ", ee);
                    }
                }
            }

            if (response != null && isRequestSucceeded) {
                JSONObject userObject = new JSONObject(response.getBody().asString());
                if (userObject.optString("balance").equalsIgnoreCase(userBalance)) {
                    logger.info("Update balance successful. Sent a request for: " + userBalance
                            + "\n The current value is " + userObject.optString("balance"));
                } else {
                    logger.error("Updating the balance failed and there are difference in result than the request: "
                            + "\nSend the request for: " + userBalance
                            + "\nReceived in the response: " + userObject.optString("balance"));
                }
                user = getUserInfoById(user.getId(), adminUser);
            } else {
                logger.error("FATAL: Update balance failed for number: {}", phoneNumber);
            }
        }
        return user;
    }

    public User setUserReferralStatusByPhoneNumber(Boolean isReferralsDisabled, User adminUser, String phoneNumber) {
        logger.info("Set User Referral Status Process Started");
        logger.info("Sending request to get the userObject for phoneNumber: " + phoneNumber);
        User user = searchForUser(phoneNumber, adminUser);
        if (user != null) {
            logger.info("Sending request to set referral status to " + isReferralsDisabled +
                    " for user with phoneNumber " + phoneNumber);
            Response response = RestAssured.given()
                    .header("Authorization", "Bearer "
                            + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                    .cookie(configs.getTestWpSecCookieName()
                            , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpSecCookieName()))
                    .cookie(configs.getTestWpLoggedInCookieName()
                            , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                    .cookie(configs.getTestWpNodeAuthorizationCookieName()
                            , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                    .param("action", "save_user_data")
                    .formParam("id", user.getId())
                    .formParam("data[referrals_disabled]", isReferralsDisabled)
                    .when()
                    .post(configs.getBaseURL() + updateUserReferralStatusEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response();

            JSONObject userObject = new JSONObject(response.getBody().asString());

            if (userObject.optJSONObject("referrals").optBoolean("status") != isReferralsDisabled) {
                logger.info("Set user referral status was successful.Sent a request for [referrals_disabled]: " + isReferralsDisabled +
                        "\nReceived in the response " + "referrals.status: " + userObject.optJSONObject("referrals").optBoolean("status"));
            } else {
                logger.error("Set user referral status failed and there is a difference in result than the request:"
                        + "\nSent a request for [referrals_disabled]: " + isReferralsDisabled
                        + "\nReceived in the response: " + userObject.optJSONObject("referrals").optBoolean("status"));
            }
            user = getUserInfoById(user.getId(), adminUser);
        }
        return user;
    }

    public User changeUserRole(User adminUser, User targetUser, String roleName) {
        if (targetUser != null) {

            Response response = RestAssured.given()
                    .urlEncodingEnabled(false)
                    .contentType("application/x-www-form-urlencoded")
                    .param("action", "save_user_data")
                    .header("cookie", configs.getTestWpLoggedInCookieName() + "="
                            + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()) + ";")
                    .formParam("id", targetUser.getId())
                    .formParam("data[roles][]", roleName)
                    .when()
                    .post(configs.getBaseURL() + updateUserRoleEndpoint);
            response.then()
                    .statusCode(200)
                    .extract().response();

            JSONObject userObject = new JSONObject(response.getBody().asString());
            targetUser.setRole(new Role());
            targetUser.getRole().setRoleName(userObject.getJSONArray("roles").getString(0));

            if (userObject.getJSONArray("roles").toString().contains(targetUser.getRole().getRoleName())) {
                logger.info("Role '{}' successfully assigned to user {}", roleName, targetUser.getId());
            } else {
                logger.error("Failed to assign role '{}' to user {}", roleName, targetUser.getId());
            }
        }
        return targetUser;
    }

    /**
     * This method sends a request to refund an order
     *
     * @param adminUser    The admin user object initiating the action , required for authorization purposes.
     * @param orderId      The ID of the order to be refund.Order has to be in completed status
     * @param isWallet     A boolean indicating if refund will be returned on wallet or credit card. Valid values are (true, false)
     * @param refundReason The reason of refund required . Valid values are ("delivery issue", "delivery-issue", "product issue", "product-issue")
     * @param productId    This is the id of product to be refunded in case choosing refund reason is "product issue".
     */
    public Order refundBalance(User adminUser,
                               Order order,
                               String orderId,
                               float refundAmount,
                               boolean isWallet,
                               String refundReason,
                               int productId) {

        logger.info("Initiating refund process for Order ID: {}", orderId);

        String reason;
        String reasonDetails = "";
        boolean refund = true;
        int productQty = 1;

        switch (refundReason.toLowerCase()) {
            case "delivery issue", "delivery-issue" -> {
                reason = "delivery-issue";
                reasonDetails = "late-delivery";
            }
            case "product issue", "product-issue" -> {
                reason = "product-issue";
                reasonDetails = "item-missing";
            }
            case "order-bad-experience", "order bad experience"->{
                reason = "order-bad-experience";
                reasonDetails = "bad-da-attitude";
            }
            case "gratuity issue", "gratuity-issue" -> {
                reason = "gratuity-issue";
                reasonDetails = "bad-da-attitude";
            }
            default -> {
                reason = "delivery-issue";
                reasonDetails = "late-delivery";
            }
        }

        String requestBody = "toWallet=" + isWallet +
                "&balance%5Border_id%5D=" + orderId +
                "&balance%5Bamount%5D=" + refundAmount +
                "&balance%5Breason%5D=" + reason +
                "&balance%5Breason_details%5D%5B%5D=" + reasonDetails +
                "&balance%5Bproducts_with_issue%5D%5B0%5D%5Bid%5D=" + productId +
                "&balance%5Bproducts_with_issue%5D%5B0%5D%5Bqty%5D=" + productQty +
                "&refund=" + refund;

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpSecCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpSecCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .header("content-type", "application/x-www-form-urlencoded; charset=UTF-8")
                .body(requestBody)
                .when()
                .post(configs.getBaseURL() + refundBalanceEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject responseObject = new JSONObject(response.getBody().asString());

        Order parsedRefund = new OrderPaymentDataParser().parseRefundJsonObject(responseObject, order);

        if (responseObject.optBoolean("success", true)) {
            logger.info("Refund successful. Refund transaction ID: {}", parsedRefund.getRefundTransactionId());
        } else {
            logger.warn("Refund failed for Order ID: {}", orderId);
        }
        return parsedRefund;
    }

    public Order refundTransaction(User adminUser, Order order, String refundTransactionId) {

        logger.info("Sending request to get refund transaction for Transaction Order ID: " + refundTransactionId);

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpSecCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpSecCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .header("content-type", "application/x-www-form-urlencoded; charset=UTF-8")
                .formParam("refund_transaction_id", refundTransactionId)
                .when()
                .post(configs.getBaseURL() + refundTransactionEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        String responseBody = response.getBody().asString();
        JSONObject responseObject = new JSONObject(responseBody);

        Order parsedRefundTransaction = new OrderPaymentDataParser().parseRefundTransactionJsonObject(responseObject, order);

        return parsedRefundTransaction;
    }

    public Order getUserCompletedOrders(User orderOwner , User adminUser ,int numberOfOrders) {
        logger.info("Sending request to get last {} completed Orders for user with id {}" ,numberOfOrders,orderOwner.getId());

        boolean isRequestSucceeded = false;
        Response response = null;
        int retryCount = 0;

        while (!isRequestSucceeded && retryCount < MAX_RETRIES) {
            logger.info("Trying to get {} completed orders request. Trial number: {}", numberOfOrders, retryCount + 1);
            try {
                response = RestAssured.given()
                        .header("Authorization", "Bearer "
                                + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                        .header("content-type", "application/x-www-form-urlencoded; charset=UTF-8")
                        .queryParam("limit", numberOfOrders)
                        .queryParam("user_id", orderOwner.getId())
                        .when()
                        .get(configs.getBaseURL() + getUserOrdersEndpoint)
                        .then()
                        .extract().response();

                if (response.statusCode() == 200) {
                    isRequestSucceeded = true;
                    logger.info("Received status code of 200 for get {} completed order request for user ID {} "
                            , numberOfOrders, orderOwner.getId());
                }
                else {
                    retryCount++;
                    logger.error("Getting {} completed orders request of try {} for user ID {}"
                            ,numberOfOrders, retryCount + 1, orderOwner.getId());
                    logger.error("Failed response body is: {}", response.getBody().asString());
                }
            } catch (Exception e) {
                logger.error("Sending get completed orders request failed with exception: ", e);
                retryCount++;
                logger.info("Retrying to get completed orders retry count: " + retryCount + "...");
            }
        }

        if (response != null && isRequestSucceeded) {

            logger.info("Getting User's completed Orders from Switcher was successful , Parsing Orders Data..");
            JSONObject responseObject = new JSONObject(response.getBody().asString());
            return new OrdersDataParser().parseOrderJsonObject(responseObject.optJSONObject("data")
                    , new OrdersDataParser().REQUEST_SOURCE_SWITCHER);
        }
        else {
            logger.error("FATAL: Getting Last {} Completed orders for user with id {} failed,Giving Up.."
                    , numberOfOrders, orderOwner.getId());
            return null;
        }
    }

    public List<Order> getAllOrders(User adminUser, int OrderOwnerId, boolean returnData, Integer offset) {
        logger.info("Getting the orders list for user with ID \"" + OrderOwnerId + "\"");
        logger.info("Sending a getAllOrdersUser request to endpoint \"" + getUsersOrdersSwitcherEndpoint + "\"");

        // Send the POST request with form data
        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName(), adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName(), adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .multiPart("id", OrderOwnerId)
                .multiPart("return_data", String.valueOf(returnData))
                .multiPart("offset", offset)
                .post(configs.getBaseURL() + getUsersOrdersSwitcherEndpoint);

        // Log the response body
        String responseBody = response.getBody().asString();
        logger.info("Raw Response Body: " + responseBody);

        // Check if the response is HTML
        String contentType = response.header("Content-Type");
        if (contentType != null && contentType.contains("text/html")) {
            // Handle HTML response
            String htmlResponse = responseBody;
            // Convert HTML to JSON
            JSONObject jsonFromHtml = convertHtmlToJson(htmlResponse);
            if (jsonFromHtml == null) {
                logger.error("Failed to convert HTML to JSON");
                return new ArrayList<>();
            }
            // Log the JSON object
            logger.info("Converted JSON from HTML: " + jsonFromHtml);

            // Parse the JSON object into a list of orders
            List<Order> orders = new ArrayList<>();
            try {
                JSONArray ordersArray = jsonFromHtml.names();
                for (int i = 0; i < ordersArray.length(); i++) {
                    String key = ordersArray.getString(i);
                    JSONObject orderJson = jsonFromHtml.getJSONObject(key); // Use key to get JSONObject
                    orders.add(new OrdersDataParser().parseOrderJsonObject(orderJson, new OrdersDataParser().REQUEST_SOURCE_CUSTOMER_APP));
                }
            } catch (JSONException e) {
                logger.error("Failed to parse orders from JSON: ", e);
            }
            return orders;
        } else {
            // Handle JSON response
            List<Order> allOrders = new ArrayList<>();
            try {
                JSONArray responseBodyJsonArray = new JSONArray(responseBody);
                logger.info("Total count of found orders is " + responseBodyJsonArray.length());
                logger.info("Starting to parse each order in the list...");
                for (int i = 0; i < responseBodyJsonArray.length(); i++) {
                    if (responseBodyJsonArray.get(i) instanceof JSONObject) {
                        JSONObject orderJson = responseBodyJsonArray.getJSONObject(i);
                        logger.info("Parsing order: " + orderJson.toString());
                        allOrders.add(new OrdersDataParser().parseOrderJsonObject(orderJson, new OrdersDataParser().REQUEST_SOURCE_CUSTOMER_APP));
                    } else {
                        logger.error("Expected a JSONObject but found: " + responseBodyJsonArray.get(i).getClass().getSimpleName());
                    }
                }
                logger.info("User Orders count are : " + allOrders.size());
            } catch (Exception e) {
                logger.error("Failed to process the orders response: ", e);
                logger.error("Getting the orders list for user with ID \"" + OrderOwnerId + "\" and phoneNumber \""
                        + OrderOwnerId + "\" failed and full API response is below \n" + responseBody);
            }
            return allOrders;
        }
    }
}
