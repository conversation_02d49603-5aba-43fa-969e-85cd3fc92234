package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.TransferDataParser;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import models.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class TransitApiClient extends BaseHelper {
    private final Configs configs;
    private final String getTransfersEndpoint = "/api/transfer";
    private final String getTransfersCountEndpoint = "/api/transfer/count";
    private final String getTransferDetailsEndpoint = "/api/transfer/{transferId}";
    private final String deleteTransferProductLine = "/api/transfer-product-line/delete";
    private final String postChangeDestinationEndpoint = "/api/transfer/{transferId}/change-destination";
    private final String postDestinationLocationsEndpoint = "/api/location/search";
    private final String changeDestinationProductLine = "/api/transfer-product-line/";
    private final String searchEndPoint="/api/transfer/search";
    private static final Logger logger = LoggerFactory.getLogger(TransitApiClient.class);
    private final String sendTransferEndPoint = "/api/transfer/{transferId}/send";

    public TransitApiClient(Configs configs) {
        this.configs = configs;
    }

    public Response getTransfersResponse(User adminUser, String warehouseId, int skip, String[] statusIds) {
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .queryParam("warehouseId", warehouseId)
                .queryParam("skip", skip)
                .queryParam("statusIds", statusIds)
                .get(configs.getTransitBaseURL() + getTransfersEndpoint)
                .then()
                .extract().response();
        logger.info("Called  GET transfer endpoint and response code is: " + response.statusCode());
        return response;
    }

    public Transfer getTransferDetailsResponse(User adminUser, Transfer transfer) {
        logger.info("The transfer id is : " + transfer.getId());
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .pathParam("transferId", transfer.getId())
                .get(configs.getTransitBaseURL() + getTransferDetailsEndpoint)
                .then()
                .extract().response();
        logger.info("Called  GET transfer details endpoint and response code is: " + response.statusCode());
        JSONObject responseObject = new JSONObject(response.getBody().asString());
        transfer.setProductLineID(responseObject.optJSONObject("payload")
                .optJSONArray("groupedProductLines").optJSONObject(0).
                optJSONArray("transferProductLines").getJSONObject(0).optInt("id"));
        logger.info("productLineID= " + transfer.getProductLineID());
        return transfer;
    }

    public List<Transfer> getAllTransfers(User adminUser, String warehouseId) {
        String allTransferJsonResponse = getTransfersResponse(adminUser, warehouseId, 0, new String[]{"1", "2", "3"})
                .then()
                .statusCode(200)
                .extract().body().asString();
        JSONObject transferObject = new JSONObject(allTransferJsonResponse);
        List<Transfer> parsedTransfers = new TransferDataParser().parseTransfersJsonObject(transferObject);
        for (Transfer transfer : parsedTransfers) {
            logger.info("Number of Products: " + transfer.getNumberOfProducts());
            logger.info("Transfer ID: " + transfer.getId());
        }
        return parsedTransfers;
    }

    public int getSumNumberOfTransfers(User adminUser, Warehouse warehouse) {
        int totalTransfers = 0;
        do {
            Response response = getTransfersResponse(adminUser, warehouse.getId(), totalTransfers, new String[]{"1", "2", "3"});
            JSONObject jsonObject = new JSONObject(response.asString());
            JSONArray payLoadArray = jsonObject.getJSONArray("payload");
            if (payLoadArray.length() == 0) {
                break;
            }
            for (int i = 0; i < payLoadArray.length(); i++) {
                JSONObject payLoadObject = payLoadArray.getJSONObject(i);
                int numberOfTransfers = payLoadObject.getJSONArray("transfers").length();
                System.out.println("Number of transfers: " + numberOfTransfers);
                totalTransfers += numberOfTransfers;
            }
        }
        while (totalTransfers % 30 == 0);
        return totalTransfers;
    }

    public Response getTransfersCountResponse(User adminUser, Warehouse warehouse) {
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .queryParam("warehouseId", warehouse.getId())
                .get(configs.getTransitBaseURL() + getTransfersCountEndpoint)
                .then()
                .extract().response();
        logger.info
                ("Called  GET transfer count endpoint and response code is: " + response.statusCode());
        return response;
    }

    public int getNotSentTransfersCount(User adminUser, Warehouse warehouse) {
        Response response = getTransfersCountResponse(adminUser, warehouse);
        response.getBody().asString();
        int notSentTotal = response.jsonPath().getInt("payload.notSent");
        return notSentTotal;
    }

    public int getSentTransfersCount(User adminUser, Warehouse warehouse) {
        Response response = getTransfersCountResponse(adminUser, warehouse);
        response.getBody().asString();
        int sentTotal = response.jsonPath().getInt("payload.sent");
        return sentTotal;
    }

    public List<Transfer> getAllNotSentTransfers(User adminUser, String warehouseId) {
        String allTransferJsonResponse = getTransfersResponse(adminUser, warehouseId, 0, new String[]{"1"})
                .then()
                .statusCode(200)
                .extract().body().asString();
        JSONObject transferObject = new JSONObject(allTransferJsonResponse);
        List<Transfer> parsedTransfers = new TransferDataParser().parseTransfersJsonObject(transferObject);
        return parsedTransfers;
    }

    public List<Transfer> getAllSentTransfers(User adminUser, String warehouseId) {
        String allTransferJsonResponse = getTransfersResponse(adminUser, warehouseId, 0, new String[]{"3"})
                .then()
                .extract().body().asString();
        JSONObject transferObject = new JSONObject(allTransferJsonResponse);
        List<Transfer> parsedTransfers = new TransferDataParser().parseTransfersJsonObject(transferObject);
        return parsedTransfers;
    }

    public Response deleteTransferProductLine(User adminUser, Transfer productLineId) {
        logger.info("Initiating delete transfer product line request for productID: {}", productLineId.getProductLineID());
        JSONObject requestBody = new JSONObject();
        requestBody.put("transferProductLineId", productLineId.getProductLineID());
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .body(requestBody.toString())
                .post(configs.getTransitBaseURL() + deleteTransferProductLine)
                .then()
                .extract().response();
        if (response.getStatusCode() == 404) {
            logger.info("The product line is successfully deleted and response code is: {}", response.statusCode());
        } else if (response.getStatusCode() == 404) {
            logger.info("Transfer ID is not found and response body is: {}", response.getBody().asString());
        } else {
            logger.error("unexpected status code: {}", response.statusCode());
        }
        return response;
    }

    public Transfer getNumberOfProductsEqualOne(User adminUser, String warehouseId) {
        List<Transfer> transfers = getAllNotSentTransfers(adminUser, warehouseId);
        Transfer transferWithExactlyOneProduct = null;
        if (!transfers.isEmpty()) {
            for (Transfer t : transfers) {
                int numberOfProducts = t.getNumberOfProducts();
                if (numberOfProducts == 1 && transferWithExactlyOneProduct == null) {
                    transferWithExactlyOneProduct = t;
                    logger.info("Found transfer with exactly 1 product. Transfer ID: " + t.getId());
                }
            }
        }
        return transferWithExactlyOneProduct;
    }

    public Response getTransfer(User adminUser, Warehouse warehouse, int statusIds) {
        Response response = RestAssured.given().contentType(ContentType.JSON)
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .queryParam("warehouseId", warehouse.getId())
                .queryParam("statusIds", statusIds)
                .get(configs.getTransitBaseURL() + getTransfersEndpoint)
                .then()
                .extract().response();
        return response;
    }

    public Response sendTransfer(String transferId, User adminUser) {
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .pathParam("transferId", transferId)
                .post(configs.getTransitBaseURL() + sendTransferEndPoint)
                .then()
                .extract().response();
        return response;

    }

    public Transfer getNumberOfProductsMoreThanOne(User adminUser, String warehouseId) {
        List<Transfer> transfers = getAllNotSentTransfers(adminUser, warehouseId);
        Transfer transferWithMoreThanOneProduct = null;
        if (!transfers.isEmpty()) {
            for (Transfer t : transfers) {
                int numberOfProducts = t.getNumberOfProducts();
                if (numberOfProducts > 1 && transferWithMoreThanOneProduct == null) {
                    transferWithMoreThanOneProduct = t;
                    logger.info("Found transfer with more than 1 product. Transfer ID: " + t.getId());
                }
            }
        }
        return transferWithMoreThanOneProduct;
    }

    public Response deleteTransferHaveMoreThanOneProduct(User adminUser, Transfer productLineId) {
        String requestBody = "{\n" +
                "    \"transferProductLineId\": " + productLineId.getProductLineID() + "\n" +
                "}";
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .body(requestBody)
                .post(configs.getTransitBaseURL() + deleteTransferProductLine)
                .then()
                .statusCode(201)
                .extract().response();
        logger.info("The product line is successfully deleted and response code is: {}", response.getStatusCode());
        return response;
    }

    public List<TransferDestinationLocations> destinationLocationsList(User adminUser) {
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .post(configs.getTransitBaseURL() + postDestinationLocationsEndpoint)
                .then()
                .extract().response();

        List<TransferDestinationLocations> destinationLocations = new ArrayList<>();
        JSONArray destinationLocationsArray = new JSONObject(response.getBody().asString()).optJSONArray("payload");
        if (!destinationLocationsArray.isEmpty()) {
            for (int i = 0; i < destinationLocationsArray.length(); i++) {
                JSONObject destinationLocationsObject = destinationLocationsArray.optJSONObject(i);
                TransferDestinationLocations transferDestinationLocations = new TransferDestinationLocations();
                transferDestinationLocations.setId(destinationLocationsObject.optInt("id"));
                transferDestinationLocations.setName(destinationLocationsObject.optString("name"));
                destinationLocations.add(transferDestinationLocations);
            }
        }
        return destinationLocations;
    }

    public boolean checkTransferLocation(User adminUser, String warehouseId) {
        Transfer firstSentTransfer = getAllSentTransfers(adminUser, warehouseId).getFirst();
        return firstSentTransfer.getDestinationName().equals(destinationLocationsList(adminUser).getFirst().getName());
    }

    public Response changeDestination(User adminUser, Transfer transfer, Warehouse warehouse, TransferDestinationLocations destination) {

        if (!checkTransferLocation(adminUser, warehouse.getId())) {
            JSONObject requestBody = new JSONObject();
            requestBody.put("destinationId", String.valueOf(destination.getId()));
            Response response = RestAssured.given()
                    .contentType(ContentType.JSON)
                    .header("Authorization", "Bearer "
                            + adminUser.getAuthorizationToken())
                    .pathParam("transferId", transfer.getId())
                    .body(requestBody.toString())
                    .post(configs.getTransitBaseURL() + postChangeDestinationEndpoint)
                    .then()
                    .extract().response();
            logger.info("Called  Post change destination endpoint and response code is: " + response.statusCode());
            return response;
        } else {
            logger.info("The transfer location is the same as the destination location");
            return null;
        }
    }
    public boolean checkNotSentTransferLocation(User adminUser, String warehouseId) {
        Transfer firstNotSentTransfer = getNumberOfProductsEqualOne(adminUser, warehouseId);
        return firstNotSentTransfer.getDestinationName().equals(destinationLocationsList(adminUser).getFirst().getName());
    }
    public Response changeDestinationOfNotSentTransfer(User adminUser, Warehouse warehouse,Transfer productLineId, TransferDestinationLocations destination) {
        boolean isSameDestination = checkNotSentTransferLocation(adminUser, warehouse.getId());
        if (isSameDestination) {
            logger.info("New destination is the same as the current destination. No change needed.");
            return null;
        }
        String requestBody = "{\n" +
                "    \"destinationId\": " + destination.getId() + "\n" +
                "}";
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .body(requestBody)
                .post(configs.getTransitBaseURL() + changeDestinationProductLine + productLineId.getProductLineID() + "/change-destination/")
                .then()
                .statusCode(201)
                .extract().response();

        logger.info("The destination is successfully changed and response code is: " + response.statusCode());
        return response;
    }

    public Response searchForTransfer(User adminUser, String warehouse,Transfer transfer) {
        String requestBody = "{\n" +
                "    \"searchInput\": \"" + transfer.getId() + "\",\n" +
                "    \"warehouseId\": \n" +
                "        \"" + warehouse + "\"\n" +
                "    \n" +
                "}";
        Response response = RestAssured.given()
                .contentType(ContentType.JSON)
                .header("Authorization", "Bearer " + adminUser.getAuthorizationToken())
                .body(requestBody)
                .post(configs.getTransitBaseURL() + searchEndPoint )
                .then()
                .statusCode(201)
                .extract().response();
        logger.info
                ("The transfer is successfully found and response code is: " + response.statusCode());
        return response;
    }
}
