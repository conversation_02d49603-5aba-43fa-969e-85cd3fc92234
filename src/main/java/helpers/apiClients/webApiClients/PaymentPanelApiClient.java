package helpers.apiClients.webApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.OrderPaymentDataParser;
import helpers.dataParsers.OrdersDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.Order;
import models.User;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PaymentPanelApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(PaymentPanelApiClient.class);
    private final String loginEndPoint = "/api/business/auth/login";
    private final String checkTransactionStatusEndpoint = "/api/business/transactions/{transactionId}/provider-status";
    private final String voidPaymentEndpoint = "/api/business/orders/{orderTransactionId}/void";
    private final String getOrderTransactionDetailsEndpoint ="/api/business/orders/{orderPaymentId}";
    private final String refundEndpoint = "/api/business/orders/{orderTransactionId}/refund";
    private static final int MAX_RETRIES = 10;
    private static final int RETRY_DELAY_MS = 5000;
    public PaymentPanelApiClient(Configs configs) {
        this.configs = configs;
    }

    public User loginToPaymentPanel(String email, String bypassScriptPass, User user) {
        logger.info("Sending a login request to paymentPanel using endpoint {} using email: {}",
                loginEndPoint, email);

        String requestBody = "{\n" +
                "    \"email\": \"" + email + "\",\n" +
                "    \"password\": \"" + bypassScriptPass + "\"\n" +
                "}";

        // Create a request and send the POST request
        Response response = RestAssured.given()
                .header("content-type", "application/json")
                .body(requestBody)
                .when()
                .post(configs.getPaymentServiceBaseURL() + loginEndPoint)
                .then()
                .extract().response();

        if (response.statusCode() == 200) {
            user.setAuthorizationToken(
                    new JSONObject(response.getBody().asString()).getString("token"));
        } else {
            logger.error("Payment panel Auth token creation failed as API call failed with status code: {} " +
                            "and response: {}", response.statusCode(), response.getBody().asString());
        }

        logger.info("User authorization token retrieved and saved in User object.");

        return user;
    }

    public String checkTransactionStatus(User orderOwner, String transactionId) {
        logger.info("Check Transaction Status Process has started ....");

        if (transactionId == null || transactionId.isEmpty()){
            logger.error("TransactionID is null or empty. Can't proceed.");
            return null;
        }
        
        boolean isRequestSucceeded = false;
        Response response = null;
        int retryCount = 0;

        while (!isRequestSucceeded && retryCount < MAX_RETRIES) {
            logger.info("Trying to check transaction status. Trial number: {}", retryCount + 1);
            try {
                response = RestAssured.given()
                        .header("Authorization", orderOwner.getAuthorizationToken())
                        .header("content-type", "application/json")
                        .pathParams("transactionId", transactionId)
                        .when()
                        .get(configs.getPaymentServiceBaseURL() + checkTransactionStatusEndpoint)
                        .then()
                        .extract().response();

                if (response.statusCode() == 200) {
                    isRequestSucceeded = true;
                    logger.info("Received status code of 200 for transaction ID: " + transactionId);
                } else {
                    retryCount++;
                    logger.error("Check transaction status request of try {} for transaction ID {}", retryCount + 1, transactionId);
                    logger.error("Failed response body is: {}", response.getBody().asString());
                }
            } catch (Exception e) {
                logger.error("Checking transaction status request failed with exception: ", e);
                retryCount++;
                logger.info("Retrying to check transaction status...");
                try {
                    Thread.sleep(RETRY_DELAY_MS); // Delay before retrying
                } catch (InterruptedException ie) {
                    logger.error("Trying to delay the thread for {} milliseconds failed with exception: ", RETRY_DELAY_MS, ie);
                    Thread.currentThread().interrupt(); // Reset the interrupt status
                }
            }
        }

        if (response != null && isRequestSucceeded) {
            JSONObject responseObject = new JSONObject(response.getBody().asString());
            String transactionStatus = responseObject.optString("status");

            if (!transactionStatus.contains("ERROR")) {
                logger.info("Check Transaction status was successful for transaction ID: " + transactionId +
                        "\nAnd the transaction status is: " + transactionStatus);
            } else {
                logger.info("Check transaction status was not successful and this is the response: " + responseObject);
            }

            return transactionStatus;
        } else {
            logger.error("FATAL: Check Transaction Status failed for transaction ID: {}", transactionId);
            return "ERROR";
        }
    }

    public Order voidPayment(User paymentPanelUser, Order order) {

        logger.info("Process of voiding payment of order started ...");

        Response response = RestAssured.given()
                .header("Authorization", paymentPanelUser.getAuthorizationToken())
                .header("content-type", "application/json")
                .pathParams("orderTransactionId", order.getOrderPaymentId())
                .when()
                .post(configs.getPaymentServiceBaseURL() + voidPaymentEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject responseObject = new JSONObject(response.getBody().asString());

        if (responseObject.optJSONObject("response").optBoolean("success"))
            logger.info("Voiding process is completed successfully for order ID : " + order.getOrderId() +
                    "\nWith Order Transaction ID : " + order.getOrderPaymentId());
        else
            logger.info("Voiding process has failed and this is the response : " + responseObject);

        return order;
    }

    public Order getOrderTransactionDetails(User paymentPanelUser,Order order, String orderType){

        logger.info("Process of getting order transaction details started...");

        String orderPaymentId;
        switch (orderType.toLowerCase()) {
            case "order" -> orderPaymentId = order.getOrderPaymentId();
            case "gratuity" -> orderPaymentId = order.getGratuityTransactionId();
            default -> {
                logger.warn("Unknown order type: " + orderType + ". Defaulting to order payment ID.");
                orderPaymentId = order.getOrderPaymentId();
            }
        }

        Response response = RestAssured.given()
                .header("Authorization", paymentPanelUser.getAuthorizationToken())
                .header("content-type", "application/json")
                .pathParams("orderPaymentId", orderPaymentId)
                .when()
                .get(configs.getPaymentServiceBaseURL() + getOrderTransactionDetailsEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("Getting transaction for order ID : " + order.getOrderId() + " details was successful ");
        JSONObject responseObject = new JSONObject(response.getBody().asString());

        logger.info("Parsing the transactions object...");
            //Case Payment is for order
        if (orderType.equalsIgnoreCase("order")){
            order = new OrderPaymentDataParser().parseOrderTransactionJsonObject(order,responseObject
                    , new OrdersDataParser().REQUEST_SOURCE_ORDER_PAYMENT_PANEL);
        }else{
            //Case payment is for gratuity
            order = new OrderPaymentDataParser().parseOrderTransactionJsonObject(order,responseObject
                    , new OrdersDataParser().REQUEST_SOURCE_GRATUITY_PAYMENT_PANEL);
        }

        return order;
    }

    public void RefundPayment(User paymentPanelUser,
                                String orderTransactionId,
                                Boolean isWallet,
                                float refundedAmount) {

        logger.info("Process of refund payment of order started ...");

        // Construct the JSON payload without converting values to strings
        String requestBody = "{\n" +
                "    \"toWallet\": " + isWallet + ",\n" +
                "    \"amount\": " + refundedAmount + "\n" +
                "}";

        Response response = RestAssured.given()
                .header("Authorization", paymentPanelUser.getAuthorizationToken())
                .header("content-type", "application/json")
                .pathParams("orderTransactionId", orderTransactionId)
                .body(requestBody)
                .when()
                .post(configs.getPaymentServiceBaseURL() + refundEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject responseObject = new JSONObject(response.getBody().asString());

        if (responseObject.optJSONObject("response").optBoolean("success")) {
            logger.info(String.format(
                    "Refunding process is completed successfully With Order Gratuity Transaction ID: %s",
                    orderTransactionId
            ));
        } else {
            logger.error(String.format(
                    "Refunding process has failed and this is the response: %s",
                    responseObject
            ));
        }
    }

}
