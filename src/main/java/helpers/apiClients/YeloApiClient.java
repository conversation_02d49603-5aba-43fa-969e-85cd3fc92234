package helpers.apiClients;

import helpers.BaseHelper;
import helpers.dataParsers.RestaurantsDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.Restaurant;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class YeloApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(YeloApiClient.class);

    private final String getAllRestaurantsDetailsInYeloEndpoint = "/api/marketplace/marketplace_get_city_storefronts_v3";

    public YeloApiClient(Configs configs) {
        this.configs = configs;
    }

    public Response getRestaurantsByLocationEndpointResponse(double latitude, double longitude){
        logger.info("Sending a getRestaurantsByLocation request in yelo for " +
                "latitude: \"" + latitude + "\" and logitude: \"" + longitude + "\"");

        return RestAssured.given()
                .contentType("application/json")
                .queryParam("post_to_get",1)
                .queryParam("latitude",latitude)
                .queryParam("longitude",longitude)
                .queryParam("marketplace_user_id",configs.getYeloMarketplaceUserId())
                .queryParam("limit",100)
                .get(configs.getYeloBaseURL()+getAllRestaurantsDetailsInYeloEndpoint)
                .then()
                .extract().response();
    }

    public List<Restaurant> getRestaurantsDataByLocation() {
        //Call the endpoint itself map the whole response to a variable
        Response response =
                getRestaurantsByLocationEndpointResponse(configs.getTestLatitude(), configs.getTestLongitude());
        JSONObject responseJsonObject = new JSONObject(response.getBody().asString());

        //Assert status code in response
        response.then().assertThat().statusCode(200);

        List<Restaurant> restaurantsList = new ArrayList<>();
        try {
            //Extract Restaurants Array
            if (responseJsonObject.optInt("status") == 200 && responseJsonObject.optString("message").equals("Successful")) {
                JSONArray retrievedRestaurantsArray = responseJsonObject.optJSONArray("data");
                if (!retrievedRestaurantsArray.isEmpty()) {
                    logger.info("Starting to parse restaurants with a total count of :" + retrievedRestaurantsArray.length());
                    List<JSONObject> restaurantsJsonObjects = new RestaurantsDataParser()
                            .parseJsonArrayToListOfJsonObjects(retrievedRestaurantsArray);
                    for (JSONObject restaurant : restaurantsJsonObjects) {
                        restaurantsList.add(new RestaurantsDataParser()
                                .parseRestaurantJsonObject(restaurant, new RestaurantsDataParser().REQUEST_SOURCE_YELO));
                    }
                } else {
                    logger.error("Retrieved Restaurants Array is Empty .Here is the full response Body : " + responseJsonObject);
                }
            } else {
                logger.error("status key value in the response body is not Successful or missing.Here is the full response Body : " + responseJsonObject);
            }
        } catch (Exception e) {
            logger.error("Parsing restaurants List failed with exception.", e);
        }
        return restaurantsList;
    }

}
