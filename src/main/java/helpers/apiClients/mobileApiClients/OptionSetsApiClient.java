package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

public class OptionSetsApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(OptionSetsApiClient.class);
    private final String createModifiersEndpoint = "/modifiers";
    private final String createOptionSetEndpoint = "/option-sets";
    private final String enableModifierEndPoint = "/supply-chain/modifiers/toggle";
    private final String syncModifiersEndPoint = "/supply-chain/modifiers/sync";
    private final String getProductFromCatalogEndpoint="/products";
    private final String removeProductFromOptionSetEndpoint= "/products/{productId}";

    public OptionSetsApiClient(Configs configs) {
        this.configs = configs;
    }

    public OptionSets createModifiers(User adminUser, Category category, int price, String modifierEnglishName) {
        logger.info("Sending a createModifier request to endpoint \"{}\" with category ID: \"{}\" and price \"{}\" and modifier Name \"{}\"",
                createModifiersEndpoint, category.getId(), price, modifierEnglishName);

        JSONObject requestBody = new JSONObject();
        requestBody.put("internalName", modifierEnglishName);
        requestBody.put("name", "modifier English name");
        requestBody.put("nameAr", "الاسم بالعربى");
        requestBody.put("categoryId", category.getId());
        requestBody.put("isCharged", true);
        requestBody.put("price", price);
        requestBody.put("taxPercentage", 0);
        requestBody.put("status", "publish");

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .header("content-type", "application/json")
                .body(requestBody)
                .when()
                .post(configs.getCatalogBaseURL() + createModifiersEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(201)
                .extract().response();

        JSONObject responseObject = new JSONObject(response.getBody().asString());
        OptionSets modifierId = new OptionSets();
        modifierId.setModifierId(responseObject.optInt("id"));
        return modifierId;
    }

    public OptionSets createOptionSet(User adminUser, OptionSets modifier, int productId, String selectionType, String optionSetEnglishName) {
        logger.info("Sending a createOptionSet request to endpoint \"{}\" with modifier ID: {} and product ID: {} and option set English name: \"{}\"",
                createOptionSetEndpoint, modifier.getModifierId(), productId, optionSetEnglishName);

        // Build the request body using helper
        JSONObject requestBody = buildOptionSetRequestBody(modifier, productId, selectionType, optionSetEnglishName);

        // Send the request
        Response response = RestAssured.given()
                .header("Authorization", "Bearer " +
                        adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName(),
                        adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName(),
                        adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .header("Content-Type", "application/json")
                .body(requestBody.toString())
                .when()
                .post(configs.getCatalogBaseURL() + createOptionSetEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(201)
                .extract().response();

        // Extract and return the created OptionSet ID
        JSONObject responseObject = new JSONObject(response.getBody().asString());
        OptionSets createdOptionSet = new OptionSets();
        createdOptionSet.setOptionSetId(responseObject.optInt("id"));
        return createdOptionSet;
    }

    private JSONObject buildOptionSetRequestBody(OptionSets modifier, int productId, String selectionType, String optionSetEnglishName) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("name", "option name");
        requestBody.put("nameAr", "الاسم بالعربى");
        requestBody.put("internalName", optionSetEnglishName);
        requestBody.put("status", "publish");
        requestBody.put("optionSetTypeId", 1);
        requestBody.put("selectionType", selectionType);
        requestBody.put("required", true);
        requestBody.put("maxSelectionPerOption", 1);
        requestBody.put("minSelection", 1);
        requestBody.put("maxSelection", 1);

        // Conditional flag based on selection type
        boolean withItemCounter = !selectionType.equalsIgnoreCase("single");
        requestBody.put("withItemCounter", withItemCounter);

        // Linked Products array
        JSONArray linkedProducts = new JSONArray();
        JSONObject linkedProduct = new JSONObject();
        linkedProduct.put("productId", productId);
        linkedProducts.put(linkedProduct);
        requestBody.put("linkedProducts", linkedProducts);

        // Options array
        JSONArray options = new JSONArray();
        JSONObject option = new JSONObject();
        option.put("modifierId", modifier.getModifierId());
        option.put("rank", 1);
        options.put(option);
        requestBody.put("options", options);

        return requestBody;
    }

    public void enableModifier(User adminUser, OptionSets modifier, String warehouseId) {
        logger.info("Sending an enableModifier request to endpoint \"{}\"", enableModifierEndPoint);

        JSONObject requestBody = new JSONObject();
        requestBody.put("facilityId", warehouseId);
        requestBody.put("status", true);
        requestBody.put("modifierId", modifier.getModifierId());

        Response response = RestAssured.given()
                .header("Authorization", "Bearer " +
                        adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName(),
                        adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName(),
                        adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .header("Content-Type", "application/json")
                .body(requestBody.toString())
                .when()
                .post(configs.getBaseURL() + enableModifierEndPoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        JSONObject enableModifierJsonObject = new JSONObject(response.getBody().asString());

        if ("success".equalsIgnoreCase(enableModifierJsonObject.optString("status"))) {
            logger.info("Modifier enabled successfully (modifierId: {}).", modifier.getModifierId());
        } else {
            logger.error("Failed to enable modifier. Response: {}", enableModifierJsonObject);
        }
    }

    public void syncModifiers() {
        logger.info("Sending a syncModifiers request to endpoint \"{}\"", syncModifiersEndPoint);

        Response response = RestAssured.given()
                .header("token", "123456789")
                .header("Content-Type", "application/json")
                .when()
                .post(configs.getBaseURL() + syncModifiersEndPoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        JSONObject syncModifiersJsonObject = new JSONObject(response.getBody().asString());

        if ("success".equalsIgnoreCase(syncModifiersJsonObject.optString("status"))) {
            logger.info("Sync modifiers completed successfully.");
        } else {
            logger.error("Failed to sync modifiers. Response: {}", syncModifiersJsonObject);
        }
    }

    public Product getProductsFromCatalog(User adminUser, List<Product> productsIds) {
        logger.info("Sending a getProductsFromCatalog request to endpoint \"{}\"", getProductFromCatalogEndpoint);

        // Extract MySQL IDs and join them with "-"
        String formattedProductIds = productsIds.stream()
                .map(product -> String.valueOf(product.getMysqlId()))
                .collect(Collectors.joining("-"));

        String filterQuery = "id in " + formattedProductIds
                + ", linkedOptionsCount eq 0"
                + ", status eq 'publish'" ;

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName(),
                        adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName(),
                        adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .header("Content-Type", "application/json")
                .queryParam("filter", filterQuery)
                .when()
                .get(configs.getCatalogBaseURL() + getProductFromCatalogEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        JSONObject getProductsJsonObject = new JSONObject(response.getBody().asString());
        Product catalogProduct = new Product();
        catalogProduct.setCatalogProductId(
                getProductsJsonObject.getJSONArray("data").getJSONObject(0).getInt("id"));

        return catalogProduct;
    }

    public Product removeProductFromOptionSet(User adminUser, int productId) {
        logger.info("Sending a removeProductFromOptionSet request to endpoint \"{}\"", removeProductFromOptionSetEndpoint);

        String requestBody = "{\n" +
                " \"linkedOptionSets\": []\n" +
                "}";

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName(),
                        adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName(),
                        adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .header("Content-Type", "application/json")
                .pathParams("productId", productId)
                .body(requestBody)
                .when()
                .patch(configs.getCatalogBaseURL() + removeProductFromOptionSetEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        JSONObject removeProductFromOptionSetJsonObject = new JSONObject(response.getBody().asString());
        Product productDetails = new Product();
        productDetails.setCustomizable(
                removeProductFromOptionSetJsonObject.optBoolean("isCustomizable"));
        return productDetails;
    }

}
