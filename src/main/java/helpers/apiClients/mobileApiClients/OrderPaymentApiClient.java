package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.OrderPaymentDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.Order;
import models.User;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;

public class OrderPaymentApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(OrderPaymentApiClient.class);
    private final String createOrderInPaymentServiceEndpoint = "/api/order";
    private final String payForOrderInPaymentServiceEndpoint = "/api/order/{orderTransactionId}/pay";
    private final String inaiCheckoutEndpoint = "/v1/payments";
    private final String createGratuityInRatingEndpoint = "/ar/wp-json/breadfast/v3/gratuity/create";
    private final String capturePaymentEndpoint = "/api/order/{orderTransactionId}/capture";

    public OrderPaymentApiClient(Configs configs) {
        this.configs = configs;
    }

    public Order createOrderInPaymentService(User orderOwner, Order order, Boolean useBalance, String orderType) {

        logger.info("Creating an order in payment service for user with ID: " + orderOwner.getId() +
                "\nfor order with ID: " + order.getOrderId() + ", and order type : " + orderType);

        Response response = RestAssured.given()
                .header("Authorization", orderOwner.getAuthorizationToken())
                .header("key", getPaymentKey(orderType))
                .header("secret", configs.getPaymentServiceSecret())
                .header("content-type", "application/json")
                .body(getOrderInPaymentServiceRequestBody(orderOwner, order, useBalance, orderType))
                .when()
                .post(configs.getPaymentServiceBaseURL() + createOrderInPaymentServiceEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject responseObject = new JSONObject(response.getBody().asString());

        if (responseObject.optJSONObject("response").optJSONObject("order").optString("status").equals("NEW")) {
            logger.info("Order created in payment service successfully");

            if (orderType.equals("order")) {
                order = new OrderPaymentDataParser().parseOrderPaymentJsonObject(order
                        , responseObject.optJSONObject("response"), new OrderPaymentDataParser().SHOPPING_APP);
                logger.info("Order Transaction ID is : " + order.getOrderPaymentId());
            } else if (orderType.equals("gratuity")){
                order = new OrderPaymentDataParser().parseOrderPaymentJsonObject(order
                        , responseObject.optJSONObject("response"), new OrderPaymentDataParser().GRATUITY);
                logger.info("Order Gratuity Transaction ID is : " + order.getGratuityTransactionId());
            } else if (orderType.equals("Billing-aps-integration")) {
                order = new OrderPaymentDataParser().parseOrderPaymentJsonObject(order
                        , responseObject.optJSONObject("response"), new OrderPaymentDataParser().BILLING);
                logger.info("Order Billing Transaction ID is : " + order.getOrderPaymentId());
            } else {
                order = new OrderPaymentDataParser().parseOrderPaymentJsonObject(order
                        , responseObject.optJSONObject("response"), new OrderPaymentDataParser().TOPUP);
                logger.info("Order Topup Transaction ID is : " + order.getOrderPaymentId());
            }

        } else
            logger.info("There was a problem with creating the order in payment service . This is the response : " + responseObject);

        return order;
    }

    public Order payOrderInPaymentService(User orderOwner, Order order, String orderType) {

        logger.info("Starting payment process of order with ID : " + order.getOrderId());

        String transactionId;
        String paymentKey = getPaymentKey(orderType);

        if (orderType.equalsIgnoreCase("gratuity")) {
            logger.info("Payment is for gratuity,Transaction ID for gratuity is " + order.getGratuityTransactionId());
            transactionId = order.getGratuityTransactionId();
        } else if (orderType.equalsIgnoreCase("order")) {
            logger.info("Payment is for the order with Payment ID : " + order.getOrderPaymentId());
            transactionId = order.getOrderPaymentId();
        } else if(orderType.equalsIgnoreCase("Billing-aps-integration")){
            logger.info("Payment is for billing order with Payment ID : " + order.getBillOrderId());
            transactionId = order.getBillOrderId();
        } else{
            logger.info("Payment is for top up order with Payment ID : " + order.getTopUpOrderId());
            transactionId = order.getTopUpOrderId();
        }

        Response response = RestAssured.given()
                .header("Authorization", orderOwner.getAuthorizationToken())
                .header("key", paymentKey)
                .header("secret", configs.getPaymentServiceSecret())
                .header("content-type", "application/json")
                .pathParams("orderTransactionId", transactionId)
                .when()
                .post(configs.getPaymentServiceBaseURL() + payForOrderInPaymentServiceEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject responseObject = new JSONObject(response.getBody().asString());
        String orderStatus = responseObject.optJSONObject("response").optJSONObject("order").optString("status");

        //Case Payment is for order
        if (orderType.equalsIgnoreCase("order")) {
            //Assert Payment was successful in case of inai or wallet
            if (orderStatus.equals("PENDING") || orderStatus.equals("AUTHORIZED")) {

                logger.info("Order Has been paid Successfully in the payment service." +
                        "\nStart Parsing of order payment transaction..");

                order = new OrderPaymentDataParser().parseOrderTransactionJsonObject(order, responseObject.optJSONObject("response"),new OrderPaymentDataParser().REQUEST_SOURCE_CUSTOMER_APP);

                //Handle case payment was partial CC & wallet
                if (order.getOrderCCPaymentTransaction() != null && order.getOrderWalletPaymentTransaction() != null) {
                    logger.info("Payment has been made with CC & wallet." +
                            "\nAmount paid in CC is : " + order.getOrderCCPaymentTransaction().optFloat("amount") +
                            "and order provider ID is : " + order.getProviderOrderId() +
                            "\nAmount paid in wallet is : " + order.getOrderWalletPaymentTransaction().optFloat("amount"));
                    // Ensure current balance is not null before parsing it
                    String currentBalance = orderOwner.getCurrentBalance();
                    if (currentBalance != null) {
                        //Update user balance upon making the payment from wallet
                        float floatBalance = Float.parseFloat(orderOwner.getCurrentBalance());
                        orderOwner.setCurrentBalance(String.valueOf(floatBalance - order.getOrderWalletPaymentTransaction().optFloat("amount")));
                        logger.info("New User Balance after payment is : " + orderOwner.getCurrentBalance());
                    }
                    else {
                        logger.error("Current balance is null. Unable to update balance after payment.");
                    }
                }
                //Handle case payment was CC only
                else if (order.getOrderCCPaymentTransaction() != null && order.getProviderOrderId() != null) {
                    logger.info("Payment has been made fully with CC" +
                            "\nAmount paid in CC is : " + order.getOrderCCPaymentTransaction().optFloat("amount") +
                            "and order provider ID is : " + order.getProviderOrderId());
                }
                //Handle case payment was fully from wallet
                else {
                    logger.info("Payment has been made fully with Wallet." +
                            "\nAmount Paid from wallet is : " + order.getOrderWalletPaymentTransaction().optFloat("amount"));
                    //Update user balance upon making the payment from wallet
                    // Ensure current balance is not null before parsing it
                    String currentBalance = orderOwner.getCurrentBalance();
                    if (currentBalance != null) {
                        float floatBalance = Float.parseFloat(orderOwner.getCurrentBalance());
                        orderOwner.setCurrentBalance(String.valueOf(floatBalance - order.getOrderWalletPaymentTransaction().optFloat("amount")));
                        logger.info("New User Balance after payment is : " + orderOwner.getCurrentBalance());
                    }
                    else {
                        logger.error("Current balance is null. Unable to update balance after payment.");
                    }
                }
            } else
                logger.info("Order was not paid successfully, this is the response : " + responseObject);
        }
        //Case Payment is for billing order
        else if (orderType.equalsIgnoreCase("Billing-aps-integration")) {
            //Assert Payment was successful in case of inai or wallet
            if (orderStatus.equals("PENDING") || orderStatus.equals("AUTHORIZED")) {

                logger.info("Order Has been paid Successfully in the payment service." +
                        "\nStart Parsing of order payment transaction..");

                order = new OrderPaymentDataParser().parseOrderTransactionJsonObject(order, responseObject.optJSONObject("response"),new OrderPaymentDataParser().REQUEST_SOURCE_CUSTOMER_APP);

                //Handle case payment was partial CC & wallet
                if (order.getOrderCCPaymentTransaction() != null && order.getOrderWalletPaymentTransaction() != null) {
                    logger.info("Payment has been made with CC & wallet." +
                            "\nAmount paid in CC is : " + order.getOrderCCPaymentTransaction().optFloat("amount") +
                            "and order provider ID is : " + order.getProviderOrderId() +
                            "\nAmount paid in wallet is : " + order.getOrderWalletPaymentTransaction().optFloat("amount"));
                    // Ensure current balance is not null before parsing it
                    String currentBalance = orderOwner.getCurrentBalance();
                    if (currentBalance != null) {
                        //Update user balance upon making the payment from wallet
                        float floatBalance = Float.parseFloat(orderOwner.getCurrentBalance());
                        orderOwner.setCurrentBalance(String.valueOf(floatBalance - order.getOrderWalletPaymentTransaction().optFloat("amount")));
                        logger.info("New User Balance after payment is : " + orderOwner.getCurrentBalance());
                    }
                    else {
                        logger.error("Current balance is null. Unable to update balance after payment.");
                    }
                }
                //Handle case payment was CC only
                else if (order.getOrderCCPaymentTransaction() != null && order.getProviderOrderId() != null) {
                    logger.info("Payment has been made fully with CC" +
                            "\nAmount paid in CC is : " + order.getOrderCCPaymentTransaction().optFloat("amount") +
                            "and order provider ID is : " + order.getProviderOrderId());
                }
                //Handle case payment was fully from wallet
                else {
                    logger.info("Payment has been made fully with Wallet." +
                            "\nAmount Paid from wallet is : " + order.getOrderWalletPaymentTransaction().optFloat("amount"));
                    // Ensure current balance is not null before parsing it
                    String currentBalance = orderOwner.getCurrentBalance();
                    if (currentBalance != null) {
                        //Update user balance upon making the payment from wallet
                        float floatBalance = Float.parseFloat(currentBalance);
                        orderOwner.setCurrentBalance(String.valueOf(floatBalance - order.getOrderWalletPaymentTransaction().optFloat("amount")));
                        logger.info("New User Balance after payment is : " + orderOwner.getCurrentBalance());
                    }
                    else {
                        logger.error("Current balance is null. Unable to update balance after payment.");
                    }
                }
            } else
                logger.info("Order was not paid successfully, this is the response : " + responseObject);
        }

        //Case Payment is for top-up  order
        else if (orderType.equalsIgnoreCase("topup")) {
            //Assert Payment was successful in case of inai or wallet
            if (orderStatus.equals("PENDING") || orderStatus.equals("AUTHORIZED")) {

                logger.info("Order Has been paid Successfully in the payment service." +
                        "\nStart Parsing of order payment transaction..");

                order = new OrderPaymentDataParser().parseOrderTransactionJsonObject(order, responseObject.optJSONObject("response"),new OrderPaymentDataParser().REQUEST_SOURCE_CUSTOMER_APP);

                //Handle case payment was CC only
                if (order.getOrderCCPaymentTransaction() != null && order.getProviderOrderId() != null) {
                    logger.info("Payment has been made fully with CC" +
                            "\nAmount paid in CC is : " + order.getOrderCCPaymentTransaction().optFloat("amount") +
                            "and order provider ID is : " + order.getProviderOrderId());
                }
            } else
                logger.info("Order was not paid successfully, this is the response : " + responseObject);
        }
        //Case payment is for gratuity
        else {
            if (orderStatus.equals("SUCCESS") || orderStatus.equals("PENDING")) {
                logger.info("Gratuity Payment Has been initialized successfully..parsing the data");
                order = new OrderPaymentDataParser().parseGratuityTransactionObject(order, responseObject.optJSONObject("response"));
            } else logger.info("Gratuity was not paid successfully and this is the response" + responseObject);
        }

        return order;
    }

    public String payOrderInInai(Order order, String orderType, boolean isValidCc) {

        logger.info("Process of payment in inai has started for order ID : " + order.getOrderId());

        String inaiOrderId;

        //In Case the payment is for gratuity set it to gratuity provider order ID
        if (orderType.equalsIgnoreCase("gratuity")) {
            inaiOrderId = order.getGratuityProviderOrderId();
            logger.info("Payment in inai is for gratuity, Order ID in Inai is  :" + inaiOrderId +
                    "\nWith payment transaction ID : " + order.getCcGratuityTransactionId());
        } else if (orderType.equalsIgnoreCase("order")) {
            inaiOrderId = order.getProviderOrderId();
            logger.info("Payment in inai is for Shopping order, Order ID in Inai is  :" + inaiOrderId +
                    "\nWith payment transaction ID : " + order.getCcTransactionId());
        } else if (orderType.equalsIgnoreCase("Billing-aps-integration")) {
            inaiOrderId = order.getProviderOrderId();
            logger.info("Payment in inai is for Billing Order , Order ID in Inai is  :" + inaiOrderId +
                    "\nWith payment transaction ID : " + order.getCcTransactionId());
        } else {
            inaiOrderId = order.getProviderOrderId();
            logger.info("Payment in inai is for topup Order , Order ID in Inai is  :" + inaiOrderId +
                    "\nWith payment transaction ID : " + order.getCcTransactionId());
        }

        String formulatedTestCreditCard;

        if (isValidCc) {
             formulatedTestCreditCard= configs.getTestCreditCard().replaceAll("\\s+", "");
        }
        else {
            formulatedTestCreditCard= configs.getDeclinedCreditCard().replaceAll("\\s+", "");
        }

        String formulatedExpiryMonth = configs.getTestExpDate().substring(0, 2);
        String formulatedExpiryYear = "20" + configs.getTestExpDate().substring(5);

        String requestBody = "{\n" +
                "    \"details\": {\n" +
                "        \"card\": {\n" +
                "            \"cvc\": \"" + configs.getTestCVC() + "\",\n" +
                "            \"exp_month\": \"" + formulatedExpiryMonth + "\",\n" +
                "            \"exp_year\": " + formulatedExpiryYear + ",\n" +
                "            \"number\": \"" + formulatedTestCreditCard + "\",\n" +
                "            \"holder_name\": \"test\"\n" +
                "        },\n" +
                "        \"country\": \"EGY\",\n" +
                "        \"payer\": {\n" +
                "            \"name\": \"test\"\n" +
                "        },\n" +
                "        \"address\": {\n" +
                "            \"country\": \"EGY\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"order_id\": \"" + inaiOrderId + "\",\n" +
                "    \"payment_type\": \"ONE_TIME\",\n" +
                "    \"rail\": \"card\",\n" +
                "    \"token\": \"" + configs.getInaiToken() + "\"\n" +
                "}";

        Response response = RestAssured.given()
                .header("content-type", "application/json")
                .body(requestBody)
                .when()
                .post(configs.getInaiBaseURL() + inaiCheckoutEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject responseObject = new JSONObject(response.getBody().asString());

        if (responseObject.optString("message").equalsIgnoreCase("Payment Pending")
                && responseObject.optJSONObject("vendor_response").optInt("status_code") == 200) {

            logger.info("Payment in inai is initiated successfully , Starting to parse the response... ");
            order = new OrderPaymentDataParser().parseInaiRedirectionUrl(order, responseObject);

        } else
            logger.info("Payment in inai was not initiated successfully and this is the response " + responseObject);

        return order.getInaiRedirectionUrl();
    }

    public Order capturePayment(User orderOwner, Order order, String orderType) {

        logger.info("Process of capturing payment of order started ...");

        String paymentKey = getPaymentKey(orderType);

        Response response = RestAssured.given()
                .header("Authorization", orderOwner.getAuthorizationToken())
                .header("key", paymentKey)
                .header("secret", configs.getPaymentServiceSecret())
                .header("content-type", "application/json")
                .pathParams("orderTransactionId", order.getOrderPaymentId())
                .when()
                .post(configs.getPaymentServiceBaseURL() + capturePaymentEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        JSONObject responseObject = new JSONObject(response.getBody().asString());

        if (responseObject.optJSONObject("response").optBoolean("success"))
            logger.info("Capture process is completed successfully for order ID : " + order.getOrderId() +
                    "\nWith Order Transaction ID : " + order.getOrderPaymentId());
        else
            logger.info("Capture process has failed and this is the response : " + responseObject);

        return order;
    }

    public Order createGratuityInRatingUsingApi(User orderOwner, Order order, String gratuityMethod) {
        String requestBody = "{\n" +
                "    \"order_id\" : \"" + order.getOrderId() + "\",\n" +
                "    \"origin\"   : \"rating_screen\",\n" +
                "    \"method\"   : \"" + getGratuityMethod(gratuityMethod) + "\",\n" +
                "    \"amount\"   : \"5\"\n" +
                "}";

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + orderOwner.getAuthorizationToken())
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getBaseURL() + createGratuityInRatingEndpoint)
                .then()
                .statusCode(200)
                .extract().response();
        JSONObject responseObject = new JSONObject(response.getBody().asString());

        Order parsedGratuity = new OrderPaymentDataParser().parseGratuityJsonObject(responseObject, order);

        if (responseObject.optString("code").equalsIgnoreCase("success")) {
            logger.info((" Create gratuity successfully: " + "and gratuity id is : " + parsedGratuity.getGratuityOrderId()));
        } else {
            logger.info((" Failed creating gratuity "));
        }
        return parsedGratuity;
    }

    private String getGratuityMethod(String gratuityMethod) {
        return switch (gratuityMethod.toLowerCase()) {
            case "balance", "wallet" -> "wallet";
            case "cc", "credit" -> "inai";
            default -> "wallet";
        };
    }

    private String getOrderInPaymentServiceRequestBody(User orderOwner, Order order, Boolean useBalance, String orderType) {

        String captureMethod = "MANUAL";
        float amount;
        String orderId;
        int minimumGratuityFromCC = 5; //Limitation on any CC transaction
        int billingAmount = 100;
        int topUpAmount = 50;

        //Check if the order created in payment is shopping order or gratuity
        if (orderType.equalsIgnoreCase("order")) {

            orderId = order.getOrderId();

            //Check if there is gratuity with order
            if (order.getTotalWithGratuity() != null) amount = order.getTotalWithGratuity();
            else amount = order.getTotal();

            logger.info("Order type is a shopping order , Setting order ID to : " + orderId +
                    "\nAnd Order amount to : " + amount);
        } else if (orderType.equalsIgnoreCase("gratuity")){
            orderId = order.getGratuityOrderId();
            amount = minimumGratuityFromCC;
            captureMethod = "IMMEDIATE"; //Capture method in gratuity is immediate in CC & wallet
            logger.info("Order type is gratuity , Gratuity ID is : " + order.getGratuityOrderId() +
                    "\nAnd gratuity amount is : " + amount);
            logger.info("Use Balance Has been set to : " + useBalance);
        } else if (orderType.equalsIgnoreCase("Billing-aps-integration")){
            orderId = UUID.randomUUID().toString();
            amount = billingAmount;
            logger.info("Order type is a billing order , Setting order ID to : " + orderId +
                    "\nAnd Order amount to : " + amount);
        } else{
            orderId = UUID.randomUUID().toString();
            amount = topUpAmount;
            logger.info("Order type is a top up order , Setting order ID to : " + orderId +
                    "\nAnd Order amount to : " + amount);
        }

        return "{\n" +
                "    \"amount\": " + amount + ", \n" +
                "    \"currency\": \"EGP\",\n" +
                "    \"capture_method\": \"" + captureMethod + "\",\n" +
                "    \"use_balance\": " + useBalance + ",\n" +
                "    \"description\": \"test EG payment\",\n" +
                "    \"client_order_id\": \"" + orderId + "\"\n" +
                "}";
    }

    private String getPaymentKey(String orderType) {

        return switch (orderType.toLowerCase()){
            case "gratuity" -> configs.getPaymentGratuityKey();
            case "billing-aps-integration" -> configs.getPaymentBillingKey();
            case "topup" -> configs.getPaymentTopUpKey();
            default -> configs.getPaymentShoppingKey();
        };
    }
}
