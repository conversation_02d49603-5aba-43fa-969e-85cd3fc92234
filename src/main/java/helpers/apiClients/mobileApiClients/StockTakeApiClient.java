package helpers.apiClients.mobileApiClients;
import helpers.BaseHelper;
import helpers.apiClients.webApiClients.ControlRoomV2ApiClient;
import helpers.dataParsers.CategoriesParser;
import helpers.dataParsers.ProductsParser;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import models.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class StockTakeApiClient extends BaseHelper{
    private final Configs configs;
    private final String getAllCategoriesEndPoint = "/stocktake/products/categories";
    private final String getProductsAtCategoryAndSubCategory= "/stocktake/products";
    private final String getUserDetails ="/shopper/user/getUserData";
    private final String loginEndPoint = "/wp-json/shopper/v1/user/login";
    private final String scanningProductEndPoint = "/stocktake/products/scan";

    private static final Logger logger = LoggerFactory.getLogger(ControlRoomV2ApiClient.class);

    public StockTakeApiClient(Configs configs) {
        super();
        this.configs = configs;
    }

    public User loginAndGetAuthorizationToken(User stockTaker) {
        Response response = RestAssured.given()
                .contentType(ContentType.URLENC)
                .formParam("phone", configs.getStockTakerCountryCode()
                        .replaceAll("\\D+", "")+stockTaker.getLocalPhoneNumber())
                .formParam("password", stockTaker.getBypassScriptPassword())
                .when()
                .post(configs.getBaseURL()+loginEndPoint)
                .then()
                .statusCode(200)
                .extract().response();
        stockTaker.setAuthorizationToken(
                new JSONObject(response.getBody().asString()).optJSONObject("data").optString("token"));
        return stockTaker;
    }

    public User getStockTakerUserDetails(User stockTaker){
        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + stockTaker.getAuthorizationToken())
                .queryParam("lat", configs.getTestLatitude())
                .queryParam("lng", configs.getTestLongitude())
                .queryParam("lang", "en")
                .noContentType()
                .get(configs.getBaseURL() + getUserDetails)
                .then()
                .statusCode(200)
                .extract().response();

        stockTaker.setFpId(
                new JSONObject(response.getBody().asString())
                        .optJSONObject("result")
                        .optJSONObject("fp")
                        .optString( "_id"));

        return stockTaker;
    }

    public List<Category> getAllCategoriesResponse(User stockTakeUser){
        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer " + stockTakeUser.getAuthorizationToken())
                .get(configs.getStockTakeBaseURL() + getAllCategoriesEndPoint)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("ListAllCategories and received a response code of 200");

        JSONObject retrievedObject = new JSONObject(response.getBody().asString());
        List<Category> allCategories = new ArrayList<>();

        if (retrievedObject.has("payload") && !retrievedObject.getJSONArray("payload").isEmpty()) {
            JSONArray payloadArray = retrievedObject.getJSONArray("payload");
            logger.info("Total count of categories in JSON response is " + payloadArray.length());
            logger.info("Starting to parse each category in the list...");
            for (int i = 0; i < payloadArray.length(); i++) {
                JSONObject categoryJsonObject = payloadArray.getJSONObject(i);
                allCategories.add(
                        new CategoriesParser().parseCategoryJsonObject(categoryJsonObject, CategoriesParser.STOCK_TAKE));
            }
            logger.info("Categories count is: " + allCategories.size());

            logger.info("Starting to parse products in each subCategory");
            for (Category c : allCategories){
                logger.info("Adding products for subcategories in Category with ID {} and name {}"
                        , c.getId()
                        , c.getName());
                for (Category sc : c.getSubCategories()){
                    logger.info("Pointing to subCategory with ID {} within category {}", c.getId(), sc.getId());
                    sc.setStockTakeProducts(
                            listActiveProductsAtCategoryAndSubCategory(stockTakeUser
                                    , stockTakeUser.getFpId()
                                    , c.getCategoryId()
                                    , sc.getCategoryId()
                                    , "1"));
                    logger.info("Added count of {} to the stockTakeProducts list.", sc.getStockTakeProducts().size());
                }
            }
        } else {
            logger.error("Found 0 categories");
        }
        return allCategories;
    }

    public List<Product> getAllActiveProductsOnCategories(User stockTakeUser){
        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer " + stockTakeUser.getAuthorizationToken())
                .get(configs.getStockTakeBaseURL() + getAllCategoriesEndPoint)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("ListAllCategories and received a response code of 200");

        JSONObject retrievedObject = new JSONObject(response.getBody().asString());
        List<Category> allCategories = new ArrayList<>();
        List<Product> allProducts = new ArrayList<>(); // List to hold all active and inactive products

        if (retrievedObject.has("payload") && !retrievedObject.getJSONArray("payload").isEmpty()) {
            JSONArray payloadArray = retrievedObject.getJSONArray("payload");
            logger.info("Total count of categories in JSON response is " + payloadArray.length());
            logger.info("Starting to parse each category in the list...");
            for (int i = 0; i < payloadArray.length(); i++) {
                JSONObject categoryJsonObject = payloadArray.getJSONObject(i);
                allCategories.add(
                        new CategoriesParser().parseCategoryJsonObject(categoryJsonObject, CategoriesParser.STOCK_TAKE));
            }
            logger.info("Categories count is: " + allCategories.size());
            logger.info("Starting to parse products in each subCategory");
            for (Category c : allCategories){
                logger.info("Adding products for subcategories in Category with ID {} and name {}"
                        , c.getId()
                        , c.getName());
                for (Category sc : c.getSubCategories()){
                    logger.info("Pointing to subCategory with ID {} within category {}", c.getId(), sc.getId());
                    List<Product> products =
                            listActiveProductsAtCategoryAndSubCategory(stockTakeUser
                                    , stockTakeUser.getFpId()
                                    , c.getCategoryId()
                                    , sc.getCategoryId()
                                    , "1");

                            allProducts.addAll(products);
                }
            }
            logger.info("Total products retrieved: " + allProducts.size());
        } else {
            logger.error("Found 0 categories");
        }
        return allProducts;
    }

    public List <Product> listActiveProductsAtCategoryAndSubCategory(User stockTaker
            , String fpId
            , String categoryId
            , String subCategoryId
            , String status){

        logger.info("Sending list Active Products request for " +
                "FP with ID {}" +
                ", category with ID {}" +
                ", SubCategory with ID {}" +
                " and status {}"
                , fpId, categoryId
                , subCategoryId
                , status);

        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer " + stockTaker.getAuthorizationToken())
                .queryParam("fpId", fpId)
                .queryParam("filters[categoryIds]", categoryId)
                .queryParam("filters[subCategoryIds]", subCategoryId)
                .queryParam("filters[activeStatus]", status)
                .get(configs.getStockTakeBaseURL() + getProductsAtCategoryAndSubCategory)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        List<Product> products = new ArrayList<>();
        JSONObject responseObject = new JSONObject(response.getBody().asString());
        JSONArray categories = responseObject.getJSONObject("payload").optJSONArray("categories");

        for (int i = 0; i < categories.length(); i++) {
            JSONObject category = categories.getJSONObject(i);
            JSONArray subCategories = category.getJSONArray("subCategories");
            for (int j = 0; j < subCategories.length(); j++) {
                JSONObject subCategory = subCategories.getJSONObject(j);
                    List<JSONObject> productsJsonObjectList =
                            new ProductsParser().parseJsonArrayToListOfJsonObjects(
                                    subCategory.getJSONArray("products"));
                    for (JSONObject pj : productsJsonObjectList) {
                        if (pj.getBoolean("isActive")) {
                            products.add(new ProductsParser().parseProductJsonObject(pj));
                        }
                    }
            }
        }
        return products;
    }
    public List <Product> listInActiveProductsAtCategoryAndSubCategory(User stockTaker
            , String fpId
            , String categoryId
            , String subCategoryId
            , String status){

        logger.info("Sending list InActive Products request for " +
                        "FP with ID {}" +
                        ", category with ID {}" +
                        ", SubCategory with ID {}" +
                        " and status {}"
                , fpId, categoryId
                , subCategoryId
                , status);

        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer " + stockTaker.getAuthorizationToken())
                .queryParam("fpId", fpId)
                .queryParam("filters[categoryIds]", categoryId)
                .queryParam("filters[subCategoryIds]", subCategoryId)
                .queryParam("filters[activeStatus]", status)
                .get(configs.getStockTakeBaseURL() + getProductsAtCategoryAndSubCategory)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        List<Product> products = new ArrayList<>();
        JSONObject responseObject = new JSONObject(response.getBody().asString());
        JSONArray categories = responseObject.getJSONObject("payload").optJSONArray("categories");

        for (int i = 0; i < categories.length(); i++) {
            JSONObject category = categories.getJSONObject(i);
            JSONArray subCategories = category.getJSONArray("subCategories");
            for (int j = 0; j < subCategories.length(); j++) {
                JSONObject subCategory = subCategories.getJSONObject(j);
                List<JSONObject> productsJsonObjectList =
                        new ProductsParser().parseJsonArrayToListOfJsonObjects(
                                subCategory.getJSONArray("products"));
                for (JSONObject pj : productsJsonObjectList) {
                    if (!pj.getBoolean("isActive")) {
                        products.add(new ProductsParser().parseProductJsonObject(pj));
                    }
                }
            }
        }
        return products;
    }

public List<Product> listAllProductsAtCategoryOnly(User stockTaker, String fpId, String categoryId,String subCategoryId, String status) {
    logger.info("Sending list In/Active Products request for " +
                    "FP with ID {}" +
                    ", category with ID {}" +
                    ", SubCategory with ID {}" +
                    " and status {}"
            , fpId, categoryId
            , subCategoryId
            , status);

    Response response = RestAssured.given()
            .contentType("application/json")
            .header("Authorization", "Bearer " + stockTaker.getAuthorizationToken())
            .queryParam("fpId", fpId)
            .queryParam("filters[categoryIds]", categoryId)
            .queryParam("filters[subCategoryIds]", subCategoryId)
            .queryParam("filters[activeStatus]", status) // Assuming status "2" retrieves both active and inactive
            .get(configs.getStockTakeBaseURL() + getProductsAtCategoryAndSubCategory)
            .then()
            .log().ifValidationFails()
            .statusCode(200)
            .extract().response();

    List<Product> products = new ArrayList<>();
    JSONObject responseObject = new JSONObject(response.getBody().asString());
    JSONArray categories = responseObject.getJSONObject("payload").optJSONArray("categories");

    // Iterate through the categories and subcategories to gather products
    for (int i = 0; i < categories.length(); i++) {
        JSONObject category = categories.getJSONObject(i);
        JSONArray subCategories = category.getJSONArray("subCategories");
        for (int j = 0; j < subCategories.length(); j++) {
            JSONObject subCategory = subCategories.getJSONObject(j);
            List<JSONObject> productsJsonObjectList =
                    new ProductsParser().parseJsonArrayToListOfJsonObjects(
                            subCategory.getJSONArray("products"));
            for (JSONObject pj : productsJsonObjectList) {
                boolean isActive = pj.getBoolean("isActive");
                // Check if the product status is either active or inactive
                if (isActive || !isActive) { // This will always be true, so we can just add all products.
                    products.add(new ProductsParser().parseProductJsonObject(pj));
                }
            }
        }
    }
    return products;
}

    public Response scanningProductNotOnTheSystem(User stockTaker, String fpId, String barcode){
        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + stockTaker.getAuthorizationToken())
                .queryParam("barcode", barcode)
                .queryParam("fp_id", fpId)
                .get(configs.getStockTakeBaseURL() +scanningProductEndPoint )
                .then()
                .extract().response();
        logger.info
                ("Scanning product not at the system endpoint and response code is: " + response.statusCode());
        return response;
    }
public String getFirstInactiveProductBarcode(User stockTakeUser, String fpId, String categoryId, String subCategoryId, String status) {
    logger.info("Sending list Active Products request for FP with ID {}, category with ID {}, SubCategory with ID {} and status {}", fpId, categoryId, subCategoryId, status);

    Response response = RestAssured.given()
            .contentType("application/json")
            .header("Authorization", "Bearer " + stockTakeUser.getAuthorizationToken())
            .queryParam("fpId", fpId)
            .queryParam("filters[categoryIds]", categoryId)
            .queryParam("filters[subCategoryIds]", subCategoryId)
            .queryParam("filters[activeStatus]", status)
            .get(configs.getStockTakeBaseURL() + getProductsAtCategoryAndSubCategory)
            .then()
            .log().ifValidationFails()
            .statusCode(200)
            .extract().response();

    JSONObject responseObject = new JSONObject(response.getBody().asString());
    JSONArray categories = responseObject.optJSONObject("payload").optJSONArray("categories");

    if (categories == null || categories.length() == 0) {
        logger.warn("No categories found in response payload");
        return null; // Return null if no categories are found
    }

    for (int i = 0; i < categories.length(); i++) {
        JSONObject category = categories.getJSONObject(i);
        JSONArray subCategories = category.optJSONArray("subCategories");

        if (subCategories == null || subCategories.length() == 0) {
            logger.warn("No subcategories found for category ID {}", category.getString("id"));
            continue; // Skip to the next category
        }

        for (int j = 0; j < subCategories.length(); j++) {
            JSONObject subCategory = subCategories.getJSONObject(j);
            List<JSONObject> productsJsonObjectList = new ProductsParser().parseJsonArrayToListOfJsonObjects(subCategory.optJSONArray("products"));

            for (JSONObject pj : productsJsonObjectList) {
                if (!pj.optBoolean("isActive", true)) { // Use optBoolean to avoid exception
                    JSONArray barcodesArray = pj.optJSONArray("barcodes");
                    if (barcodesArray != null && barcodesArray.length() > 0) {
                        String barcode = barcodesArray.optString(0, null); // Safely get the first barcode
                        if (barcode != null) {
                            logger.info("Barcode of the first inactive product: {}", barcode);
                            return barcode; // Return the first barcode as a string
                        }
                    }
                }
            }
        }
    }

    logger.warn("No inactive products found in the provided categories/subcategories");
    return null; // Return null if no inactive products found
}
    public String getInActiveProductBarcodeOnCategories(User stockTakeUser) {
        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer " + stockTakeUser.getAuthorizationToken())
                .get(configs.getStockTakeBaseURL() + getAllCategoriesEndPoint)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("ListAllCategories and received a response code of 200");

        JSONObject retrievedObject = new JSONObject(response.getBody().asString());
        List<Category> allCategories = new ArrayList<>();
        String barcode = "";

        if (retrievedObject.has("payload") && !retrievedObject.getJSONArray("payload").isEmpty()) {
            JSONArray payloadArray = retrievedObject.getJSONArray("payload");
            logger.info("Total count of categories in JSON response is " + payloadArray.length());

            for (int i = 0; i < payloadArray.length(); i++) {
                JSONObject categoryJsonObject = payloadArray.getJSONObject(i);
                allCategories.add(new CategoriesParser().parseCategoryJsonObject(categoryJsonObject, CategoriesParser.STOCK_TAKE));
            }

            logger.info("Categories count is: " + allCategories.size());

            for (Category category : allCategories) {
                for (Category subCategory : category.getSubCategories()) {
                    barcode = getFirstInactiveProductBarcode(
                            stockTakeUser,
                            stockTakeUser.getFpId(),
                            category.getCategoryId(),
                            subCategory.getCategoryId(),
                            "0" // assuming "0" is the status for inactive products
                    );

                    // Check if the returned barcode is not null
                    if (barcode != null) {
                        logger.info("Inactive product barcode found: {}", barcode);
                        return barcode;
                    } else {
                        logger.warn("No inactive product barcode found for category {} and subcategory {}"
                                , category.getCategoryId(), subCategory.getCategoryId());
                    }
                }
            }
        } else {
            logger.error("Found 0 categories");
        }

        return barcode; // Return the full response object for assertion in tests
    }

    public List<Product> getAllProductsOnCategories(User stockTakeUser) {
        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer " + stockTakeUser.getAuthorizationToken())
                .get(configs.getStockTakeBaseURL() + getAllCategoriesEndPoint)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("ListAllCategories and received a response code of 200");

        JSONObject retrievedObject = new JSONObject(response.getBody().asString());
        List<Category> allCategories = new ArrayList<>();
        List<Product> allProducts = new ArrayList<>(); // List to hold all active and inactive products

        if (retrievedObject.has("payload") && !retrievedObject.getJSONArray("payload").isEmpty()) {
            JSONArray payloadArray = retrievedObject.getJSONArray("payload");
            logger.info("Total count of categories in JSON response is " + payloadArray.length());

            // Parse categories and their products
            for (int i = 0; i < payloadArray.length(); i++) {
                JSONObject categoryJsonObject = payloadArray.getJSONObject(i);
                allCategories.add(new CategoriesParser().parseCategoryJsonObject(categoryJsonObject, CategoriesParser.STOCK_TAKE));
            }

            logger.info("Categories count is: " + allCategories.size());
            // Iterate over categories and subcategories to collect products
            for (Category category : allCategories) {
                for (Category subCategory : category.getSubCategories()) {
                    // Parse active and inactive products (status "2" for both active and inactive)
                    List<Product> products = listAllProductsAtCategoryOnly(
                            stockTakeUser,
                            stockTakeUser.getFpId(),
                            category.getCategoryId(),
                            subCategory.getCategoryId(),
                            "2" // Status "2" indicates both active and inactive products
                    );

                    // Add all parsed products to the overall list
                    allProducts.addAll(products);
                }
            }

            // Log and process all collected products
            logger.info("Total products retrieved: " + allProducts.size());
        } else {
            logger.error("Found 0 categories");
        }

        return allProducts;
    }

    public Response scanningInActiveProductOnTheSystem(User stockTaker, String fpId, String barcode){
        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + stockTaker.getAuthorizationToken())
                .queryParam("barcode", barcode)
                .queryParam("fp_id", fpId)
                .get(configs.getStockTakeBaseURL() +scanningProductEndPoint )
                .then()
                .extract().response();
        logger.info
                ("Scanning product at the system and response code is: " + response.statusCode());
        return response;
    }
}
