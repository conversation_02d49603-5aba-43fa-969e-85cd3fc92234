package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import io.restassured.http.ContentType;
import models.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PickerAppApiClient extends BaseHelper {

    private final Configs configs;
    public PickerAppApiClient(Configs configs) {
        this.configs = configs;
    }
    private static final Logger logger = LoggerFactory.getLogger(FleetAppApiClient.class);
    private final String assignOrderToPickerEndpoint = "/picker-service/picker-order/assign";
    private final String loginEndpoint = "/wp-json/shopper/v1/user/login";

    private final String changePickerStatusEndpoint ="/picker-service/user/update-user-status";
    public Map<String, Object> login(User pickerUser, String phoneNumber, String password) {

        int statusCode = -1;
        String[] errors = { "No errors" };
        String message = "No message";

        Response response = RestAssured.given()
                .contentType(ContentType.MULTIPART)
                .multiPart("phone", phoneNumber)
                .multiPart("password", password)
                .header("Cookie", configs.getTestWpLoggedInCookieName())
                .post(configs.getBaseURL() + loginEndpoint)
                .then()
                .extract().response();
        JSONObject responseJson = new JSONObject(response.getBody().asString());

        if (responseJson.has("data") && !responseJson.isNull("data")) {
            JSONObject data = responseJson.getJSONObject("data");
            pickerUser.setAuthorizationToken(data.optString("token"));
            pickerUser.setUserName(data.optString("user_nicename"));
        } else {
            message = response.jsonPath().getString("message");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("statusCode", response.getStatusCode());
        result.put("message", message);
        result.put("errors", errors);

        return result;
    }

    public Map<String, Object> changePickerStatus(User pickerUser,Double longitude, Double latitude, String language, String newStatus) {

        Response response = RestAssured.given()
                .contentType(ContentType.TEXT)
                .queryParam("long", longitude)
                .queryParam("lat", latitude)
                .queryParam("lang", language)
                .queryParam("newStatus", newStatus)
                .header("Authorization", "Bearer " + pickerUser.getAuthorizationToken())
                .put(configs.getBaseURL() + changePickerStatusEndpoint)
                .then()
                .extract().response();

        Map<String, Object> result = new HashMap<>();
        result.put("statusCode", response.getStatusCode());
        result.put("data", response.jsonPath().getString("data"));
        result.put("message", response.jsonPath().getString("message"));

        return result;
    }

    public Map<String, Object> assignOrderToPicker(User admin, String adminUser, String pickerUser, List<Order> userOrders) {
        JSONArray orderIdsJsonArray = new JSONArray();
        for (Order order : userOrders) {
            orderIdsJsonArray.put(order.getOrderId());
        }
        String requestBody = "{\r\n" +
                "    \"assignedBy\": " + adminUser + ",\r\n" +
                "    \"pickerId\": " + pickerUser + ",\r\n" +
                "    \"orderIds\": [\r\n" +
                "        " + orderIdsJsonArray + "\r\n" +
                "    ]\r\n" +
                "}";

        Response response = RestAssured.given()
                .contentType("application/json")
                .body(requestBody)
                .header("Authorization", "Bearer " + admin.getAuthorizationToken())
                .post(configs.getBaseURL() + assignOrderToPickerEndpoint)
                .then()
                .extract().response();

        int statusCode = response.getStatusCode();
        String message = response.jsonPath().getString("message");
        Map<String, Object> result = new HashMap<>();
        result.put("statusCode", statusCode);

        if (statusCode == 200) {
            result.put("message", message);
            logger.info(message);
        } else if (statusCode == 400 || statusCode == 404 || statusCode == 409) {
            result.put("message", message);
            logger.error(message);
        }

        return result;
    }
}
