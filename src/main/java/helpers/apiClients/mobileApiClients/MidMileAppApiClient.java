package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.TripsDataParser;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import models.Configs;
import models.Trip;
import models.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class MidMileAppApiClient extends BaseHelper {
    private final Configs configs;
    private final String midMileLoginEndpoint = "/api/authentication/login";
    private final String getDispatcherTripsEndpoint = "/api/trips/dispatcher";
    private final String midMileStopActionEndpoint = "/api/trips/stop/action";
    private final String midMileTaskActionEndpoint = "/api/trips/task/action";
    private final String midMileListingEndPoint = "/api/trips/dispatcher";
    private static final Logger logger = LoggerFactory.getLogger(MidMileAppApiClient.class);

    public MidMileAppApiClient(Configs configs) {
        this.configs = configs;
    }

    public Response getLoginEndpointResponse(String phoneNumber, String password) {
        logger.info("Calling the MidMile login endpoint \"" + midMileLoginEndpoint + "\" for phoneNumber \""
                + phoneNumber + "\" and password \"" + password + "\"");
        String requestBody = "{\n" +
                "\"phone\": \"" + phoneNumber + "\",\n" +
                "\"password\": \"" + password + "\"\n" +
                "}";
        logger.info("JSON object to be sent to endpoint is \n\"" + requestBody + "\"");
        return RestAssured.given()
                .contentType(ContentType.JSON)
                .when()
                .body(requestBody)
                .post(configs.getMidMileAppBaseURL() + midMileLoginEndpoint)
                .then()
                .extract().response();
    }

    public Response getDispatcherTripsEndpointResponse(User dispatcher){
        String bearerToken = getMidMileUserObject(dispatcher).getAuthorizationToken();
        Response response =  RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer " + bearerToken)
                .get(configs.getMidMileAppBaseURL() + getDispatcherTripsEndpoint)
                .then()
                .extract().response();
        logger.info("Called the dispatcher GET trips endpoint and response code is: " + response.statusCode());
        return response;
    }

    public List<Trip> getAllDispatcherTrips(User dispatcher, String targetArray){
        String allTripsJsonResponse = getDispatcherTripsEndpointResponse(dispatcher)
                .then()
                .statusCode(200)
                .extract().body().asString();

        List<Trip> allTrips = new ArrayList<>();

        JSONArray tripsJsonArray = new JSONObject(allTripsJsonResponse).optJSONObject("data").optJSONArray(targetArray.toLowerCase());

        if (!tripsJsonArray.isEmpty()) {
            List<JSONObject> tripsJsonObjects = new TripsDataParser().parseJsonArrayToListOfJsonObjects(tripsJsonArray);
            logger.info("Found \"" + tripsJsonObjects.size() + "\" trips in the array of \""
                    + targetArray.toLowerCase() + "\"");
            for (JSONObject e : tripsJsonObjects){

                allTrips.add(new TripsDataParser().parseTripJsonObject(e, targetArray));
            }
            logger.info("Parsed all trips in \"" + targetArray.toLowerCase() + "\" array and the count is \""
                    + allTrips.size() + "\"");
        } else {
            logger.error("Target trips of \"" + targetArray + "\" is empty.");
        }
        return allTrips;
    }

    public Response postStopActionAPI(User dispatcher, String lat, String lng, Trip trip) {
        String bearerToken = getMidMileUserObject(dispatcher).getAuthorizationToken();

        String requestBody = "{\n" +
            "  \"action\": \"" + trip.getAction() + "\",\n" +
                    "  \"stopId\": " + trip.getStopId() + ",\n" +
                    "  \"location\": {\n" +
                    "     \"lat\": \"" + lat + "\",\n" +
                    "     \"lng\": \"" + lng + "\"\n" +
                    "  }\n" +
                    "}";

        logger.info("JSON object to be sent to endpoint is \n\"" + requestBody + "\"");
          return RestAssured.given()
                 .header("Authorization", "Bearer " + bearerToken)
                 .contentType(ContentType.JSON)
                 .when()
                 .body(requestBody)
                 .post(configs.getMidMileAppBaseURL() + midMileStopActionEndpoint)
                 .then()
                 .extract().response();
    }

    public Response postTaskActionAPI(User dispatcher,  String lat, String lng, Trip trip) {

        String bearerToken = getMidMileUserObject(dispatcher).getAuthorizationToken();

        String requestBody = "{\n" +
                "  \"action\": \"" + trip.getAction() + "\",\n" +
                "  \"taskId\": " + trip.getTaskId() + ",\n" +
                "  \"location\": {\n" +
                "     \"lat\": \"" + lat + "\",\n" +
                "     \"lng\": \"" + lng + "\"\n" +
                "  }\n" +
                "}";

        logger.info("JSON object to be sent to endpoint is \n\"" + requestBody + "\"");

        return  RestAssured.given()
                .header("Authorization", "Bearer " + bearerToken)
                .contentType(ContentType.JSON)
                .when()
                .body(requestBody)
                .post(configs.getMidMileAppBaseURL() + midMileTaskActionEndpoint)
                .then()
                .extract().response();
    }

    public User getMidMileUserObject(User midMileUser) {
        logger.info("Initiating the midMile UserObject fetching process...");
        JSONObject responseJsonObject =
                new JSONObject(getLoginEndpointResponse(
                        configs.getMidMilePhoneCountryCode().replaceAll("\\D+", "")
                                + midMileUser.getLocalPhoneNumber()
                        , midMileUser.getBypassScriptPassword())
                        .then()
                        .statusCode(200)
                        .extract().response()
                        .getBody().asString()).optJSONObject("data");

        midMileUser.setId(responseJsonObject.optString("id"));
        midMileUser.setUserName(responseJsonObject.optString("username"));
        midMileUser.setFirstName(responseJsonObject.optString("fname"));
        midMileUser.setLastName(responseJsonObject.optString("lname"));
        midMileUser.setFullName(responseJsonObject.optString("full_name"));
        midMileUser.setBlocked(responseJsonObject.optBoolean("is_blocked"));
        midMileUser.setAuthorizationToken(responseJsonObject.optString("token"));

        if (!midMileUser.getId().isBlank() || midMileUser.getId() != null) {
            logger.info("Logged in successfully with the midMile user with ID: {}", midMileUser.getId());
        } else {
            logger.error("Fetching the user object of the midMile user failed for phoneNumber \""
                    + configs.getTestMobileNumber().replaceAll("\\D+", "")
                    + midMileUser.getLocalPhoneNumber() + "\" and password \""
                    + midMileUser.getBypassScriptPassword() + "\"");
        }

        return midMileUser;
    }

    public Response getMidMileUserOrders(User midMileUser) {
        logger.info("Sending a getMidMileUserOrders request to endpoint \""
                + midMileListingEndPoint + "\"");

        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + midMileUser.getAuthorizationToken())
                .header("content-type", "application/json")
                .when()
                .get(configs.getMidMileAppBaseURL() + midMileListingEndPoint)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("GetAllOrdersUser Request completed and received a response code of 200");
        logger.info("Response body: " + response.getBody().asString());

        return response;
    }
}
