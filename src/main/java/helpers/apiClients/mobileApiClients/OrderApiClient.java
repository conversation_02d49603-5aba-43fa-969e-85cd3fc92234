package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.OrdersDataParser;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import models.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.Assert;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.*;

public class OrderApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(OrderApiClient.class);
    private final String createOrderEndpoint = "/wp-json/breadfast/v3/orders/create";
    private final String updateOrderStatusEndpoint = "/wp-json/breadfast/v3/internal/orders/update-status";
    private final String getAllOrdersEndpoint = "/wp-json/breadfast/v3/orders/get";
    private final String InaiCallBackEndpoint = "/wp-json/breadfast/v3/hook/inai-callback";
    private final String cancelOrderEndpoint = "/wp-json/breadfast/v3/orders/update_status";
    private final String syncOrderEndpoint = "/consumer-api/sync-service/orders/";
    private final String centralBaseUrl;

    public OrderApiClient(Configs configs) {
        this.configs = configs;
        centralBaseUrl = this.configs.isUseOrderApiV2() ? configs.getOrderServiceBaseURL() : configs.getBaseURL();
    }

    public Response getCreateOrderEndpointResponse(User orderOwner,
                                                   Address address,
                                                   String deliveryFees,
                                                   String tippingAmount,
                                                   String paymentMethod,
                                                   Product product,
                                                   String coupon,
                                                   Warehouse warehouse,
                                                   String date,
                                                   Boolean useBalance,
                                                   Boolean isScheduled,
                                                   Boolean isScheduledExpress,
                                                   Boolean giftReceipt) {

        int doNotUseBalance;
        if (useBalance) doNotUseBalance = 0;
        else doNotUseBalance = 1;

        logger.info("Flag to useOrderApiV2 is {} Starting the create order process for single product with baseURL: {}"
                , configs.isUseOrderApiV2(), centralBaseUrl);

        logger.info("Starting to create an order with:"
                + "\n Area: " + warehouse.getAreaName()
                + "\n Product: " + product.getMysqlId()
                + "\n Delivery Fees: " + deliveryFees
                + "\n Tipping: " + tippingAmount
                + "\n PaymentMethod: " + paymentMethod
                + "\n Warehouse: " + warehouse.getName()
                + "\n date: " + date
                + "\n don't Use Balance: " + doNotUseBalance
                + "\n isScheduled: " + isScheduled
                + "\n scheduled_express: " + isScheduledExpress
                + "\n giftReceipt: " + giftReceipt
                + "\nAnd Endpoint is: " + createOrderEndpoint);

        String deliveryTime;
        if (isScheduled == false) {
            deliveryTime = warehouse.getTimeSlot();
        } else {
            deliveryTime = getUTCFutureTimeStamp("hh:mm a", 5, 1);
        }
        logger.info("Delivery time of order is: {}", deliveryTime);

        JSONObject productJsonObject = buildProductJson(Collections.singletonList(product), false);
        String productObject = productJsonObject.toString();
        logger.info("Final productObject to send: {}", productObject);

        String warehouseObject = "{\"warehouseId\":\"" + warehouse.getId() + "\", " +
                "\"shiftId\":\"" + warehouse.getShiftId() + "\", \"warehouseName\":\"" + warehouse.getName() + "\", " +
                "\"serve\":\"now\",\"isScheduled\":" + isScheduled + "}";

        logger.info("warehouseObject is: {}", warehouseObject);

        String couponObject = "{\"success\":true,\"loading\":true,\"code\":\"" + coupon + "\"}";

        String otherDataObject = "{\"platform\":\"ios\",\"gift_receipt\":" + giftReceipt + ",\"dont_use_balance\":" + doNotUseBalance + "}";

        return RestAssured.given()
                .header("Authorization", "Bearer " + orderOwner.getAuthorizationToken())
                .contentType("multipart/form-data")
                .multiPart("address_id", address.getId())
                .multiPart("area", warehouse.getAreaName())
                .multiPart("delivery_date", date)
                .multiPart("delivery_fees", deliveryFees)
                .multiPart("payment_method", getPaymentMethod(paymentMethod))
                .multiPart("delivery_time", deliveryTime)
                .multiPart("products", productObject)
                .multiPart("coupon", couponObject)
                .multiPart("warehouse", warehouseObject)
                .multiPart("gratuity", tippingAmount)
                .multiPart("scheduled_express", isScheduledExpress)
                .multiPart("other_data", otherDataObject)
                .post(centralBaseUrl + createOrderEndpoint)
                .then()
                .log().body()
                .extract().response();
    }

    public Response getCreateOrderWithMultipleProductsEndpointResponse(User orderOwner,
                                                                       Address address,
                                                                       String deliveryFees,
                                                                       String tippingAmount,
                                                                       String paymentMethod,
                                                                       List<Product> products,
                                                                       String coupon,
                                                                       Warehouse warehouse,
                                                                       String date,
                                                                       Boolean useBalance,
                                                                       Boolean isScheduled,
                                                                       Boolean isScheduledExpress,
                                                                       Boolean giftReceipt,
                                                                       String deliveryNote) {

        int doNotUseBalance = useBalance ? 0 : 1;

        logger.info("Flag to useOrderApiV2 is {} Starting the create order process for single product with baseURL: {}"
                , configs.isUseOrderApiV2(), centralBaseUrl);

        logger.info("Starting to create an order with:"
                + "\n Area: " + warehouse.getAreaName()
                + "\n Delivery Fees: " + deliveryFees
                + "\n Tipping: " + tippingAmount
                + "\n PaymentMethod: " + paymentMethod
                + "\n Warehouse: " + warehouse.getName()
                + "\n date: " + date
                + "\n don't Use Balance: " + doNotUseBalance
                + "\n isScheduled: " + isScheduled
                + "\n scheduled_express: " + isScheduledExpress
                + "\n giftReceipt: " + giftReceipt);

        String deliveryTime = isScheduled
                ? getUTCFutureTimeStamp("hh:mm a", 5, 1)
                : warehouse.getTimeSlot();

        logger.info("Delivery time of order is: {}", deliveryTime);

        JSONObject productJsonObject = buildProductJson(products, true);
        String productObject = productJsonObject.toString();
        logger.info("Final productObject to send: {}", productObject);

        String warehouseObject = "{\"warehouseId\":\"" + warehouse.getId() + "\", " +
                "\"shiftId\":\"" + warehouse.getShiftId() + "\", \"warehouseName\":\"" + warehouse.getName() + "\", " +
                "\"serve\":\"now\",\"isScheduled\":" + isScheduled + "}";

        String couponObject = "{\"success\":true,\"loading\":true,\"code\":\"" + coupon + "\"}";

        String otherDataObject = "{\"platform\":\"ios\",\"gift_receipt\":" + giftReceipt + ",\"dont_use_balance\":" + doNotUseBalance + "}";

        return RestAssured.given()
                .header("Authorization", "Bearer " + orderOwner.getAuthorizationToken())
                .contentType("multipart/form-data")
                .multiPart("address_id", address.getId())
                .multiPart("area", warehouse.getAreaName())
                .multiPart("delivery_date", date)
                .multiPart("delivery_fees", deliveryFees)
                .multiPart("payment_method", getPaymentMethod(paymentMethod))
                .multiPart("delivery_time", deliveryTime)
                .multiPart("products", productObject)
                .multiPart("coupon", couponObject)
                .multiPart("warehouse", warehouseObject)
                .multiPart("gratuity", tippingAmount)
                .multiPart("scheduled_express", isScheduledExpress)
                .multiPart("other_data", otherDataObject)
                .multiPart("notes", deliveryNote)
                .post(centralBaseUrl + createOrderEndpoint)
                .then()
                .log().body()
                .extract().response();
    }

    private JSONObject buildProductJson(List<Product> products, boolean requireTwoProducts) {

        if (requireTwoProducts) {
            // Ensure exactly 2 products
            if (products.size() > 2) {
                logger.warn("More than 2 products passed. Only the first 2 will be used.");
                products = products.subList(0, 2);
            }
            if (products.size() != 2) {
                logger.error("Exactly 2 products are required to create the order.");
                Assert.fail("Product list must contain exactly 2 items.");
            }
        }

        JSONObject productJsonObject = new JSONObject();

        for (Product product : products) {
            String productKey;

            if (product.getOption() != null && !product.getOption().isEmpty()) {
                Options firstOption = product.getOption().get(0);
                productKey = product.getMongoId() + "_" + firstOption.getId() + "_quantity=1";

                JSONArray optionSetsArray = new JSONArray();
                JSONObject optionSetObj = new JSONObject();
                optionSetObj.put("id", product.getOptionSet().get(0).getOptionSetId());

                JSONArray optionsArray = new JSONArray();
                JSONObject optionObj = new JSONObject();
                optionObj.put("id", firstOption.getId());
                optionObj.put("quantity", 1);
                optionObj.put("type", firstOption.getType());
                optionObj.put("price", firstOption.getPrice());
                if (firstOption.getSalePrice() != null) {
                    optionObj.put("salePrice", firstOption.getSalePrice());
                }
                optionsArray.put(optionObj);

                optionSetObj.put("options", optionsArray);
                optionSetsArray.put(optionSetObj);

                JSONObject productDetails = new JSONObject();
                productDetails.put("product_id", product.getMysqlId());
                productDetails.put("quantity", 1);
                productDetails.put("subtotal", product.getPrice());
                productDetails.put("salePrice", product.getSalePrice());
                productDetails.put("price", product.getPrice());
                productDetails.put("optionSets", optionSetsArray);
                productDetails.put("total", product.getSalePrice());

                productJsonObject.put(productKey, productDetails);
            } else {
                productKey = String.valueOf(product.getMysqlId());

                JSONObject productDetails = new JSONObject();
                productDetails.put("product_id", product.getMysqlId());
                productDetails.put("quantity", 1);
                productDetails.put("subtotal", product.getPrice());
                productDetails.put("salePrice", product.getSalePrice());
                productDetails.put("price", product.getPrice());
                productDetails.put("total", product.getSalePrice());

                productJsonObject.put(productKey, productDetails);
            }
        }
        return productJsonObject;
    }

    public Order CreateOrderWithMultipleProductsUsingApi(User orderOwner,
                                                         String deliveryFees,
                                                         String tippingAmount,
                                                         String paymentMethod,
                                                         List<Product> products,
                                                         String coupon,
                                                         Warehouse warehouse,
                                                         String deliveryDate,
                                                         Boolean useBalance,
                                                         Boolean isScheduled,
                                                         Boolean isScheduledExpress,
                                                         Boolean giftReceipt,
                                                         String deliveryNote) {
        Order createdOrder = new Order();
        if (!isDateInCorrectFormatForOrderApi(deliveryDate)) {
            logger.error("Order's deliveryDate \"" + deliveryDate + "\" doesn't match the correct format of d MMMM, yyyy");
            return createdOrder;
        }

        Response response = getCreateOrderWithMultipleProductsEndpointResponse(
                orderOwner,
                orderOwner.getAddress(),
                deliveryFees,
                tippingAmount,
                paymentMethod,
                products,
                coupon,
                warehouse,
                deliveryDate,
                useBalance,
                isScheduled,
                isScheduledExpress,
                giftReceipt,
                deliveryNote);

        try {
            response.then()
                    .log().ifValidationFails()
                    .statusCode(200)
                    .body("code", equalTo("update_order_success"));
            JSONObject orderJsonObject = new JSONObject(response.getBody().asString());
            if (orderJsonObject.optString("code").toLowerCase().contains("update_order_success")
                    && orderJsonObject.optJSONObject("message").optString("id") != null) {
                logger.info("Parsing the received order object...");
                createdOrder = new OrdersDataParser().parseOrderJsonObject(orderJsonObject.optJSONObject("message")
                        , new OrdersDataParser().REQUEST_SOURCE_CUSTOMER_APP);
                logger.info("Finalized parsing order object for order with ID \"" + createdOrder.getOrderId()
                        + "\" and orderNumber \"" + createdOrder.getOrderNumber() + "\"");
            } else {
                logger.error("Response code is 200. However, there is an error in the response and order is not " +
                        "created and full response is below: {}", response.getBody().asString());
            }
        } catch (Exception e) {
            logger.error("Creating order failed with exception.", e);
        }
        return createdOrder;
    }

    public Order CreateOrderUsingApi(User orderOwner,
                                     String deliveryFees,
                                     String tippingAmount,
                                     String paymentMethod,
                                     Product product,
                                     String coupon,
                                     Warehouse warehouse,
                                     String deliveryDate,
                                     Boolean useBalance,
                                     Boolean isScheduled,
                                     Boolean isScheduledExpress,
                                     Boolean giftReceipt) {
        Order createdOrder = new Order();
        if (!isDateInCorrectFormatForOrderApi(deliveryDate)) {
            logger.error("Order's deliveryDate \"" + deliveryDate + "\" doesn't match the correct format of d MMMM, yyyy");
            return createdOrder;
        }

        Response response = getCreateOrderEndpointResponse(
                orderOwner,
                orderOwner.getAddress(),
                deliveryFees,
                tippingAmount,
                paymentMethod,
                product,
                coupon,
                warehouse,
                deliveryDate,
                useBalance,
                isScheduled,
                isScheduledExpress,
                giftReceipt);

        try {
            response.then()
                    .log().ifValidationFails()
                    .statusCode(200);

            response.then()
                    .log().ifValidationFails()
                    .body("code", equalTo("update_order_success"));

            JSONObject orderJsonObject = new JSONObject(response.getBody().asString());
            if (orderJsonObject.optString("code").toLowerCase().contains("update_order_success")) {
                logger.info("Parsing the received order object...");
                createdOrder = new OrdersDataParser().parseOrderJsonObject(orderJsonObject.optJSONObject("message")
                        , new OrdersDataParser().REQUEST_SOURCE_CUSTOMER_APP);
                logger.info("Finalized parsing order object for order with ID \"" + createdOrder.getOrderId()
                        + "\" and orderNumber \"" + createdOrder.getOrderNumber() + "\"");
            } else {
                logger.error("Response code is 200. However, there is an error in the response and order is not " +
                        "created and full response is below: \n" + response.getBody().asString());
            }
        } catch (Exception e) {
            logger.error("Creating order failed with exception.", e);
        }
        return createdOrder;
    }

    public void cancelOrder(User orderOwner, String orderId, String orderStatus, Boolean isCreditOrder) {

        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + orderOwner.getAuthorizationToken())
                .contentType("multipart/form-data")
                .multiPart("status", orderStatus)
                .multiPart("order_id", orderId)
                .multiPart("credit", isCreditOrder)
                .post(configs.getBaseURL() + cancelOrderEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        JSONObject orderCancelledJsonObject = new JSONObject(response.getBody().asString());

        if (orderCancelledJsonObject.optJSONObject("message").optString("status").equals(orderStatus)) {
            logger.info("orderId {} status updated successfully to {}", orderId, orderStatus);
        } else {
            logger.error("Failed to update status");
        }
    }

    public List<Order> listAllOrdersUser(User ordersOwner) {
        logger.info("Getting the orders list for user with ID \"" + ordersOwner.getId() + "\" and phoneNumber \""
                + ordersOwner.getPhoneNumber() + "\"");
        logger.info("Sending a getAllOrdersUser request to endpoint \"" + getAllOrdersEndpoint + "\"");

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + ordersOwner.getAuthorizationToken())
                .contentType("application/json")
                .post(centralBaseUrl + getAllOrdersEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("GetAllOrdersUser Request completed and received a response code of 200");

        List<Order> allOrders = new ArrayList<>();
        if (new JSONObject(response.getBody().asString()).optString("code").contains("get_orders_success")) {
            JSONObject retrievedList = new JSONObject(response.getBody().asString()).optJSONObject("message");
            if (!(retrievedList == null)) {
                logger.info("Total count of found orders is " + retrievedList.length());
                logger.info("Starting to parse each order in the list...");
                for (String key : retrievedList.keySet()) {
                    allOrders.add(new OrdersDataParser().parseOrderJsonObject(retrievedList.optJSONObject(key)
                            , new OrdersDataParser().REQUEST_SOURCE_CUSTOMER_APP));
                }
                logger.info("Orders processing completed and count of processed orders is: " + allOrders.size());
            } else {
                logger.error("Found 0 orders.");
            }
        } else {
            logger.error("Getting the orders list for user with ID \"" + ordersOwner.getId() + "\" and phoneNumber \""
                    + ordersOwner.getPhoneNumber() + "\" failed and full API response is below \n"
                    + response.getBody().asString());
        }
        return allOrders;
    }

    public void markOrderAsNotReceived(User adminUser, String orderId, String orderStatus) {
        logger.info("Mark Order As Not Received");
        logger.info("Sending request to mark order as not received for Order ID: " + orderId);

        switch (orderStatus.toLowerCase()) {
            case "not received":
            case "not-received":
                orderStatus = "not-received";
                break;
            default:
                orderStatus = "not-received";
        }

        String requestBody = "{\n" +
                "    \"orders_ids\": [\n" +
                "        \"" + orderId + "\"\n" +
                "    ],\n" +
                "    \"status\": \"" + orderStatus + "\"\n" +
                "}";

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .header("content-type", "application/json")
                .body(requestBody)
                .when()
                .post(configs.getBaseURL() + updateOrderStatusEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        if (response.jsonPath().getString("message." + orderId + ".new_status").equals(orderStatus)) {
            logger.info("orderId {} status updated successfully to {}", orderId, orderStatus);
        } else {
            logger.error("Failed to update status");
        }
    }

    public void markOrderAsFailed(User adminUser, String orderId, String orderStatus) {
        logger.info("Mark Order As Failed");
        logger.info("Sending request to mark order as failed for Order ID: " + orderId);

        orderStatus = switch (orderStatus.toLowerCase()) {
            default -> "failed";
        };

        String requestBody = "{\n" +
                "    \"orders_ids\": [\n" +
                "        \"" + orderId + "\"\n" +
                "    ],\n" +
                "    \"status\": \"" + orderStatus + "\"\n" +
                "}";

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .cookie(configs.getTestWpLoggedInCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpLoggedInCookieName()))
                .cookie(configs.getTestWpNodeAuthorizationCookieName()
                        , adminUser.getAdminAuthorisationCookies().get(configs.getTestWpNodeAuthorizationCookieName()))
                .header("content-type", "application/json")
                .body(requestBody)
                .when()
                .post(configs.getBaseURL() + updateOrderStatusEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        if (response.jsonPath().getString("message." + orderId + ".new_status").equals(orderStatus)) {
            logger.info("orderId {} status updated successfully to {}", orderId, orderStatus);
        } else {
            logger.error("Failed to update status");
        }
    }

    public void getInaiCallBackEndpointResponse(String cardBrand, int clientOrderId, String orderId, String orderStatus, double wholeTotalAmount, double transactionAmount,
                                                double walletTransactionAmount, String cardId, String last4, String cardType, String transactionId, String currency, String trxCardProviderType,
                                                String transactionStatus, String transactionType, String walletProviderType, String walletTransactionId) {
        // Construct JSON request body using variables
        String requestBody = "{\n" +
                "    \"order\": {\n" +
                "        \"cards\": [\n" +
                "            {\n" +
                "                \"brand\": \"" + cardBrand + "\",\n" +
                "                \"id\": \"" + cardId + "\",\n" +
                "                \"last_4\": \"" + last4 + "\",\n" +
                "                \"type\": \"" + cardType + "\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"client_order_id\": " + clientOrderId + ",\n" +
                "        \"id\": \"" + orderId + "\",\n" +
                "        \"status\": \"" + orderStatus + "\",\n" +
                "        \"total_amount\": \"" + wholeTotalAmount + "\",\n" +
                "        \"transactions\": [\n" +
                "            {\n" +
                "                \"amount\": " + transactionAmount + ",\n" +
                "                \"card\": {\n" +
                "                    \"brand\": \"" + cardBrand + "\",\n" +
                "                    \"id\": \"" + cardId + "\",\n" +
                "                    \"last_4\": \"" + last4 + "\",\n" +
                "                    \"type\": \"" + cardType + "\"\n" +
                "                },\n" +
                "                \"currency\": \"" + currency + "\",\n" +
                "                \"id\": \"" + transactionId + "\",\n" +
                "                \"provider_type\": \"" + trxCardProviderType + "\",\n" +
                "                \"status\": \"" + transactionStatus + "\",\n" +
                "                \"type\": \"" + transactionType + "\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"amount\": " + walletTransactionAmount + ",\n" +
                "                \"currency\": \"" + currency + "\",\n" +
                "                \"id\": \"" + walletTransactionId + "\",\n" +
                "                \"provider_type\": \"" + walletProviderType + "\",\n" +
                "                \"status\": \"" + transactionStatus + "\",\n" +
                "                \"type\": \"" + transactionType + "\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";

        // Send the POST request to the specified endpoint and extract the response
        Response response = RestAssured.given()
                .header("token", configs.getApplePayInaiCallBackToken())
                .contentType(ContentType.JSON)
                .body(requestBody) // Attach the request body
                .post(configs.getBaseURL() + InaiCallBackEndpoint) // Send the POST request to the specified endpoint
                .then()
                .extract().response(); // Extract the response
        logger.info("Response status code: " + response.getStatusCode()); // Check the status code
    }

    public Response syncOrder(String orderId) {
        logger.info("Syncing order with ID: {}", orderId);
        logger.info("Sending sync request to endpoint: {}", configs.getBaseURL() + syncOrderEndpoint + orderId);

        Response response = RestAssured.given()
                .contentType("application/json")
                .post(configs.getBaseURL() + syncOrderEndpoint + orderId)
                .then()
                .extract().response();

        logger.info("Sync order request completed with status code: {}", response.getStatusCode());

        if (response.getStatusCode() == 200
                && new JSONObject(response.then().extract().body().asString()).optString("status")
                .equalsIgnoreCase("success")) {
            JSONArray orderDetails = new JSONObject(response.getBody().asString())
                    .getJSONObject("payload")
                    .getJSONObject("result")
                    .getJSONArray("ordersDetails");
            if (orderDetails.length() > 0) {
                String status = orderDetails.getJSONObject(0).getString("status");
                logger.info("Status of the first array in the details: {}", status);
            } else {
                logger.warn("The orderDetails array is empty.");
            }
        } else {
            logger.error("Sync order request failed with status code: {} and response: {}",
                    response.getStatusCode(), response.getBody().asString());
        }

        return response;
    }

    private String getPaymentMethod(String paymentMethod) {
        return switch (paymentMethod.toLowerCase()) {
            case "cash", "cod" -> "cod";
            case "cc", "credit", "inai" -> "inai";
            case "apple_pay", "applepay", "apple" -> "apple_pay";
            default -> "cod";
        };
    }

    private String getPaymentTitle(String paymentTitle) {
        return switch (paymentTitle.toLowerCase()) {
            case "cash", "cod" -> "cash on delivery";
            case "cc", "credit", "inai" -> "Credit/Debit";
            case "apple_pay", "applepay", "apple" -> "Apple Pay";
            case "balance" -> "Balance";
            default -> "cod";
        };
    }

    public boolean isDateInCorrectFormatForOrderApi(String date) {
        try {
            String pattern = "d MMMM, yyyy";
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
            formatter.parse(date); // Try parsing the date string
        } catch (Exception e) {
            return false;
        }
        return true; // If parsing succeeds, the format is correct
    }
}
