package helpers.apiClients.mobileApiClients;

import com.opencsv.CSVReader;
import groovy.json.JsonException;
import helpers.EncryptionHelper;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Address;
import models.CardService;
import models.Configs;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.StringReader;
import java.security.SecureRandom;
import java.util.List;

public class CardServiceApiClient {
    String loginSchemeEndpoint = "/api/v1/web/user/login";
    String cardStatusEndpoint = "/api/v2/web/cards/status";
    String loginUserByPasscode = "/api/v2/mobile/wallet_users/login";
    String cardsPoolEndpoint = "/api/v1/web/cards/pool";
    String setPassCodeEndpoint = "/api/v2/mobile/wallet_users/createPin";
    String setPinEndpoint = "/api/v1/mobile/cards/set-pin";
    String linkCardEndpoint = "/api/v2/mobile/cards/link";
    String activateEndpoint = "/api/v1/mobile/cards/activate";
    String resetPinEndpoint = "/api/v2/mobile/cards/reset-pin";
    String getBalanceEndPoint = "/api/v1/mobile/wallet_users/getbalance";
    String getReceiverDetailsEndpoint = "/api/v1/mobile/wallet_users/show";
    String getTrxHistoryEndPoint = "/api/v1/mobile/wallet_users/history";
    String singleAdjustment = "/api/v1/web/adjustment/single/confirmTransation";
    String checkSenderEndpoint = "/api/v1/mobile/transaction/checkSender";
    String replaceEndpoint = "/api/v1/mobile/cards/replace";
    String generateBatchEndpoint = "/api/v1/web/invitation-codes/generate";
    String exportInvitationCode = "/api/v1/web/invitation-codes/export";
    String consumeCodeEndpoint = "/api/v1/web/invitation-codes/consume";
    String completeTransactionEndpoint = "/api/v1/mobile/transaction/completeTransaction";
    String cardDetailsEndpoint = "/api/v1/mobile/cards/details";
    String resetPasscodeEndpoint = "/api/v2/mobile/wallet_users/resetUserMPin";
    String validateCodeEndPoint = "/api/v2/mobile/wallet_users/validate-code";
    String validateNationalIDEndPoint = "/api/v2/mobile/wallet_users/validate-national-id";
    String updatePasscodeEndPoint = "/api/v1/mobile/wallet_users/updateMPin";
    String changeCardStatusEndpoint = "/api/v1/web/wallet_users/received";
    String createCardUserEndpoint = "/api/v1/web/wallet_users/createCardUser";
    String editCustomerDetailsEndpoint = "/api/v1/web/wallet_users/update";
    String sendOtpForReplaceCardEndpoint = "/api/v1/mobile/otp/send";
    String validateReplaceCardOtpEndpoint = "/api/v1/mobile/otp/validate";
    String verifyPasscodeForReplacingCard = "/api/v1/mobile/wallet_users/verifyMPin";
    private static final Logger logger = LoggerFactory.getLogger(CardServiceApiClient.class);
    Configs configs;

    public CardServiceApiClient(Configs configs) {
        this.configs = configs;
    }

    public CardService getCardServiceToken(String userName, String password, CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + loginSchemeEndpoint +
                "\" and username is: \"" + userName + "\" and the password is: \"" + password + "\"");

        String requestBody = "{\n" +
                "\"username\": \"" + userName + "\",\n" +
                "\"password\": \"" + password + "\"\n" +
                "}";

        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + loginSchemeEndpoint)
                    .then()
                    .log().ifValidationFails()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            // Fetching the token and add it to CardService
            JSONObject jsonObject = new JSONObject(response);
            cardService.setCardServiceToken(jsonObject.optString("token"));

            logger.info("Successfully logged in using the endpoint: {}", loginSchemeEndpoint);
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + loginSchemeEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return cardService;
    }

    public String getCardStatus(String breadfastID, CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + cardStatusEndpoint +
                "\" and breadfast_id as: \"" + breadfastID + "\"");

        String body = "{\"breadfast_id\": \"" + breadfastID + "\"}";
        String requestBody = "{\"data\":\"" + new EncryptionHelper(configs).encryptData(body) + "\"}";

        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getCardServiceToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + cardStatusEndpoint).then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + cardStatusEndpoint +
                    "\" with message \"" + jsonObject.optString("message") +
                    "\" and card status \"" + jsonObject.getJSONObject("data").getString("status") + "\"");

            return new JSONObject(response).getJSONObject("data").getString("status");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + cardStatusEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    // {"breadfast_id":"1234","mpin":"702570","device_info":{"device_id":"1234"}};
    public void setPasscode(String breadfastID, CardService cardService) {
        cardService.setPassCode(configs.getDefaultCardPasscode());

        SecureRandom secureRandom = new SecureRandom();
        String deviceId = secureRandom.toString();

        logger.info("Initiating an API call to the endpoint \"" + setPassCodeEndpoint +
                "\" and breadfast id as: \"" + breadfastID +
                "\" and the mpin as \"" + cardService.getPassCode() +
                "\" and device_id as \"" + deviceId + "\"");

        String body = "{\n" +
                "\"breadfast_id\": \"" + breadfastID + "\",\n" +
                "\"mpin\": \"" + cardService.getPassCode() + "\",\n" +
                "\"device_info\": {\"device_id\": \"" + deviceId + "\"}\n" +
                "}";
        String requestBody = "{\"data\":\"" + new EncryptionHelper(configs).encryptData(body) + "\"}";

        try {
            String response = RestAssured.given().contentType("application/json")
                    .header("Content-Type", "application/json")
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + setPassCodeEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            logger.info("Request is sent successfully to the endpoint \"" + setPassCodeEndpoint
                    + "\" with success message " + new JSONObject(response).optString("message"));
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + setPassCodeEndpoint +
                    "\" with error message " + e.getMessage());
        }
    }

    public void getCardsPool(String contractNumber,
                             String typeId, String productNumber,
                             CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + cardsPoolEndpoint + "\"");

        String requestBody = "{\n" +
                "\"cardsCount\": 1,\n" +
                "\"contractNumber\": \"" + contractNumber + "\",\n" +
                "\"typeId\": \"" + typeId + "\",\n" +
                "\"productNumber\": \"" + productNumber + "\"\n" +
                "}";

        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getCardServiceToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + cardsPoolEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            // Parse the response body as a JSONArray since "data" is an array
            JSONObject jsonObject = new JSONObject(response);
            cardService.setBcid(jsonObject.getJSONArray("data").getJSONObject(0)
                    .optString("cardToken"));
            cardService.setPackageNumber(jsonObject.getJSONArray("data").getJSONObject(0)
                    .optString("packageNumber"));

            logger.info("Request is sent successfully to the endpoint \"" + cardsPoolEndpoint
                    + "\" with success message " + jsonObject.optString("message")
                    + "\" and the BCID is " + cardService.getBcid()
                    + "\" and the packageNumber is " + cardService.getPackageNumber());
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + cardsPoolEndpoint +
                    "\" with error message " + e.getMessage());
        }
    }
    public Response loginByPasscodeAndGetUserToken(String breadfastID, CardService cardService) {
        SecureRandom secureRandom = new SecureRandom();
        String deviceId = secureRandom.toString();

        logger.info("Initiating an API call to the endpoint \"" + loginUserByPasscode +
                "\" and send Breadfast ID as: \"" + breadfastID +
                "\" and the mpin as \"" + cardService.getPassCode() +
                "\" and device_id as\"" + deviceId + "\"");

        String body = "{\"breadfast_id\": \"" + breadfastID + "\",\"mpin\": \""
                + cardService.getPassCode() + "\",\"scheme_id\": 1,"
                + "\"device_info\": {\"device_id\": \"" + deviceId + "\"}\n" +
                "}";
        String requestBody = "{\"data\":\"" + new EncryptionHelper(configs).encryptData(body) + "\"}";

        try {
            Response response = RestAssured.given().contentType("application/json")
                    .header("Content-Type", "application/json")
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + loginUserByPasscode)
                    .then()
                    .extract().response();

            if (response.statusCode() == 200){
                //Fetching card token from json body and saved it to cardService
                JSONObject jsonObject = new JSONObject(response.getBody().asString());
                cardService.setUserToken(jsonObject.optString("token"));

                logger.info("Successfully logged in using the endpoint {}", loginUserByPasscode);
            }

            return response;
        }
        catch (JsonException e) {
            logger.error("Failed to parse JSON response: {}", e.getMessage());
        }
        catch (Exception e) {
            logger.error("An error occurred during calling endpoint {} with error message: {}"
                    , loginUserByPasscode, e.getMessage());
        }
        return null;
    }

    public void linkCard(CardService cardService) {
        logger.info("Initiating an API call to the endpoint {} and send card BC_ID as: {}"
                , linkCardEndpoint, cardService.getBcid());

        String requestBody = "{\n" +
                "    \"bcid\": \"" + cardService.getBcid() + "\"\n" + // Use the cardToken parameter here
                "}";

        try {
            String response = RestAssured.given()
                    .header("Content-Type", "application/json")
                    .header("Authorization", cardService.getUserToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + linkCardEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + linkCardEndpoint
                    + "\" with success message " + jsonObject.optString("message"));
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + linkCardEndpoint +
                    "\" with error message " + e.getMessage());
        }
    }

    public String setPin(CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + setPinEndpoint + "\"");

        try {
            String response = RestAssured.given()
                    .header("Authorization", cardService.getUserToken())
                    .post(configs.getCardServicesBaseURL() + setPinEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + setPinEndpoint
                    + "\" with success message \"" + jsonObject.optString("message")
                    + "\" and activation link is " + jsonObject.getJSONObject("data").getString("webview_link"));

            //Set pin through activation link
            return jsonObject.getJSONObject("data").getString("webview_link");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + setPinEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public void activateCard(CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + activateEndpoint + "\"");

        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .post(configs.getCardServicesBaseURL() + activateEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + activateEndpoint
                    + "\" with success message " + jsonObject.optString("message"));
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + activateEndpoint +
                    "\" with error message " + e.getMessage());
        }
    }

    public String resetPin(CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + resetPinEndpoint + "\"" +
                " sending bcid as: \"" + cardService.getBcid() + "\"" +
                "  and national_id as: \"" + cardService.getNationalId() + "\"");

        String requestBody = "{\n" +
                "  \"bcid\": \"" + cardService.getBcid() + "\",\n" +
                "  \"national_id\": \"" + cardService.getNationalId() + "\"\n" +
                "}";

        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + resetPinEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + resetPinEndpoint
                    + "\" with success message " + jsonObject.optString("message")
                    + " and activation link is " + jsonObject.getJSONObject("data").getString("webview_link"));

            //Set pin through activation link
            return jsonObject.getJSONObject("data").getString("webview_link");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + resetPinEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public String receiverDetails(String mobileNo, CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + getReceiverDetailsEndpoint +
                "\" and send mobile number as: \"" + mobileNo + "\"");

        String requestBody = "{\n" +
                "\"mobile_number\": \"" + mobileNo + "\"\n" +
                "}";
        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + getReceiverDetailsEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + getReceiverDetailsEndpoint
                    + "\" with success message " + jsonObject.optString("message"));
            return new JSONObject(response).getString("message");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + getReceiverDetailsEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public Double getBalanceEndPointResponse(CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + getBalanceEndPoint + "\"");

        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .post(configs.getCardServicesBaseURL() + getBalanceEndPoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + getBalanceEndPoint
                    + "\" with success message " + jsonObject.optString("message")
                    + "\" with data " + jsonObject.optString("data"));
            return new JSONObject(response).getJSONObject("data").getDouble("balance");

        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + getBalanceEndPoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public String getTransactionHistoryEndPoint(CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + getTrxHistoryEndPoint + "\"");

        try {
            String requestBody = "{\n" +
                    "    \"limit\" :5,\n" +
                    "    \"offset\" : 0\n" +
                    "}";
            String response = RestAssured.given()
                    .contentType("application/json")
                    .body(requestBody)
                    .header("Authorization", cardService.getUserToken())
                    .post(configs.getCardServicesBaseURL() + getTrxHistoryEndPoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + getTrxHistoryEndPoint
                    + "\" with success message " + jsonObject.optString("message")
                    + "\" with data " + jsonObject.optString("data"));
            return new JSONObject(response).getString("message");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + getTrxHistoryEndPoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public void singleAdjustmentEndPoint(CardService cardService, String adminUser) {
        logger.info("Initiating an API call to the endpoint \"" + singleAdjustment + "\"");

        String requestBody = "{\n" +
                "\"bcid\": \"" + cardService.getBcid() + "\",\n" +
                "\"action\": \"credit\"," +
                "\"amount\": \"1000\",\n" +
                "\"notes\": \"Test\"," +
                "\"username\": \"" + adminUser + "\"}";

        logger.info("Check the request body \"" + requestBody + "\"");

        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .body(requestBody)
                    .header("Authorization", cardService.getCardServiceToken())
                    .post(configs.getCardServicesBaseURL() + singleAdjustment)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);

            //Fetching response message and validate it
            logger.info("Request is sent successfully to the endpoint \"" + singleAdjustment +
                    "\" with success message " + jsonObject.optString("message"));
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + singleAdjustment +
                    "\" with error message " + e.getMessage());
        }

    }

    public String checkSender(String mobileNo, float amount,CardService cardService,String transferReason , String notes) {
        logger.info("Initiating an API call to the endpoint \"" + checkSenderEndpoint +
                "\" and send mobile number as: \"" + mobileNo + "\"");

        JSONObject requestBody = new JSONObject();
        requestBody.put("amount", amount);
        requestBody.put("recieverMobile", mobileNo);
        requestBody.put("type", "p2p");
        requestBody.put("note", notes);
        requestBody.put("transfer_reason", transferReason);

        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .body(requestBody.toString())
                    .post(configs.getCardServicesBaseURL() + checkSenderEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            //Get the requestRefNum from the response and add it to CardService
            JSONObject jsonObject = new JSONObject(response);
            cardService.setRequestRefNum(jsonObject.getJSONObject("data").getString("requestRefNum"));

            //Fetching response message and validate it
            logger.info("Request is sent successfully to the endpoint \"" + checkSenderEndpoint +
                    "\" with success message " + jsonObject.optString("message"));

            return jsonObject.getJSONObject("data").getString("transfer_reason");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + checkSenderEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public String completeTransaction(CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + completeTransactionEndpoint + "\"");

        String requestBody = "{\n" +
                "\"mpin\": \"" + cardService.getPassCode() + "\",\n" +
                "\"requestRefNum\": \"" + cardService.getRequestRefNum() + "\"\n" +
                "}";

        logger.info("Check the request body \"" + requestBody + "\"");

        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + completeTransactionEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);

            //Fetching response message and validate it
            logger.info("Request is sent successfully to the endpoint \"" + completeTransactionEndpoint +
                    "\" with success message " + jsonObject.optString("message"));
            return new JSONObject(response).getString("message");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + completeTransactionEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public String replaceCard(CardService cardService, String otp) {
        logger.info("Initiating an API call to the endpoint \"" + replaceEndpoint + "\"");
        String requestBody = "{\"otp\": " + otp + "}";
        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + replaceEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();
            JSONObject jsonObject = new JSONObject(response);
            logger.info
                    ("Request is sent successfully to the endpoint \"" + replaceEndpoint
                            + "\" with success message " + jsonObject.optString("message"));
            return new JSONObject(response).getString("message");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + replaceEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public void generateInvitationCodeBatch(CardService cardService) {
        logger.info("Initiating API calls to the endpoints " + generateBatchEndpoint + "\"");

        String requestBody = "{\"numberOfCodes\": 1}";
        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getCardServiceToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + generateBatchEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();
            JSONObject jsonObject = new JSONObject(response);

            // Retrieve id as an integer
            int id = jsonObject.getJSONObject("data").getInt("id");
            // Convert it to a string if needed
            cardService.setBatchId(String.valueOf(id));

            logger.info("Request is sent successfully to the endpoint \"" + generateBatchEndpoint +
                    "\" with success message " + jsonObject.optString("message") +
                    " and batch id is " + id);
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + generateBatchEndpoint +
                    "\" with error message " + e.getMessage());
        }
    }

    public String exportInvitationCode(CardService cardService) {
        logger.info("Initiating API calls to the endpoints " + exportInvitationCode);

        String requestBody = "{\"batchId\":" + cardService.getBatchId() + "}";
        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getCardServiceToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + exportInvitationCode)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            //Parse the CSV response
            List<String[]> csvRows = new CSVReader(new StringReader(response)).readAll();

            //Extract the invitation code from row number 2 in the csv
            for (String[] row : csvRows) {
                cardService.setInvitationCode(row[0]);
            }

            logger.info("Request is sent successfully to the endpoint \"" + exportInvitationCode +
                    "\" and the invitation code is " + cardService.getInvitationCode());

            return cardService.getInvitationCode();
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + exportInvitationCode +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public void consumeInvitationCode(String mobileNo, String id, CardService cardService) {
        logger.info("Initiating API calls to the endpoints " + consumeCodeEndpoint + "\"");

        try {
            String requestBody = "{\n" +
                    "\"mobileNumber\": \"" + mobileNo + "\",\n" +
                    "\"invitationCode\": \"" + cardService.getInvitationCode() + "\",\n" +
                    "\"breadfastId\": \"" + id + "\"\n" +
                    "}";

            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getCardServiceToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + consumeCodeEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();
            JSONObject jsonObject = new JSONObject(response);

            //Fetching response message and validate it
            logger.info("Request is sent successfully to the endpoint \"" + consumeCodeEndpoint +
                    "\" with success message " + jsonObject.optString("message"));
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + consumeCodeEndpoint +
                    "\" with error message " + e.getMessage());
        }
    }

    public String getCardDetails(CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + cardDetailsEndpoint + "\"");

        try {
            String response = RestAssured.given()
                    .header("Authorization", cardService.getUserToken())
                    .post(configs.getCardServicesBaseURL() + cardDetailsEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            //Get card last 4 digits from the response
            JSONObject jsonObject = new JSONObject(response);
            cardService.setCardLastFourDigits(jsonObject.getJSONObject("data").getString("last_4"));

            //Fetching response message and validate it
            logger.info("Request is sent successfully to the endpoint \"" + cardDetailsEndpoint
                    + "\" with success message \"" + jsonObject.optString("message")
                    + "\" with data " + jsonObject.optString("data"));

            return new JSONObject(response).getString("message");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + cardDetailsEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public void collectCard(CardService cardService, String walletUserId, String pickupLocationId) {
        logger.info("Initiating an API call to the endpoint \"" + changeCardStatusEndpoint + "\"");

        //Parse the JSON result to extract the wallet user id and extract the walletUserId from the first object in the array
        JSONArray walletUserIdsJsonArray = new JSONArray(walletUserId);  // Convert the query result to a JSON array
        JSONObject walletUserJson = walletUserIdsJsonArray.getJSONObject(0);  // Get the first JSON object
        walletUserId = walletUserJson.getString("id");  // Extract the 'id' field

        try {
            String requestBody = "{\n" +
                    "\"walletUserId\": \"" + walletUserId + "\",\n" +
                    "\"packageNumber\": \"" + cardService.getPackageNumber() + "\",\n" +
                    "\"pickupLocationId\": \"" + pickupLocationId + "\"\n" +
                    "}";

            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getCardServiceToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + changeCardStatusEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();
            JSONObject jsonObject = new JSONObject(response);

            //Fetching response message and validate it
            logger.info("Request is sent successfully to the endpoint \"" + changeCardStatusEndpoint
                    + "\" with success message \"" + jsonObject.optString("message"));
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + changeCardStatusEndpoint +
                    "\" with error message " + e.getMessage());
        }
    }

    public String resetPasscode(String breadfastID) {

        logger.info("Initiating an API call to the endpoint \"" + resetPasscodeEndpoint +
                "\" and breadfast_id as: \"" + breadfastID + "\"");

        String body = "{\n" +
                "    \"scheme_id\": 1,\n" +
                "    \"breadfast_id\": \"" + breadfastID + "\"\n" +
                "}";
        String requestBody = "{\"data\":\"" + new EncryptionHelper(configs).encryptData(body) + "\"}";

        try {
            String response = RestAssured.given().contentType("application/json")
                    .header("Content-Type", "application/json")
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + resetPasscodeEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);
            logger.info("Request is sent successfully to the endpoint \"" + resetPasscodeEndpoint
                    + "\" with success message " + jsonObject.optString("message"));
            return jsonObject.getString("message");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + resetPasscodeEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public String validateCode(String breadfastID, String verificationCode, CardService cardService) {

        String body = "{\n" +
                "    \"verification_code\": \"" + verificationCode + "\",\n" +
                "    \"breadfast_id\": \"" + breadfastID + "\"\n" +
                "}";
        String requestBody = "{\"data\":\"" + new EncryptionHelper(configs).encryptData(body) + "\"}";
        try {
            String response = RestAssured.given().contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + validateCodeEndPoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();
            JSONObject jsonObject = new JSONObject(response);
            logger.info("Request is sent successfully to the endpoint \"" + validateCodeEndPoint
                    + "\" with success message " + jsonObject.optString("message"));
            return jsonObject.getString("message");

        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + validateCodeEndPoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public String validateNationalID(String breadfastID, CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + validateNationalIDEndPoint + "\"" +
                "    \"breadfast_id\": \"" + breadfastID + "\",\n" +
                "  and national_id as: \"" + cardService.getNationalId() + "\"");

        String body = "{\n" +
                "    \"breadfast_id\": \"" + breadfastID + "\",\n" +
                "    \"national_id\": \"" + cardService.getNationalId() + "\"\n" +
                "}";
        String requestBody = "{\"data\":\"" + new EncryptionHelper(configs).encryptData(body) + "\"}";

        try {
            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + validateNationalIDEndPoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();
            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + validateNationalIDEndPoint
                    + "\" with success message " + jsonObject.optString("message"));
            return jsonObject.getString("message");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + validateNationalIDEndPoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public String updatePasscode(CardService cardService, String verificationCode) {

        logger.info("Initiating an API call to the endpoint \"" + updatePasscodeEndPoint +
                "\" national_id as: \"" + cardService.getNationalId() +
                "\" and the new mpin as \"" + configs.getUpdatedCardPasscode() + "\"");

        String body = "{\n" +
                "    \"mpin\": \"" + configs.getUpdatedCardPasscode() + "\",\n" +
                "    \"confirm_mpin\": \"" + configs.getUpdatedCardPasscode() + "\",\n" +
                "    \"code\": \"" + verificationCode + "\",\n" +
                "    \"national_id\": \"" + cardService.getNationalId() + "\"\n" +
                "}";

        String requestBody = "{\"data\":\"" + new EncryptionHelper(configs).encryptData(body) + "\"}";
        try {
            String response = RestAssured.given().contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + updatePasscodeEndPoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();
            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + updatePasscodeEndPoint
                    + "\" with success message " + jsonObject.optString("message"));

            return jsonObject.getString("message");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + updatePasscodeEndPoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public void createCardUser(String mobileNo, String id, String email, String firstName,
                               String remainingName, String nationalId, String nationalIdExpiryDate,
                               CardService cardService) {
        logger.info("Initiating an API call to the endpoint \"" + createCardUserEndpoint +
                "\" mobile number as: \"" + mobileNo + "\", Breadfast ID  as \"" + id +
                "\", email as: \"" + email + "\", first name as: \"" + firstName +
                "\", remaining name as: \"" + remainingName + "\", national ID as: \"" + nationalId +
                "\", national ID expiry date as \"" + nationalIdExpiryDate + "\"");

        cardService.setNationalId(nationalId);

        String body = "{\n" +
                "\"mobile_number\": \"" + mobileNo + "\",\n" +
                "\"breadfast_id\": \"" + id + "\",\n" +
                "\"email\": \"" + email + "\",\n" +
                "\"first_name\": \"" + firstName + "\",\n" +
                "\"remaining_name\": \"" + remainingName + "\",\n" +
                "\"national_id\": \"" + nationalId + "\",\n" +
                "\"national_id_expiry_date\": \"" + nationalIdExpiryDate + "\"\n" +
                "}";

        String requestBody = "{\"data\":\"" + new EncryptionHelper(configs).encryptData(body) + "\"}";

        logger.info(requestBody);

        try {
            String response = RestAssured.given().contentType("application/json")
                    .header("Authorization", cardService.getCardServiceToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + createCardUserEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();
            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request is sent successfully to the endpoint \"" + createCardUserEndpoint
                    + "\" with success message " + jsonObject.optString("message"));
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + createCardUserEndpoint +
                    "\" with error message " + e.getMessage());
        }
    }

    public void editCustomerDetails(CardService cardService, String walletUserId, String firstName,
                                    String remainingFullName, String nationalId, String nationalIdExpiryDate,
                                    Address testAddress, String gender, String city, String birthdate) {

        logger.info("Initiating an API call to the endpoint \"" + editCustomerDetailsEndpoint + "\"");

        try {
            // Parse the JSON result to extract the wallet user id
            JSONArray walletUserIdsJsonArray = new JSONArray(walletUserId);
            JSONObject walletUserJson = walletUserIdsJsonArray.getJSONObject(0);
            walletUserId = walletUserJson.getString("id");

            String requestBody = "{\n" +
                    "\"userId\": \"" + walletUserId + "\",\n" +
                    "\"fname\": \"" + firstName + "\",\n" +
                    "\"lname\": \"" + remainingFullName + "\",\n" +
                    "\"national_id\": \"" + nationalId + "\",\n" +
                    "\"address\": \"" + testAddress + "\",\n" +
                    "\"gender\": \"" + gender + "\",\n" +
                    "\"city\": \"" + city + "\",\n" +
                    "\"birthdate\": \"" + birthdate + "\",\n" +
                    "\"expiry_date\": \"" + nationalIdExpiryDate + "\"\n" +
                    "}";

            logger.info("Request body to be sent: {}", requestBody);

            String response = RestAssured.given()
                    .contentType("application/json")
                    .header("Authorization", cardService.getCardServiceToken())
                    .body(requestBody)
                    .post(configs.getCardServicesBaseURL() + editCustomerDetailsEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();
            JSONObject jsonObject = new JSONObject(response);

            logger.info("Request was successful to endpoint \"{}\" with message: \"{}\"",
                    editCustomerDetailsEndpoint, jsonObject.optString("message"));
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + editCustomerDetailsEndpoint +
                    "\" with error message " + e.getMessage());
        }
    }

    public String sendOtpForReplaceCard(String mobileNo, String type, CardService cardService) {

        logger.info("Initiating an API call to the endpoint \"" + sendOtpForReplaceCardEndpoint +
                "\" mobile number as : \"" + mobileNo +
                "\" and the type as \"" + type + "\"");

        JSONObject json = new JSONObject();
        json.put("mobile_number", mobileNo);
        json.put("type", type);
        String body = json.toString();

        try {
            String response = RestAssured.given().contentType("application/json")
                    .header("Content-Type", "application/json")
                    .header("Authorization", cardService.getCardServiceToken())
                    .body(body)
                    .post(configs.getCardServicesBaseURL() + sendOtpForReplaceCardEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);
            logger.info("Request is sent successfully to the endpoint \"" + sendOtpForReplaceCardEndpoint
                    + "\" with success message " + jsonObject.optString("message"));
            return jsonObject.getString("message");
        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + sendOtpForReplaceCardEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public String validateReplacedCardOtp(String mobileNo, String verificationCode, String type, CardService cardService) {

        logger.info("Initiating an API call to the endpoint \"" + validateReplaceCardOtpEndpoint +
                "\" mobile number as : \"" + mobileNo +
                "\" and the type as \"" + type + "\"");

        JSONObject json = new JSONObject();
        json.put("verification_code", verificationCode);
        json.put("mobile_number", mobileNo);
        json.put("type", type);
        String body = json.toString();

        try {
            String response = RestAssured.given().contentType("application/json")
                    .header("Authorization", cardService.getCardServiceToken())
                    .body(body)
                    .post(configs.getCardServicesBaseURL() + validateReplaceCardOtpEndpoint)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();
            JSONObject jsonObject = new JSONObject(response);
            logger.info("Request is sent successfully to the endpoint \"" + validateReplaceCardOtpEndpoint
                    + "\" with success message " + jsonObject.optString("message"));
            return jsonObject.getString("message");

        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling API \"" + validateReplaceCardOtpEndpoint +
                    "\" with error message " + e.getMessage());
        }
        return null;
    }

    public boolean verifyPasscodeForReplacingCard(String breadfastID, CardService cardService) {
        SecureRandom secureRandom = new SecureRandom();
        String deviceId = secureRandom.toString();

        logger.info("Initiating an API call to the endpoint \"" + verifyPasscodeForReplacingCard +
                "\" and send Breadfast ID as: \"" + breadfastID +
                "\" and the mpin as \"" + cardService.getPassCode() +
                "\" and device_id as \"" + deviceId + "\"");

        JSONObject deviceInfo = new JSONObject();
        deviceInfo.put("device_id", deviceId);

        JSONObject bodyJson = new JSONObject();
        bodyJson.put("breadfast_id", breadfastID);
        bodyJson.put("mpin", cardService.getPassCode());
        bodyJson.put("scheme_id", 1);
        bodyJson.put("device_info", deviceInfo);

        String encryptedBody = new EncryptionHelper(configs).encryptData(bodyJson.toString());

        JSONObject requestBody = new JSONObject();
        requestBody.put("data", encryptedBody);

        try {
            String response = RestAssured.given().contentType("application/json")
                    .header("Authorization", cardService.getUserToken())
                    .body(requestBody.toString())
                    .post(configs.getCardServicesBaseURL() + verifyPasscodeForReplacingCard)
                    .then()
                    .statusCode(200)
                    .extract().response()
                    .getBody().asString();

            JSONObject jsonObject = new JSONObject(response);
            logger.info("Request is sent successfully to the endpoint \"" + verifyPasscodeForReplacingCard
                    + "\" with success message " + jsonObject.optString("success"));
            return jsonObject.getBoolean("success");

        } catch (JsonException e) {
            logger.error("Failed to parse JSON response: {}", e.getMessage());
        } catch (Exception e) {
            logger.error("An error occurred during calling endpoint {} with error message: {}",
                    verifyPasscodeForReplacingCard, e.getMessage());
        }
        return false;
    }
}
