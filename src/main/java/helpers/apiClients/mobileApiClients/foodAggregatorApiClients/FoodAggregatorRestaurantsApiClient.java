package helpers.apiClients.mobileApiClients.foodAggregatorApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.BusinessCategoryDataParser;
import helpers.dataParsers.RestaurantsDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.BusinessCategory;
import models.Configs;
import models.Restaurant;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.ArrayList;
import java.util.List;

public class FoodAggregatorRestaurantsApiClient extends BaseHelper {

    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(FoodAggregatorRestaurantsApiClient.class);
    private final String getRestaurantsByLocationEndpoint = "/api/v2/restaurants";
    private final String getSingleRestaurantByIdEndpoint = "/api/v1/restaurants/{restaurantId}";
    private final String getBusinessCategoriesEndpoint = "/api/v1/categories";

    public FoodAggregatorRestaurantsApiClient(Configs configs) {
        this.configs = configs;
    }

    public Response getRestaurantsByLocationEndpointResponse(double latitude, double longitude, int page, int pageSize){
        logger.info("Sending a getRestaurantsByLocation request for " +
                "latitude: \"" + latitude + "\", longitude: \"" + longitude +
                "\", page: \"" + page + "\", pageSize: \"" + pageSize + "\"");

        return RestAssured.given()
                .contentType("application/json")
                .queryParam("latitude",latitude)
                .queryParam("longitude",longitude)
                .queryParam("page", page)
                .queryParam("pageSize", pageSize)
                .get(configs.getFoodAggregatorServiceBaseURL()+getRestaurantsByLocationEndpoint)
                .then()
                .extract().response();
    }

    public List<Restaurant> getRestaurantsDataByLocation() {
        List<Restaurant> allRestaurantsList = new ArrayList<>();
        int currentPage = 1;
        int pageSize = 10;
        int totalPages = 1; // Will be updated after first call

        do {
            logger.info("Fetching restaurants from page: " + currentPage);

            //Call the endpoint itself map the whole response to a variable
            Response response =
                    getRestaurantsByLocationEndpointResponse(configs.getTestLatitude(), configs.getTestLongitude(), currentPage, pageSize);
            JSONObject responseJsonObject = new JSONObject(response.getBody().asString());

            //Assert status code in response
            response.then().assertThat().statusCode(200);

            //Extract Restaurants Array
            if(responseJsonObject.optString("status").equals("success")){
                JSONObject payload = responseJsonObject.optJSONObject("payload");

                // Update total pages from response (only needed on first iteration)
                if (currentPage == 1) {
                    totalPages = payload.optInt("totalPages", 1);
                    logger.info("Total pages to fetch: " + totalPages);
                }

                JSONArray retrievedRestaurantsArray = payload.optJSONArray("data");
                if (retrievedRestaurantsArray != null && !retrievedRestaurantsArray.isEmpty()) {
                    logger.info("Starting to parse " + retrievedRestaurantsArray.length() +
                            " restaurants from page " + currentPage + " of " + totalPages);
                    List<JSONObject> restaurantsJsonObjects = new RestaurantsDataParser()
                            .parseJsonArrayToListOfJsonObjects(retrievedRestaurantsArray);
                    for (JSONObject restaurant : restaurantsJsonObjects) {
                        allRestaurantsList.add(new RestaurantsDataParser().parseRestaurantJsonObject(restaurant,new RestaurantsDataParser().REQUEST_SOURCE_CUSTOMER_APP));
                    }
                }
                else {
                    logger.error("Retrieved Restaurants Array is Empty for page " + currentPage + ". Here is the full response Body : " + responseJsonObject);
                }
            }
            else {
                logger.error("status key value in the response body is not Success or missing for page " + currentPage + ". Here is the full response Body : " + responseJsonObject);
                break; // Exit loop on error
            }

            currentPage++;

        } while (currentPage <= totalPages);

        logger.info("Successfully fetched " + allRestaurantsList.size() +
                " restaurants across " + (currentPage - 1) + " pages");

        return allRestaurantsList;
    }

    public Response filterRestaurantsByCategoryEndpointResponse(double latitude, double longitude, int categoryId, int page, int pageSize){
        logger.info("Sending a filterRestaurantsByCategory request for " +
                "latitude: \"" + latitude + "\", longitude: \"" + longitude +
                "\", categoryId: \"" + categoryId + "\", page: \"" + page + "\", pageSize: \"" + pageSize + "\"");

        return RestAssured.given()
                .contentType("application/json")
                .queryParam("latitude",latitude)
                .queryParam("longitude",longitude)
                .queryParam("category_id",categoryId)
                .queryParam("page", page)
                .queryParam("pageSize", pageSize)
                .get(configs.getFoodAggregatorServiceBaseURL()+getRestaurantsByLocationEndpoint)
                .then()
                .extract().response();
    }

    public List<Restaurant> getRestaurantsDataByCategoryId(int categoryId) {
        List<Restaurant> allRestaurantsList = new ArrayList<>();
        int currentPage = 1;
        int pageSize = 10;
        int totalPages = 1; // Will be updated after first call

        do {
            logger.info("Fetching restaurants from page: " + currentPage + " for category ID: " + categoryId);

            //Call the endpoint itself map the whole response to a variable
            Response response =
                    filterRestaurantsByCategoryEndpointResponse(configs.getTestLatitude(), configs.getTestLongitude(), categoryId, currentPage, pageSize);
            JSONObject responseJsonObject = new JSONObject(response.getBody().asString());

            //Assert status code in response
            response.then().assertThat().statusCode(200);

            //Extract Restaurants Array
            if(responseJsonObject.optString("status").equals("success")){
                JSONObject payload = responseJsonObject.optJSONObject("payload");

                // Update total pages from response (only needed on first iteration)
                if (currentPage == 1) {
                    totalPages = payload.optInt("totalPages", 1);
                    logger.info("Total pages to fetch for category ID " + categoryId + ": " + totalPages);
                }

                JSONArray retrievedRestaurantsArray = payload.optJSONArray("data");
                if (retrievedRestaurantsArray != null && !retrievedRestaurantsArray.isEmpty()) {
                    logger.info("Starting to parse " + retrievedRestaurantsArray.length() +
                            " restaurants from page " + currentPage + " of " + totalPages + " for category ID: " + categoryId);
                    List<JSONObject> restaurantsJsonObjects = new RestaurantsDataParser()
                            .parseJsonArrayToListOfJsonObjects(retrievedRestaurantsArray);
                    for (JSONObject restaurant : restaurantsJsonObjects) {
                        allRestaurantsList.add(new RestaurantsDataParser().parseRestaurantJsonObject(restaurant,new RestaurantsDataParser().REQUEST_SOURCE_CUSTOMER_APP));
                    }
                }
                else {
                    if (currentPage == 1) {
                        logger.error("No Restaurant is Linked To {} category ID, Here is the full response {} : ",categoryId,responseJsonObject);
                    } else {
                        logger.warn("No restaurants found on page " + currentPage + " for category ID: " + categoryId);
                    }
                }
            }
            else {
                logger.error("status key value in the response body is not Success or missing for page " + currentPage +
                        " and category ID " + categoryId + ". Here is the full response Body : " + responseJsonObject);
                break; // Exit loop on error
            }

            currentPage++;

        } while (currentPage <= totalPages);

        logger.info("Successfully fetched " + allRestaurantsList.size() +
                " restaurants across " + (currentPage - 1) + " pages for category ID: " + categoryId);

        return allRestaurantsList;
    }

    public Response getSingleRestaurantByIdEndpointResponse(double latitude, double longitude,int id){

        logger.info("Sending a getSingleRestaurantById request for restaurantId = " + id);

        return RestAssured.given()
                .contentType("application/json")
                .pathParam("restaurantId",id)
                .queryParam("latitude",latitude)
                .queryParam("longitude",longitude)
                .get(configs.getFoodAggregatorServiceBaseURL()+getSingleRestaurantByIdEndpoint)
                .then()
                .extract().response();
    }

    public Restaurant getSingleRestaurantByIdData(int id) {
        //Call the endpoint itself map the whole response to a variable
        Response response = getSingleRestaurantByIdEndpointResponse(configs.getTestLatitude(),configs.getTestLongitude(),id);
        JSONObject responseJsonObject = new JSONObject(response.getBody().asString());

        //Assert status code in response
        response.then().assertThat().statusCode(200);

        //Extract Restaurant Object
        JSONObject retrievedRestaurantJsonObject = new JSONObject();

        if (responseJsonObject.optString("status").equals("success")) {
            retrievedRestaurantJsonObject = responseJsonObject.optJSONObject("payload");
            logger.info("Starting to parse restaurant with Id : " + retrievedRestaurantJsonObject.optInt("id"));
        } else {
            logger.error("status key value in the response body is not Success or missing.Here is the full response Body : " + responseJsonObject);
        }
        return new RestaurantsDataParser().parseRestaurantJsonObject(retrievedRestaurantJsonObject,new RestaurantsDataParser().REQUEST_SOURCE_CUSTOMER_APP);
    }

    public Response getBusinessCategoriesEndpointResponse(double latitude, double longitude){

        logger.info("Sending a getBusinessCategories request ");

        return RestAssured.given()
                .contentType("application/json")
                .queryParam("latitude",latitude)
                .queryParam("longitude",longitude)
                .get(configs.getFoodAggregatorServiceBaseURL()+getBusinessCategoriesEndpoint)
                .then()
                .extract().response();
    }

    public List<BusinessCategory> getBusinessCategoriesData() {
        //Call the endpoint itself map the whole response to a variable
        Response response = getBusinessCategoriesEndpointResponse(configs.getTestLatitude(),configs.getTestLongitude());
        JSONObject responseJsonObject = new JSONObject(response.getBody().asString());

        //Assert status code in response
        response.then().assertThat().statusCode(200);

        //Extract Categories Array
        List<BusinessCategory> businessCategoriesList = new ArrayList<>();
        if(responseJsonObject.optString("status").equals("success")){
            JSONArray retrievedCategoriesArray = responseJsonObject.optJSONArray("payload");
            if (!retrievedCategoriesArray.isEmpty()) {
                logger.info("Starting to parse categories with a total count of :" + retrievedCategoriesArray.length());
                List<JSONObject> businessCategoriesJsonObjects = new BusinessCategoryDataParser()
                        .parseJsonArrayToListOfJsonObjects(retrievedCategoriesArray);
                for (JSONObject category : businessCategoriesJsonObjects) {
                    businessCategoriesList.add(new BusinessCategoryDataParser().parseBusinessCategoryJsonObject(category) );
                }
            }
            else {
                logger.error("Retrieved Categories Array is Empty .Here is the full response Body : " + responseJsonObject);
            }
        }
        else {
            logger.error("status key value in the response body is not Success or missing.Here is the full response Body : " + responseJsonObject);
        }
        return businessCategoriesList;
    }

}
