package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.PaymentCategoryParser;
import helpers.dataParsers.PaymentProviderParser;
import helpers.dataParsers.PaymentServiceParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.PaymentCategory;
import models.PaymentService;
import models.PaymentServiceProvider;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class MobilePayServicesApiClient extends BaseHelper {
    private final Configs configs;

    private static final Logger logger = LoggerFactory.getLogger(MobilePayServicesApiClient.class);

    private final String getValidCategoriesEndpoint = "/api/categories/has-providers";

    private final String getProvidersByCategoryIdEndpoint = "/api/categories/%s/with-providers";

    private final String getServicesByProviderIdEndpoint = "/api/providers/%s/with-services";

    public MobilePayServicesApiClient(Configs configs) {
        this.configs = configs;
    }

    public List<PaymentCategory> getAllPaymentCategories(String authToken){
        logger.info("Sending a request to retrieve all PaymentCategories using authToken: " + authToken);
        Response response = RestAssured.given()
                .header("Authorization", authToken)
                .get(configs.getBillingServicesBaseURL() + getValidCategoriesEndpoint)
                .then()
                .log().ifError()
                .statusCode(200)
                .extract().response();

        logger.info("GetAllCategories Request completed and received a response code of 200");

        JSONArray categoriesJsonArray = new JSONArray(response.getBody().asString());
        List<PaymentCategory> paymentCategories = new ArrayList<>();

        logger.info("Starting to parse the payment categories");
        for (JSONObject e : new PaymentCategoryParser().parseJsonArrayToListOfJsonObjects(categoriesJsonArray)){
            PaymentCategory pc = new PaymentCategoryParser().parsePaymentCategoryJsonObject(e);

            List<PaymentServiceProvider> paymentServiceProvidersPerCategory =
                    getAllPaymentProvidersInCertainCategory(pc.getCategoryId(), authToken);

            for (PaymentServiceProvider psp : paymentServiceProvidersPerCategory){
                List<PaymentService> paymentServices =
                        getAllPaymentServicesInCertainProvider(psp.getServiceProviderId(), authToken);
                psp.setPaymentServices(paymentServices);
                pc.getPaymentServiceProviders().add(psp);
            }
            paymentCategories.add(pc);
        }

        logger.info("Completed Parsing of " + paymentCategories.size() + " payment categories");
        return paymentCategories;
    }

    public List<PaymentServiceProvider> getAllPaymentProvidersInCertainCategory(int categoryId, String authToken){
        logger.info("Sending a GET request for all providers in category " + categoryId + " and using authToken: "
                + authToken);
        Response response = RestAssured.given()
                .header("Authorization", authToken)
                .get(configs.getBillingServicesBaseURL() + String.format(getProvidersByCategoryIdEndpoint, categoryId))
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("GetProvidersByCategoryId " + categoryId + " completed and received a response code of 200");

        JSONObject responseJsonObject = new JSONObject(response.getBody().asString());
        JSONArray providersJsonArray = new JSONArray(responseJsonObject.optJSONArray("providers"));
        List<PaymentServiceProvider> providersList = new ArrayList<>();

        logger.info("Starting to parse payment providers in category " + categoryId);
        for (JSONObject e : new PaymentProviderParser().parseJsonArrayToListOfJsonObjects(providersJsonArray)){
            providersList.add(new PaymentProviderParser().parsePaymentProviderJsonObject(e));
        }

        logger.info("Completed Parsing of " + providersList.size() + " payment providers in category " + categoryId);
        return providersList;
    }

    public List<PaymentService> getAllPaymentServicesInCertainProvider(int providerId, String authToken){
        logger.info("Sending a GET request for all services in provider " + providerId + " and using authToken: "
                + authToken);
        Response response = RestAssured.given()
                .header("Authorization", authToken)
                .get(configs.getBillingServicesBaseURL() + String.format(getServicesByProviderIdEndpoint, providerId))
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("GetServicesByProviderId " + providerId + " completed and received a response code of 200");

        JSONObject responseJsonObject = new JSONObject(response.getBody().asString());
        JSONArray servicesJsonArray = responseJsonObject.optJSONArray("services");
        List<PaymentService> paymentServices = new ArrayList<>();

        logger.info("Starting to parse payment services in provider " + providerId);
        for (JSONObject e : new PaymentServiceParser().parseJsonArrayToListOfJsonObjects(servicesJsonArray)){
            paymentServices.add(new PaymentServiceParser().parsePaymentServiceJsonObject(e));
        }
        logger.info("Completed parsing of " + paymentServices.size() + " payment service in provider " + providerId);
        return paymentServices;
    }
}
