package helpers.apiClients.mobileApiClients;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.User;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import helpers.BaseHelper;

public class KitchenApiClient extends BaseHelper {
    private final Configs configs;
    private final String postLoginBaristaEndPoint = "/barista/login";

    private final String getBaristaUser = "/barista/users/details";
    private final String updateChefStatusEndpoint = "/chef/users/status";
    private final String getChefOrdersEndpoint = "/chef/orders";
    private final String postLoginChef= "/order-fulfillment/user/login";

    private final String setManagersAndDas = "/warehouses-service/warehouses/{warehouseId}/managersAndDas";

    private static final Logger logger = LoggerFactory.getLogger(KitchenApiClient.class);

    public KitchenApiClient(Configs configs) {
        this.configs = configs;
    }

    public Response postLoginsEndpointResponse(String phoneNumber, String password) {
        String requestBody = "{\"phone\": \"" + phoneNumber + "\", \"password\": \"" + password + "\"}";
        logger.info("JSON object to be sent to endpoint is \n\"" + requestBody + "\"");
        Response response = RestAssured.given()
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getPickerServicesBaseURL() + postLoginBaristaEndPoint)
                .then()
                .extract().response();
        return response;
    }

    public User getChefUserObject(User chefUser) {
        JSONObject responseJsonObject =
                new JSONObject(postLoginsEndpointResponse(configs.getChefCountryCode()
                        + configs.getChefPhoneNumber(), configs.getChefPassword()).getBody().asString()).optJSONObject("data");

        chefUser.setId(responseJsonObject.optString("id"));
        chefUser.setUserName(responseJsonObject.optString("username"));
        chefUser.setFirstName(responseJsonObject.optString("fname"));
        chefUser.setLastName(responseJsonObject.optString("lname"));
        chefUser.setFullName(responseJsonObject.optString("full_name"));
        chefUser.setBlocked(responseJsonObject.optBoolean("is_blocked"));
        chefUser.setAuthorizationToken(responseJsonObject.optString("token"));
        return chefUser;
    }

    public Response getChefData(User chefUser) {
        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + chefUser.getAuthorizationToken())
                .header("content-type", "application/json")
                .when()
                .get(configs.getPickerServicesBaseURL() + getBaristaUser)
                .then()
                .statusCode(200)
                .extract().response();
        return response;
    }

    public Response updateChefStatus(User chefUser, String status) {
        String requestBody = "{\n    \"newStatus\":\"" + status + "\"\n}";
        return RestAssured.given()
                .contentType("application/json")
                .body(requestBody)
                .header("Authorization", "Bearer " + chefUser.getAuthorizationToken())
                .put(configs.getPickerServicesBaseURL() + updateChefStatusEndpoint)
                .then()
                .extract().response();
    }

    public Response getChefAssignedOrders(User chefUser) {
        return RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer " + chefUser.getAuthorizationToken())
                .header("Content-Type", "application/json")
                .get(configs.getPickerServicesBaseURL() + getChefOrdersEndpoint)
                .then()
                .extract().response();

    }

    public String getChefAssignedWarehouseId(User chefUser) {
        Response response = getChefData(chefUser);
        response.getBody().asString();
        String assignedFpId = response.jsonPath().getString("data.fp._id");
        return assignedFpId;
    }

    public void unAssignSupportManagerToFps(User adminUser, String warehouseId, String supportManagerUserId) {

        String requestBody = "{\n" + "\t\"supportManagersIds\" : \"" + supportManagerUserId + "\"\n" + "}";
        RestAssured.given()
                .header("Authorization", "Bearer "
                        + adminUser.getAuthorizationToken())
                .contentType("application/json")
                .body(requestBody)
                .pathParam("warehouseId", warehouseId)
                .put(configs.getBaseURL() + setManagersAndDas)
                .then()
                .statusCode(200)
                .extract().response();
    }

    public Response loginChef(String phoneNumber, String password) {
        logger.info("Initiating the chef login request for phone#: {}", phoneNumber);

        String requestBody = "{\"phone\": \"" + phoneNumber + "\", \"password\": \"" + password + "\"}";

        return RestAssured.given()
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getPickerServicesBaseURL()+ postLoginChef)
                .then()
                .extract().response();
    }
}
