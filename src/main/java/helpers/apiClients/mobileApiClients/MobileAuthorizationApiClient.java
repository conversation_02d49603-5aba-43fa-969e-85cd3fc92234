package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.User;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MobileAuthorizationApiClient extends BaseHelper {
    private final Configs configs;

    private static final Logger logger = LoggerFactory.getLogger(MobileAuthorizationApiClient.class);

    private final String sendOtpEndpoint = "/wp-json/breadfast/v4/user/send-otp";
    private final String verifyOtpEndpoint = "/wp-json/breadfast/v4/user/verify-otp";
    private final String registerEndpoint = "/wp-json/breadfast/v4/user/register";
    private final String getUserDataEndpoint = "/wp-json/breadfast/v3/user/data";
    private final String logoutEndpoint = "/wp-json/breadfast/v3/user/logout";

    public MobileAuthorizationApiClient(Configs configs) {
        this.configs = configs;
    }

    public void sendOtpToPhoneNumber(String phoneNumber, String countryCode){
        logger.info("sending a sendOTP request with phoneNumber \"{}\" and countryCode \"{}\"",
                phoneNumber,
                countryCode);

        String requestBody = "{\n" +
                "\"phone\": \"" + phoneNumber + "\",\n" +
                "\"country_code\": \"" + countryCode + "\"\n" +
                "}";

        Response response = RestAssured.given()
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getBaseURL() + sendOtpEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        JSONObject jsonObject = new JSONObject(response.getBody().asString());
        if (jsonObject.optInt("status") == 200){
            logger.info("OTP should be sent. Received the message \"" + jsonObject.optString("message") + "\"");
        } else {
            logger.error("OTP is not sent. Received the message \"" + jsonObject.optString("message") + "\"");
        }
    }

    public User verifyOtpRequest(String phoneNumber, String countryCode, String otp, User user){
        logger.info("Sending a verify Otp request with phoneNumber: \"{}\" and countryCode: \"{}\" with OTP: \"{}\"",
                phoneNumber,
                countryCode,
                otp);

        String requestBody = "{\n" +
                "\"phone\": \"" + phoneNumber + "\",\n" +
                "\"country_code\": \"" + countryCode + "\",\n" +
                "\"otp\": \"" + otp + "\"" +
                "}";

        Response response = RestAssured.given()
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getBaseURL() + verifyOtpEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        JSONObject jsonObject = new JSONObject(response.getBody().asString());
        if (jsonObject.optInt("status") == 200){
            logger.info("OTP is verified successfully.");
            if (jsonObject.has("data")){
                logger.info("Found the data JSON object.");
                if (jsonObject.optJSONObject("data").has("token")){
                    logger.info("Found the token key inside the data object");
                    user.setAuthorizationToken(jsonObject.optJSONObject("data").optString("token"));
                    user.setInDeletionProcess(jsonObject.optJSONObject("data")
                            .optBoolean("was_in_deletion_process"));
                    logger.info("Setting authToken and deletion flag in the user object completed successfully." +
                            "\nCurrent was_in_deletion_process is: \"" + user.isInDeletionProcess() + "\"");
                } else if (jsonObject.optJSONObject("data").has("register_token")){
                    logger.info("Found the register_token key inside the data object");
                    user.setRegisterToken(jsonObject.optJSONObject("data").optString("register_token"));
                    logger.info("Setting the register_token value in the user object completed successfully.");

                } else {
                    logger.error("VerifyOTP endpoint response format is not as expected. Data object doesn't contain" +
                            "token nor register_token");
                }
            }
        } else {
            logger.error("status key value in the response body is not 200 or missing.");
        }

        return user;
    }

    public User registerUserRequest(String firstName, String lastName, String email, String registerToken, String referralCode, User user){
        logger.info("""
        Sending a register request with the below details:
        firstName: {}
        lastName: {}
        email: {}
        referralCode: {}
        registerToken: {}
        """, firstName, lastName, email, referralCode, registerToken);

        String requestBody = "{" +
                "\n\"first_name\": \"" + firstName + "\"," +
                "\n\"last_name\": \"" + lastName + "\"," +
                "\n\"email\": \"" + email + "\"," +
                "\n\"ref_code\": \"" + referralCode + "\"," +
                "\n\"register_token\": \"" + registerToken + "\"" +
                "}";

        Response response = RestAssured.given()
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getBaseURL() + registerEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        JSONObject jsonObject = new JSONObject(response.getBody().asString());
        if (jsonObject.optInt("status") == 200){
            logger.info("Register request completed successfully.");
            JSONObject userJsonObject = jsonObject.optJSONObject("data");
            user.setId(String.valueOf(userJsonObject.optInt("id")));
            user.setAuthorizationToken(userJsonObject.optString("token"));
            logger.info("User's ID is set to: " + user.getId());
        }
        return user;
    }

    public User getUserReferralCode(User user){
        logger.info("Sending a request to get user data for user with ID: {}", user.getId());
        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + user.getAuthorizationToken())
                .contentType("application/json")
                .post(configs.getBaseURL() + getUserDataEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        JSONObject jsonObject = new JSONObject(response.getBody().asString());
        if (jsonObject.optString("code").equalsIgnoreCase("user_data_success")) {
            logger.info("Getting User Info request completed successfully.");
            JSONObject userJsonObject = jsonObject.optJSONObject("data").optJSONObject("referrals");

            if (userJsonObject != null) {
                user.setReferralCode(userJsonObject.optString("code", ""));
                logger.info("User's Referral Code is set to: {}", user.getReferralCode());
            } else {
                logger.warn("Referrals information is missing in the response.");
            }
        } else {
            logger.error("Retrieving user data failed for userID: {}", user.getId());
        }

        return user;
    }

    public void logoutUserRequest(User user){
        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + user.getAuthorizationToken())
                .contentType("application/json")
                .post(configs.getBaseURL() + logoutEndpoint)
                .then()
                .statusCode(200)
                .extract().response();
    }
}
