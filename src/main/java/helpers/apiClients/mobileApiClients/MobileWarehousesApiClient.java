package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.*;
import helpers.factories.dataFactories.customerAppDataFactories.ProductsDataFactory;
import io.restassured.RestAssured;
import io.restassured.config.HttpClientConfig;
import io.restassured.config.RestAssuredConfig;
import io.restassured.response.Response;
import models.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class MobileWarehousesApiClient extends BaseHelper {
    private final Configs configs;
    private final ProductsDataFactory productsDataFactory;
    private static final Logger logger = LoggerFactory.getLogger(MobileWarehousesApiClient.class);
    private final String getWarehouseByLocationEndpoint = "/warehouses-service/warehouses/location-data-new";
    private final String getCategoriesByWarehouseIdEndpoint = "/warehouses-service/warehouses/subcategories";
    private final String getProductsBySubCategoryIdEndpoint = "/warehouses-service/warehouses/subcategories/";
    private final String getAreasEndpoint = "/wp-json/breadfast/v3/main/areas";
    private final String getProductDetailsEndpoint = "/warehouses-service/warehouses/products/{product}";
    private final String getAvailabilityEndpoint = "/warehouses-service/warehouses/availability";

    public MobileWarehousesApiClient(Configs configs) {
        this.configs = configs;
        productsDataFactory = new ProductsDataFactory(this.configs);
    }

    public Warehouse getWarehouseByLocation(String latitude, String longitude) {
        logger.info("Sending a getWarehouseByLocation request for latitude: \"{}\" and longitude: \"{}\"" +
                " to endpoint \"{}\"", latitude, longitude, getAvailabilityEndpoint);
        Response response = RestAssured.given()
                .queryParam("lat", latitude)
                .queryParam("lng", longitude)
                .get(configs.getBaseURL() + getAvailabilityEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        Warehouse retrievedWarehouse = null;

        JSONObject warehouseJsonObject = new JSONObject(response.getBody().asString());
        if (warehouseJsonObject.optJSONObject("warehouse") != null
                && warehouseJsonObject.optJSONObject("warehouse").optString("id") != null
                && !warehouseJsonObject.optJSONObject("warehouse").optString("id").isEmpty()
                && !warehouseJsonObject.optJSONObject("warehouse").optString("id").isBlank()) {
            logger.info("Retrieved a warehouse with the ID: \"{}\" and name: \"{}\""
                    , warehouseJsonObject.optJSONObject("warehouse").optString("id")
                    , warehouseJsonObject.optJSONObject("warehouse").optString("name"));
            retrievedWarehouse = new WarehousesDataParser().parseWarehouseJsonObject(warehouseJsonObject
                    , new WarehousesDataParser().REQUEST_SOURCE_CUSTOMER_APP);
            logger.info("Parsed the JSON object from the endpoint \"{}\" response for warehouse \"{}\"",
                    getAvailabilityEndpoint, retrievedWarehouse.getId());
        } else {
            logger.error("This area with latitude: \"{}\" and longitude: \"{}\" isn't covered by any warehouse."
                    , latitude, longitude);
        }
        return retrievedWarehouse;
    }

    public List<Category> getCategoriesByWarehouse(String warehouseId, String nowTomorrowType) {
        logger.info("Sending a getCategoriesByWarehouse for warehouse \"" + warehouseId + "\" and it's for \""
                + nowTomorrowType + "\" categories");
        Response response = RestAssured.given()
                .queryParam("serve", nowTomorrowType)
                .queryParam("warehouseId", warehouseId)
                .get(configs.getBaseURL() + getCategoriesByWarehouseIdEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        List<Category> retrievedList = new ArrayList<>();
        JSONArray categoriesJsonArray = new JSONObject(response.getBody().asString())
                .getJSONObject("data").getJSONArray("categories");

        if (!categoriesJsonArray.isEmpty()) {
            logger.info("Starting to parse categories with total count of: " + categoriesJsonArray.length());
            List<JSONObject> categoriesJsonObjects =
                    new WarehousesDataParser().parseJsonArrayToListOfJsonObjects(categoriesJsonArray);
            for (int i = 0; i < categoriesJsonObjects.size(); i++){
                if ((i < 2 && configs.getMobileBuildEnabled())
                        || configs.getExcludedCategoryIds().contains(categoriesJsonObjects.get(i).optInt("id")))
                    continue;
                JSONObject e = categoriesJsonObjects.get(i);

                Category c = new CategoriesParser()
                        .parseCategoryJsonObject(e, new CategoriesParser().REQUEST_SOURCE_CUSTOMER_APP);

                logger.info("Parsed the category with it's subCategories excluding the now and later lists.");

                logger.info("Starting to parse the now and later lists for category  with id \"" + c.getId()
                        + "\" and name \"" + c.getName() + "\"");

                for (Category sc : c.getSubCategories()) {
                    sc.setNowProductsInclusive(getProductsInSubcategoryById(sc.getId()
                            , warehouseId
                            , "now"));
                    sc.setLaterProductsInclusive(getProductsInSubcategoryById(sc.getId()
                            , warehouseId
                            , "later"));

                    //Define the unique products with serve of Now and tomorrow
                    sc.setNowProductsExclusive(productsDataFactory.identifyNowOnlyProducts(sc.getNowProductsInclusive()
                            , sc.getLaterProductsInclusive()));
                    sc.setLaterProductsExclusive(productsDataFactory.identifyLaterOnlyProducts(
                            sc.getLaterProductsInclusive(), sc.getNowProductsInclusive()));

                    //Define products with positive stocks
                    if (!sc.getNowProductsExclusive().isEmpty())
                        sc.setNowProductsWithPositiveStocks(
                                productsDataFactory.identifyProductsWithPositiveStock(sc.getNowProductsExclusive()));
                    if (!sc.getLaterProductsExclusive().isEmpty())
                        sc.setLaterProductsWithPositiveStocks(
                                productsDataFactory.identifyProductsWithPositiveStock(sc.getLaterProductsExclusive()));
                }
                retrievedList.add(c);
            }
            logger.info("Parsed the list of categories and total objects count is: " + retrievedList.size());
        } else {
            logger.info("Found 0 categories matching the warehouse of ID \"" + warehouseId + "\" and of type \""
                    + nowTomorrowType + "\"");
        }
        return retrievedList;
    }

    public List<Product> getProductsInSubcategoryById(int subCategoryId, String warehouseId, String nowTomorrowType) {
        logger.info("Sending a getProductsInSubcategoryById for warehouse \"" + warehouseId + "\" and subcategory \""
                + subCategoryId + "\" and of type \"" + nowTomorrowType + "\"");
        try {
            // Configure RestAssured to set a timeout of 30 seconds
            RestAssuredConfig config = RestAssured.config()
                    .httpClient(HttpClientConfig.httpClientConfig()
                            .setParam("http.connection.timeout", 30000)
                            .setParam("http.socket.timeout", 30000));

            Response response = RestAssured.given()
                    .config(config)
                    .queryParam("serve", nowTomorrowType)
                    .queryParam("warehouseId", warehouseId)
                    .get(configs.getBaseURL() + getProductsBySubCategoryIdEndpoint + subCategoryId)
                    .then()
                    .log().ifValidationFails()
                    .statusCode(200)
                    .extract().response();

            List<Product> productsList = new ArrayList<>();
            JSONArray productsJsonArray = new JSONArray(response.getBody().asString());
            if (productsJsonArray.length() > 0) {
                logger.info("Received total products of \"{}\" with type \"{}\" in warehouse \"{}\" and Category \"{}\"",
                        productsJsonArray.length(), nowTomorrowType, warehouseId, subCategoryId);
                List<JSONObject> productsJsonObjects =
                        new ProductsParser().parseJsonArrayToListOfJsonObjects(productsJsonArray);

                for (JSONObject e : productsJsonObjects) {
                    // Fetch its full details
                    try {
                        Product product = getProductDetails(e.optInt("id"), warehouseId, nowTomorrowType);

                        // Add the fully-detailed product to the list
                        productsList.add(product);
                    } catch (Exception ex) {
                        logger.error("Failed to fetch details for product ID: {}. Error: {}"
                                , e.optInt("id"), ex.getMessage());
                    }
                }

                logger.info("Parsed products list with total count of {} products", productsList.size());

            } else {
                logger.error("Received 0 products with type \"{}\" in warehouse \"{}\" and Category \"{}\"",
                        nowTomorrowType, warehouseId, subCategoryId);
            }
            return productsList;
        } catch (Exception e) {
            logger.error("An error occurred while fetching products for subcategory ID: {} " +
                            "in warehouse ID: {} with type: {}. Returning an empty list.",
                    subCategoryId, warehouseId, nowTomorrowType, e);
            return new ArrayList<>();
        }
    }

    public List<Area> getAreas() {
        logger.info("Sending a getAreas request to endpoint \"" + getAreasEndpoint + "\"");
        Response response = RestAssured.given()
                .get(configs.getBaseURL() + getAreasEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        logger.info("GetAreas Request completed and received a response code of 200");

        JSONObject responseObject = new JSONObject(response.getBody().asString());

        List<Area> parsedAreas = new ArrayList<>();
        if (responseObject.has("areas")) {
            JSONObject areasObject = responseObject.getJSONObject("areas");

            logger.info("Retrieved areas count is\"" + areasObject.length() + "\"");
            logger.info("Parsing the Areas Json Lists...");

            // Iterate over keys in the "areas" object
            for (String key : areasObject.keySet()) {

                // Fetch the JSON object corresponding to the key
                JSONObject areaObject = areasObject.getJSONObject(key);

                parsedAreas.add(new AreaDataParser().parseAreaJsonObject(areaObject));
            }
            logger.info("Successfully parsed the areas JsonList and processed \"" + parsedAreas.size() + "\" parsed objects.");
        } else {
            logger.error("Response does not contain 'areas' key or is not a valid JSON object");
        }
        return parsedAreas;
    }

    public Product getProductDetails(int productId, String warehouseId, String nowTomorrowType) {
        logger.info("Sending a getProductDetails request to endpoint \"{}\" with warehouse ID \"{}\",  type \"{}\" " +
                        "and product ID \"{}\"", getProductDetailsEndpoint, warehouseId, nowTomorrowType, productId);

        Response response = RestAssured.given()
                .pathParam("product",productId)
                .queryParam("serve", nowTomorrowType)
                .queryParam("warehouseId", warehouseId)
                .get(configs.getBaseURL() + getProductDetailsEndpoint)
                .then()
                .log().ifValidationFails()
                .extract().response();

        return new ProductsParser().parseProductJsonObject(new JSONObject(response.getBody().asString()));
    }

}
