package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.User;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.ArrayList;
import java.util.List;

public class DeleteAccountApiClient extends BaseHelper {

    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(DeleteAccountApiClient.class);
    private final String deleteContentEndpoint = "/wp-json/breadfast/v3/delete/content";

    public DeleteAccountApiClient(Configs configs) {
        this.configs = configs;
    }

    public List<String> deleteContentUsingApi(User user) {
        logger.info("Starting the delete content process for user with ID: {}", user.getId());
        List<String> deleteReasons = new ArrayList<>();

        // Make the API call
        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + user.getAuthorizationToken())
                .header("Accept", "application/json")
                .get(configs.getBaseURL() + deleteContentEndpoint)
                .then().extract().response();

        // Log the response status code
        logger.info("API Response Status Code: {}", response.getStatusCode());

        // Check if the status code is 200
        if (response.getStatusCode() != 200) {
            logger.error("Delete content API call failed for user with ID \"{}\". Status code: {}"
                    , user.getId()
                    , response.getStatusCode());
            return deleteReasons; // Return an empty list and exit the method
        }

        logger.info("Delete content API call was successful for user with ID \"{}\"", user.getId());

        // Parse the response
        JSONObject responseObject = new JSONObject(response.getBody().asString());

        if (responseObject.has("data") && responseObject.getJSONObject("data").has("delete_reasons")) {
            // Extract the delete reasons only
            responseObject.getJSONObject("data").getJSONArray("delete_reasons").forEach(reason -> {
                JSONObject reasonObject = (JSONObject) reason; // Cast to JSONObject
                deleteReasons.add(reasonObject.getString("value")); // Store only the "value" field
            });
        }

        return deleteReasons;
    }
}
