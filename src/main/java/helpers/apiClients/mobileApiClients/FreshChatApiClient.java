package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.FreshChatMessageDataParser;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import models.Configs;
import models.FreshChatConversation;
import models.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Map;

import static org.hamcrest.Matchers.*;

public class FreshChatApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(FreshChatApiClient.class);
    private final String createUserEndPoint = "/v2/users";
    private final String createConversationEndPoint = "/v2/conversations/";
    private final String listMessagesEndpoint = "/v2/conversations/{conversationId}/messages";
    private final String sendMessagesEndpoint = "/v2/conversations/{conversationId}/messages";

    public FreshChatApiClient(Configs configs) {
        this.configs = configs;
    }

    public User createUserOnFreshChat(User user){
        logger.info("Initiating the create user on freshChat Using endpoint: {} for user with ID: {}"
                , createUserEndPoint, user.getId());

        JSONObject requestBody = new JSONObject();
        JSONArray propertiesArray = new JSONArray();

        requestBody.put("email", user.getEmailAddress());
        requestBody.put("first_name", user.getFirstName());
        requestBody.put("last_name", user.getLastName());
        requestBody.put("phone", user.getLocalPhoneNumber());
        requestBody.put("reference_id", user.getId());

        propertiesArray.put(new JSONObject().put("name", "external_id").put("value", user.getId()));
        propertiesArray.put(new JSONObject().put("name", "cf_bot_token").put("value", user.getChatbotJwtToken()));
        propertiesArray.put(new JSONObject().put("name", "cf_lang").put("value", "en"));

        requestBody.put("properties", propertiesArray);

        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + configs.getFreshChatApiKey())
                .accept(ContentType.JSON)
                .contentType(ContentType.JSON)
                .body(requestBody.toString()) // Convert JSONObject to string
                .when()
                .post(configs.getFreshChatApiBaseUrl() + createUserEndPoint)
                .then()
                .log().ifValidationFails()
                .assertThat()
                .statusCode(201)
                .body("id", notNullValue())
                .extract()
                .response();

        user.setFreshChatId(response.jsonPath().getString("id"));

        logger.info("Created user successfully and the user with email {} has the freshChat ID: {}"
                , user.getEmailAddress(), user.getFreshChatId());

        return user;
    }

    public FreshChatConversation createConversationOnFreshChat(User user
            ,FreshChatConversation conversation
            , String openingText){
        logger.info("Initiating the create conversation request for user with " +
                "ID {}, channel with ID {} and opening text: \"{}\""
                , user.getFreshChatId()
                , configs.getFreshChatChannelId()
                , openingText);

        JSONArray messages = new JSONArray();
        messages.put(constructMessageObject(openingText, user));

        JSONObject userObject = new JSONObject();
        userObject.put("id", user.getFreshChatId());

        JSONArray users = new JSONArray();
        users.put(userObject);

        JSONObject requestBody = new JSONObject();
        requestBody.put("status", "new");
        requestBody.put("messages", messages);
        requestBody.put("channel_id", configs.getFreshChatChannelId());
        requestBody.put("users", users);

        logger.info("Constructed the JSON request payload as: {}", requestBody);

        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + configs.getFreshChatApiKey())
                .accept(ContentType.JSON)
                .contentType(ContentType.JSON)
                .body(requestBody.toString())
                .when()
                .post(configs.getFreshChatApiBaseUrl() + createConversationEndPoint)
                .then()
                .log().ifValidationFails()
                .statusCode(202)
                .body("messages", notNullValue())
                .body("messages", hasSize(greaterThanOrEqualTo(1)))
                .body("messages", everyItem(instanceOf(Map.class)))
                .body("conversation_id", notNullValue())
                .body("conversation_internal_id", notNullValue())
                .body("url", notNullValue())
                .body("app_id", notNullValue())
                .extract()
                .response();

        logger.info("Parsing the conversation object received in response");

        // Fetch necessary info from
        conversation.setConversationId(response.jsonPath().getString("conversation_id"));
        conversation.setConversationInternalId(response.jsonPath().getString("conversation_internal_id"));
        conversation.setAppId(response.jsonPath().getString("app_id"));
        conversation.setConversationUrl(response.jsonPath().getString("url"));

        logger.info("Completed the conversation create request and the received conversation ID is: {}"
                , conversation.getConversationId());

        return conversation;
    }

    public FreshChatConversation listMessagesInConversation(FreshChatConversation conversation){
        int maxRetries = 3;
        int retryCount = 0;
        int delayTimeBetweenRequests = 2;

        JSONArray messagesArray;

        logger.info("Initiating a list messages request for conversation with ID: {}", conversation.getConversationId());

        while (retryCount < maxRetries) {
            logger.info("FreshChat List messages Request trial #: {}", retryCount + 1);
            Response response = RestAssured.given()
                    .header("Authorization", "Bearer " + configs.getFreshChatApiKey())
                    .accept(ContentType.JSON)
                    .pathParam("conversationId", conversation.getConversationId())
                    .get(configs.getFreshChatApiBaseUrl() + listMessagesEndpoint)
                    .then()
                    .statusCode(200)
                    .extract()
                    .response();

            JSONObject jsonResponse = new JSONObject(response.asString());
            messagesArray = jsonResponse.optJSONArray("messages");

            if (messagesArray != null && messagesArray.length() > 2) {
                logger.info("Found more than 2 messages in the response. Parsing the response and proceeding...");
                for (JSONObject messageObject : new FreshChatMessageDataParser()
                        .parseJsonArrayToListOfJsonObjects(messagesArray))
                        conversation.getMessages().add(new FreshChatMessageDataParser()
                                .parseFreshChatMessageObject(messageObject));

                logger.info("Parsed all messages from response. Proceeding...");

                return conversation;
            }

            retryCount++;
            try {
                Thread.sleep(Duration.ofSeconds(delayTimeBetweenRequests));
            } catch (InterruptedException e) {
                logger.error("Wait between requests failed. Forcing an interrupt of the Thread. " +
                        "Returning the conversation object without modifications");
                return conversation;
            }
        }
        logger.error("Messages count in the response is either 0 or less than 2. Conversation ID: {}"
                , conversation.getConversationId());
        return conversation;
    }

    private JSONObject constructMessageObject(String txtMessage, User user){
        JSONObject textContent = new JSONObject();
        textContent.put("content", txtMessage);

        JSONObject messagePart = new JSONObject();
        messagePart.put("text", textContent);

        JSONArray messageParts = new JSONArray();
        messageParts.put(messagePart);

        JSONObject messageObject = new JSONObject();
        messageObject.put("message_parts", messageParts);
        messageObject.put("channel_id", configs.getFreshChatChannelId());
        messageObject.put("message_type", "normal");
        messageObject.put("actor_type", "user");
        messageObject.put("actor_id", user.getFreshChatId());

        return messageObject;
    }
}
