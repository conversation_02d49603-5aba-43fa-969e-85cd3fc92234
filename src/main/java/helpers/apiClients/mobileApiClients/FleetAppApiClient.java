package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.FleetTripDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class FleetAppApiClient extends BaseHelper {

    private final Configs configs;

    public FleetAppApiClient(Configs configs) {
        this.configs = configs;
    }

    private static final Logger logger = LoggerFactory.getLogger(FleetAppApiClient.class);
    private final String fleetAppLoginEndpoint = "/v1/auth/login";
    private final String fleetAppVerifyPhoneEndpoint = "/v1/auth/phone/verify";
    private final String fleetAppDaDataEndpoint = "/v1/profile/da";
    private final String getTripsEndpoint = "/v1/trips/";
    private final String changeDaStatusEndpoint = "/v1/profile/";
    private final String assignTripEndpoint = "/control-room/trips/assign";
    private final String getTripTasksEndpoint = "/v1/trips/sync";
    private final String tripActionsEndpoint = "/v1/trips/trip/action";
    private final String taskActionsEndpoint = "/v1/tasks/action";
    private final String logoutEndpoint = "/v1/profile/logout?lang=ar";
    private final String fleetAppUpdateDaStatusEndpoint = "/v1/profile/";

    public Map<String, Object> verifyPhoneNumber(String phoneNumber) {

        int statusCode = -1;
        String[] errors = {"No errors"};
        boolean hasAccess = false;
        int userId = 0;
        String message = "No message";

        String requestBody = "{\"phone\": \"" + phoneNumber + "\", \"source\": \"fleet\"}";
        Response response = RestAssured.given()
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getFleetAppBaseURL() + fleetAppVerifyPhoneEndpoint)
                .then()
                .extract().response();

        statusCode = response.getStatusCode();
        if (statusCode == 200) {
            hasAccess = response.jsonPath().getBoolean("hasAccess");
            userId = response.jsonPath().getInt("userId");
        } else if (statusCode == 403 || statusCode == 400) {
            errors = response.jsonPath().getList("errors").toArray(new String[0]);
            message = response.jsonPath().getString("message");
        }

        if (statusCode == 200) {
            if (hasAccess) {
                logger.info("User has access. User ID: {}", userId);
            }
        } else if (statusCode == 403) {
            logger.info("Phone Number not registered. Error: {}", Arrays.toString(errors));
        } else if (statusCode == 400) {
            logger.info("User not assigned to fp. Error: {}", Arrays.toString(errors));
        }

        Map<String, Object> result = new HashMap<>();
        result.put("statusCode", statusCode);
        result.put("message", message);
        result.put("userId", userId);
        result.put("errors", errors);
        result.put("hasAccess", hasAccess);

        return result;
    }

    public Map<String, Object> login(User daUser, String phoneNumber, String password) {
        String requestBody = "{\"phone\": \"" + phoneNumber + "\", \"password\": \"" + password + "\", \"source\": \"fleet\"}";
        int statusCode = -1;
        String[] errors = {"No errors"};
        String message = "No message";

        Response response = RestAssured.given()
                .contentType("application/json")
                .body(requestBody)
                .post(configs.getFleetAppBaseURL() + fleetAppLoginEndpoint)
                .then()
                .extract().response();
        JSONObject responseJson = new JSONObject(response.getBody().asString());

        statusCode = response.getStatusCode();
        if (statusCode == 200) {
            daUser.setId(responseJson.optString("id"));
            daUser.setUserName(responseJson.optString("username"));
            daUser.setBlocked(responseJson.optBoolean("isBlocked"));
            daUser.setAuthorizationToken(responseJson.optString("token"));
        } else if (statusCode == 401 || statusCode == 400) {
            errors = response.jsonPath().getList("errors").toArray(new String[0]);
            message = response.jsonPath().getString("message");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("statusCode", response.getStatusCode());
        result.put("id", daUser.getId());
        result.put("token", daUser.getAuthorizationToken());
        result.put("message", message);
        result.put("errors", errors);

        return result;
    }

    public Map<String, Object> getDaData(User daUser) {

        int statusCode = -1;
        String errors = "No errors";
        String message = "No message";

        String requestBody = "{}";
        Response response = RestAssured.given()

                .contentType("application/json")
                .body(requestBody)
                .header("Authorization", "Bearer "
                        + daUser.getAuthorizationToken())
                .get(configs.getFleetServiceBaseURL() + fleetAppDaDataEndpoint)
                .then()
                .extract().response();
        JSONObject responseJson = new JSONObject(response.getBody().asString());

        statusCode = response.getStatusCode();
        if (statusCode == 200) {
            daUser.setId(responseJson.optString("id"));
            daUser.setFullName(responseJson.optString("name"));
            daUser.setHrId(responseJson.optInt("hrId"));
            daUser.setDaFpNameAssigned(responseJson.optString("fpName"));
            daUser.setDaStatus(responseJson.optString("status"));
            daUser.setDaScore(responseJson.optString("score"));
            daUser.setDaBalance(responseJson.optInt("balance"));
            daUser.setDaLocked(responseJson.optBoolean("isLocked"));

        } else if (statusCode == 401 || statusCode == 400) {
            errors = response.jsonPath().getString("errors");
            message = response.jsonPath().getString("message");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("statusCode", response.getStatusCode());
        result.put("status", daUser.getDaStatus());
        result.put("message", message);
        result.put("errors", errors);

        return result;

    }

    public FleetTrip assignTrip(User daUser, List<String> userOrders) {

        int statusCode = -1;

        String requestBody = "{\n" +
                "    \"daId\": " + daUser.getId() + ",\n" +
                "    \n" +
                "    \"ordersId\": [\n" +
                "        " + userOrders + "\n" +
                "    ]\n" +
                "}";

        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer "
                        + daUser.getAuthorizationToken())
                .body(requestBody)
                .post(configs.getPickerServicesBaseURL() + assignTripEndpoint)
                .then()
                .extract().response();

        JSONObject responseJson = new JSONObject(response.getBody().asString());

        statusCode = response.getStatusCode();
        if (statusCode == 201) {
            logger.info(response.jsonPath().getString("data"));
            return FleetTripDataParser.parseTripAssignedData(responseJson);

        } else if (statusCode == 400 || statusCode == 404 || statusCode == 401) {
            String errors = response.jsonPath().getString("errors");
            String message = response.jsonPath().getString("message");
            logger.error(errors);
            logger.error(message);
        }
        return null;
    }

    public FleetTrip assignOrderToExistingTrip(User daUser, List<Order> userOrders, Integer assignedTripId) {

        int statusCode = -1;

        JSONArray orderIdsJsonArray = new JSONArray();
        for (Order order : userOrders) {
            orderIdsJsonArray.put(order.getOrderId());
        }

        String requestBody = "{\n" +
                "    \"daId\": " + daUser.getId() + ",\n" +
                "    \"tripId\": " + assignedTripId + ",\n" +
                "    \"ordersId\": [\n" +
                "        " + orderIdsJsonArray + "\n" +
                "    ]\n" +
                "}";

        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer "
                        + daUser.getAuthorizationToken())
                .body(requestBody)
                .post(configs.getPickerServicesBaseURL() + assignTripEndpoint)
                .then()
                .extract().response();

        JSONObject responseJson = new JSONObject(response.getBody().asString());

        statusCode = response.getStatusCode();
        if (statusCode == 201) {
            logger.info(response.jsonPath().getString("data"));
            return FleetTripDataParser.parseTripAssignedData(responseJson);

        } else if (statusCode == 400 || statusCode == 404 || statusCode == 401) {
            String errors = response.jsonPath().getString("errors");
            String message = response.jsonPath().getString("message");
            logger.error(errors);
            logger.error(message);
        }
        return null;
    }

    public FleetTrip getDaTripsAssigned(User daUser) {

        String errors = "No errors";
        String message = "No message";
        int statusCode = -1;

        String requestBody = "{}";
        Response response = RestAssured.given()
                .contentType("application/json")
                .header("service", "control-room")
                .header("token", "123456789")
                .body(requestBody)
                .get(configs.getFleetServiceBaseURL() + getTripsEndpoint + daUser.getId())
                .then()
                .extract().response();
        JSONObject responseJson = new JSONObject(response.getBody().asString());

        statusCode = response.getStatusCode();

        if (statusCode == 200) {

            return FleetTripDataParser.parseFleetTrip(responseJson);

        } else if (statusCode == 400 || statusCode == 404 || statusCode == 401) {
            errors = response.jsonPath().getString("errors");
            message = response.jsonPath().getString("message");
            logger.error(errors);
            logger.error(message);
        }

        return null;
    }

    public HashMap<String, String> changeDaStatus(User daUser, String status) {

        int statusCode;

        String requestBody = "{\n    \"status\" : \"" + status + "\"\n}";

        Response response = RestAssured.given()
                .contentType("application/json")
                .header("token", "123456789")
                .body(requestBody)
                .header("Authorization", "Bearer "
                        + daUser.getAuthorizationToken())
                .put(configs.getFleetServiceBaseURL() + changeDaStatusEndpoint + daUser.getId() + "/status")
                .then()
                .extract().response();

        JSONObject responseJson = new JSONObject(response.getBody().asString());

        statusCode = response.getStatusCode();

        HashMap<String, String> result = new HashMap<>();

        if (statusCode == 200) {

            daUser.setDaStatus(responseJson.optString("status"));
            result.put("statusCode", String.valueOf(statusCode));

        } else if (statusCode == 400 || statusCode == 404 || statusCode == 401) {
            String errors = response.jsonPath().getString("errors");
            String message = response.jsonPath().getString("message");
            logger.error(errors);
            logger.error(message);
            result.put("errors", errors);
            result.put("message", message);

        }

        return result;
    }

    public FleetTrip getTripTasks(User daUser) {

        String errors = "No errors";
        String message = "No message";
        int statusCode = -1;

        FleetTrip fleetTrip = new FleetTrip();

        String requestBody = "{\n    \"daId\" : " + daUser.getId() + "\n}";
        Response response = RestAssured.given()
                .contentType("application/json")
                .header("service", "control-room")
                .header("token", "123456789")
                .body(requestBody)
                .post(configs.getFleetServiceBaseURL() + getTripTasksEndpoint)
                .then()
                .extract().response();
        String responseBody = response.getBody().asString();

        statusCode = response.getStatusCode();

        if (statusCode == 201) {
            if (responseBody == null || responseBody.trim().isEmpty()) {
                logger.error("Response is empty despite status code 201.");
            } else {
                JSONObject responseJson = new JSONObject(responseBody);
                fleetTrip = FleetTripDataParser.parseTripTasksData(responseJson);
            }
        } else if (statusCode == 400 || statusCode == 404 || statusCode == 401) {
            errors = response.jsonPath().getString("errors");
            message = response.jsonPath().getString("message");
            logger.error(errors);
            logger.error(message);
        }

        return fleetTrip;

    }

    public Map<String, String> tripActions(User daUser, String action, Integer assignedTripId, double latitude, double longitude) {

        String errors = "No errors";
        String message = "No message";
        int statusCode = -1;

        String requestBody = "{\n" +
                "  \"action\": \"" + action + "\",\n" +
                "  \"tripId\": " + assignedTripId + ",\n" +
                "  \"location\": {\n" +
                "    \"lat\": \"" + latitude + "\",\n" +
                "    \"lng\": \"" + longitude + "\"\n" +
                "  }\n" +
                "}";
        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer "
                        + daUser.getAuthorizationToken())
                .body(requestBody)
                .post(configs.getFleetServiceBaseURL() + tripActionsEndpoint)
                .then()
                .extract().response();

        JSONObject responseJson = new JSONObject(response.getBody().asString());

        statusCode = response.getStatusCode();
        Map<String, String> result = new HashMap<>();

        if (statusCode == 200) {

            logger.info(String.valueOf(responseJson));

        } else if (statusCode == 400 || statusCode == 404 || statusCode == 401) {
            message = response.jsonPath().getString("message");
            logger.error(message);
        }

        result.put("message", message);
        return result;

    }

    public void taskActions(User daUser, String action, String taskId, double latitude, double longitude) {

        String message = "No message";
        int statusCode = -1;

        String requestBody = "{\n" +
                "  \"action\": \"" + action + "\",\n" +
                "  \"taskId\": " + taskId + ",\n" +
                "  \"location\": {\n" +
                "    \"lat\": \"" + latitude + "\",\n" +
                "    \"lng\": \"" + longitude + "\"\n" +
                "  }\n" +
                "}";
        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer "
                        + daUser.getAuthorizationToken())
                .body(requestBody)
                .post(configs.getFleetServiceBaseURL() + taskActionsEndpoint)
                .then()
                .extract().response();

        JSONObject responseJson = new JSONObject(response.getBody().asString());

        statusCode = response.getStatusCode();

        if (statusCode == 200) {

            logger.info(String.valueOf(responseJson));

        } else if (statusCode == 400 || statusCode == 404 || statusCode == 401) {
            message = response.jsonPath().getString("message");
            logger.error(message);
        }

    }

    public void cashCollect(User daUser, String action, String taskId, double latitude, double longitude, Double amount) {

        String message = "No message";
        int statusCode = -1;

        String requestBody = "{\n" +
                "  \"action\": \"" + action + "\",\n" +
                "  \"taskId\": " + taskId + ",\n" +
                "  \"location\": {\n" +
                "    \"lat\": \"" + latitude + "\",\n" +
                "    \"lng\": \"" + longitude + "\"\n" +
                "  },\n" +
                "  \"params\": {\n" +
                "    \"amount\": " + amount + "\n" +
                "  }\n" +
                "}";
        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer "
                        + daUser.getAuthorizationToken())
                .body(requestBody)
                .post(configs.getFleetServiceBaseURL() + taskActionsEndpoint)
                .then()
                .extract().response();

        JSONObject responseJson = new JSONObject(response.getBody().asString());

        statusCode = response.getStatusCode();

        if (statusCode == 200) {

            logger.info(String.valueOf(responseJson));

        } else if (statusCode == 400 || statusCode == 404 || statusCode == 401) {
            message = response.jsonPath().getString("message");
            logger.error(message);
        }

    }

    public Map<String, String> logout(User daUser) {

        String message = "No message";
        int statusCode = -1;

        String requestBody = "{}";
        Response response = RestAssured.given()
                .contentType("application/json")
                .header("Authorization", "Bearer "
                        + daUser.getAuthorizationToken())
                .body(requestBody)
                .post(configs.getFleetServiceBaseURL() + logoutEndpoint)
                .then()
                .extract().response();

        Map<String, String> result = new HashMap<>();

        statusCode = response.getStatusCode();

        if (statusCode == 200) {

            logger.info("Logout is successfull");

        } else if (statusCode == 400 || statusCode == 404 || statusCode == 401) {
            message = response.jsonPath().getString("message");
            logger.error(message);
        }
        result.put("message", message);
        return result;
    }

    public Response updateDaStatus(User daUser, String warehouseId, String status, double lat, double lng) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("status", status);
        jsonObject.put("daData", new JSONObject()
                .put("fpId", warehouseId)
                .put("location", new JSONObject()
                        .put("lat", Double.toString(lat))
                        .put("lng", Double.toString(lng))));

        Response response = RestAssured.given()
                .header("Authorization", "Bearer "
                        + daUser.getAuthorizationToken())
                .contentType("application/json")
                .body(jsonObject.toString())
                .put(configs.getFleetServiceBaseURL() + fleetAppUpdateDaStatusEndpoint + daUser.getId() + "/status")
                .then()
                .statusCode(200)
                .extract().response();
        return response;
    }
}
