package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.Product;
import models.User;
import models.Warehouse;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class CheckoutApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(CheckoutApiClient.class);

    private final String checkoutApiEndpoint = "/wp-json/breadfast/v3/user/checkout";

    public CheckoutApiClient(Configs configs) {
        this.configs = configs;
    }

    public void getCheckoutDetails(User orderOwner, Warehouse warehouse, List<Product> products) {
        logger.info("Sending request to checkout API ....");

        // Print the product details
        for (Product product : products) {
            logger.info("Checked out Product ID: " + product.getMysqlId() +
                    ", Sale Price: " + product.getSalePrice() +
                    ", Extra Sales Price: " + product.getExtraSalesPrice() +
                    ", Automated Discounts: " + product.getAutomatedDiscounts());
        }

        double subtotal = 0.0;

        //Calculate cart subtotal based on product sale price type
        for (int i = 0; i < Math.min(2, products.size()); i++) {
            Product testProduct = products.get(i);

            boolean hasNoAutomatedDiscounts = testProduct.getAutomatedDiscounts() == null ||
                    testProduct.getAutomatedDiscounts().isEmpty() ||
                    testProduct.getAutomatedDiscounts().stream()
                            .noneMatch(d -> d.getFpId().equals(warehouse.getId()));

            // Determine product price and discount based on price type
            if (testProduct.getSalePrice() == 0 &&
                    testProduct.getExtraSalesPrice().isEmpty() &&
                    hasNoAutomatedDiscounts) {
                // Regular price
                subtotal += testProduct.getPrice();
            } else if (testProduct.getSalePrice() != 0 &&
                    testProduct.getExtraSalesPrice().isEmpty() &&
                    hasNoAutomatedDiscounts) {
                // Sale price
                subtotal += testProduct.getSalePrice();
            } else {
                // Extra sale price
                double extraSalePrice = Float.parseFloat(testProduct.getExtraSalesPrice());
                subtotal += extraSalePrice;
            }
        }

            logger.info("Calculated subtotal based on products' prices is: {}", subtotal);

            Response response = RestAssured.given()
                    .header("Authorization", "Bearer " + orderOwner.getAuthorizationToken())
                    .contentType("multipart/form-data")
                    .multiPart("subtotal", subtotal)
                    .multiPart("warehouseId", warehouse.getId())
                    .post(configs.getBaseURL() + checkoutApiEndpoint)
                    .then()
                    .extract().response();

            response.then()
                    .log().ifValidationFails()
                            .statusCode(200);

            logger.info("Request on checkout API {} was successful and received a response code of 200"
                    , checkoutApiEndpoint);

            JSONObject responseObject = new JSONObject(response.getBody().asString());
            double deliveryFees = responseObject.optDouble("delivery_fees");

            logger.info("Delivery fees received from checkout API is: {} , setting it to warehouse object.. ", deliveryFees);
            //set warehouse delivery fees based on response
            warehouse.setDeliveryFees(deliveryFees);

            logger.info("Delivery fees set to warehouse object successfully");
        }

    public Warehouse getCheckoutDetails(User orderOwner, Warehouse warehouse, Product product) {
        logger.info("Sending request to checkout API ....");

        double subtotal = 0.0;

        //Calculate cart subtotal based on product sale price type

            boolean hasNoAutomatedDiscounts = product.getAutomatedDiscounts() == null ||
                    product.getAutomatedDiscounts().isEmpty() ||
                    product.getAutomatedDiscounts().stream()
                            .noneMatch(d -> d.getFpId().equals(warehouse.getId()));

            // Determine product price and discount based on price type
            if (product.getSalePrice() == 0 &&
                    product.getExtraSalesPrice().isEmpty() &&
                    hasNoAutomatedDiscounts) {
                // Regular price
                subtotal += product.getPrice();
            } else if (product.getSalePrice() != 0 &&
                    product.getExtraSalesPrice().isEmpty() &&
                    hasNoAutomatedDiscounts) {
                // Sale price
                subtotal += product.getSalePrice();
            } else {
                // Extra sale price
                double extraSalePrice = Float.parseFloat(product.getExtraSalesPrice());
                subtotal += extraSalePrice;
            }

            logger.info("Calculated subtotal based on products' prices is: {}", subtotal);

            Response response = RestAssured.given()
                    .header("Authorization", "Bearer " + orderOwner.getAuthorizationToken())
                    .contentType("multipart/form-data")
                    .multiPart("subtotal", subtotal)
                    .multiPart("warehouseId", warehouse.getId())
                    .post(configs.getBaseURL() + checkoutApiEndpoint)
                    .then()
                    .log().ifValidationFails()
                    .statusCode(200)
                    .extract().response();
            logger.info("Request on checkout API was successful and received a response code of 200");
            JSONObject responseObject = new JSONObject(response.getBody().asString());
            double deliveryFees = responseObject.optDouble("delivery_fees");
            logger.info("Delivery fees received from checkout API is: {} , setting it to warehouse object.. ", deliveryFees);
            //set warehouse delivery fees based on response
            warehouse.setDeliveryFees(deliveryFees);
            logger.info("Delivery fees set to warehouse object successfully");
            return warehouse;
    }
}
