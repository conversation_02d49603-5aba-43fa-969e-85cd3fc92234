package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.Configs;
import models.User;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ChatbotApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(ChatbotApiClient.class);
    private final String generateChatbotJwtEndpoint = "/wp-json/chatbot/v1/token";

    public ChatbotApiClient(Configs configs) {
        this.configs = configs;
    }

    public User generateChatbotJwt(User user){
        logger.info("Starting to generate a JWT token for the chatbot for user with phone number {} and ID {}"
                , user.getPhoneNumber(), user.getId());
        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + user.getAuthorizationToken())
                .noContentType()
                .get(configs.getBaseURL() + generateChatbotJwtEndpoint)
                .then()
                .statusCode(200)
                .extract().response();

        logger.info("GenerateChatbotJwt Request is sent successfully and received 200.");
        JSONObject jsonObject = new JSONObject(response.getBody().asString());
        if (jsonObject.has("token") && jsonObject.has("userId")){
            logger.info("Received a chatbot token for user with ID: {}", jsonObject.optInt("userId"));
            user.setChatbotJwtToken(jsonObject.optString("token"));
        } else {
            logger.error("Response code is 200. However, Chatbot JWT is not received in the response." +
                    "We are expecting a json with token and userId keys");
        }

        if (user.getChatbotJwtToken() != null){
            logger.info("User's chatbot JWT is saved successfully and it's value is: {}", user.getChatbotJwtToken());
        }

        return user;
    }
}
