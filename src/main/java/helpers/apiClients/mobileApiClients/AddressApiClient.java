package helpers.apiClients.mobileApiClients;

import helpers.BaseHelper;
import helpers.dataParsers.AddressDataParser;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import models.*;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AddressApiClient extends BaseHelper {
    private final Configs configs;
    private static final Logger logger = LoggerFactory.getLogger(AddressApiClient.class);

    private final String getAddAddressEndpoint = "/wp-json/breadfast/v3/user/update-address";

    public AddressApiClient(Configs configs) {
        this.configs = configs;
    }

    public Address createAddressUsingApi(User user, Address address, Warehouse warehouse, boolean isDefault){

        logger.info("""
                Starting to create an address using endpoint {} with:
                firstName: {}
                lastName: {}
                areaId: {}
                areaName: {}
                address: {}
                phone: {}
                default_address: {}""",
                getAddAddressEndpoint,
                address.getFirstName(),
                address.getLastName(),
                warehouse.getArea().getId(),
                warehouse.getArea().getEnName(),
                address.getFullAddress(),
                address.getPhoneNumber(),
                isDefault);

        Response response = RestAssured.given()
                .header("Authorization", "Bearer " + user.getAuthorizationToken())
                .contentType("multipart/form-data")
                .multiPart("first_name", address.getFirstName())
                .multiPart("last_name", address.getLastName())
                .multiPart("area", warehouse.getArea().getId())
                .multiPart("address", address.getFullAddress())
                .multiPart("phone", address.getPhoneNumber())
                .multiPart("flat", address.getFloorNumber())
                .multiPart("floor", address.getFlatNumber())
                .multiPart("label", address.getAddressLabel())
                .multiPart("default_address", isDefault)
                .multiPart("location",getLocationObject())
                .post(configs.getBaseURL() + getAddAddressEndpoint)
                .then()
                .log().ifValidationFails()
                .statusCode(200)
                .extract().response();

        logger.info("Created address successfully for user with ID \""
                + user.getId() + "\"");

        JSONObject responseObject = new JSONObject(response.getBody().asString());
        JSONObject addressesObject = responseObject.getJSONObject("data").getJSONObject("addresses");

        String addressId = addressesObject.keys().next();
        JSONObject addressObject = addressesObject.getJSONObject(addressId);
        logger.info(addressObject.toString());

        logger.info("Starting to parse created address object...");
        Address parsedAddress = new AddressDataParser().parseAddressJsonObject(addressObject);
        logger.info("Finalized parsing address object for user with ID \"" + user.getId());

        return parsedAddress;
    }

    public String getLocationObject(){
       return "{\"lat\": "+ configs.getTestLatitude() +",\"lng\": "+configs.getTestLongitude()+"}";
    }
}
