package helpers;

import models.Configs;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.*;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.OAEPParameterSpec;
import javax.crypto.spec.PSource;

public class EncryptionHelper {
    Configs configs;

    public EncryptionHelper(Configs configs) {
        this.configs = configs;
    }

    public String encryptData(String data) {
        try {
            PublicKey publicKey = this.getPublicKey(configs.getCardLoginMobileUserPublicKey());
            byte[] encryptedData = this.encrypt(data, publicKey);
            return Base64.getEncoder().encodeToString(encryptedData);
        } catch (Exception e){
            return null;
        }
    }

    private PublicKey getPublicKey(String publicKeyString) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(publicKeyString);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePublic(spec);
    }

    private byte[] encrypt(String data, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-256AndMGF1Padding");
        OAEPParameterSpec oaepParams =
                new OAEPParameterSpec("SHA-256"
                        , "MGF1"
                        , new MGF1ParameterSpec("SHA-256"), PSource.PSpecified.DEFAULT);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey, oaepParams);
        return cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }
}
