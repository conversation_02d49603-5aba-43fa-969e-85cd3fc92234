package helpers.dataParsers;

import models.Discount;
import models.OptionSets;
import models.Options;
import models.Product;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class ProductsParser extends BaseDataParser {
    private static final Logger logger = LoggerFactory.getLogger(ProductsParser.class);

    public Product parseProductJsonObject(JSONObject productJsonObject) {
        Product parsedObject = new Product();

        parsedObject.setMongoId(productJsonObject.optString("_id"));
        parsedObject.setMysqlId(productJsonObject.optInt("id"));
        parsedObject.setShopId(productJsonObject.optInt("shopId"));

        if (productJsonObject.optJSONArray("tags") != null)
            parsedObject.setTags(parseJsonArrayToListOfStrings(productJsonObject.optJSONArray("tags")));
        parsedObject.setExtraSalesPrice(productJsonObject.optString("extraSalePrice"));

        if (productJsonObject.optJSONObject("maxAmountPerOrder") != null)
            parsedObject.setMaxAmountPerNowOrder(productJsonObject
                    .optJSONObject("maxAmountPerOrder").optInt("now"));

        if (productJsonObject.optJSONObject("maxAmountPerOrder") != null)
            parsedObject.setMaxAmountPerLaterOrder(productJsonObject
                    .optJSONObject("maxAmountPerOrder").optInt("later"));

        if (productJsonObject.optJSONObject("name") != null)
            parsedObject.setName(productJsonObject.optJSONObject("name").optString("en"));

        if (productJsonObject.optJSONObject("name") != null)
            parsedObject.setArabicName(productJsonObject.optJSONObject("name").optString("ar"));

        parsedObject.setPriority(productJsonObject.optInt("priority"));
        parsedObject.setSalePrice(productJsonObject.optDouble("salePrice"));

        if (productJsonObject.optJSONObject("brand") != null) {
            parsedObject.setBrandName(productJsonObject.optJSONObject("brand").optString("en"));
            parsedObject.setArabicBrandName(productJsonObject.optJSONObject("brand").optString("ar"));
        }

        if (productJsonObject.optJSONObject("subBrand") != null) {
            parsedObject.setSubBrandName(productJsonObject.optJSONObject("subBrand").optString("en"));
            parsedObject.setArabicSubBrandName(productJsonObject.optJSONObject("subBrand").optString("ar"));
        }

        if (productJsonObject.has("automatedDiscount") && !productJsonObject.isNull("automatedDiscount")){
            parsedObject.setAutomatedDiscounts(new ArrayList<>());
            Discount parsedAutomatedDiscount;
            for (String fpId : productJsonObject.optJSONObject("automatedDiscount").keySet()){
                parsedAutomatedDiscount = new Discount();
                parsedAutomatedDiscount.setFpId(fpId);
                parsedAutomatedDiscount.setId(productJsonObject.optJSONObject("automatedDiscount")
                        .optJSONObject(fpId).optString("_id"));
                parsedAutomatedDiscount.setPrice(productJsonObject.optJSONObject("automatedDiscount")
                        .optJSONObject(fpId).optFloat("price"));
                parsedAutomatedDiscount.setStock(productJsonObject.optJSONObject("automatedDiscount")
                        .optJSONObject(fpId).optFloat("stock"));
                parsedObject.getAutomatedDiscounts().add(parsedAutomatedDiscount);
            }
        }

        // Parse OptionSets and Options
        if (productJsonObject.optJSONArray("optionSets") != null) {
            // Retrieve the array of optionSets from the product JSON
            JSONArray optionSetsArray = productJsonObject.optJSONArray("optionSets");

            // Store the count of optionSets
            parsedObject.setOptionSetsCount(optionSetsArray.length());

            //List for storing OptionSets and Options objects
            List<OptionSets> optionSetsList = new ArrayList<>();
            List<Options> optionsList = new ArrayList<>();

            // Parse only the first optionSet from this array
            JSONObject optionSetObj = optionSetsArray.optJSONObject(0);
            if (optionSetObj != null) {
                OptionSets optionSet = new OptionSets();
                optionSet.setOptionSetId(optionSetObj.optInt("id"));

                if (optionSetObj.optJSONObject("optionSetType") != null) {
                    optionSet.setOptionSetTypeId(optionSetObj.optJSONObject("optionSetType").optInt("id"));
                }

                optionSetsList.add(optionSet);

                // Log the first optionSet details
                logger.info("First OptionSet parsed: optionSetId={}, optionSetTypeId={}",
                        optionSet.getOptionSetId(),
                        optionSet.getOptionSetTypeId());

                // Parse options inside the first optionSet
                if (optionSetObj.optJSONArray("options") != null) {
                    JSONArray optionsArray = optionSetObj.optJSONArray("options");

                    if (optionsArray.length() > 0) {
                        JSONObject optionObj = optionsArray.optJSONObject(0);

                        Options option = new Options();
                        option.setId(optionObj.optInt("id"));
                        option.setName(optionObj.optString("name"));
                        option.setType(optionObj.optString("type"));
                        option.setPrice(optionObj.optDouble("price"));
                        option.setSalePrice(optionObj.has("salePrice") && !optionObj.isNull("salePrice")
                                ? optionObj.optDouble("salePrice") : null);
                        option.setStock(optionObj.optInt("stock"));

                        optionsList.add(option);

                        logger.info("First Option parsed: id={}, name={}, price={}, type={}, stock={}",
                                option.getId(),
                                option.getName(),
                                option.getPrice(),
                                option.getType(),
                                option.getStock());
                    } else {
                        logger.warn("First OptionSet has no options.");
                    }
                }
            }

            // Set only the first OptionSet and its Options into Product
            parsedObject.setOptionSet(optionSetsList);
            parsedObject.setOption(optionsList);
        }

        parsedObject.setPrice(productJsonObject.optDouble("price"));
        parsedObject.setCategoryId(productJsonObject.optInt("categoryId"));
        parsedObject.setSubCategoryId(productJsonObject.optInt("subCategoryId"));
        parsedObject.setFpPrice(productJsonObject.optDouble("fpPrice"));
        parsedObject.setStock(productJsonObject.optDouble("stock"));
        parsedObject.setNowStock(productJsonObject.optDouble("nowStock"));
        parsedObject.setExtraSalePriceInHouseDiscount(productJsonObject.optDouble("extraSalePriceInhouseDiscount"));
        parsedObject.setExtraSalePriceVendorDiscount(productJsonObject.optDouble("extraSalePriceVendorDiscount"));
        parsedObject.setAutomatedDiscountPrice(productJsonObject.optString("automatedDiscountPrice"));
        parsedObject.setAutomatedDiscountStock(productJsonObject.optString("automatedDiscountStock"));
        parsedObject.setProductType(productJsonObject.optString("productType"));
        parsedObject.setCustomizable(productJsonObject.optBoolean("isCustomizable"));

        if (productJsonObject.optJSONArray("barcodes") != null) {
            JSONArray barcodesArray = productJsonObject.optJSONArray("barcodes");

            if (barcodesArray.length() > 0) {
                String firstBarcode = barcodesArray.optString(0);
                parsedObject.setBarcode(firstBarcode);  // Set the first barcode
            }
        }

        return parsedObject;
    }

    public Product parseProductLogStockJsonObject(JSONArray productLogStockJsonObject , int orderId) {
        Product parsedObject = new Product();
        for (int i = 0; i < productLogStockJsonObject.length(); i++) {
            JSONObject currentObject = productLogStockJsonObject.getJSONObject(i);
            // Check if orderId exists and matches the target
            if (!currentObject.isNull("orderId") && currentObject.getInt("orderId") == orderId) {
                parsedObject.setDelta(currentObject.optInt("delta"));
            }
        }
        return parsedObject;
    }
}
