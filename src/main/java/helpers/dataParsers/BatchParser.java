package helpers.dataParsers;

import models.Batch;
import org.json.JSONObject;

public class BatchParser extends BaseDataParser {

    public Batch parseProductBatchJsonObject(JSONObject batchJsonObject) {

        Batch parsedObject = new Batch();

        parsedObject.setId(batchJsonObject.optInt("id"));
        parsedObject.setBatchId(batchJsonObject.optInt("batchId"));
        parsedObject.setQty(batchJsonObject.optInt("qty"));
        parsedObject.setTotalQty(batchJsonObject.optInt("totalQty"));
        parsedObject.setAvailable(batchJsonObject.optInt("available"));
        parsedObject.setReserved(batchJsonObject.optInt("reserved"));
        parsedObject.setName(batchJsonObject.optString("name"));
        parsedObject.setExpiryDate(batchJsonObject.optString("expiryDate"));
        parsedObject.setProductionDate(batchJsonObject.optString("productionDate"));
        parsedObject.setBestBefore(batchJsonObject.optString("bestBefore"));
        parsedObject.setRemovalDate(batchJsonObject.optString("removalDate"));
        parsedObject.setShelfLife(batchJsonObject.optInt("shelfLife"));
        parsedObject.setDaysForExpiry(batchJsonObject.optInt("daysForOOV"));
        parsedObject.setDaysForOOV(batchJsonObject.optInt("daysForExpiry"));
        parsedObject.setHasWrongBatch(batchJsonObject.optBoolean("hasWrongBatch"));

        return parsedObject;
    }
}
