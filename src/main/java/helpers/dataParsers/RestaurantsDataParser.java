package helpers.dataParsers;

import models.Restaurant;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class RestaurantsDataParser extends BaseDataParser{

   public Restaurant parseRestaurantJsonObject(JSONObject restaurantJsonObject , String source){

       Restaurant parsedObject = new Restaurant();

       switch (source){

           case REQUEST_SOURCE_CUSTOMER_APP -> {

               parsedObject.setYeloId(restaurantJsonObject.optInt("id"));
               parsedObject.setName(restaurantJsonObject.optString("name"));
               parsedObject.setDescription(restaurantJsonObject.optString("description"));
               parsedObject.setLogo(restaurantJsonObject.optString("logo"));
               parsedObject.setCover(restaurantJsonObject.optString("cover"));
               parsedObject.setOperatingStatus(restaurantJsonObject.optString("operatingStatus"));
               parsedObject.setDeliveryFee(restaurantJsonObject.optDouble("deliveryFee"));
               parsedObject.setDeliveryTime(restaurantJsonObject.optInt("deliveryTime"));
               parsedObject.setMinimumOrderAmount(restaurantJsonObject.optDouble("minimumOrderAmount"));
               parsedObject.setRating(restaurantJsonObject.optDouble("rating"));
               parsedObject.setReviewCount(restaurantJsonObject.optInt("reviewCount"));
               parsedObject.setIsDeliveredByBreadfast(restaurantJsonObject.optBoolean("isDeliveredByBreadfast"));

               //Parse business categories list
               JSONArray retrievedBusinessCategoriesArray = restaurantJsonObject.optJSONArray("businessCategories");
               if(!retrievedBusinessCategoriesArray.isEmpty()){
                   List<String> businessnCategoriesList = new ArrayList<>();
                   for(int i = 0 ; i < retrievedBusinessCategoriesArray.length() ; i++){
                       businessnCategoriesList.add(retrievedBusinessCategoriesArray.optString(i));
                   }
                   parsedObject.setBusinessCategories(businessnCategoriesList);
               }
               break;
           }

           case REQUEST_SOURCE_YELO -> {
               parsedObject.setYeloId(restaurantJsonObject.optInt("user_id"));
               parsedObject.setDeliveryTime(restaurantJsonObject.optInt("delivery_time"));
               parsedObject.setName(restaurantJsonObject.optString("store_name"));
               parsedObject.setRating(restaurantJsonObject.optDouble("store_rating"));
               parsedObject.setReviewCount(restaurantJsonObject.optInt("total_ratings_count"));
               parsedObject.setDeliveryFee(restaurantJsonObject.optDouble("delivery_charge"));
               parsedObject.setDescription(restaurantJsonObject.optString("description"));
               parsedObject.setMinimumOrderAmount(restaurantJsonObject.optDouble("merchantMinimumOrder"));
               parsedObject.setLogo(restaurantJsonObject.optString("logo"));
               parsedObject.setCover(restaurantJsonObject.optString("banner_image"));

               //Map Yelo Available yelo status
               if(restaurantJsonObject.optInt("available")==1){
                   parsedObject.setOperatingStatus("OPEN");
               }
               else{
                   parsedObject.setOperatingStatus("CLOSED");
                   }
               //Parse business categories String
               String businessCategoriesName = restaurantJsonObject.optString("business_categories_name");
               List<String> businessCategoriesList;

               if (businessCategoriesName != null && !businessCategoriesName.trim().isEmpty()) {
                   businessCategoriesList = Arrays.asList(businessCategoriesName.split(","));
               } else {
                   businessCategoriesList = new ArrayList<>();
               }
               parsedObject.setBusinessCategories(businessCategoriesList);
               break;
           }

       }

       return parsedObject;
   }
}
