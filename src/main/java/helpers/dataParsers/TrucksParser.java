package helpers.dataParsers;

import models.Truck;
import org.json.JSONObject;

public class TrucksParser extends BaseDataParser{
    public Truck parseTruckJsonObject(JSONObject truckJsonObject){
        Truck parsedObject = new Truck();
        // Parse basic truck attributes
        parsedObject.setId(truckJsonObject.optInt("id"));
        parsedObject.setPlateChars(truckJsonObject.optString("plateChars"));
        parsedObject.setPlateNum(truckJsonObject.optInt("plateNum"));
        parsedObject.setTypeId(truckJsonObject.optInt("typeId"));
        parsedObject.setSupplierId(truckJsonObject.optInt("supplierId"));
        parsedObject.setStorageCondition(truckJsonObject.optString("storageCondition"));
        parsedObject.setLength(truckJsonObject.optInt("length"));
        parsedObject.setWidth(truckJsonObject.optInt("width"));
        parsedObject.setHeight(truckJsonObject.optInt("height"));
        parsedObject.setMaxWeight(truckJsonObject.optInt("maxWeight"));
        parsedObject.setActive(truckJsonObject.optBoolean("isActive"));

        // Parse type object
        JSONObject typeJson = truckJsonObject.optJSONObject("type");
        parsedObject.setTypeName(typeJson.optString("name"));

        // Parse supplier object
        JSONObject supplierJson = truckJsonObject.optJSONObject("supplier");
        parsedObject.setSupplierName(supplierJson.optString("name"));

        return parsedObject;
    }
}
