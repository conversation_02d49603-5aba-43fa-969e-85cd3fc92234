package helpers.dataParsers;

import models.Transfer;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class TransferDataParser extends BaseDataParser {
public List<Transfer> parseTransfersJsonObject(JSONObject jsonResponse) {
    List<Transfer> transfersList = new ArrayList<>();
    JSONArray payloadArray = jsonResponse.optJSONArray("payload");
    if (payloadArray != null) {
        for (int i = 0; i < payloadArray.length(); i++) {
            JSONObject payloadObject = payloadArray.optJSONObject(i);
            JSONArray transfersArray = payloadObject.optJSONArray("transfers");
            if (transfersArray != null) {
                for (int j = 0; j < transfersArray.length(); j++) {
                    JSONObject transferJsonObject = transfersArray.optJSONObject(j);
                    Transfer transfer = new Transfer();
                    transfer.setId(transferJsonObject.optInt("id"));
                    transfer.setDestinationName(transferJsonObject.optString("destinationName"));
                    transfer.setNumberOfProducts(transferJsonObject.optInt("numberOfProducts"));
                    transfer.setStatusId(transferJsonObject.optInt("statusId"));

                    JSONArray categoriesArray = transferJsonObject.optJSONArray("categories");
                    List<String> categoriesList = new ArrayList<>();
                    if (categoriesArray != null) {
                        for (int k = 0; k < categoriesArray.length(); k++) {
                            categoriesList.add(categoriesArray.optString(k));
                        }
                    }
                    transfer.setCategories(categoriesList);
                    transfersList.add(transfer);
                }
            }
        }
    }
    return transfersList;
}
}
