package helpers.dataParsers;

import models.Timeslot;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

public class TimeslotDataParser extends BaseDataParser{

    public List<Timeslot> parseTimeslotJsonObject(JSONObject timeslotJsonObject) {

        List<Timeslot> timeslots = new ArrayList<>();

        Timeslot parsedObject = new Timeslot();

        if (timeslotJsonObject.has("timeslot") && !timeslotJsonObject.isNull("timeslot")) {

            JSONObject mainTimeslotObject = timeslotJsonObject.getJSONObject("timeslot");

            parsedObject.setCurrentTimeslotCapacity(mainTimeslotObject.getInt("capacity"));
            parsedObject.setCurrentTimeslot(mainTimeslotObject.getString("timeslot"));
            parsedObject.setCurrentTimeslot24hr(set24hrformat(mainTimeslotObject.getString("timeslot")));
            parsedObject.setCurrentTimeslotId(mainTimeslotObject.getInt("sId"));
            parsedObject.setInstantFlag(timeslotJsonObject.getBoolean("instant"));
        }
        parsedObject.setWarehouseStatus(timeslotJsonObject.getString("status"));
        timeslots.add(parsedObject);

        JSONArray slotsArray = timeslotJsonObject.optJSONArray("slots");
        if (slotsArray != null) {
            timeslots.addAll(IntStream.range(0, slotsArray.length())
                    .mapToObj(slotsArray::getJSONObject)
                    .map(slotObject -> {
                        Timeslot slot = new Timeslot();
                        List<Integer> timeslotIds = new ArrayList<>();
                        List<Integer> timeslotCapacity = new ArrayList<>();
                        timeslotIds.add(slotObject.getInt("sId"));
                        slot.setTimeslotIds(timeslotIds);
                        timeslotCapacity.add(slotObject.getInt("capacity"));
                        slot.setTimeslotCapacity(timeslotCapacity);
                        return slot;
                    })
                    .toList());
        }

        return timeslots;
    }

    public List<Integer> parseTimeslotIdsJsonObject(JSONObject timeslotIdsJsonObject){

        List<Integer> timeSlotIdsList = new ArrayList<>();

        List<JSONObject> mainDeliveryModelsArray = parseJsonArrayToListOfJsonObjects(timeslotIdsJsonObject
                .optJSONArray("deliveryModels"));

        for(JSONObject deliveryModel:mainDeliveryModelsArray){
            //Filter by the used delivery model with id 1
            if(deliveryModel.optInt("id")==1){
                List<JSONObject> timeSlotsObjectList =parseJsonArrayToListOfJsonObjects(deliveryModel.optJSONArray("timeslots"));
                for(JSONObject timeSlotObject:timeSlotsObjectList){
                    timeSlotIdsList.add(timeSlotObject.optInt("id"));
                }
            }
        }
        return timeSlotIdsList;
    }
    public String set24hrformat(String timeslot) {
        String formattedTimeslot = null;
        try {
            String[] times = timeslot.split("-");

            DateFormat inputFormat = new SimpleDateFormat("hh:mm a");
            DateFormat twentyFourHourFormat = new SimpleDateFormat("HH:mm");

            String startTime24hr = twentyFourHourFormat.format(inputFormat.parse(times[0]));
            String endTime24hr = twentyFourHourFormat.format(inputFormat.parse(times[1]));

            formattedTimeslot = startTime24hr + "-" + endTime24hr;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return formattedTimeslot;
    }
}
