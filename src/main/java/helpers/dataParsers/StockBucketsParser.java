package helpers.dataParsers;

import models.StockBuckets;
import org.json.JSONObject;

public class StockBucketsParser {

    public StockBuckets parseStock(JSONObject stockObject) {
        StockBuckets stock = new StockBuckets();

        if (stockObject == null) {
            throw new IllegalArgumentException("Stock object is null and cannot be parsed.");
        }

        stock.setOnApp(stockObject.optInt("onApp"));
        stock.setReserved(stockObject.optInt("reserved"));
        stock.setFpStock(stockObject.optInt("fpStock"));
        stock.setNotSellable(stockObject.optInt("notSellable"));
        stock.setMissing(stockObject.optInt("missing"));
        stock.setTotalLiability(stockObject.optInt("totalLiability"));

        return stock;
    }
}
