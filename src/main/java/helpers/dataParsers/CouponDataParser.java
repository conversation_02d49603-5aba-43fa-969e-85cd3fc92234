package helpers.dataParsers;

import models.Coupon;
import org.json.JSONObject;

public class CouponDataParser extends BaseDataParser{

    public Coupon parseCouponJsonObject(JSONObject couponJsonObject){
        Coupon parsedObject = new Coupon();
        parsedObject.setCouponCode(couponJsonObject.optString("code"));
        parsedObject.setAmount(couponJsonObject.optJSONObject("type_info").optString("amount"));
        parsedObject.setPercentValue(couponJsonObject.optJSONObject("type_info").optInt("percent_value"));
        parsedObject.setCouponType(couponJsonObject.optString("coupon_type"));
        parsedObject.setCouponTheme(couponJsonObject.optString("coupon_theme"));
        parsedObject.setActive(couponJsonObject.optBoolean("active"));
        parsedObject.setType(couponJsonObject.optString("type"));
        parsedObject.setId(String.valueOf(couponJsonObject.optInt("id")));

        //Fetch the coupon constrains
        parsedObject.setOrderType(couponJsonObject.optJSONObject("constrains")
                .optJSONObject("general").optString("order_type"));
        parsedObject.setStartDate(couponJsonObject.optJSONObject("constrains")
                .optJSONObject("general").optString("start_date"));
        parsedObject.setEndDate(couponJsonObject.optJSONObject("constrains")
                .optJSONObject("general").optString("end_date"));
        return parsedObject;
    }
}
