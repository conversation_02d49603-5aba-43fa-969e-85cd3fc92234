package helpers.dataParsers;

import models.Address;
import org.json.JSONObject;

public class AddressDataParser extends BaseDataParser{
    public Address parseAddressJsonObject(JSONObject jsonObject){
        Address parsedObject = new Address();
        parsedObject.setId(jsonObject.optString("id"));
        parsedObject.setAreaId(jsonObject.optString("area"));
        parsedObject.setFullAddress(jsonObject.optString("address"));
        parsedObject.setAddressLabel(jsonObject.optString("label"));
        parsedObject.setFirstName(jsonObject.optString("first_name"));
        parsedObject.setLastName(jsonObject.optString("last_name"));
        parsedObject.setPhoneNumber(jsonObject.optString("phone"));
        parsedObject.setFlatNumber(jsonObject.optString("flat"));
        parsedObject.setFloorNumber(jsonObject.optString("floor"));
        parsedObject.setDefault(jsonObject.optBoolean("default_address"));
        parsedObject.setLocationObject(jsonObject.optJSONObject("location"));
        parsedObject.setAreaName(jsonObject.optString("area_name"));
        parsedObject.setDeliveryInstructions(jsonObject.optString("delivery_instructions"));

        return parsedObject;
    }
}
