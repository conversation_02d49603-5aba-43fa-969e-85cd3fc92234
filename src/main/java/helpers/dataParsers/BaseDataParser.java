package helpers.dataParsers;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class BaseDataParser {
    public final String REQUEST_SOURCE_CONTROL_ROOM = "controlRoom";
    public final String REQUEST_SOURCE_CUSTOMER_APP = "customerApp";
    public final String REQUEST_SOURCE_MIDMILE_APP = "midMile";
    public final String REQUEST_SOURCE_ORDER_PAYMENT_PANEL = "orderPaymentPanel";
    public final String REQUEST_SOURCE_GRATUITY_PAYMENT_PANEL = "gratuityPaymentPanel";
    public final String REQUEST_SOURCE_YELO = "yelo";

    public final String REQUEST_SOURCE_SWITCHER = "switcher";
    public final String SHOPPING_APP="shopping";
    public final String GRATUITY="gratuity";
    public final String BILLING = "Billing-aps-integration";
    public final String TOPUP = "topup";
    public static final String STOCK_TAKE="stockTake";

    public List<JSONObject> parseJsonArrayToListOfJsonObjects(JSONArray jsonArray){
        List<JSONObject> parsedList = new ArrayList<>();
        for (Object e : jsonArray){
            parsedList.add((JSONObject) e);
        }
        return parsedList;
    }

    public List<String> parseJsonArrayToListOfStrings(JSONArray jsonArray){
        List<String> parsedList = new ArrayList<>();
        for (Object e : jsonArray){
            parsedList.add((String) e);
        }
        return parsedList;
    }
}
