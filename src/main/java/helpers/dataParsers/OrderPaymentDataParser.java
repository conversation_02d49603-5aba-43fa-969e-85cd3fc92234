package helpers.dataParsers;

import models.Order;
import models.OrderPaymentTransactions;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

public class OrderPaymentDataParser extends BaseDataParser {
    public Order parseOrderPaymentJsonObject(Order order, JSONObject orderPaymentJsonObject, String appName) {

        switch (appName) {
            case SHOPPING_APP -> {
                order.setOrderPaymentId(orderPaymentJsonObject.
                        optJSONObject("order").optString("id"));
            }
            case GRATUITY -> {
                order.setGratuityTransactionId(orderPaymentJsonObject.
                        optJSONObject("order").optString("id"));
            }
            case BILLING -> {
                order.setOrderPaymentId(orderPaymentJsonObject.
                        optJSONObject("order").optString("id"));
                order.setBillOrderId(orderPaymentJsonObject.
                        optJSONObject("order").optString("id"));
            }
            case TOPUP -> {
                order.setOrderPaymentId(orderPaymentJsonObject.
                        optJSONObject("order").optString("id"));
                order.setTopUpOrderId(orderPaymentJsonObject.
                        optJSONObject("order").optString("id"));
            }

        }
        return order;
    }

    public Order parseOrderTransactionJsonObject(Order order, JSONObject orderTransactionsJsonObject,String source) {
        OrderPaymentTransactions paymentTransactions = new OrderPaymentTransactions();
        switch (source){
            case REQUEST_SOURCE_CUSTOMER_APP -> {
                List<JSONObject> transactions = parseJsonArrayToListOfJsonObjects(orderTransactionsJsonObject
                        .optJSONObject("order").optJSONArray("transactions"));
                //Handle case payment is wallet only
                for (JSONObject t : transactions) {
                    if ("inai".equalsIgnoreCase(t.optString("provider_name"))) {
                        order.setOrderCCPaymentTransaction(t);
                        order.setProviderOrderId(order.getOrderCCPaymentTransaction().optString("provider_order_id"));
                        order.setCcTransactionId(order.getOrderCCPaymentTransaction().optString("id"));
                    }
                    else if ("APS".equalsIgnoreCase(t.optString("provider_name"))) {
                        order.setOrderCCPaymentTransaction(t);
                        order.setProviderOrderId(order.getOrderCCPaymentTransaction().optString("provider_order_id"));
                        order.setCcTransactionId(order.getOrderCCPaymentTransaction().optString("id"));
                        order.setApsRedirectionURL(order.getOrderCCPaymentTransaction().optString("redirect_url"));
                    }
                    else {
                        order.setOrderWalletPaymentTransaction(t);
                        order.setWalletTransactionId(order.getOrderWalletPaymentTransaction().optString("id"));
                    }
                }
            }
            case REQUEST_SOURCE_ORDER_PAYMENT_PANEL -> {

                order.setOrderPaymentTransactions(paymentTransactions);
                //Parse Main body keys
                order.getOrderPaymentTransactions()
                        .setOrderPaymentTransactionId(orderTransactionsJsonObject.optString("id"));
                order.getOrderPaymentTransactions()
                        .setClientOrderId(orderTransactionsJsonObject.optString("clientOrderId"));
                order.getOrderPaymentTransactions()
                        .setOrderPaymentTransactionStatus(orderTransactionsJsonObject.optString("status"));
                order.getOrderPaymentTransactions()
                        .setOrderPaymentTransactionAmount(orderTransactionsJsonObject.optString("amount"));
                order.getOrderPaymentTransactions()
                        .setOrderPaymentTransactionType(orderTransactionsJsonObject.optString("type"));

                //Handle different payment transactions
                List<JSONObject> transactions = parseJsonArrayToListOfJsonObjects(orderTransactionsJsonObject
                        .optJSONArray("transactions"));
                //Iterate on all transaction objects
                for (JSONObject t : transactions) {
                    //Case Transaction Inai of type charge
                    if(t.optString("type").equalsIgnoreCase("CHARGE") 
                            && t.optJSONObject("provider").optString("type").equalsIgnoreCase("PAYMENT")){
                        order.getOrderPaymentTransactions()
                                .setChargeCCPaymentTransactionId(t.optString("id"));
                        order.getOrderPaymentTransactions()
                                .setChargeCCPaymentTransactionAmount(t.optFloat("amount"));
                        order.getOrderPaymentTransactions()
                                .setChargeCCPaymentTransactionStatus(t.optString("status"));
                        order.getOrderPaymentTransactions()
                                .setChargeCCPaymentTransactionType(t.optString("type"));
                        order.getOrderPaymentTransactions()
                                .setChargeCCExternalTransactionId(t.optString("externalOrderId"));

                        //Case Transaction Wallet of type charge
                    } else if (t.optString("type").equalsIgnoreCase("CHARGE")
                            && t.optJSONObject("provider").optString("type").equalsIgnoreCase("wallet")) {
                        order.getOrderPaymentTransactions()
                                .setChargeWalletPaymentTransactionId(t.optString("id"));
                        order.getOrderPaymentTransactions()
                                .setChargeWalletPaymentTransactionAmount(t.optFloat("amount"));
                        order.getOrderPaymentTransactions()
                                .setChargeWalletPaymentTransactionStatus(t.optString("status"));
                        order.getOrderPaymentTransactions()
                                .setChargeWalletPaymentTransactionType(t.optString("type"));

                        //Case Transaction Inai of type Refund
                    } else if(t.optString("type").equalsIgnoreCase("REFUND")
                            && t.optJSONObject("provider").optString("type").equalsIgnoreCase("PAYMENT")){
                        order.getOrderPaymentTransactions()
                                .setRefundCCPaymentTransactionId(t.optString("id"));
                        order.getOrderPaymentTransactions()
                                .setRefundCCPaymentTransactionAmount(t.optFloat("amount"));
                        order.getOrderPaymentTransactions()
                                .setRefundCCPaymentTransactionStatus(t.optString("status"));
                        order.getOrderPaymentTransactions()
                                .setRefundCCPaymentTransactionType(t.optString("type"));
                    }
                    //Case Transaction Wallet of type Refund
                    else{
                        order.getOrderPaymentTransactions()
                                .setRefundWalletPaymentTransactionId(t.optString("id"));
                        order.getOrderPaymentTransactions()
                                .setRefundWalletPaymentTransactionAmount(t.optFloat("amount"));
                        order.getOrderPaymentTransactions()
                                .setRefundWalletPaymentTransactionStatus(t.optString("status"));
                        order.getOrderPaymentTransactions()
                                .setRefundWalletPaymentTransactionType(t.optString("type"));
                    }
                }
            }
            case REQUEST_SOURCE_GRATUITY_PAYMENT_PANEL -> {

                order.setOrderPaymentTransactions(paymentTransactions);
                //Parse Main body keys
                order.getOrderPaymentTransactions()
                        .setGratuityTransactionId(orderTransactionsJsonObject.optString("id"));
                order.getOrderPaymentTransactions()
                        .setGratuityClientOrderId(orderTransactionsJsonObject.optString("clientOrderId"));
                order.getOrderPaymentTransactions()
                        .setGratuityTransactionStatus(orderTransactionsJsonObject.optString("status"));

                //Handle different payment transactions
                List<JSONObject> transactions = parseJsonArrayToListOfJsonObjects(orderTransactionsJsonObject
                        .optJSONArray("transactions"));
                //Iterate on all transaction objects
                for (JSONObject t : transactions) {
                    //Case Gratuity Transaction Inai of type Refund
                    if (t.optString("type").equalsIgnoreCase("REFUND")) {
                        order.getOrderPaymentTransactions()
                                .setRefundGratuityOrderTransactionType(t.optJSONObject("provider").optString("type"));

                        //Case Gratuity Transaction Inai of type charge
                    }else {
                        order.getOrderPaymentTransactions()
                                .setChargeGratuityOrderTransactionType(t.optJSONObject("provider").optString("type"));
                    }
                }
            }
        }

        return order;
    }

    public Order parseGratuityTransactionObject(Order order, JSONObject providerJsonObject){
        List<JSONObject> transactions = parseJsonArrayToListOfJsonObjects(providerJsonObject
                .optJSONObject("order").optJSONArray("transactions"));

        if(transactions.getFirst().optString("provider_name").equalsIgnoreCase("inai")) {
            order.setGratuityProviderOrderId(transactions.getFirst().optString("provider_order_id"));
        }
            order.setCcGratuityTransactionId(transactions.getFirst().optString("id"));
            return order;
    }

    public Order parseInaiRedirectionUrl(Order order , JSONObject inaiJsonObject){

        order.setInaiRedirectionUrl(inaiJsonObject.optJSONObject("redirect").optString("url"));

        return order;
    }

    public Order parseGratuityJsonObject(JSONObject gratuityJsonObject, Order order) {
        order.setGratuityOrderId(gratuityJsonObject.optJSONObject("data").optString("id"));
        return order;
    }

    public Order parseRefundJsonObject(JSONObject refundJsonObject, Order order) {
        order.setRefundTransactionId(refundJsonObject.optJSONObject("data").optString("refund_transaction_id"));
        return order;
    }

    public Order parseRefundTransactionJsonObject(JSONObject refundTransactionJsonObject, Order order) {
        order.setRefundAmount(refundTransactionJsonObject.optJSONArray("transactions").optJSONObject(0).optString("amount"));
        order.setRefundType(refundTransactionJsonObject.optJSONArray("transactions").optJSONObject(0).optString("type"));
        return order;
    }
}
