package helpers.dataParsers;

import models.PaymentCategory;
import org.json.JSONObject;

public class PaymentCategoryParser extends BaseDataParser{
    public PaymentCategory parsePaymentCategoryJsonObject(JSONObject jsonObject){
        PaymentCategory parsedObject = new PaymentCategory();

        parsedObject.setCategoryId(jsonObject.optInt("id"));
        parsedObject.setArName(jsonObject.optString("name_ar"));
        parsedObject.setEnName(jsonObject.optString("name_en"));
        parsedObject.setArDescription(jsonObject.optString("description_ar"));
        parsedObject.setEnDescription(jsonObject.optString("description_en"));
        parsedObject.setImageUrl(jsonObject.optString("image"));
        parsedObject.setStatus(jsonObject.optBoolean("status"));
        parsedObject.setOrderIndex(jsonObject.optInt("orderIndex"));
        parsedObject.setCreatedAt(jsonObject.optString("createdAt"));
        parsedObject.setUpdatedAt(jsonObject.optString("updatedAt"));
        parsedObject.setScreenDeepLink(jsonObject.optString("screenDeepLink"));
        parsedObject.setScreenUrl(jsonObject.optString("screenUrl"));

        return parsedObject;
    }
}
