package helpers.dataParsers;

import models.Warehouse;
import org.json.JSONObject;

public class WarehousesDataParser extends BaseDataParser{
    public Warehouse parseWarehouseJsonObject(JSONObject warehouseJsonObject, String source){
        Warehouse parsedObject = new Warehouse();

        switch (source){
            case REQUEST_SOURCE_CONTROL_ROOM -> {
                parsedObject.setLocation(warehouseJsonObject.optJSONObject("location"));
                parsedObject.setNowStatus(warehouseJsonObject.optJSONObject("nowStatus"));
                parsedObject.setFeatureFlags(warehouseJsonObject.optJSONObject("featureFlags"));
                parsedObject.setSettings(warehouseJsonObject.optJSONObject("settings"));
                parsedObject.setTimeZone(warehouseJsonObject.optString("timeZone"));
                parsedObject.setStatus(warehouseJsonObject.optString("status"));
                parsedObject.setSupportManagers(
                        parseJsonArrayToListOfStrings(warehouseJsonObject.optJSONArray("supportManagersIds")));
                parsedObject.setDeliveryAssociates(
                        parseJsonArrayToListOfStrings(warehouseJsonObject.optJSONArray("deliveryAssociatesIds")));
                parsedObject.setOnFloorAssociatesCount(warehouseJsonObject.optString("onFloorAssociatesCount"));
                parsedObject.setDeliveryAssociateCapacityPerOrder(
                        warehouseJsonObject.optString("deliveryAssociateCapacityPerOrder"));
                parsedObject.setMaxDeliveryHours(warehouseJsonObject.optString("maxDeliveryHours"));
                parsedObject.setId(warehouseJsonObject.optString("_id"));
                parsedObject.setName(warehouseJsonObject.optString("name"));
                parsedObject.setManager(warehouseJsonObject.optJSONObject("manager"));
            }

            case REQUEST_SOURCE_CUSTOMER_APP -> {
                parsedObject.setFullWarehouseJsonObject(warehouseJsonObject);
                parsedObject.setId(warehouseJsonObject.optJSONObject("warehouse").optString("id"));
                parsedObject.setName(warehouseJsonObject.optJSONObject("warehouse").optString("name"));
                parsedObject.setShiftId(warehouseJsonObject.optJSONObject("warehouse").optString("shiftId"));
                parsedObject.setCurrentTimeSlot(warehouseJsonObject.optBoolean("isCurrentTimeslot"));
                parsedObject.setAreaName(warehouseJsonObject.optString("area"));
                parsedObject.setNotifyMeFeatureEnabled(warehouseJsonObject.optBoolean("notifyMeFeatureIsEnabled"));
                parsedObject.setLaterSupported(warehouseJsonObject.optString("laterSupported"));
                parsedObject.setNowSupported(warehouseJsonObject.optString("nowSupported"));
                parsedObject.setSupportedFps(
                        parseJsonArrayToListOfJsonObjects(warehouseJsonObject.optJSONArray("fps")));
                parsedObject.setType(parsedObject.getSupportedFps().get(0).optString("type"));
                parsedObject.setStatus(parsedObject.getSupportedFps().get(0).optString("status"));

                //Parse timeslot related keys
                if (warehouseJsonObject.optJSONObject("timeslot") != null) {
                    parsedObject.setTimeSlot(warehouseJsonObject.optJSONObject("timeslot").optString("timeslot"));
                    parsedObject.setTimeSlotId(warehouseJsonObject.optJSONObject("timeslot").optString("timeslotId"));
                }
            }
        }
        return parsedObject;
    }
}
