package helpers.dataParsers;

import models.PaymentServiceProvider;
import org.json.JSONObject;

public class PaymentProviderParser extends BaseDataParser{
    public PaymentServiceProvider parsePaymentProviderJsonObject(JSONObject jsonObject){
        PaymentServiceProvider parsedObject = new PaymentServiceProvider();

        parsedObject.setServiceProviderId(jsonObject.optInt("id"));
        parsedObject.setArName(jsonObject.optString("name_ar"));
        parsedObject.setEnName(jsonObject.optString("name_en"));
        parsedObject.setArDescription(jsonObject.optString("description_ar"));
        parsedObject.setEnDescription(jsonObject.optString("description_en"));
        parsedObject.setImageUrl(jsonObject.optString("image"));
        parsedObject.setStatus(jsonObject.optBoolean("status"));
        parsedObject.setCreatedAt(jsonObject.optString("createdAt"));
        parsedObject.setUpdatedAt(jsonObject.optString("updatedAt"));

        return parsedObject;
    }
}
