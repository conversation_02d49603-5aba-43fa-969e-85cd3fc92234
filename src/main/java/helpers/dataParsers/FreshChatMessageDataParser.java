package helpers.dataParsers;

import models.FreshChatMessage;
import org.json.JSONArray;
import org.json.JSONObject;

public class FreshChatMessageDataParser extends BaseDataParser{
    public FreshChatMessage parseFreshChatMessageObject(JSONObject messageObject){
        FreshChatMessage message = new FreshChatMessage();

        // Set the message ID
        message.setId(messageObject.optString("id"));

        // Extract the content from message_parts
        JSONArray messagePartsArray = messageObject.optJSONArray("message_parts");
        if (messagePartsArray != null && !messagePartsArray.isEmpty()) {
            JSONObject firstPartObject = messagePartsArray.optJSONObject(0);
            if (firstPartObject != null) {
                JSONObject textObject = firstPartObject.optJSONObject("text");
                if (textObject != null) {
                    message.setTextContent(textObject.optString("content"));
                }
            }
        }

        return message;
    }
}
