package helpers.dataParsers;

import models.PaymentService;
import models.PaymentServiceInputParam;
import org.json.JSONObject;

public class PaymentServiceParser extends BaseDataParser{
    public PaymentService parsePaymentServiceJsonObject(JSONObject jsonObject){
        PaymentService parsedObject = new PaymentService();

        parsedObject.setServiceId(jsonObject.optInt("id"));
        parsedObject.setArName(jsonObject.optString("name_ar"));
        parsedObject.setEnName(jsonObject.optString("name_en"));
        parsedObject.setArDescription(jsonObject.optString("description_ar"));
        parsedObject.setEnDescription(jsonObject.optString("description_en"));
        parsedObject.setFeesType(jsonObject.optString("feesType"));
        parsedObject.setFeesValue(jsonObject.optInt("feesValue"));
        parsedObject.setFeesMinimum(jsonObject.optInt("feesMin"));
        parsedObject.setFeesMaximum(jsonObject.optInt("feesMax"));
        parsedObject.setStatus(jsonObject.optBoolean("status"));
        parsedObject.setType(jsonObject.optString("type"));
        parsedObject.setCreatedAt(jsonObject.optString("createdAt"));
        parsedObject.setUpdatedAt(jsonObject.optString("updatedAt"));

        //Parse Input Params JSONArray
        for (JSONObject e : parseJsonArrayToListOfJsonObjects(jsonObject.optJSONArray("inputParams"))){
            PaymentServiceInputParam inputParam = new PaymentServiceInputParam();
            inputParam.setInputParamId(e.optInt("id"));
            inputParam.setEnLabel(e.optString("label_en"));
            inputParam.setArLabel(e.optString("label_ar"));
            inputParam.setEnPlaceHolder(e.optString("placeholder_en"));
            inputParam.setArPlaceHolder(e.optString("placeholder_ar"));
            inputParam.setType(e.optString("fieldType"));
            inputParam.setMinimumLength(e.optInt("minLength"));
            inputParam.setMaximumLength(e.optInt("maxLength"));
            inputParam.setCreatedAt(e.optString("createdAt"));
            inputParam.setUpdatedAt(e.optString("updatedAt"));
            parsedObject.getInputParamsList().add(inputParam);
        }

        //Parse the amountRange JSONObject
        if (jsonObject.optJSONObject("amountRange") != null){
            parsedObject.setMinimumAmountRange(jsonObject.optJSONObject("amountRange").optInt("min"));
            parsedObject.setMaximumAmountRange(jsonObject.optJSONObject("amountRange").optInt("max"));
        }

        //Parse beeServiceObject
        if (jsonObject.optJSONObject("beeService") != null){
            parsedObject.setBeeServiceId(jsonObject.optJSONObject("beeService").optInt("id"));
            parsedObject.setBeeServiceChargeList(parseJsonArrayToListOfJsonObjects(jsonObject
                    .optJSONObject("beeService").optJSONArray("serviceChargeList")));
        }

        return parsedObject;
    }
}
