package helpers.dataParsers;

import models.Category;
import models.Product;
import models.User;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class CategoriesParser extends BaseDataParser{
    public Category parseCategoryJsonObject(JSONObject categoryJsonObject, String source){
        Category parsedObject = new Category();

        switch (source){
            case REQUEST_SOURCE_CUSTOMER_APP, "subCategoryParser" -> {
                parsedObject.setTag(categoryJsonObject.optJSONObject("tag"));
                parsedObject.setIsClosed(categoryJsonObject.optBoolean("isClosed"));
                parsedObject.setSort(categoryJsonObject.optInt("sort"));
                parsedObject.setPublishedAt(categoryJsonObject.optString("publishedAt"));
                parsedObject.setId(categoryJsonObject.optInt("id"));
                parsedObject.setName(categoryJsonObject.optString("name"));
                parsedObject.setArabicName(categoryJsonObject.optString("name_ar"));
                parsedObject.setDescription(categoryJsonObject.optString("description"));
                parsedObject.setArabicDescription(categoryJsonObject.optString("description_ar"));
                parsedObject.setCountOfProducts(categoryJsonObject.optInt("numOfProducts"));

                //Parse the subCategories into a list of categories
                if (categoryJsonObject.has("subcategories")){
                    List<Category> subCategories = new ArrayList<>();
                    List<JSONObject> subCategoriesJsonArray =
                            parseJsonArrayToListOfJsonObjects(categoryJsonObject.optJSONArray("subcategories"));
                    for (JSONObject e : subCategoriesJsonArray){
                        subCategories.add(parseCategoryJsonObject(e, "subCategoryParser"));
                    }
                    parsedObject.setSubCategories(subCategories);
                }
            }
            case STOCK_TAKE, "stockTakeSubCategoryParser" -> {
                // To Do stockTake object parsing
                parsedObject.setId(Integer.parseInt(categoryJsonObject.optString("id")));
                parsedObject.setCategoryId(String.valueOf(categoryJsonObject.optString("id")));
                parsedObject.setName(String.valueOf(categoryJsonObject.optString("name")));
                parsedObject.setArabicName(String.valueOf(categoryJsonObject.optString("nameAr")));

                //Parse the subCategories into a list of categories
                if (categoryJsonObject.has("subCategories")){
                    List<Category> subCategories = new ArrayList<>();
                    List<JSONObject> subCategoriesJsonArray =
                            parseJsonArrayToListOfJsonObjects(categoryJsonObject.optJSONArray("subCategories"));
                    for (JSONObject e : subCategoriesJsonArray){
                        subCategories.add(parseCategoryJsonObject(e, "stockTakeSubCategoryParser"));
                    }
                    parsedObject.setSubCategories(subCategories);
                }

            }
        }
        return parsedObject;
    }
}
