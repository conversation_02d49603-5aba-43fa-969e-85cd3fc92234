package helpers.dataParsers;

import models.Trip;
import org.json.JSONObject;

public class TripsDataParser extends BaseDataParser {
    public Trip parseTripJsonObject(JSONObject tripJsonObject, String sourceArray) {
        Trip parsedObject = new Trip();

        if (sourceArray == "receivals" && tripJsonObject.has("action")) {
            parsedObject.setAction(tripJsonObject.getJSONObject("action").optString("key"));
            parsedObject.setStopId(tripJsonObject.optInt("stopId"));
        } else if (sourceArray == "deliveries" && tripJsonObject.has("action")) {
                parsedObject.setAction(tripJsonObject.getJSONObject("action").optString("key"));
            parsedObject.setStopId(tripJsonObject.optInt("stopId"));
        } else {
            for (int i = 0; i < tripJsonObject.getJSONArray("tasks").length(); i++) {
                parsedObject.setTaskId(tripJsonObject.getJSONArray("tasks").getJSONObject(i).optInt("taskId"));
                parsedObject.setAction(tripJsonObject.getJSONArray("tasks").getJSONObject(i)
                        .getJSONObject("action").optString("key"));
            }
        }
        return parsedObject;
        }
    }

