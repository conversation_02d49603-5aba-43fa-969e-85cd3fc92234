package helpers.dataParsers;

import models.*;
import org.json.JSONArray;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;

public class OrdersDataParser extends BaseDataParser {
    public Order parseOrderJsonObject(JSONObject orderJsonObject, String source) {
        Order parsedObject = new Order();
        User orderOwner = new User();
        Address ownerAddress = new Address();

        switch (source) {
            case REQUEST_SOURCE_CONTROL_ROOM -> {
                parsedObject.setOrderId(String.valueOf(orderJsonObject.optInt("id")));
                parsedObject.setOrderNumber(orderJsonObject.optString("orderNumber"));
                parsedObject.setStatus(orderJsonObject.optString("status"));
                parsedObject.setLastAction(orderJsonObject.optString("lastAction"));
                parsedObject.setNowTomorrowType(orderJsonObject.optString("deliveryModel"));
                parsedObject.setAddressId(orderJsonObject.optString("addressId"));
                parsedObject.setNotes(orderJsonObject.optJSONObject("notes"));
                parsedObject.setFpId(orderJsonObject.optString("fpId"));
                parsedObject.setFpName(orderJsonObject.optString("fpName"));
                parsedObject.setTotalAmount(String.valueOf(orderJsonObject.optDouble("totalAmount")));
                parsedObject.setCollectedAmount(String.valueOf(orderJsonObject.optDouble("collectedAmount")));
                parsedObject.setTimeSlot(orderJsonObject.optString("timeslot"));
                parsedObject.setDeliveryDate(orderJsonObject.optString("deliveryDate"));
                parsedObject.setArea(orderJsonObject.optString("area"));
                parsedObject.setSubArea(orderJsonObject.optString("subarea"));

                orderOwner.setFullName(orderJsonObject.optString("customerName"));

                ownerAddress.setId(orderJsonObject.optString("addressId"));
                ownerAddress.setFullAddress(orderJsonObject.optString("address"));
                ownerAddress.setLocation(orderJsonObject.optJSONObject("addressLocation"));

                parsedObject.setAddress(ownerAddress);
                orderOwner.setAddress(ownerAddress);

                parsedObject.setUser(orderOwner);
            }
            case REQUEST_SOURCE_MIDMILE_APP -> {
                // To Do midMile object parsing
                parsedObject.setDate(String.valueOf(orderJsonObject.optString("date")));
                parsedObject.setOrderId(orderJsonObject.optString("id"));
                parsedObject.setOrderNumber(String.valueOf(orderJsonObject.optString("orderNumber")));
                parsedObject.setReferenceIn(String.valueOf(orderJsonObject.optString("referenceIn")));
                parsedObject.setReferenceOut(String.valueOf(orderJsonObject.optString("referenceOut")));
                parsedObject.setInternalCategory(String.valueOf(orderJsonObject.optString("internalCategory")));
                parsedObject.setSourceLocation(String.valueOf(orderJsonObject.optString("sourceLocation")));
                parsedObject.setDestination(String.valueOf(orderJsonObject.optString("destination")));
                parsedObject.setStatus(String.valueOf(orderJsonObject.optString("status")));
                if (!orderJsonObject.isNull("dispatcher")) {
                    parsedObject.setDispatcher(new User());
                    parsedObject.getDispatcher().setId(String.valueOf(orderJsonObject.optJSONObject("dispatcher").optInt("id")));
                    parsedObject.getDispatcher().setFullName(String.valueOf(orderJsonObject.optJSONObject("dispatcher").optString("name")));
                }
            }
            case REQUEST_SOURCE_CUSTOMER_APP -> {
                //Parsing direct values
                parsedObject.setOrderId(String.valueOf(orderJsonObject.optInt("id")));
                parsedObject.setOrderNumber(orderJsonObject.optString("order_number").replace("#", ""));
                parsedObject.setDeliveryDate(orderJsonObject.optString("delivery_date"));
                parsedObject.setStatus(orderJsonObject.optString("status"));
                parsedObject.setTimeSlot(orderJsonObject.optString("timeslot"));
                parsedObject.setNow(orderJsonObject.optBoolean("now"));
                parsedObject.setNowTomorrowType(orderJsonObject.optString("type"));
                parsedObject.setDeliveryFees(orderJsonObject.optFloat("delivery_fees"));
                parsedObject.setGiftReceipt(orderJsonObject.optBoolean("gift_receipt"));
                parsedObject.setPaymentMethod(orderJsonObject.optString("payment_method"));
                parsedObject.setPaymentTitle(orderJsonObject.optString("payment_title"));
                parsedObject.setTotal(orderJsonObject.optFloat("total"));
                parsedObject.setSubtotal(orderJsonObject.optFloat("subtotal"));
                parsedObject.setDiscount(orderJsonObject.optFloat("discount"));
                parsedObject.setTotalInvoice(orderJsonObject.optFloat("total_invoice"));
                parsedObject.setCancellable(orderJsonObject.optBoolean("cancellable"));
                parsedObject.setArea(orderJsonObject.optJSONObject("address").optString("area"));
                parsedObject.setScheduled(orderJsonObject.optBoolean("is_scheduled"));
                parsedObject.setBalanceUsedInOrder(orderJsonObject.optDouble("balance_used"));
                parsedObject.setTotalDueAmount(orderJsonObject.optDouble("total_due"));
                parsedObject.setIsOrderTimeSlotShifted(orderJsonObject.optBoolean("isTimeSlotShifted"));
                parsedObject.setCaptureMethod(orderJsonObject.optString("capture_method"));
                parsedObject.setIsScheduledExpress(orderJsonObject.optBoolean("scheduled_express"));
                parsedObject.setPromisedTime(orderJsonObject.optString("promised_time"));
                parsedObject.setCurrentStatus(orderJsonObject.optString("current_status"));

                //Parsing Service Fess Field
                parsedObject.setServiceFees(orderJsonObject.optInt("service_fees"));
                parsedObject.setDeliveryNote(orderJsonObject.optString("notes"));

                //Parsing Order Coupon Name
                if (orderJsonObject.has("coupon")) {
                    JSONArray couponArray = orderJsonObject.optJSONArray("coupon");
                    if (couponArray != null && couponArray.length() > 0) {
                        parsedObject.setOrderCouponName(couponArray.optString(0));
                    }
                }

                //Parsing inner JSONs
                parsedObject.setStatuses(orderJsonObject.optJSONObject("statuses"));
                if (orderJsonObject.optJSONObject("gratuity_details") != null) {
                    parsedObject.setGratuityAmount(orderJsonObject.optJSONObject("gratuity_details").optString("gratuity_amount"));
                    parsedObject.setGratuityFundsUsed(orderJsonObject.optJSONObject("gratuity_details").optString("gratuityFundsUsed"));
                    parsedObject.setTotalWithGratuity(parsedObject.getTotal() + Integer.parseInt(parsedObject.getGratuityAmount()));
                }

                //Parse Order Products List and Details
                if(orderJsonObject.has("products")){

                    List<Product> parsedProducts = new ArrayList<>();
                    JSONArray productsArray = orderJsonObject.optJSONArray("products");

                    List<JSONObject> parsedProductsJsonList = parseJsonArrayToListOfJsonObjects(productsArray);

                    for (JSONObject productJson : parsedProductsJsonList){
                        Product product = new Product();
                        product.setMysqlId(productJson.optInt("id"));
                        product.setName(productJson.optString("name"));
                        product.setArabicName(productJson.optString("name_ar"));
                        product.setQuantity(productJson.optInt("quantity"));
                        product.setSubtotal(productJson.optDouble("subtotal"));
                        product.setTotal(productJson.optDouble("total"));
                        product.setIsFreeGift(productJson.optBoolean("free"));

                        parsedProducts.add(product);
                    }
                    parsedObject.setOrderProducts(parsedProducts);
                }

                //Parse User & Address object Details
                parsedObject.setUser(new User());
                parsedObject.getUser().setId(orderJsonObject.optString("customer"));
                parsedObject.getUser().setFullName(orderJsonObject.optString("customer_name"));
                parsedObject.getUser().setAddress(new AddressDataParser()
                        .parseAddressJsonObject(orderJsonObject.optJSONObject("address")));
                parsedObject.getUser().getAddress().setId(orderJsonObject.optJSONObject("address").optString("address_id"));
                parsedObject.getUser().getAddress().setAddressLabel(orderJsonObject.optJSONObject("address").optString("label"));
                parsedObject.getUser().getAddress().setFirstName(orderJsonObject.optJSONObject("address").optString("first_name"));
                parsedObject.getUser().getAddress().setLastName(orderJsonObject.optJSONObject("address").optString("last_name"));
                parsedObject.getUser().getAddress().setPhoneNumber(orderJsonObject.optJSONObject("address").optString("phone"));
                parsedObject.getUser().getAddress().setAreaName(orderJsonObject.optJSONObject("address").optString("area"));
                parsedObject.getUser().getAddress().setAreaId(orderJsonObject.optJSONObject("address").optString("area_id"));
                parsedObject.getUser().getAddress().setFullAddress(orderJsonObject.optJSONObject("address").optString("address"));
                parsedObject.getUser().getAddress().setDeliveryInstructions(orderJsonObject.optJSONObject("address").optString("delivery_instructions"));

                //Parsing inner warehouse object
                parsedObject.setWarehouse(new Warehouse());
                parsedObject.getWarehouse().setId(orderJsonObject.optJSONObject("warehouse").optString("id"));
                parsedObject.getWarehouse().setName(
                        orderJsonObject.optJSONObject("warehouse").optString("name"));
                parsedObject.getWarehouse().setShiftId(
                        orderJsonObject.optJSONObject("warehouse").optString("shift_id"));
                parsedObject.getWarehouse().setTimeSlotId(
                        orderJsonObject.optJSONObject("warehouse").optString("timeslot_id"));

                //Parsing Fees Object
                if(orderJsonObject.has("fees")) {
                    List<JSONObject> feesJsonList = parseJsonArrayToListOfJsonObjects(orderJsonObject
                            .optJSONArray("fees"));

                    for (JSONObject feesObject : feesJsonList) {
                        if ("Delivery".equalsIgnoreCase(feesObject.optString("name"))) {
                            parsedObject.setDeliveryFeesInFeesObject(feesObject.optFloat("total"));
                        } else if ("Discount".equalsIgnoreCase(feesObject.optString("name"))) {
                            parsedObject.setDiscountFeesInFeesObject(feesObject.optDouble("total"));
                        } else if ("Service Fees".equalsIgnoreCase(feesObject.optString("name"))) {
                            parsedObject.setServiceFeesInFeesObject(feesObject.optDouble("total"));
                        } else {
                            parsedObject.setReferralDiscountInFeesObject(feesObject.optDouble("total"));
                        }
                    }
                }
            }
            case REQUEST_SOURCE_SWITCHER -> {
                List<JSONObject> userOrders = parseJsonArrayToListOfJsonObjects(orderJsonObject.
                        optJSONArray("orders"));
                for (JSONObject o : userOrders) {
                    parsedObject.setOrderId(String.valueOf(o.optInt("id")));
                    parsedObject.setTotalInvoice(o.optFloat("total_invoice"));
                    if(!o.isNull("balance_used") ) {
                        parsedObject.setBalanceUsedInOrder(o.optDouble("balance_used"));
                    }
                    else parsedObject.setBalanceUsedInOrder(0.0);
                    parsedObject.setCollectedAmount(String.format("%.2f", o.optDouble("collected")));
                    parsedObject.setPaymentTitle(o.optString("payment_title"));
                    parsedObject.setPaidByInai(o.optBoolean("is_paid_by_inai"));
                    parsedObject.setCredit(o.optBoolean("credit"));
                    parsedObject.setStatus(o.optString("status"));
                }
            }
        }

        return parsedObject;
    }
    public static JSONObject convertHtmlToJson(String htmlResponse) {
        try {
            // Create a JSONArray from the HTML response
            JSONArray jsonArray = new JSONArray(htmlResponse);

            // If successful, get the first JSONObject from the array (assuming there's only one because this is a new user)
            if (jsonArray.length() > 0) {
                return jsonArray.getJSONObject(0);
            } else {
                // Return an empty JSONObject if the array is empty
                return new JSONObject();
            }
        } catch (Exception e) {
            // If parsing as JSONArray fails,Parse as JSONObject directly
            try {
                return new JSONObject(htmlResponse);
            } catch (Exception ex) {
                // If both parsing attempts fail, return an empty JSONObject
                return new JSONObject();
            }
        }
    }
}

