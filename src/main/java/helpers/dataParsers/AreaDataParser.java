package helpers.dataParsers;

import models.Area;
import org.json.JSONObject;

public class AreaDataParser extends BaseDataParser {
    public Area parseAreaJsonObject(JSONObject jsonObject) {
        Area parsedObject = new Area();

        parsedObject.setId(jsonObject.optString("id"));
        parsedObject.setArName(jsonObject.optString("name_ar"));
        parsedObject.setEnName(jsonObject.optString("name_en"));
        parsedObject.setDefaultName(jsonObject.optString("default_name"));

        return parsedObject;
    }

    public Area parseSubAreaJsonObject(JSONObject subAreaJsonObject) {
        Area parsedSubAreaObject = new Area();
        parsedSubAreaObject.setValue(subAreaJsonObject.optString("value"));
        parsedSubAreaObject.setLabel(subAreaJsonObject.optString("label"));
        return parsedSubAreaObject;
    }
}
