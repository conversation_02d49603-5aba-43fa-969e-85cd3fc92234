package helpers.dataParsers;

import models.FleetTrip;
import models.Order;
import models.FleetTrip;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.*;

public class FleetTripDataParser extends BaseDataParser {
    public static FleetTrip parseFleetTrip(JSONObject response) {
        FleetTrip fleetTrip = new FleetTrip();

        fleetTrip.setNumberOfAssignedTrips(response.getInt("numberOfAssignedTrips"));
        fleetTrip.setNumberOfAssignedOrders(response.getInt("numberOfAssignedOrders"));

        fleetTrip.setOnGoing(parseFleetTripDetailsList(response.getJSONArray("onGoing")));
        fleetTrip.setUpComing(parseFleetTripDetailsList(response.getJSONArray("upComing")));

        return fleetTrip;
    }

    private static List<FleetTrip.FleetTripDetails> parseFleetTripDetailsList(JSONArray jsonArray) {
        List<FleetTrip.FleetTripDetails> fleetTripDetailsList = new ArrayList<>();
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject tripObject = jsonArray.getJSONObject(i);
            FleetTrip.FleetTripDetails fleetTripDetails = new FleetTrip.FleetTripDetails();
            fleetTripDetails.setTripId(tripObject.getInt("id"));
            fleetTripDetails.setTripNumber(tripObject.getString("tripNumber"));
            fleetTripDetails.setOrders(parseOrderList(tripObject.getJSONArray("orders")));
            fleetTripDetailsList.add(fleetTripDetails);
        }
        return fleetTripDetailsList;
    }

    private static List<Order> parseOrderList(JSONArray jsonArray) {
        List<Order> orders = new ArrayList<>();
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject orderObject = jsonArray.getJSONObject(i);
            Order order = new Order();
            order.setOrderId(String.valueOf(orderObject.getInt("id")));
            order.setKitchenPickup(orderObject.getBoolean("kitchenPickup"));
            order.setPharmacyPickup(orderObject.getBoolean("pharmacyPickup"));
            orders.add(order);
        }
        return orders;
    }

    public static FleetTrip parseTripAssignedData(JSONObject response) {

        FleetTrip fleetTrip = new FleetTrip();

        JSONObject data = response.optJSONObject("data");

            fleetTrip.setTripId(data.optInt("tripId"));
            fleetTrip.setTripNumber(data.optString("tripNumber"));

        return fleetTrip;
    }

    public static FleetTrip parseTripTasksData(JSONObject response) {
        FleetTrip fleetTrip = new FleetTrip();

        // Parse "assignedTrips"
        JSONObject assignedTripsObject = response.optJSONObject("assignedTrips");
        if (assignedTripsObject != null) {
            List<FleetTrip.AssignedTrip> assignedTrips = new ArrayList<>();
            for (String key : assignedTripsObject.keySet()) {
                JSONObject assignedTripObject = assignedTripsObject.optJSONObject(key);

                if (assignedTripObject != null) {
                    FleetTrip.AssignedTrip assignedTrip = new FleetTrip.AssignedTrip();

                    assignedTrip.setTripReference(assignedTripObject.optString("tripReference"));
                    assignedTrip.setStatusId(assignedTripObject.optInt("statusId"));
                    assignedTrip.setTripId(assignedTripObject.optInt("tripId"));
                    
                    // Parse "action"
                    JSONObject actionObject = assignedTripObject.optJSONObject("action");
                    if (actionObject != null) {
                        FleetTrip.AssignedTrip.Action action = new FleetTrip.AssignedTrip.Action();
                        action.setName(actionObject.optString("name"));
                        action.setAction(actionObject.optString("action"));
                        action.setEnabled(actionObject.optBoolean("enabled"));
                        assignedTrip.setAction(action);
                    }

                    // Parse "areas"
                    JSONArray areasArray = assignedTripObject.optJSONArray("areas");
                    if (areasArray != null) {
                        List<String> areas = new ArrayList<>();
                        for (int i = 0; i < areasArray.length(); i++) {
                            areas.add(areasArray.optString(i));
                        }
                        assignedTrip.setAreas(areas);
                    }

                    // Parse "pickups"
                    JSONObject pickupsObject = assignedTripObject.optJSONObject("pickups");
                    if (pickupsObject != null) {
                        List<FleetTrip.AssignedTrip.Pickup> pickups = new ArrayList<>();
                        for (String locationType : pickupsObject.keySet()) {
                            JSONObject locationTypeObject = pickupsObject.optJSONObject(locationType);
                            if (locationTypeObject != null) {
                                for (String pickupId : locationTypeObject.keySet()) {
                                    JSONObject pickupObject = locationTypeObject.optJSONObject(pickupId);
                                    if (pickupObject != null) {
                                        FleetTrip.AssignedTrip.Pickup pickup = new FleetTrip.AssignedTrip.Pickup();
                                        pickup.setId(pickupObject.optString("id"));
                                        pickup.setLocationType(pickupObject.optString("locationType"));
                                        pickup.setTripId(pickupObject.optString("tripId"));
                                        pickup.setType(pickupObject.optString("type"));
                                        pickup.setLocationTypeId(pickupObject.optInt("locationTypeId"));
                                        pickup.setStatusId(pickupObject.optInt("statusId"));
                                        pickup.setStatus(pickupObject.optString("status"));

                                        // Parse "action" inside pickup
                                        JSONObject pickupActionObject = pickupObject.optJSONObject("action");
                                        if (pickupActionObject != null) {
                                            FleetTrip.AssignedTrip.Action pickupAction = new FleetTrip.AssignedTrip.Action();
                                            pickupAction.setName(pickupActionObject.optString("name"));
                                            pickupAction.setAction(pickupActionObject.optString("action"));
                                            pickupAction.setEnabled(pickupActionObject.optBoolean("enabled"));
                                            pickup.setAction(pickupAction);
                                        }

                                        // Parse "details" inside pickup
                                        JSONObject detailsObject = pickupObject.optJSONObject("details");
                                        if (detailsObject != null) {
                                            FleetTrip.AssignedTrip.Pickup.PickupDetails details = new FleetTrip.AssignedTrip.Pickup.PickupDetails();
                                            details.setEta(detailsObject.optString("eta"));
                                            details.setOrderNumber(detailsObject.optString("orderNumber"));
                                            details.setTimeslot(detailsObject.optString("timeslot"));
                                            details.setDeliveryDeadlineTs(detailsObject.optString("deliveryDeadlineTs"));
                                            details.setStatus(detailsObject.optString("status"));

                                            pickup.setDetails(details);
                                        }

                                        pickups.add(pickup);
                                    }
                                }
                            }
                        }
                        assignedTrip.setPickups(pickups);
                    }

                    assignedTrips.add(assignedTrip);
                }
            }
            fleetTrip.setAssignedTrips(assignedTrips);
        }

        // Parse "currentTrip"
        JSONObject currentTripObject = response.optJSONObject("currentTrip");
        if (currentTripObject != null) {
            for (String tripKey : currentTripObject.keySet()) {
                JSONObject currentTripData = currentTripObject.optJSONObject(tripKey);
                if (currentTripData != null) {
                    FleetTrip.CurrentTrip currentTrip = new FleetTrip.CurrentTrip();

                    currentTrip.setTripId(tripKey);
                    currentTrip.setTripReference(currentTripData.optString("tripReference"));
                    currentTrip.setStatusId(currentTripData.optInt("statusId"));
                    currentTrip.setStatus(currentTripData.optString("status"));

                    // Parse "action"
                    JSONObject actionObject = currentTripData.optJSONObject("action");
                    if (actionObject != null) {
                        FleetTrip.CurrentTrip.Action action = new FleetTrip.CurrentTrip.Action();
                        action.setName(actionObject.optString("name"));
                        action.setAction(actionObject.optString("action"));
                        action.setEnabled(actionObject.optBoolean("enabled"));
                        currentTrip.setAction(action);
                    }

                    // Parse "areas"
                    JSONArray areasArray = currentTripData.optJSONArray("areas");
                    if (areasArray != null) {
                        List<String> areas = new ArrayList<>();
                        for (int i = 0; i < areasArray.length(); i++) {
                            areas.add(areasArray.optString(i));
                        }
                        currentTrip.setAreas(areas);
                    }

                    // Parse "pickups"
                    JSONObject pickupsObject = currentTripData.optJSONObject("pickups");
                    if (pickupsObject != null) {
                        List<FleetTrip.CurrentTrip.Pickup> pickups = new ArrayList<>();
                        for (String locationType : pickupsObject.keySet()) {
                            JSONObject locationTypeObject = pickupsObject.optJSONObject(locationType);
                            if (locationTypeObject != null) {
                                for (String pickupId : locationTypeObject.keySet()) {
                                    JSONObject pickupObject = locationTypeObject.optJSONObject(pickupId);
                                    if (pickupObject != null) {
                                        FleetTrip.CurrentTrip.Pickup pickup = new FleetTrip.CurrentTrip.Pickup();
                                        pickup.setId(pickupObject.optString("id"));
                                        pickup.setLocationType(pickupObject.optString("locationType"));
                                        pickup.setTripId(pickupObject.optString("tripId"));
                                        pickup.setType(pickupObject.optString("type"));
                                        pickup.setLocationTypeId(pickupObject.optInt("locationTypeId"));
                                        pickup.setStatusId(pickupObject.optInt("statusId"));
                                        pickup.setStatus(pickupObject.optString("status"));

                                        // Parse "action" inside pickup
                                        JSONObject pickupActionObject = pickupObject.optJSONObject("action");
                                        if (pickupActionObject != null) {
                                            FleetTrip.CurrentTrip.Action pickupAction = new FleetTrip.CurrentTrip.Action();
                                            pickupAction.setName(pickupActionObject.optString("name"));
                                            pickupAction.setAction(pickupActionObject.optString("action"));
                                            pickupAction.setEnabled(pickupActionObject.optBoolean("enabled"));
                                            pickup.setAction(pickupAction);
                                        }

                                        // Parse "details" inside pickup
                                        JSONObject detailsObject = pickupObject.optJSONObject("details");
                                        if (detailsObject != null) {
                                            FleetTrip.CurrentTrip.Pickup.PickupDetails details = new FleetTrip.CurrentTrip.Pickup.PickupDetails();
                                            details.setEta(detailsObject.optString("eta"));
                                            details.setOrderNumber(detailsObject.optString("orderNumber"));
                                            details.setTimeslot(detailsObject.optString("timeslot"));
                                            details.setDeliveryDeadlineTs(detailsObject.optString("deliveryDeadlineTs"));
                                            details.setStatus(detailsObject.optString("status"));

                                            pickup.setDetails(details);
                                        }

                                        pickups.add(pickup);
                                    }
                                }
                            }
                        }
                        currentTrip.setPickups(pickups);
                    }

                    JSONObject deliveriesObject = currentTripData.optJSONObject("deliveries");
                    if (deliveriesObject != null) {
                        List<FleetTrip.CurrentTrip.Delivery> deliveries = new ArrayList<>();
                        for (String deliveryId : deliveriesObject.keySet()) {
                            JSONObject deliveryObject = deliveriesObject.optJSONObject(deliveryId);
                            if (deliveryObject != null) {
                                FleetTrip.CurrentTrip.Delivery delivery = new FleetTrip.CurrentTrip.Delivery();
                                delivery.setId(deliveryObject.optString("id"));
                                delivery.setLocationType(deliveryObject.optString("locationType"));
                                delivery.setTripId(deliveryObject.optString("tripId"));
                                delivery.setType(deliveryObject.optString("type"));
                                delivery.setLocationTypeId(deliveryObject.optInt("locationTypeId"));
                                delivery.setStatusId(deliveryObject.optInt("statusId"));
                                delivery.setStatus(deliveryObject.optString("status"));

                                // Parse "action" inside delivery
                                JSONObject deliveryActionObject = deliveryObject.optJSONObject("action");
                                if (deliveryActionObject != null) {
                                    FleetTrip.CurrentTrip.Action deliveryAction = new FleetTrip.CurrentTrip.Action();
                                    deliveryAction.setName(deliveryActionObject.optString("name"));
                                    deliveryAction.setAction(deliveryActionObject.optString("action"));
                                    deliveryAction.setEnabled(deliveryActionObject.optBoolean("enabled"));
                                    delivery.setAction(deliveryAction);
                                }

                                // Parse "details" inside delivery
                                JSONObject detailsObject = deliveryObject.optJSONObject("details");
                                if (detailsObject != null) {
                                    FleetTrip.CurrentTrip.Delivery.Details details = new FleetTrip.CurrentTrip.Delivery.Details();
                                    details.setAmount(detailsObject.optString("amount"));
                                    details.setOrderNumber(detailsObject.optString("orderNumber"));
                                    details.setPickingStatus(detailsObject.optInt("pickingStatus"));
                                    details.setCustomerNotes(detailsObject.optString("customerNotes"));
                                    details.setHasFragileItems(detailsObject.optBoolean("hasFragileItems"));
                                    details.setHasFrozenItems(detailsObject.optBoolean("hasFrozenItems"));
                                    details.setEta(detailsObject.optString("eta"));
                                    details.setTotalToCollect(detailsObject.optString("totalToCollect"));
                                    details.setFpId(detailsObject.optString("fpId"));
                                    details.setTimeslot(detailsObject.optString("timeslot"));
                                    details.setDeliveryDeadlineTs(detailsObject.optString("deliveryDeadlineTs"));
                                    details.setShelfNumber(detailsObject.optInt("shelfNumber"));
                                    details.setSubArea(detailsObject.optString("subArea"));
                                    details.setId(detailsObject.optInt("id"));
                                    details.setStatus(detailsObject.optString("status"));
                                    details.setHasCoffeeItems(detailsObject.optBoolean("hasCoffeeItems"));

                                    // Parsing CashCollection object
                                    JSONObject cashCollectionObject = detailsObject.optJSONObject("cashCollection");
                                    if (cashCollectionObject != null) {
                                        FleetTrip.CurrentTrip.Delivery.Details.CashCollection cashCollection = new FleetTrip.CurrentTrip.Delivery.Details.CashCollection();
                                        cashCollection.setMaxAmountToCollect(cashCollectionObject.optDouble("maxAmountToCollect"));
                                        cashCollection.setAmountToCollect(cashCollectionObject.optDouble("amountToCollect"));
                                        cashCollection.setMinSafeAmountToCollect(cashCollectionObject.optDouble("minSafeAmountToCollect"));
                                        cashCollection.setMaxSafeAmountToCollect(cashCollectionObject.optDouble("maxSafeAmountToCollect"));
                                        details.setCashCollection(cashCollection);
                                    }

                                    delivery.setDetails(details);
                                }

                                deliveries.add(delivery);
                            }
                        }
                        currentTrip.setDeliveries(deliveries);
                    }

                    fleetTrip.setCurrentTrip(currentTrip);
                    break;
                }
            }
        }

        return fleetTrip;
    }

}
