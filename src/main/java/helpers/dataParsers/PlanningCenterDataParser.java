package helpers.dataParsers;

import models.PlanningCenter;
import models.Product;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class PlanningCenterDataParser extends BaseDataParser{

    public List<PlanningCenter> parsedInternalCategoriesJsonObject(JSONObject responseJsonObject) {
        List<PlanningCenter> parsedCategories = new ArrayList<>();
        JSONArray payloadArray = responseJsonObject.optJSONArray("payload");
        if (payloadArray != null && payloadArray.length() > 0) {
            for (int i = 0; i < payloadArray.length(); i++) {
                JSONObject categoryObject = payloadArray.getJSONObject(i);
                PlanningCenter parsedObject = new PlanningCenter();
                parsedObject.setInternalCategoryId(categoryObject.optInt("id"));
                parsedObject.setInternalCategoryName(categoryObject.optString("name"));
                parsedObject.setAutomated(categoryObject.optBoolean("isAutomated"));
                parsedCategories.add(parsedObject);
            }
        }
        return parsedCategories;
    }

    public Product parsedProductsJsonObject(JSONObject responseJsonObject) {
        Product parsedObject = new Product();
        parsedObject.setMysqlId(responseJsonObject.
                optJSONObject("payload").
                optJSONArray("products").
                optJSONObject(0).optInt("id"));
        return parsedObject;
    }

    public PlanningCenter parsedInternalOrderJsonObject(JSONObject responseJsonObject) {
        PlanningCenter parsedObject = new PlanningCenter();
        parsedObject.setPayload(responseJsonObject.optJSONArray("payload"));
        JSONArray payloadArray = parsedObject.getPayload();
        JSONObject internalOrderObject = payloadArray.getJSONObject(0);
        parsedObject.setInternalOrderId(internalOrderObject.optInt("id"));
        parsedObject.setStatusId(internalOrderObject.optInt("statusId"));
        parsedObject.setReadyToSync(internalOrderObject.optInt("readyToSync"));
        parsedObject.setTriedToSendBefore(internalOrderObject.optInt("triedToSendBefore"));
        return parsedObject;
    }

    public PlanningCenter parsedInternalOrderProductsJsonObject(JSONObject responseJsonObject) {
        PlanningCenter parsedObject = new PlanningCenter();
        JSONArray payloadArray = responseJsonObject.optJSONArray("payload");
        JSONObject internalOrderProductObject = payloadArray.getJSONObject(0);
        parsedObject.setInternalOrderId(internalOrderProductObject.optInt("id"));
        parsedObject.setInternalOrderProductId(internalOrderProductObject.optInt("productId"));
        parsedObject.setAdjustedQuantity(internalOrderProductObject.optInt("adjustedQuantity"));
        return parsedObject;
    }

}
