package helpers.dataParsers;

import models.BusinessCategory;
import org.json.JSONObject;

public class BusinessCategoryDataParser extends BaseDataParser{

    public BusinessCategory parseBusinessCategoryJsonObject(JSONObject businessJsonObject){

        BusinessCategory parsedObject = new BusinessCategory();

        parsedObject.setYeloId(businessJsonObject.optInt("id"));
        parsedObject.setName(businessJsonObject.optString("name"));
        parsedObject.setLogo(businessJsonObject.optString("logo"));

        return parsedObject;
    }
}
