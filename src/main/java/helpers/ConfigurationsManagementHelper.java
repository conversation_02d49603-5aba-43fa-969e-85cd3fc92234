package helpers;

import models.Configs;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;

import java.lang.reflect.Method;

public class ConfigurationsManagementHelper {
    Configs configs;
    DataHelper dataHelper = new DataHelper();
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationsManagementHelper.class);
    public ConfigurationsManagementHelper(Configs configs) {
        this.configs = configs;
    }

    public Configs updateConfigsToMatchTestTag(Method method){
        if (method.getAnnotation(Tags.class) != null && method.getAnnotation(Tags.class).value().length != 0) {
            logger.info("Fetched the tags for test \"" + method.getName() + "\" and the current tags count is \""
                    + method.getAnnotation(Tags.class).value().length + "\"");
            for (Tag tag : method.getAnnotation(Tags.class).value()) {
                logger.info("Processing tag \"" + tag.value() + "\"...");
                switch (tag.value().toLowerCase().replaceAll("[^a-zA-Z0-9]", "")) {
                    case "ios" -> {
                        configs.setMobileBuildEnabled(true);
                        configs.setAndroidBuildEnabled(false);
                        configs.setiOSBuildEnabled(true);
                        configs = dataHelper.readIosConfigs(configs);
                        configs = dataHelper.readBrowserStackConfigs(configs);
                        configs = dataHelper.readLambdaTestConfigs(configs);
                    }
                    case "android" -> {
                        configs.setMobileBuildEnabled(true);
                        configs.setAndroidBuildEnabled(true);
                        configs.setiOSBuildEnabled(false);
                        configs = dataHelper.readAndroidConfigs(configs);
                        configs = dataHelper.readBrowserStackConfigs(configs);
                        configs = dataHelper.readLambdaTestConfigs(configs);
                    }
                    case "web" -> {
                        configs.setWebBuildEnabled(true);
                        configs = dataHelper.readWebConfigs(configs);
                    }
                    case "cardservice" -> {
                        configs.setCardServicesTestsEnabled(true);
                        configs = dataHelper.readCardServiceConfigs(configs);
                    }
                    case "mobileshopping" -> configs.setBuildMobileCategoriesAndProductsTestData(true);
                    case "shops", "mobileshops", "shop", "shopmobile" -> configs.setBuildMobileShopsTestData(true);
                    case "controlroom", "deliverycapacitymanagement", "planningcenter", "transit" -> configs.setBuildWebControlRoomWarehousesAndOrders(true);
                    case "roles" -> configs.setRolesTestsEnabled(true);
                    case "webhooks" -> configs.setWebhookBuildEnabled(true);
                    case "customerapp", "customerappreactnative" -> configs.setTargetApp("customerAppReactNative");
                    case "customerappnative" -> configs.setTargetApp("customerAppNative");
                    case "midmile" -> configs.setTargetApp("midMileApp");
                    case "fleetapp" -> configs.setTargetApp("fleetApp");
                    case "database" -> configs.setConnectToDB(true);
                    case "stocktake","stocktaker","stock" -> configs.setBuildStockTakerTestData(true);
                    case "restaurants" , "foodaggregator" -> configs.setBuildFoodAggregatorTestData(true);
                    case "foreigncountry" -> configs.setUseForeignCountryData(true);
                    case "billing" -> configs.setBuildMobileBillingTestData(true);
                    case "skipadminauthorizationstep"-> configs.setSkipAdminAuthorizationStep(true);
                }
                logger.info("Finalised processing tag \"" + tag.value() + "\".");
            }
        } else {
            logger.error("Didn't find any tags associated with test method. Proceeding with configs as is.");
        }
        return configs;
    }
}
