package helpers.apiValidator;

import models.Order;
import models.Warehouse;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.Assert;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class GetOrderApiValidator {
    private static final Logger logger = LoggerFactory.getLogger(GetOrderApiValidator.class);

    public void assertOnDeliveryNote(String deliveryNote,String expectedDeliveryNote){
        if (!deliveryNote.equals(expectedDeliveryNote)){
            Assert.fail("Checkout Delivery Note isn't as Expected" + deliveryNote);
        }
    }
    public void assertOrderStatus(String orderStatus,String expectedOrderStatus) {
        if (!orderStatus.equals(expectedOrderStatus)){
            Assert.fail("Status isn't as your Expectation" + orderStatus);
        }
    }
    public void assertCancellable(boolean cancellable,boolean expectedCancellableValue ) {
        if (expectedCancellableValue != cancellable){
            Assert.fail("Cancel value isn't as your Expectation" + cancellable);
        }
    }

    public void assertCurrentOrderStatus(String currentOrderStatus,String expectedCurrentOrderStatus) {
        if (!currentOrderStatus.equals(expectedCurrentOrderStatus)) {
            Assert.fail("Current_status isn't as your Expectation" + currentOrderStatus);
        }
    }
    public void assertPaymentMethod(String paymentMethod,String expectedPaymentMethod) {
        if (!paymentMethod.equals(expectedPaymentMethod)) {
            Assert.fail("payment_method isn't as your Expectation" + paymentMethod);
        }
    }
    public void assertPaymentTitle(String paymentTitle,String expectedPaymentTitle) {
        if (!paymentTitle.equals(expectedPaymentTitle)) {
            Assert.fail("payment_title isn't as your Expectation" + paymentTitle);
        }
    }
    public void assertBalanceUsed(double balanceUsed,double expectedBalanceUsed) {
        if (balanceUsed != expectedBalanceUsed) {
            Assert.fail("balance_used isn't as your Expectation" + balanceUsed);
        }
    }

    public void assertNow(boolean now,boolean expectedNowValue ) {
        if (expectedNowValue != now){
            Assert.fail("now value isn't as your Expectation" + now);
        }
    }

    public void assertIsTimeSlotShifted(boolean isTimeSlotShifted,boolean expectedIsTimeSlotShifted ) {
        if (expectedIsTimeSlotShifted != isTimeSlotShifted){
            Assert.fail("IsTimeSlotShifted value isn't as your Expectation" + isTimeSlotShifted);
        }
    }

    public void assertIsScheduled(boolean isScheduled,boolean expectedIsScheduled ) {
        if (expectedIsScheduled != isScheduled){
            Assert.fail("isScheduled value isn't as your Expectation" + isScheduled);
        }
    }

    public void assertScheduledExpress(boolean scheduledExpress,boolean expectedScheduledExpress ) {
        if (expectedScheduledExpress != scheduledExpress){
            Assert.fail("scheduledExpress value isn't as your Expectation" + scheduledExpress);
        }
    }
}
