package helpers.apiValidator;

import models.*;
import org.apache.commons.math3.util.Precision;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.Assert;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class CreateOrderApiValidator {

    private static final Logger logger = LoggerFactory.getLogger(CreateOrderApiValidator.class);

    public void assertProductObjectInResponse(Product testProduct, Product responseProduct, String fpId) {

        List<String> validationErrors = new ArrayList<>();

        if (testProduct.getMysqlId() != responseProduct.getMysqlId()) {
            validationErrors.add("Product ID mismatch: expected " + testProduct.getMysqlId() +
                    " but got " + responseProduct.getMysqlId());
        }

        if (!testProduct.getName().equalsIgnoreCase(responseProduct.getName())) {
            validationErrors.add("Product name mismatch: expected " + testProduct.getName() +
                    " but got " + responseProduct.getName());
        }

        if (!testProduct.getArabicName().equalsIgnoreCase(responseProduct.getArabicName())) {
            validationErrors.add("Product Arabic name mismatch: expected " + testProduct.getArabicName() +
                    " but got " + responseProduct.getArabicName());
        }
        //Asserting case Product has no sale price
        if (testProduct.getSalePrice() == 0
                && testProduct.getExtraSalesPrice().isEmpty()
                && (testProduct.getAutomatedDiscounts() == null || testProduct.getAutomatedDiscounts().isEmpty()
                || testProduct.getAutomatedDiscounts().stream().noneMatch(d -> d.getFpId().equals(fpId)))) {
            if (testProduct.getPrice() != responseProduct.getTotal()) {
                validationErrors.add("Product price mismatch: expected " + testProduct.getPrice() +
                        " but got " + responseProduct.getTotal());
            }
        }

        //Asserting case Product has sale price
        else if (testProduct.getSalePrice() != 0
                && testProduct.getExtraSalesPrice().isEmpty()
                && (testProduct.getAutomatedDiscounts() == null || testProduct.getAutomatedDiscounts().isEmpty()
                || testProduct.getAutomatedDiscounts().stream().noneMatch(d -> d.getFpId().equals(fpId)))) {
            if (testProduct.getSalePrice() != responseProduct.getTotal()) {
                validationErrors.add("Product price mismatch: expected " + testProduct.getPrice() +
                        " but got " + responseProduct.getTotal());
            }
        }

        //Asserting case Product has extra sale price
        else {
            if (Float.parseFloat(testProduct.getExtraSalesPrice()) != responseProduct.getTotal()) {
                validationErrors.add("Product price mismatch: expected " + testProduct.getPrice() +
                        " but got " + responseProduct.getTotal());
            }
        }

        //Asserting subtotal value
        if (testProduct.getPrice() != responseProduct.getSubtotal()) {
            validationErrors.add("Product subtotal mismatch: expected " + testProduct.getPrice() +
                    " but got " + responseProduct.getSubtotal());
        }

        if (!validationErrors.isEmpty()) {
            logger.info("Create order api field validation errors found :");
            validationErrors.forEach(logger::info);
            String errorMessage = "Test terminated: Create Order API has field validation errors in Product Objects\n" +
                    "Validation errors:\n" +
                    validationErrors.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("\n"));
            Assert.fail(errorMessage);
        }
    }

    public void assertProductListInResponse(List<Product> testProducts, List<Product> responseProducts, String fpId, boolean hasFreeGift, boolean hasDueAmount) {

        //Exclude Previous Due amount from product list in response if found
        if (hasDueAmount) {
            responseProducts = responseProducts.stream()
                    .filter(product -> product.getMysqlId() != 10020)
                    .collect(Collectors.toList());
        }
        //Exclude Free Gift Products in order if found
        if (hasFreeGift) {
            responseProducts = responseProducts.stream()
                    .filter(product -> !product.getIsFreeGift())
                    .collect(Collectors.toList());
        }

        if (testProducts.size() != responseProducts.size()) {
            logger.info("Product list size mismatch: expected {} but got {}",
                    testProducts.size(), responseProducts.size());
            Assert.fail("Terminating The Test: Number of products doesn't match");
            return;
        }

        List<String> validationErrors = new ArrayList<>();

        Map<Integer, Product> responseProductMap = new HashMap<>();
        for (Product responseProduct : responseProducts) {
            responseProductMap.put(responseProduct.getMysqlId(), responseProduct);
        }

        // Check each test product
        for (Product testProduct : testProducts) {
            Product responseProduct = responseProductMap.get(testProduct.getMysqlId());

            if (responseProduct == null) {
                validationErrors.add("Product with ID " + testProduct.getMysqlId() + " not found in response");
                Assert.fail("Terminating The Test: One of the products IDs was not found in response.");
                return;
            }

            // Name validation
            if (!testProduct.getName().equals(responseProduct.getName())) {
                validationErrors.add("Product name mismatch: expected " +
                        testProduct.getName() + " but got " + responseProduct.getName());
            }

            // Arabic name validation
            if (!testProduct.getArabicName().equals(responseProduct.getArabicName())) {
                validationErrors.add("Product Arabic name mismatch: expected " +
                        testProduct.getArabicName() + " but got " + responseProduct.getArabicName());
            }

            // Price validation based on different scenarios
            boolean hasNoAutomatedDiscounts = testProduct.getAutomatedDiscounts() == null ||
                    testProduct.getAutomatedDiscounts().isEmpty() ||
                    testProduct.getAutomatedDiscounts().stream()
                            .noneMatch(d -> d.getFpId().equals(fpId));

            // Case 1: No sale price
            if (testProduct.getSalePrice() == 0
                    && testProduct.getExtraSalesPrice().isEmpty()
                    && hasNoAutomatedDiscounts) {
                if (testProduct.getPrice() != responseProduct.getTotal()) {
                    validationErrors.add("Product price mismatch: expected " +
                            testProduct.getPrice() + " but got " + responseProduct.getTotal());
                }
            }
            // Case 2: Has sale price
            else if (testProduct.getSalePrice() != 0
                    && testProduct.getExtraSalesPrice().isEmpty()
                    && hasNoAutomatedDiscounts) {
                if (testProduct.getSalePrice() != responseProduct.getTotal()) {
                    validationErrors.add("Product price mismatch: expected " +
                            testProduct.getSalePrice() + " but got " + responseProduct.getTotal());
                }
            }
            // Case 3: Has extra sale price
            else {
                if (Float.parseFloat(testProduct.getExtraSalesPrice()) != responseProduct.getTotal()) {
                    validationErrors.add("Product price mismatch: expected " +
                            testProduct.getExtraSalesPrice() + " but got " + responseProduct.getTotal());
                }
            }

            // Subtotal validation
            if (testProduct.getPrice() != responseProduct.getSubtotal()) {
                validationErrors.add("Product subtotal mismatch: expected " +
                        testProduct.getPrice() + " but got " + responseProduct.getSubtotal());
            }
        }

        // Check for any products in response that weren't in test products
        for (Product responseProduct : responseProducts) {
            if (testProducts.stream().noneMatch(p -> p.getMysqlId() == responseProduct.getMysqlId())) {
                validationErrors.add("Unexpected product in response with ID: " + responseProduct.getMysqlId());
            }
        }

        if (!validationErrors.isEmpty()) {
            logger.info("Create order api field validation errors found:");
            validationErrors.forEach(logger::info);
            String errorMessage = "Test terminated: Create Order API has field validation errors in Product Objects\n" +
                    "Validation errors:\n" +
                    validationErrors.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("\n"));
            Assert.fail(errorMessage);
        }
    }

    public void assertUserDetailsInResponse(User testUser, User orderOwner) {
        List<String> validationErrors = new ArrayList<>();

        if (!testUser.getId().equals(orderOwner.getId())) {
            validationErrors.add("User ID mismatch: expected " + testUser.getId() +
                    " but got " + orderOwner.getId());
        }
        if (!testUser.getFullName().equalsIgnoreCase(orderOwner.getFullName())) {
            validationErrors.add("User Name mismatch: expected " + testUser.getFullName() +
                    " but got " + orderOwner.getFullName());
        }
        if (!testUser.getAddress().getId().equalsIgnoreCase(orderOwner.getAddress().getId())) {
            validationErrors.add("User Address ID mismatch: expected " + testUser.getAddress().getId() +
                    " but got " + orderOwner.getAddress().getId());
        }
        if (!testUser.getAddress().getAddressLabel().equalsIgnoreCase(orderOwner.getAddress().getAddressLabel())) {
            validationErrors.add("User Address Label mismatch: expected " + testUser.getAddress().getAddressLabel() +
                    " but got " + orderOwner.getAddress().getAddressLabel());
        }
        if (!testUser.getPhoneNumber().equalsIgnoreCase(orderOwner.getAddress().getPhoneNumber())) {
            validationErrors.add("User Phone number in Address mismatch: expected " + testUser.getPhoneNumber() +
                    " but got " + orderOwner.getAddress().getPhoneNumber());
        }
        if (!testUser.getAddress().getAreaName().equalsIgnoreCase(orderOwner.getAddress().getAreaName())) {
            validationErrors.add("Area Name in Address mismatch: expected " + testUser.getAddress().getAreaName() +
                    " but got " + orderOwner.getAddress().getAreaName());
        }
        if (!testUser.getAddress().getAreaId().equalsIgnoreCase(orderOwner.getAddress().getAreaId())) {
            validationErrors.add("Area ID in Address mismatch: expected " + testUser.getAddress().getAreaId() +
                    " but got " + orderOwner.getAddress().getAreaId());
        }
        if (!testUser.getAddress().getFullAddress().equalsIgnoreCase(orderOwner.getAddress().getFullAddress())) {
            validationErrors.add("Full Address mismatch: expected " + testUser.getAddress().getFullAddress() +
                    " but got " + orderOwner.getAddress().getFullAddress());
        }
        if (!validationErrors.isEmpty()) {
            logger.info("Create order api field validation errors found :");
            validationErrors.forEach(logger::info);
            String errorMessage = "Test terminated: Create Order API has field validation errors in User Details\n" +
                    "Validation errors:\n" +
                    validationErrors.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("\n"));
            Assert.fail(errorMessage);
        }
    }

    public void assertPaymentDetailsInResponse(String paymentMethod, String paymentTitle, double balanceUsed, Order testOrder) {

        List<String> validationErrors = new ArrayList<>();

        //rounding balance to the nearest two decimal points
        balanceUsed = Math.round(balanceUsed * 100.0) / 100.0;

        //Validate Payment Method
        if (!testOrder.getPaymentMethod().equalsIgnoreCase(paymentMethod)) {
            validationErrors.add("Payment Method mismatch: expected " + paymentMethod +
                    " but got " + testOrder.getPaymentMethod());
        }
        //Validate Payment Title
        if (!testOrder.getPaymentTitle().equalsIgnoreCase(paymentTitle)) {
            validationErrors.add("Payment Title mismatch: expected " + paymentTitle +
                    " but got " + testOrder.getPaymentTitle());
        }
        //TODO: REPORT ON JIRA
//        //Commented due to an issue in integration environment
//        //Validate Capture Method
//        if (!testOrder.getCaptureMethod().equalsIgnoreCase("MANUAL")) {
//            validationErrors.add("Capture Method mismatch: expected MANUAL" +
//                    " but got " + testOrder.getCaptureMethod());
//        }

        //balance used
        if (testOrder.getBalanceUsedInOrder() != balanceUsed) {
            validationErrors.add("Balance Used In Order mismatch: expected" + balanceUsed +
                    " but got " + testOrder.getBalanceUsedInOrder());
        }
        if (!validationErrors.isEmpty()) {
            logger.info("Create order api field validation errors found :");
            validationErrors.forEach(logger::info);
            String errorMessage = "Test terminated: Create Order API has field validation errors in Payment Related Keys\n" +
                    "Validation errors:\n" +
                    validationErrors.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("\n"));
            Assert.fail(errorMessage);
        }
    }

    public void assertWarehouseTimeslotDeliveryDateAndScheduleKeysInResponse(Order testOrder, Warehouse testWarehouse
            , boolean isSchedule, boolean isScheduleExpress, String futureTimestamp, String currentDate) {

        List<String> validationErrors = new ArrayList<>();

        LocalDateTime currentDateTime = LocalDateTime.now(ZoneId.of("Africa/Cairo"));
        LocalTime currentTime = currentDateTime.toLocalTime();
        LocalDateTime elevenPMToday = LocalDateTime.now(ZoneId.of("Africa/Cairo"))
                .withHour(23)
                .withMinute(0)
                .withSecond(0);
        LocalDateTime elevenPMYesterday = LocalDateTime.now(ZoneId.of("Africa/Cairo"))
                .minusDays(1)  // Go back one day
                .withHour(23)
                .withMinute(0)
                .withSecond(0);
        LocalDateTime sixAMToday = LocalDateTime.now(ZoneId.of("Africa/Cairo"))
                .withHour(6)
                .withMinute(0)
                .withSecond(0);
        LocalDateTime twelveAMTomorrow = LocalDateTime.now(ZoneId.of("Africa/Cairo"))
                .plusDays(1)
                .withHour(0)
                .withMinute(0)
                .withSecond(0);
        LocalDateTime ninePMToday = LocalDateTime.now(ZoneId.of("Africa/Cairo"))
                .withHour(21)
                .withMinute(0)
                .withSecond(0);
        final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("hh:mm a");
        LocalTime expectedTime = currentTime.plusHours(1);
        LocalTime minAllowed = expectedTime.minusMinutes(2);
        LocalTime maxAllowed = expectedTime.plusMinutes(2);

        // Handle Cases order is scheduled
        if (isSchedule) {
            //Validate isSchedule Flag & handle cases the order is scheduled
            if (testOrder.isScheduled() != isSchedule) {
                validationErrors.add("IsSchedule Flag mismatch: expected " + isSchedule +
                        " but got " + testOrder.isScheduled());

                //Validate isTimeSlotShifted in case of a scheduled order
                if (testOrder.getIsOrderTimeSlotShifted() != isSchedule) {
                    validationErrors.add("IsTimeSlotShifted Flag mismatch: expected " + isSchedule +
                            " but got " + testOrder.getIsOrderTimeSlotShifted());
                }

                //Validate promise time  & timeslot in case of a scheduled order
                if (!testOrder.getPromisedTime().equalsIgnoreCase(futureTimestamp)) {
                    validationErrors.add("Promise Time mismatch: expected " + futureTimestamp +
                            " but got " + testOrder.getPromisedTime());
                }
                if (!testOrder.getTimeSlot().equalsIgnoreCase(futureTimestamp)) {
                    validationErrors.add("Timeslot mismatch: expected " + futureTimestamp +
                            " but got " + testOrder.getPromisedTime());
                }

                //Validate Schedule Express in case of a scheduled order
                if (testOrder.getIsScheduledExpress() != isScheduleExpress) {
                    validationErrors.add("Schedule Express Flag mismatch: expected " + isScheduleExpress +
                            " but got " + testOrder.getIsScheduledExpress());

                    //Validate promise time in case of schedule express
                    if (!testOrder.getPromisedTime().equalsIgnoreCase(testWarehouse.getTimeSlot() + " Scheduled Express")) {
                        validationErrors.add("Promise Time mismatch: expected " + testWarehouse.getTimeSlot() + " Scheduled Express" +
                                " but got " + testOrder.getPromisedTime());
                    }
                }
            }
        } else {
            //Validate Promise Time in Case of a non-scheduled order
            LocalTime actualTime = LocalTime.parse(testOrder.getPromisedTime()
                    .replace("Arriving by ", "")
                    .split(" - ")[0].trim(), TIME_FORMATTER);
            //Handle PM & AM case
            if (currentDateTime.isAfter(elevenPMYesterday) && currentDateTime.isBefore(sixAMToday) ||
                    currentDateTime.isAfter(elevenPMToday) ||
                    testWarehouse.getTimeSlot().equals("12:00 AM - 01:00 AM")) {
                if (!(actualTime.isAfter(minAllowed) && actualTime.isBefore(maxAllowed.plusMinutes(1)))) {
                    validationErrors.add("Promise Time mismatch: expected " + "Arriving by " + expectedTime.format(TIME_FORMATTER) + " AM" +
                            " but got " + testOrder.getPromisedTime());
                }
            } else {
                if (!(actualTime.isAfter(minAllowed) && actualTime.isBefore(maxAllowed.plusMinutes(1)))) {
                    validationErrors.add("Promise Time mismatch: expected " + "Arriving by " + expectedTime.format(TIME_FORMATTER) + " AM" +
                            " but got " + testOrder.getPromisedTime());
                }
            }
            //Excluded as it returns null in integration environment case order is not schedule
            //Validate Timeslot in case of a non-scheduled order
//            if(!testOrder.getTimeSlot().equalsIgnoreCase(testWarehouse.getTimeSlot())){
//                validationErrors.add("Timeslot mismatch: expected " + testWarehouse.getTimeSlot() +
//                        " but got " + testOrder.getWarehouse().getTimeSlot());
//            }
        }

        //Validate delivery_date
        if (currentDateTime.isAfter(elevenPMToday) && currentDateTime.isBefore(twelveAMTomorrow) ||
                (isSchedule && currentDateTime.isAfter(ninePMToday))) {
            //If it's past 11 PM, or in a shifted/scheduled time slot in extended hours delivery date should be next day
            LocalDate expectedDeliveryDate = LocalDate.parse(currentDateTime.plusDays(1).toLocalDate().format(DateTimeFormatter.ofPattern("d MMMM, yyyy")));
            if (!testOrder.getDeliveryDate().equalsIgnoreCase(expectedDeliveryDate.toString())) {
                validationErrors.add("Delivery Date mismatch: expected " + expectedDeliveryDate +
                        " but got " + testOrder.getDeliveryDate());
            }
        } else {
            //else then order is before 11 PM, delivery date should be current date
            if (!testOrder.getDeliveryDate().equalsIgnoreCase(currentDate)) {
                validationErrors.add("Delivery Date mismatch: expected " + currentDate +
                        " but got " + testOrder.getDeliveryDate());
            }
        }

        //Validate Warehouse Object
        if (!testOrder.getWarehouse().getId().equalsIgnoreCase(testWarehouse.getId())) {
            validationErrors.add("Warehouse ID mismatch: expected " + testWarehouse.getId() +
                    " but got " + testOrder.getWarehouse().getId());
        }
        if (!testOrder.getWarehouse().getName().equalsIgnoreCase(testWarehouse.getName())) {
            validationErrors.add("Warehouse Name mismatch: expected " + testWarehouse.getName() +
                    " but got " + testOrder.getWarehouse().getName());
        }

        if (!validationErrors.isEmpty()) {
            logger.info("Create order api field validation errors found :");
            validationErrors.forEach(logger::info);
            String errorMessage = "Test terminated: Create Order API has field validation errors in Warehouse , Delivery Date & Schedule Related Keys\n" +
                    "Validation errors:\n" +
                    validationErrors.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("\n"));
            Assert.fail(errorMessage);
        }
    }

    public void assertGratuityObjectInResponse(Order testOrder, String gratuityAmount, String gratuityFundsUsed) {

        List<String> validationErrors = new ArrayList<>();

        //Assert gratuity Amount
        if (!testOrder.getGratuityAmount().equalsIgnoreCase(gratuityAmount)) {
            validationErrors.add("Gratuity Amount mismatch: expected " + gratuityAmount +
                    " but got " + testOrder.getGratuityAmount());
        }

        //Assert gratuity funds used
        if (!testOrder.getGratuityFundsUsed().equalsIgnoreCase(gratuityFundsUsed)) {
            validationErrors.add("Gratuity Funds Used mismatch: expected " + gratuityFundsUsed +
                    " but got " + testOrder.getGratuityFundsUsed());
        }

        if (!validationErrors.isEmpty()) {
            logger.info("Create order api field validation errors found :");
            validationErrors.forEach(logger::info);
            String errorMessage = "Test terminated: Create Order API has field validation errors in Gratuity Related Keys\n" +
                    "Validation errors:\n" +
                    validationErrors.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("\n"));
            Assert.fail(errorMessage);
        }
    }

    public void assertCouponObjectInResponse(String orderCouponName, Order testOrder) {
        if (!testOrder.getOrderCouponName().equalsIgnoreCase(orderCouponName)) {
            Assert.fail("Terminating The Test , Create Order API has field validation errors in Coupon Name" +"expected " +
                    orderCouponName + " but got " + testOrder.getOrderCouponName());
        }
    }

    public void assertOrderTypeAndStatusInResponse(Order testOrder, boolean isNow, String orderType, String orderStatus
            , boolean isCancellable) {

        List<String> validationErrors = new ArrayList<>();

        //Validate Now Key
        if (testOrder.isNow() != isNow) {
            validationErrors.add("Now Key mismatch: expected " + isNow +
                    " but got " + testOrder.isNow());
        }

        //Validate Order Type
        if (!testOrder.getNowTomorrowType().equalsIgnoreCase(orderType)) {
            validationErrors.add("Order Type mismatch: expected " + orderType +
                    " but got " + testOrder.getNowTomorrowType());
        }

        //Validate order status
        if (!testOrder.getStatus().equalsIgnoreCase(orderStatus)) {
            validationErrors.add("Order Status mismatch: expected " + orderStatus +
                    " but got " + testOrder.getStatus());
        }

        //cancellable
        if (testOrder.isCancellable() != isCancellable) {
            validationErrors.add("Order Cancellable Key mismatch: expected " + isCancellable +
                    " but got " + testOrder.isCancellable());
        }

        if (!validationErrors.isEmpty()) {
            logger.info("Create order api field validation errors found :");
            validationErrors.forEach(logger::info);
            String errorMessage = "Test terminated: Create Order API has field validation errors in Order Type & Status Keys\n" +
                    "Validation errors:\n" +
                    validationErrors.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("\n"));
            Assert.fail(errorMessage);
        }
    }

    public void assertGiftReceiptKeyInResponse(Order testOrder, boolean isGiftReceipt) {
        //Asser Gift Receipt Value
        if (testOrder.isGiftReceipt() != isGiftReceipt) {
            Assert.fail("Terminating The Test , Create Order API has field validation errors in Gift Receipt" + "expected" +
                    isGiftReceipt + " but got " + testOrder.isGiftReceipt() );
        }
    }

    public void assertFeesObjectAndDeliveryFeesInResponse(Warehouse testWarehouse, Order testOrder ,boolean hasCoupon ,Coupon testCoupon) {

        List<String> validationErrors = new ArrayList<>();
        double expectedDeliveryFees ;

        if (hasCoupon && testCoupon.getType().equalsIgnoreCase("free_delivery")) {
            expectedDeliveryFees = 0.0;
        }
        else  expectedDeliveryFees= testWarehouse.getDeliveryFees();

        //Validate fees
        if ((expectedDeliveryFees != testOrder.getDeliveryFeesInFeesObject())) {
            validationErrors.add("Delivery Fees in Fees Object mismatch: expected " + expectedDeliveryFees +
                    " but got " + testOrder.getDeliveryFeesInFeesObject());
        }

        //Validate delivery_fees
        if (expectedDeliveryFees != testOrder.getDeliveryFees()){
            validationErrors.add("Delivery Fees mismatch: expected " + expectedDeliveryFees +
                    " but got " + testOrder.getDeliveryFees());
        }

        //Validate service fees
        if(testOrder.getServiceFeesInFeesObject() != 0){
            if(testOrder.getServiceFeesInFeesObject() != 3){
                validationErrors.add("Service Fees mismatch: expected "+ 3 +" but got " +testOrder.getServiceFeesInFeesObject());
            }
        }
        if (!validationErrors.isEmpty()) {
            logger.info("Create order api field validation errors found :");
            validationErrors.forEach(logger::info);
            String errorMessage = "Test terminated: Create Order API has field validation errors in Fees Related Keys\n" +
                    "Validation errors:\n" +
                    validationErrors.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("\n"));
            Assert.fail(errorMessage);
        }
    }

    public void assertOrderTotalAndDiscountsInResponse(Order testOrder, List<Product> testProducts,
                                                       Warehouse testWarehouse, boolean hasCoupon, Coupon testCoupon,
                                                       Product freeGiftProduct, double dueAmount) {

        List<String> validationErrors = new ArrayList<>();
        // Initialize expected values
        double expectedOrderSubtotal = 0.0;
        double expectedOrderTotal = 0.0;
        double expectedOrderDiscount = 0.0;

        //Handle Case Due amount
        if (testOrder.getOrderProducts().stream().anyMatch(product -> product.getMysqlId() == 10020)) {
            expectedOrderSubtotal += dueAmount;
            expectedOrderTotal += dueAmount;
        }

        //Handle Service fees
        if(testOrder.getServiceFees() != 0){
            expectedOrderTotal+=3;
        }

        // Handle delivery fees with NaN check
        double deliveryFees = Double.isNaN(testWarehouse.getDeliveryFees()) ? 0.0 : testWarehouse.getDeliveryFees();

        expectedOrderSubtotal += testProducts.get(0).getPrice() + testProducts.get(1).getPrice();

        // Calculate base order total and discounts for first two products
        for (int i = 0; i < Math.min(2, testProducts.size()); i++) {
            Product testProduct = testProducts.get(i);

            boolean hasNoAutomatedDiscounts = testProduct.getAutomatedDiscounts() == null ||
                    testProduct.getAutomatedDiscounts().isEmpty() ||
                    testProduct.getAutomatedDiscounts().stream()
                            .noneMatch(d -> d.getFpId().equals(testWarehouse.getId()));

            // Determine product price and discount based on price type
            if (testProduct.getSalePrice() == 0 &&
                    testProduct.getExtraSalesPrice().isEmpty() &&
                    hasNoAutomatedDiscounts) {
                // Regular price
                expectedOrderTotal += testProduct.getPrice();
            } else if (testProduct.getSalePrice() != 0 &&
                    testProduct.getExtraSalesPrice().isEmpty() &&
                    hasNoAutomatedDiscounts) {
                // Sale price
                expectedOrderTotal += testProduct.getSalePrice();
                expectedOrderDiscount += testProduct.getPrice() - testProduct.getSalePrice();
            } else {
                // Extra sale price
                double extraSalePrice = Float.parseFloat(testProduct.getExtraSalesPrice());
                expectedOrderTotal += extraSalePrice;
                expectedOrderDiscount += testProduct.getPrice() - extraSalePrice;
            }
        }

        // Add delivery fees to order total
        expectedOrderTotal += deliveryFees;

        // Apply coupon discounts if present
        if (hasCoupon) {
            switch (testCoupon.getType().toLowerCase()) {
                case "free_delivery":
                    expectedOrderTotal -= deliveryFees;
                    break;

                case "free_gift":
                    boolean hasNoAutomatedDiscounts = freeGiftProduct.getAutomatedDiscounts() == null ||
                            freeGiftProduct.getAutomatedDiscounts().isEmpty() ||
                            freeGiftProduct.getAutomatedDiscounts().stream()
                                    .noneMatch(d -> d.getFpId().equals(testWarehouse.getId()));

                    // Calculate free gift discount based on its price type
                    //Update order Subtotal to include price of the free gift
                    if (freeGiftProduct.getSalePrice() == 0 &&
                            freeGiftProduct.getExtraSalesPrice().isEmpty() &&
                            hasNoAutomatedDiscounts) {
                        expectedOrderDiscount += freeGiftProduct.getPrice();
                        expectedOrderSubtotal += freeGiftProduct.getPrice();
                    } else if (freeGiftProduct.getSalePrice() != 0 &&
                            freeGiftProduct.getExtraSalesPrice().isEmpty() &&
                            hasNoAutomatedDiscounts) {
                        expectedOrderDiscount += freeGiftProduct.getPrice() - freeGiftProduct.getSalePrice();
                        expectedOrderSubtotal += freeGiftProduct.getSalePrice();
                    } else {
                        double extraSalePrice = Float.parseFloat(freeGiftProduct.getExtraSalesPrice());
                        expectedOrderDiscount += freeGiftProduct.getPrice() - extraSalePrice;
                        expectedOrderSubtotal += extraSalePrice;
                    }
                    break;

                case "fixed_cart":
                    double fixedDiscount = Float.parseFloat(testCoupon.getAmount());
                    expectedOrderDiscount += fixedDiscount;
                    expectedOrderTotal -= fixedDiscount;
                    break;

                case "percent":
                    double percentDiscount = (expectedOrderTotal - deliveryFees) *
                            (testCoupon.getPercentValue() / 100.0);
                    expectedOrderDiscount += percentDiscount;
                    expectedOrderTotal -= percentDiscount;
                    break;
            }
        }

        if (!String.format("%.2f", testOrder.getSubtotal()).equals(String.format("%.2f", expectedOrderSubtotal))) {
            validationErrors.add(String.format("Order subtotal mismatch: expected %.2f but got %.2f",
                    expectedOrderSubtotal, testOrder.getSubtotal()));
        }

        if (!String.format("%.2f", testOrder.getTotal()).equals(String.format("%.2f", expectedOrderTotal))) {
            validationErrors.add(String.format("Order total mismatch: expected %.2f but got %.2f",
                    expectedOrderTotal, testOrder.getTotal()));
        }

        if (!String.format("%.2f", testOrder.getDiscount()).equals(String.format("%.2f", expectedOrderDiscount))) {
            validationErrors.add(String.format("Order discount mismatch: expected %.2f but got %.2f",
                    expectedOrderDiscount, testOrder.getDiscount()));
        }
        if (!String.format("%.2f", Math.abs(testOrder.getDiscountFeesInFeesObject()))
                .equals(String.format("%.2f", Math.abs(expectedOrderDiscount)))) {
            validationErrors.add(String.format("Order discount in Fees Object mismatch: expected %.2f but got %.2f",
                    expectedOrderDiscount, testOrder.getDiscountFeesInFeesObject()));
        }

        // Report all validation errors if any found
        if (!validationErrors.isEmpty()) {
            logger.info("Order validation errors found:");
            validationErrors.forEach(logger::info);
            String errorMessage = "Test terminated: Order total, subtotal or discount validation failed\n" +
                    "Validation errors:\n" +
                    validationErrors.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("\n"));
            Assert.fail(errorMessage);
        }
    }

    public void assertOrderTotalAndDiscountsInResponse(Order testOrder, Product testProduct,
                                                       Warehouse testWarehouse, boolean hasCoupon, Coupon testCoupon,
                                                       Product freeGiftProduct, double dueAmount) {

        List<String> validationErrors = new ArrayList<>();
        // Initialize expected values
        double expectedOrderSubtotal = 0.0;
        double expectedOrderTotal = 0.0;
        double expectedOrderDiscount = 0.0;

        //Handle Case Due amount
        if (testOrder.getOrderProducts().stream().anyMatch(product -> product.getMysqlId() == 10020)) {
            expectedOrderSubtotal += dueAmount;
            expectedOrderTotal += dueAmount;
        }

        //Handle Service fees
        if(testOrder.getServiceFees() != 0){
            expectedOrderTotal+=3;
        }

        // Handle delivery fees with NaN check
        double deliveryFees = Double.isNaN(testWarehouse.getDeliveryFees()) ? 0.0 : testWarehouse.getDeliveryFees();

        expectedOrderSubtotal += testProduct.getPrice() ;

        // Calculate base order total and discounts for first two products

        boolean hasNoAutomatedDiscounts = testProduct.getAutomatedDiscounts() == null ||
                testProduct.getAutomatedDiscounts().isEmpty() ||
                testProduct.getAutomatedDiscounts().stream()
                        .noneMatch(d -> d.getFpId().equals(testWarehouse.getId()));

        // Determine product price and discount based on price type
        if (testProduct.getSalePrice() == 0 &&
                testProduct.getExtraSalesPrice().isEmpty() &&
                hasNoAutomatedDiscounts) {
            // Regular price
            expectedOrderTotal += testProduct.getPrice();
        } else if (testProduct.getSalePrice() != 0 &&
                testProduct.getExtraSalesPrice().isEmpty() &&
                hasNoAutomatedDiscounts) {
            // Sale price
            expectedOrderTotal += testProduct.getSalePrice();
            expectedOrderDiscount += testProduct.getPrice() - testProduct.getSalePrice();
        } else {
            // Extra sale price
            double extraSalePrice = Float.parseFloat(testProduct.getExtraSalesPrice());
            expectedOrderTotal += extraSalePrice;
            expectedOrderDiscount += testProduct.getPrice() - extraSalePrice;
        }

        // Add delivery fees to order total
        expectedOrderTotal += deliveryFees;

        // Apply coupon discounts if present
        if (hasCoupon) {
            switch (testCoupon.getType().toLowerCase()) {
                case "free delivery":
                    expectedOrderTotal -= deliveryFees;
                    break;

                case "free_gift":
                    // Calculate free gift discount based on its price type
                    //Update order Subtotal to include price of the free gift
                    if (freeGiftProduct.getSalePrice() == 0 &&
                            freeGiftProduct.getExtraSalesPrice().isEmpty() &&
                            hasNoAutomatedDiscounts) {
                        expectedOrderDiscount += freeGiftProduct.getPrice();
                        expectedOrderSubtotal += freeGiftProduct.getPrice();
                    } else if (freeGiftProduct.getSalePrice() != 0 &&
                            freeGiftProduct.getExtraSalesPrice().isEmpty() &&
                            hasNoAutomatedDiscounts) {
                        expectedOrderDiscount += freeGiftProduct.getPrice() - freeGiftProduct.getSalePrice();
                        expectedOrderSubtotal += freeGiftProduct.getSalePrice();
                    } else {
                        double extraSalePrice = Float.parseFloat(freeGiftProduct.getExtraSalesPrice());
                        expectedOrderDiscount += freeGiftProduct.getPrice() - extraSalePrice;
                        expectedOrderSubtotal += extraSalePrice;
                    }
                    break;

                case "fixed_cart":
                    double fixedDiscount = Float.parseFloat(testCoupon.getAmount());
                    expectedOrderDiscount += fixedDiscount;
                    expectedOrderTotal -= fixedDiscount;
                    break;

                case "percent":
                    double percentDiscount = (expectedOrderTotal - deliveryFees) *
                            (testCoupon.getPercentValue() / 100.0);
                    expectedOrderDiscount += percentDiscount;
                    expectedOrderTotal -= percentDiscount;
                    break;
            }
        }

        // Validate all calculated values against actual order
        if (!String.format("%.2f", testOrder.getSubtotal()).equals(String.format("%.2f", expectedOrderSubtotal))) {
            validationErrors.add(String.format("Order subtotal mismatch: expected %.2f but got %.2f",
                    expectedOrderSubtotal, testOrder.getSubtotal()));
        }

        if (!String.format("%.2f", testOrder.getTotal()).equals(String.format("%.2f", expectedOrderTotal))) {
            validationErrors.add(String.format("Order total mismatch: expected %.2f but got %.2f",
                    expectedOrderTotal, testOrder.getTotal()));
        }

        if (!String.format("%.2f", testOrder.getDiscount()).equals(String.format("%.2f", expectedOrderDiscount))) {
            validationErrors.add(String.format("Order discount mismatch: expected %.2f but got %.2f",
                    expectedOrderDiscount, testOrder.getDiscount()));
        }
        if (!String.format("%.2f", Math.abs(testOrder.getDiscountFeesInFeesObject()))
                .equals(String.format("%.2f", Math.abs(expectedOrderDiscount)))) {
            validationErrors.add(String.format("Order discount in Fees Object mismatch: expected %.2f but got %.2f",
                    expectedOrderDiscount, testOrder.getDiscountFeesInFeesObject()));
        }

        // Report all validation errors if any found
        if (!validationErrors.isEmpty()) {
            logger.info("Order validation errors found:");
            validationErrors.forEach(logger::info);
            String errorMessage = "Test terminated: Order total, subtotal or discount validation failed\n" +
                    "Validation errors:\n" +
                    validationErrors.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining("\n"));
            Assert.fail(errorMessage);
        }
    }
}
