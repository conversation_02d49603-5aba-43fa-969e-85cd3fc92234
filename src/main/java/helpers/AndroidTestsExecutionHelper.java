package helpers;

import io.appium.java_client.AppiumBy;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.geolocation.AndroidGeoLocation;
import io.appium.java_client.android.nativekey.AndroidKey;
import io.appium.java_client.android.nativekey.KeyEvent;
import modals.customerApp.android.*;
import modals.customerApp.android.androidHomePage.AndroidAddressSelectionScreen;
import modals.customerApp.android.androidHomePage.AndroidHomeScreen;
import modals.customerApp.android.androidMoreScreen.AndroidChooseCountryModal;
import modals.customerApp.android.androidMoreScreen.AndroidChooseLanguageModal;
import modals.customerApp.android.androidMoreScreen.AndroidMoreScreen;
import modals.customerApp.android.androidPermissionAlerts.AndroidLocationPermissionAlert;
import models.Category;
import models.Product;
import models.TestData;
import models.ValidationResults;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.Point;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AndroidTestsExecutionHelper {

    private static final Logger logger = LoggerFactory.getLogger(AndroidTestsExecutionHelper.class);
    private Map<String, String> selectorTypes = Map.of(
            "id=", "id",
            "xpath=", "xpath",
            "content-desc=", "description",
            "description", "description",
            "accessibility-id=", "accessibility-id"
    );

    public void register(AndroidCountriesSelectionScreen androidCountriesSelectionScreen,
                         AndroidLandingScreen androidLandingScreen,
                         AndroidPhoneNumberScreen androidPhoneNumberScreen,
                         AndroidCountriesListScreen androidCountriesListScreen,
                         AndroidOTPVerificationScreen androidOTPVerificationScreen,
                         TestExecutionHelper testExecutionHelper, TestData testData,
                         AndroidCreateAccountScreen androidCreateAccountScreen,
                         AndroidRegisterSuccessScreen androidRegisterSuccessScreen, AndroidHomeScreen androidHomeScreen,
                         String phoneCountry) {
        //Choose a country from countries selection dropdown
        androidCountriesSelectionScreen.chooseCountryAndSubmit(testData.getTestCountryCode());

        //Press the Login or signup button
        androidLandingScreen.pressAuthHyperLink();

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidPhoneNumberScreen, androidOTPVerificationScreen,
                        "register", phoneCountry, "customer");
            }
            default -> {
                changeCountry(androidPhoneNumberScreen, androidCountriesListScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidPhoneNumberScreen, androidOTPVerificationScreen,
                        "register", phoneCountry, "customer");
            }
        }

        //Enter Account Information
        androidCreateAccountScreen.fillInAccountInformationForm(testData);
        androidCreateAccountScreen.pressSubmitBtn();

        //Proceed from the success screen
        if (androidRegisterSuccessScreen.isConfirmationMessageDisplayed())
            androidRegisterSuccessScreen.pressProceedBtn();

        //Validate that home screen is displayed
        androidHomeScreen.isHomePageDisplayed();
    }

    public void registerUserWithReferralCode(AndroidCountriesSelectionScreen androidCountriesSelectionScreen,
                                             AndroidLandingScreen androidLandingScreen,
                                             AndroidPhoneNumberScreen androidPhoneNumberScreen,
                                             AndroidCountriesListScreen androidCountriesListScreen,
                                             AndroidOTPVerificationScreen androidOTPVerificationScreen,
                                             TestExecutionHelper testExecutionHelper, TestData testData,
                                             AndroidCreateAccountScreen androidCreateAccountScreen,
                                             AndroidRegisterSuccessScreen androidRegisterSuccessScreen,
                                             AndroidHomeScreen androidHomeScreen,
                                             String phoneCountry, String referralCode) {

        //Choose a country from countries selection dropdown
        androidCountriesSelectionScreen.chooseCountryAndSubmit(testData.getTestCountryCode());

        //Press the Login or signup button
        androidLandingScreen.pressAuthHyperLink();

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidPhoneNumberScreen, androidOTPVerificationScreen,
                        "register", phoneCountry, "customer");
            }
            default -> {
                changeCountry(androidPhoneNumberScreen, androidCountriesListScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidPhoneNumberScreen, androidOTPVerificationScreen,
                        "register", phoneCountry, "customer");
            }
        }

        //Enter Account Information
        androidCreateAccountScreen.fillInAccountInformationFormWithAdminReferralCode(testData, referralCode);
        androidCreateAccountScreen.pressSubmitBtn();

        //Proceed from the success screen
        if (androidRegisterSuccessScreen.isConfirmationMessageDisplayed())
            androidRegisterSuccessScreen.pressProceedBtn();

        //Validate that home screen is displayed
        androidHomeScreen.isHomePageDisplayed();
    }

    public void fillInAccountRegistrationFormWithReferralCode(AndroidCountriesSelectionScreen androidCountriesSelectionScreen,
                                                              AndroidLandingScreen androidLandingScreen,
                                                              AndroidPhoneNumberScreen androidPhoneNumberScreen,
                                                              AndroidCountriesListScreen androidCountriesListScreen,
                                                              AndroidOTPVerificationScreen androidOTPVerificationScreen,
                                                              TestExecutionHelper testExecutionHelper, TestData testData,
                                                              AndroidCreateAccountScreen androidCreateAccountScreen,
                                                              String phoneCountry, String referralCode) {

        //Choose a country from countries selection dropdown
        androidCountriesSelectionScreen.chooseCountryAndSubmit(testData.getTestCountryCode());

        //Press the Login or signup button
        androidLandingScreen.pressAuthHyperLink();

        //TODO:Consider Wrapping the switch case in enterPhoneNumberAndOTP ?
        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidPhoneNumberScreen, androidOTPVerificationScreen,
                        "register", phoneCountry, "customer");
            }
            default -> {
                changeCountry(androidPhoneNumberScreen, androidCountriesListScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidPhoneNumberScreen, androidOTPVerificationScreen,
                        "register", phoneCountry, "customer");
            }
        }

        //Enter Account Information with referral code
        androidCreateAccountScreen.fillInAccountInformationFormWithAdminReferralCode(testData, referralCode);
    }

    public void login(TestData testData
            , TestExecutionHelper testExecutionHelper
            , AndroidPhoneNumberScreen androidPhoneNumberScreen
            , AndroidCountriesListScreen androidCountriesListScreen
            , AndroidOTPVerificationScreen androidOTPVerificationScreen
            , AndroidSetAddressScreen androidSetAddressScreen
            , String phoneCountry){

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidPhoneNumberScreen,
                        androidOTPVerificationScreen,
                        "login", phoneCountry, "customer");
            }
            default -> {
                changeCountry(androidPhoneNumberScreen, androidCountriesListScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidPhoneNumberScreen,
                        androidOTPVerificationScreen,
                        "login", phoneCountry, "customer");
            }
        }

        androidSetAddressScreen.goToCurrentLocationAndConfirmIfDisplayed();
    }

    private void enterPhoneNumberAndOTP(TestData testData, TestExecutionHelper testExecutionHelper,
                                        AndroidPhoneNumberScreen androidPhoneNumberScreen,
                                        AndroidOTPVerificationScreen androidOTPVerificationScreen,
                                        String method, String phoneCountry, String targetUserToUse){
        if (targetUserToUse.equalsIgnoreCase("customer")){
            //Enter phone number
            switch (phoneCountry.toUpperCase()) {
                case "EG", "KSA" -> {
                    if (androidPhoneNumberScreen.isPageHeaderDisplayed())
                        androidPhoneNumberScreen.enterPhoneNumber(testData.getRandomTestUser().getLocalPhoneNumber());
                }
                default -> {
                    if (androidPhoneNumberScreen.isPageHeaderDisplayed())
                        androidPhoneNumberScreen.enterPhoneNumber(
                                testData.getRandomTestUser().getForeignLocalPhoneNumber());
                }
            }
            androidPhoneNumberScreen.pressNextBtn();

            //Fetch and Enter the OTP
            switch (phoneCountry.toUpperCase()) {
                case "EG", "KSA" -> {
                    if (androidOTPVerificationScreen.isPageHeaderDisplayed()
                            && androidOTPVerificationScreen.isPageSubHeaderDisplayed()
                            && androidOTPVerificationScreen.isPhoneNumberDisplayed(
                                    testData.getRandomTestUser().getPhoneNumber())) {
                        while (androidOTPVerificationScreen.isPageHeaderDisplayed()){
                            try {
                                testData.setRandomTestUser(testExecutionHelper.otpFactory
                                        .fetchOtp(testData, method, testData.getRandomTestUser()));
                                if (androidOTPVerificationScreen.isPageHeaderDisplayed())
                                    androidOTPVerificationScreen.enterOTP(testData.getRandomTestUser().getOtp());
                            } catch (Exception e){
                                break;
                            }
                        }
                    }
                }
                default -> {
                    if (androidOTPVerificationScreen.isPageHeaderDisplayed()
                            && androidOTPVerificationScreen.isPageSubHeaderDisplayed()
                            && androidOTPVerificationScreen
                            .isPhoneNumberDisplayed(testData.getRandomTestUser().getForeignPhoneNumber())) {
                        while (androidOTPVerificationScreen.isPageHeaderDisplayed()){
                            try {
                                testData.setRandomTestUser(testExecutionHelper.otpFactory
                                        .fetchOtp(testData, method, testData.getRandomTestUser()));
                                if (androidOTPVerificationScreen.isPageHeaderDisplayed())
                                    androidOTPVerificationScreen.enterOTP(testData.getRandomTestUser().getOtp());
                            } catch (Exception e){
                                break;
                            }
                        }
                    }
                }
            }
        }

    }

    public void changeCountry(AndroidPhoneNumberScreen androidPhoneNumberScreen,
                              AndroidCountriesListScreen androidCountriesListScreen,
                              TestData testData){
        androidPhoneNumberScreen.pressCountryCodeBtn(testData.getRandomTestUser().getPhoneCountry());
        if (androidCountriesListScreen.isCountriesListScreenDisplayed()){
            androidCountriesListScreen.searchForCountry(testData.getRandomTestUser().getForeignPhoneCountryCode());
            androidCountriesListScreen.selectCountry(testData.getRandomTestUser().getForeignPhoneCountry());
        }
    }

    public void logout(AndroidDriver androidDriver,TestData testData, AndroidHomeScreen androidHomeScreen
            , AndroidMoreScreen androidMoreScreen){
        //Go to more Tab
        if (androidHomeScreen.isHomePageDisplayed())
            androidHomeScreen.pressMoreTabBtn();

        //Dismiss the coach-marks

        androidMoreScreen.dismissCoachMarksIfDisplayed();

        scrollUntilACertainElementIsFound(androidDriver
                , androidMoreScreen.getScrollableContentContainer()
                , "down"
                , androidMoreScreen.getLogoutBtnContentDescription());

        //Logout
        if (androidMoreScreen.isFullNameDisplayed(testData.getRandomTestUser().getFirstName()
                , testData.getRandomTestUser().getLastName()))
            androidMoreScreen.pressLogoutBtn();
    }

    public void logoutSkipLocationAndAddressAlertsThenLogin(TestData testData,
                                                            TestExecutionHelper testExecutionHelper,
                                                            AndroidPhoneNumberScreen androidPhoneNumberScreen,
                                                            AndroidCountriesListScreen androidCountriesListScreen,
                                                            AndroidOTPVerificationScreen androidOTPVerificationScreen,
                                                            String phoneCountry,
                                                            AndroidDriver androidDriver,
                                                            AndroidHomeScreen androidHomeScreen,
                                                            AndroidMoreScreen androidMoreScreen,
                                                            AndroidLocationPermissionAlert androidLocationPermissionAlert,
                                                            AndroidSetAddressScreen androidSetAddressScreen) {

        //Logout a logged-in user
        logout(androidDriver, testData, androidHomeScreen, androidMoreScreen);

        //Location Permission - set to while using
        androidLocationPermissionAlert.takeActionIfAlertDisplayed("whileUsing");

        //Set address Screen -> Skip it
        androidSetAddressScreen.goToCurrentLocationAndConfirmIfDisplayed();

        //Ensure redirection to home screen > redirect to more >press on login button
        androidHomeScreen.pressHomeTabBtn();
        androidHomeScreen.pressMoreTabBtn();
        androidMoreScreen.pressLoginBtn();

        //Login
        login(testData, testExecutionHelper, androidPhoneNumberScreen, androidCountriesListScreen,
                androidOTPVerificationScreen, androidSetAddressScreen, phoneCountry);
    }

    public void setAndroidEmulatorLocation(double latitude, double longitude, AndroidDriver androidDriver){
        logger.info("Setting location to latitude {} and longitude {}", latitude, longitude);
        if (!androidDriver.isLocationServicesEnabled())
            androidDriver.toggleLocationServices();

        AndroidGeoLocation location = new AndroidGeoLocation(latitude, longitude);
        androidDriver.setLocation(location);
    }

    @Deprecated(forRemoval = true)
    private boolean scrollUntilACertainTextIsFound(AndroidDriver androidDriver, String targetText){
        try {
            return androidDriver.findElement(new AppiumBy.ByAndroidUIAutomator(
                    "new UiScrollable(new UiSelector().scrollable(true).instance(0))" +
                            ".scrollIntoView(new UiSelector().textMatches(\""+ targetText+"\").instance(0))"))
                    .isDisplayed();
        } catch (Exception e){
            return false;
        }
    }

    public void enterAsGuest(AndroidCountriesSelectionScreen androidCountriesSelectionScreen,
                             AndroidLandingScreen androidLandingScreen,
                             AndroidLocationPermissionAlert androidLocationPermissionAlert,
                             AndroidSetAddressScreen androidSetAddressScreen,
                             AndroidHomeScreen androidHomeScreen, TestData testData){
        //Choose a country from countries selection dropdown
        androidCountriesSelectionScreen.chooseCountryAndSubmit(testData.getTestCountryCode());

        androidLandingScreen.pressExploreBtn();
        androidLocationPermissionAlert.takeActionIfAlertDisplayed("whileUsing");

        androidSetAddressScreen.goToCurrentLocationAndConfirmIfDisplayed();

        androidHomeScreen.isHomePageDisplayed();
    }

    public boolean isElementDisplayedBySelector(AndroidDriver androidDriver, String elementSelector, String elementSelectorType) {
        try {
            return getElementBySelector(androidDriver, elementSelector, elementSelectorType).isDisplayed();
        } catch (Exception e) {
            return false;
        }
    }

    public WebElement getElementBySelector(AndroidDriver androidDriver, String elementSelector, String elementSelectorType) {
        try {
            switch (elementSelectorType.toLowerCase()) {
                case "id":
                    return androidDriver.findElement(AppiumBy.id(elementSelector));
                case "xpath":
                    return androidDriver.findElement(AppiumBy.xpath(elementSelector));
                case "accessibility-id":
                    return androidDriver.findElement(AppiumBy.accessibilityId(elementSelector));
                default:
                    return androidDriver.findElement(new AppiumBy.ByAndroidUIAutomator("description(\""
                            + elementSelector + "\")"));
            }
        }
        catch (Exception e) {
            return null;
        }
    }

    private boolean scrollToDirection(AndroidDriver androidDriver, WebElement scrollView, String direction){
        if (scrollView == null)
            return false;
        HashMap<String, Object> scrollObject = new HashMap<>();
        String elementId = ((RemoteWebElement) scrollView).getId();
        scrollObject.put("elementId", elementId);
        scrollObject.put("direction", direction.toLowerCase());
        scrollObject.put("percent", 0.9);

        return (boolean) (((JavascriptExecutor) androidDriver)
                .executeScript("mobile: scrollGesture", scrollObject));
    }

    public boolean isElementPartiallyDisplayed(AndroidDriver androidDriver, WebElement element, String side
            , double marginPercentage) {
        if (androidDriver == null || element == null || side == null)
            return true;

        // Get the location and size of the element
        Point elementLocation = element.getLocation();
        Dimension elementSize = element.getSize();

        // Get the size of the screen
        Dimension screenSize = androidDriver.manage().window().getSize();
        int bottomLimit = (int) (screenSize.getHeight() - (screenSize.getHeight() * marginPercentage));
        int topLimit = (int) (screenSize.getHeight() * marginPercentage);
        int rightLimit = (int) (screenSize.getWidth() - (screenSize.getWidth() * marginPercentage));
        int leftLimit = 0;

        return switch (side.toLowerCase()) {
            case "up" -> elementLocation.getY() < topLimit;
            case "down" -> (elementLocation.getY() + elementSize.getHeight()) > bottomLimit;
            case "right" -> (elementLocation.getX() + elementSize.getWidth()) > rightLimit;
            case "left" -> elementLocation.getX() < leftLimit;
            default -> true;
        };
    }

    public boolean scrollUntilACertainElementIsFound(AndroidDriver androidDriver, WebElement scrollView
            , String direction, String elementSelector){
        String elementSelectorType = getElementSelectorType(elementSelector);
        elementSelector = getSubstringSelector(elementSelector);
        boolean isTargetElementDisplayed = isElementDisplayedBySelector(androidDriver, elementSelector, elementSelectorType);
        long timeout = Duration.ofMinutes(5).toMillis();
        double marginPercentage = 0.1; // 10%
        boolean elementAtEdge = false;

        if (isTargetElementDisplayed && !elementAtEdge){
            // Define your margin values
            elementAtEdge = (isElementPartiallyDisplayed(androidDriver
                    , getElementBySelector(androidDriver, elementSelector, elementSelectorType)
                    , direction
                    , marginPercentage));
            if (elementAtEdge)
                isTargetElementDisplayed = false;
        }

        long startTime = System.currentTimeMillis();
        while (!isTargetElementDisplayed && !(System.currentTimeMillis() - startTime > timeout)){
            scrollToDirection(androidDriver, scrollView, direction);
            isTargetElementDisplayed = isElementDisplayedBySelector(androidDriver, elementSelector, elementSelectorType);

            if (isTargetElementDisplayed && !elementAtEdge){
                // Define your margin values
                elementAtEdge = (isElementPartiallyDisplayed(androidDriver
                        , getElementBySelector(androidDriver, elementSelector, elementSelectorType)
                        , direction
                        , marginPercentage));
                if (elementAtEdge)
                    isTargetElementDisplayed = false;
            }
        }
        return isTargetElementDisplayed;
    }

    public void scrollToEnd(AndroidDriver driver){
        driver.findElement(
                AppiumBy.androidUIAutomator(
                        "new UiScrollable(new UiSelector().scrollable(true))" +
                                ".scrollToEnd(10)" // 10 is the max number of swipes
                )
        );
    }

    public void addNewAddressAsCurrentLocation(AndroidHomeScreen androidHomeScreen,
                                               AndroidAddressSelectionScreen androidAddressSelectionScreen,
                                               AndroidLocationPermissionAlert androidLocationPermissionAlert,
                                               AndroidSetAddressScreen androidSetAddressScreen,
                                               AndroidDriver androidDriver){
        androidHomeScreen.pressChangeAddressBtn();
        if (androidAddressSelectionScreen.isDropDownDisplayed()){
            if (!androidAddressSelectionScreen.isAddNewAddressBtnDisplayed()) {
                scrollUntilACertainElementIsFound(androidDriver,
                        androidAddressSelectionScreen.getContentScrollViewContainer(),
                        "down",
                        androidAddressSelectionScreen.getAddNewAddressBtnContentDescription());
            }
            androidAddressSelectionScreen.pressAddNewAddressBtn();
            searchByTextAndSelectASpecificLocation(androidDriver, androidLocationPermissionAlert, androidSetAddressScreen);
        }
    }

    public void searchByTextAndSelectASpecificLocation(AndroidDriver androidDriver
            , AndroidLocationPermissionAlert androidLocationPermissionAlert
            , AndroidSetAddressScreen androidSetAddressScreen){
        androidLocationPermissionAlert.takeActionIfAlertDisplayed("whileUsing");

        if (androidSetAddressScreen.isConfirmLocationBtnDisplayed()
                && androidSetAddressScreen.isCurrentLocationSetToUnitedStates()){
            androidSetAddressScreen.enterValueInTextFieldIfLocationIsSetToUS("7A street 20, Maadi");
            androidDriver.pressKey(new KeyEvent(AndroidKey.ENTER));
            androidSetAddressScreen.selectFirstAddressDisplayedInSearchResults();
            androidSetAddressScreen.pressConfirmLocationBtn();
            return;
        }
        androidSetAddressScreen.goToCurrentLocationAndConfirmIfDisplayed();
    }

    public void changeAppCountry(AndroidHomeScreen androidHomeScreen,
                              AndroidLandingScreen androidLandingScreen,
                              AndroidMoreScreen androidMoreScreen,
                              AndroidChooseCountryModal androidChooseCountryModal,
                              String countryCode) {
        //Navigate to home screen and assert navigation
        androidHomeScreen.pressHomeTabBtn();
        androidHomeScreen.isHomePageDisplayed();

        //Navigate to more tab and assert navigation
        androidHomeScreen.pressMoreTabBtn();
        androidMoreScreen.isPageDisplayed();

        //Navigate to country modal and assert navigation
        androidMoreScreen.pressCountryBtn();
        androidChooseCountryModal.isPageDisplayed();

        //Select country and assert navigation to home screen
        androidChooseCountryModal.selectCountry(countryCode);
        androidLandingScreen.isPageDisplayed();
    }

    public void changeAppLanguage(AndroidHomeScreen androidHomeScreen,
                                  AndroidMoreScreen androidMoreScreen,
                                  AndroidChooseLanguageModal androidChooseLanguageModal,
                                  String language) {
        //Navigate to home screen and assert navigation
        androidHomeScreen.pressHomeTabBtn();
        androidHomeScreen.isHomePageDisplayed();

        //Navigate to more tab and assert navigation
        androidHomeScreen.pressMoreTabBtn();
        androidMoreScreen.isPageDisplayed();

        //Navigate to language modal and assert navigation
        androidMoreScreen.pressLanguageBtn();
        androidChooseLanguageModal.isPageDisplayed();

        //Select language and assert navigation to home screen
        androidChooseLanguageModal.selectLanguage(language);
        androidHomeScreen.isHomePageDisplayed();
    }

    public ValidationResults areAllCategoriesDisplayed(AndroidDriver androidDriver
            , AndroidHomeScreen androidHomeScreen
            , AndroidCategoryScreen androidCategoryScreen
            , List<Category> allCategories
            , String serveMode
            , ValidationResults validationResults) {
        for (Category c : allCategories){
            scrollUntilACertainElementIsFound(androidDriver
                    , androidHomeScreen.getHomeScreenScrollableContentContainer()
                    , "down"
                    , androidHomeScreen.getCategoryContentDescription(c.getId()));
            if (androidHomeScreen.isCategoryDisplayed(c.getId())){
                androidHomeScreen.pressCategory(c.getId());
                androidCategoryScreen.isPageDisplayed();

                // Validate subCategories and products per subCategory
                if (!c.getSubCategories().isEmpty()){
                    for (Category sc : c.getSubCategories()){
                        if (c.getSubCategories().size() == 1){
                            if (!androidCategoryScreen.getSubCategoryProductsTitle().isDisplayed()
                                    && !(androidCategoryScreen.getSubCategoryProductsTitle().getText()
                                    .equalsIgnoreCase(c.getSubCategories().get(0).getName())
                                    || androidCategoryScreen.getSubCategoryProductsTitle().getText()
                                    .equalsIgnoreCase(c.getSubCategories().get(0).getArabicName()))){
                                validationResults.setResult(false);
                                validationResults.addALogToValidationResults("At Category \"" + c.getId()
                                        + "\" with name \"" + c.getName()
                                        + "\". SubCategory with ID: " + c.getSubCategories().get(0).getId()
                                        + " and name \"" + c.getSubCategories().get(0).getName()
                                        + "\" title is not displayed\n");
                            }
                        } else {
                            // Reset scroll position to the first element on the left before starting scroll
                            scrollUntilACertainElementIsFound(androidDriver
                                    , androidCategoryScreen.getSubCategoriesScrollableContentContainer()
                                    , "left"
                                    , androidCategoryScreen.getSubCategoryContentDescription(
                                            c.getSubCategories().get(0).getId()));

                            // Scroll and find the target subCategory
                            scrollUntilACertainElementIsFound(androidDriver
                                    , androidCategoryScreen.getSubCategoriesScrollableContentContainer()
                                    , "right"
                                    , androidCategoryScreen.getSubCategoryContentDescription(sc.getId()));

                            // If SubCategory is found, press it
                            if (androidCategoryScreen.isSubCategoryDisplayed(sc.getId())){
                                androidCategoryScreen.pressSubCategory(sc.getId());
                            } else {
                                validationResults.setResult(false);
                                validationResults.addALogToValidationResults("At Category \"" + c.getId()
                                        + "\" with name \"" + c.getName()
                                        + "\". SubCategory with ID: " + sc.getId()
                                        + " and " + "name \"" + sc.getName() + "\" is not displayed\n");
                            }
                        }
                        // Validate each product is displayed inside each subCategory
                        for (Product p : (serveMode.equalsIgnoreCase("now")
                                ? sc.getNowProductsInclusive() : sc.getLaterProductsInclusive())) {
                            scrollUntilACertainElementIsFound(androidDriver
                                    , androidCategoryScreen.getProductsListScrollableContentContainer()
                                    , "down"
                                    , androidCategoryScreen.getProductContentDescription(p.getMongoId()));

                            if (androidCategoryScreen.isProductDisplayed(p.getMongoId())){
                                //ToDo: Add internal product details page validations
                            } else {
                                validationResults.setResult(false);
                                validationResults.addALogToValidationResults("At Category \"" + c.getId()
                                        + "\" with name \"" + c.getName()
                                        + "\". SubCategory with ID: " + sc.getId()
                                        + " and " + "name \"" + sc.getName()
                                        + "\". Product with ID \"" + p.getMongoId() + "\" "
                                        + "and name is \"" + p.getName() + "\" is not displayed\n");
                            }

                            // Reset Page scroll position to the first Product Card
                            scrollUntilACertainElementIsFound(androidDriver
                                    , androidCategoryScreen.getProductsListScrollableContentContainer()
                                    , "up"
                                    , androidCategoryScreen.getProductContentDescription(
                                            serveMode.equalsIgnoreCase("now")
                                                    ? sc.getNowProductsInclusive().get(0).getMongoId()
                                                    : sc.getLaterProductsInclusive().get(0).getMongoId()));
                        }
                    }
                }
                androidCategoryScreen.pressBackBtn();
                androidHomeScreen.isHomePageDisplayed();
            } else {
                validationResults.setResult(false);
                validationResults.addALogToValidationResults("Category with ID: " + c.getId() + " and " +
                        "name \"" + c.getName() + "\" is not displayed\n");
            }
            //Scroll back to first category or top of the page after all validations are done
            scrollUntilACertainElementIsFound(androidDriver
                    , androidHomeScreen.getHomeScreenScrollableContentContainer()
                    , "up"
                    , androidHomeScreen.getCategoryContentDescription(allCategories.get(0).getId()));
        }
        return validationResults;
    }

    public void  findAProductInCategoryScreen(AndroidHomeScreen androidHomeScreen,
                                              AndroidCategoryScreen androidCategoryScreen,
                                              AndroidDriver androidDriver,
                                              WebElement homeScreenScrollView,
                                              WebElement subCategoriesScrollView,
                                              WebElement productListScrollView,
                                              String categoryAndProductScrollDirection,
                                              String subCategoriesScrollDirection,
                                              String categoryContentDescription,
                                              int categoryId,
                                              String subCategoryContentDescription,
                                              int subCategoryId,
                                              String productContentDescription,
                                              String productID) {

        //Scroll And find a certain category using category content description
        scrollUntilACertainElementIsFound(androidDriver,homeScreenScrollView,
                categoryAndProductScrollDirection,categoryContentDescription);

        //Click On Category
        androidHomeScreen.pressCategory(categoryId);

        //Scroll And Find a certain subcategory using content description
        scrollUntilACertainElementIsFound(androidDriver,subCategoriesScrollView,
                subCategoriesScrollDirection,subCategoryContentDescription);

        //Click On Subcategory
        androidCategoryScreen.pressSubCategory(subCategoryId);

        //Scroll and find a certain element
        scrollUntilACertainElementIsFound(androidDriver,productListScrollView,
                categoryAndProductScrollDirection,productContentDescription);

        //Assert Element Exists
        androidCategoryScreen.isProductDisplayed(productID);
    }

    public void grantFineLocationPermissionToCurrentApp(AndroidDriver androidDriver){
        HashMap<String, Object> permissionsObject = new HashMap<>();
        String[] permissionsArray = {"android.permission.ACCESS_FINE_LOCATION"};
        logger.info("Adding the following permissions to the current app under test: " + Arrays.toString(permissionsArray));
        permissionsObject.put("permissions", permissionsArray);
        try {
                ((JavascriptExecutor) androidDriver)
                        .executeScript("mobile: changePermissions", permissionsObject);
            logger.info("Adding the permissions completed successfully.");
        } catch (Exception e){
            logger.error("Couldn't add the" + Arrays.toString(permissionsArray) + "permission to the app under test");
        }
    }

    public void swipeElementInDirection(AndroidDriver androidDriver, WebElement element, String direction) {
        if (!element.isDisplayed()){
            logger.error("Can't perform the swipe gesture as element isn't displayed on the screen.");
            return;
        }

        if (androidDriver == null || element == null || direction == null) {
            logger.error("Android driver, element or swipe direction is null.");
            return;
        }

        if (!direction.equalsIgnoreCase("left")
                && !direction.equalsIgnoreCase("right")){
            logger.error("Invalid swipe direction provided. Valid values are: left, right");
            return;
        }

        HashMap<String, Object> swipeObject = new HashMap<>();
        String elementId = ((RemoteWebElement) element).getId();
        swipeObject.put("elementId", elementId);
        swipeObject.put("direction", direction.toLowerCase());
        swipeObject.put("percent", 0.9);

        ((JavascriptExecutor) androidDriver)
                .executeScript("mobile: swipeGesture", swipeObject);
    }

    public String getElementSelectorType(String elementSelector){
        return selectorTypes.entrySet().stream()
                .filter(entry -> elementSelector.startsWith(entry.getKey()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse("description");
    }

    public String getSubstringSelector(String elementSelector) {
        for (String prefix : selectorTypes.keySet()) {
            if (elementSelector.startsWith(prefix)) {
                return elementSelector.substring(prefix.length());
            }
        }
        return elementSelector;
    }

    public void pullToRefresh(AndroidDriver androidDriver) {
        try {
            Dimension size = androidDriver.manage().window().getSize();
            int left = size.width / 2;
            int top = size.height / 5; // Start higher than center
            int width = size.width / 2;
            int height = size.height;

            HashMap<String, Object> swipeArgs = new HashMap<>();
            swipeArgs.put("left", left);
            swipeArgs.put("top", top);
            swipeArgs.put("width", width);
            swipeArgs.put("height", height);
            swipeArgs.put("direction", "down");
            swipeArgs.put("percent", 0.75);

            androidDriver.executeScript("mobile: swipeGesture", swipeArgs);
        } catch (Exception e) {
            logger.warn("Pull-to-refresh failed: {}", e.getMessage());
        }
    }
}

