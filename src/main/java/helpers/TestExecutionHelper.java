package helpers;

import helpers.apiClients.YeloApiClient;
import helpers.apiClients.mobileApiClients.*;
import helpers.apiClients.mobileApiClients.foodAggregatorApiClients.FoodAggregatorRestaurantsApiClient;
import helpers.apiClients.webApiClients.*;
import helpers.dataParsers.ProductsParser;
import helpers.factories.dataFactories.OtpFactory;
import helpers.factories.dataFactories.UserDataFactory;
import helpers.factories.dataFactories.customerAppDataFactories.CategoriesDataFactory;
import helpers.factories.dataFactories.customerAppDataFactories.WarehousesDataFactory;
import helpers.factories.dataFactories.testSessionsDataFactories.CustomerAppTestSessionFactory;
import modals.mainAdminPortal.*;
import modals.wordpressAdmin.UsersListPage;
import models.*;
import net.datafaker.Faker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.SkipException;

import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

public class TestExecutionHelper extends BaseHelper {
    public TestExecutionHelper(Configs configs) {
        this.configs = configs;
        this.userDataFactory = new UserDataFactory(configs);
        this.otpFactory = new OtpFactory(configs);
        this.warehousesDataFactory = new WarehousesDataFactory(configs);
        this.categoriesDataFactory = new CategoriesDataFactory(configs);
        this.customerAppTestSessionFactory = new CustomerAppTestSessionFactory(configs);
    }

    Faker faker = new Faker();
    private final Configs configs;
    private final Logger logger = LoggerFactory.getLogger(TestExecutionHelper.class);

    UserDataFactory userDataFactory;
    WarehousesDataFactory warehousesDataFactory;
    CategoriesDataFactory categoriesDataFactory;
    CustomerAppTestSessionFactory customerAppTestSessionFactory;
    OtpFactory otpFactory;

    public TestData buildTestData(TestData testData) {
        // Begin objects initialization
        testData.setRandomTestUser(new User());
        testData.setSecondaryTestUser(new User());
        testData.setTestOrder(new Order());
        testData.setTestCoupon(new Coupon());
        testData.setTestBill(new Bill());
        testData.setCardService(new CardService());
        testData.setPickerUser(new User());
        testData.setMidMileUser(new User());
        testData.setChefUser(new User());
        testData.setDaUser(new User());
        testData.setFpManagerUser(new User());
        testData.setFleetTrip(new FleetTrip());
        testData.setStockTakerUser(new User());
        testData.setPlanningCenter(new PlanningCenter());
        testData.setTransfer(new Transfer());
        // End Objects initialization

        testData.setRandomTestUser(buildRandomUserObject(testData.getRandomTestUser()));
        testData.setSecondaryTestUser(buildRandomUserObject(testData.getSecondaryTestUser()));
        testData.setTestCountryCode(configs.getTestCountryCode());
        testData = buildPaymentTestData(testData);
        testData = buildAdminUserTestData(testData);
        testData = buildTestLocationData(testData);
        testData = buildCouponTestData(testData);
        testData = buildTestPickerUserData(testData);
        testData = buildTestMidMileUserData(testData);
        testData = buildTestDaUserData(testData);
        testData = buildTestFpManagerUserData(testData);
        testData = buildPaymentPanelUserTestData(testData);

        testData = buildTestStockTakerUserData(testData);
        testData.setTestConversation(new FreshChatConversation());

        return testData;
    }

    public User buildRandomUserObject(User user){
        user = userDataFactory.generateRandomUserDetails(user);
        user = userDataFactory.generateRandomUserMobileNumber(user);
        user = userDataFactory.generateRandomUserEmailAndPassword(user);
        user = userDataFactory.generateRandomEgyptianNationalID(user);
        user.setDateOfBirth(userDataFactory.extractDateOfBirthFromNationalID(user.getNationalId()));
        user = userDataFactory.buildRandomAddressDetails(user);

        return user;
    }

    public User registerUsingApi(TestData testData, User user) {
        String phoneNumberToUse = configs.isUseForeignCountryData() ? user.getForeignPhoneNumber() : user.getPhoneNumber();
        String countryCodeToUse = configs.isUseForeignCountryData() ? user.getForeignPhoneCountryCode() : user.getPhoneCountry();

        new MobileAuthorizationApiClient(configs).sendOtpToPhoneNumber(
                phoneNumberToUse
                , countryCodeToUse);

        user = new MobileAuthorizationApiClient(configs).verifyOtpRequest(
                phoneNumberToUse,
                countryCodeToUse,
                otpFactory.fetchOtp(testData, "register", user).getOtp(),
                user);

        user = new MobileAuthorizationApiClient(configs).registerUserRequest(
                user.getFirstName(),
                user.getLastName(),
                user.getEmailAddress(),
                user.getRegisterToken(),
                user.getReferralCode(),
                user
        );

        return user;
    }

    public User createAddressUsingApi(Warehouse warehouse, User user){
        logger.info("Creating an address using the API for user with ID: {}", user.getId());

        user.setAddress(new AddressApiClient(configs).createAddressUsingApi(user
                , user.getAddress()
                , warehouse
                , true));

        logger.info("Updated the current testAddress object and the current address ID is: {}"
                , user.getAddress().getId());
        return user;
    }

    public TestData buildPaymentTestData(TestData testData) {
        testData.setTestCreditCard(configs.getTestCreditCard());
        testData.setTestExpiryDate(configs.getTestExpDate());
        testData.setTestCVC(configs.getTestCVC());
        testData.setSecondaryTestCreditCard(configs.getSecondaryTestCreditCard());
        testData.setSecondaryTestExpiryDate(configs.getSecondaryTestExpDate());
        testData.setSecondaryTestCVC(configs.getSecondaryTestCVC());
        testData.setDeclinedTestCreditCard(configs.getDeclinedCreditCard());

        return testData;
    }

    public TestData buildAdminUserTestData(TestData testData) {
        User user = new User();
        user.setPhoneNumber(configs.getAdminPhoneNumber());
        user.setLocalPhoneNumber(configs.getAdminLocalPhoneNumber());
        user.setPhoneNumber(user.getPhoneNumber().concat(user.getLocalPhoneNumber()));
        user.setPhoneCountry(configs.getAdminPhoneCountryCode());
        user.setEmailAddress(configs.getAdminGmailAddress());
        user.setEmailPassword(configs.getAdminGmailPassword());
        user.setBypassScriptPassword(configs.getBypassScriptPassword());
        user.setReferralCode(configs.getAdminReferralCode());
        if (!configs.isSkipAdminAuthorizationStep()){
            testData.setAdminUser(new AdminAuthorizationApiClient(configs).
                    loginAndGetAuthorizationTokens(user.getPhoneNumber(), user.getBypassScriptPassword(), user));
        }
        return testData;
    }

    public TestData buildPaymentPanelUserTestData(TestData testData) {
        testData.setPaymentPanelUser(new User());
        testData.getPaymentPanelUser().setEmailAddress(configs.getPaymentPanelEmail());
        testData.getPaymentPanelUser().setBypassScriptPassword(configs.getPaymentPanelPassword());

        testData.setPaymentPanelUser(new PaymentPanelApiClient(configs)
                .loginToPaymentPanel(testData.getPaymentPanelUser().getEmailAddress()
                        ,testData.getPaymentPanelUser().getBypassScriptPassword()
                        ,testData.getPaymentPanelUser())
        );

        return testData;
    }

    public TestData buildControlRoomTestData(TestData testData) {
        testData = buildWarehousesTestData(testData);
        testData = buildOrdersTestData(testData);

        return testData;
    }

    public TestData buildWarehousesTestData(TestData testData) {
        testData.setWarehousesList(new ControlRoomV2ApiClient(configs).listAllWarehouses(testData.getAdminUser()));
        testData.setTestWarehouse(new ControlRoomV2ApiClient(configs).getWarehouseByName(
                testData.getAdminUser(), configs.getTestFpName()));
        return testData;
    }

    public TestData buildCouponTestData(TestData testData) {
        testData.getTestCoupon().setCouponCode(testData.getRandomTestUser().getFirstName()
                + testData.getRandomTestUser().getLastName()
                + testData.getRandomTestUser().getLocalPhoneNumber());
        return testData;
    }

    public TestData buildTestPickerUserData(TestData testData) {
        testData.getPickerUser().setLocalPhoneNumber(configs.getPickerPhoneNumber());
        testData.getPickerUser().setBypassScriptPassword(configs.getPickerPassword());

        return testData;
    }

    private TestData buildTestMidMileUserData(TestData testData) {
        testData.getMidMileUser().setLocalPhoneNumber(configs.getMidMilePhoneNumber());
        testData.getMidMileUser().setBypassScriptPassword(configs.getMidMilePassword());

        return testData;
    }

    private TestData buildTestStockTakerUserData(TestData testData) {
        testData.getStockTakerUser().setLocalPhoneNumber(configs.getStockTakerPhoneNumber());
        testData.getStockTakerUser().setBypassScriptPassword(configs.getStockTakerPassword());
        return testData;
    }

    private TestData buildTestDaUserData(TestData testData) {
        testData.getDaUser().setLocalPhoneNumber(configs.getdAPhoneNumber());
        testData.getDaUser().setBypassScriptPassword(configs.getdAPassword());

        return testData;
    }
    private TestData buildTestFpManagerUserData(TestData testData) {
        testData.getFpManagerUser().setLocalPhoneNumber(configs.getFpManagerPhoneNumber());
        testData.getFpManagerUser().setBypassScriptPassword(configs.getFpManagerPassword());

        return testData;
    }

    public TestData buildOrdersTestData(TestData testData) {
        testData.setOrdersList(
                new ControlRoomV2ApiClient(configs).listAllActiveOrdersByWarehouse(testData.getAdminUser()
                        , testData.getTestWarehouse()
                        , configs.getTestFpDate()));
        testData = buildTestOrderTestData(testData);

        return testData;
    }

    //A function to create an order object from the string saved in configs and API response
    //Format sample:OrderID in DB/Order Number/Customer's Full Name
    public TestData buildTestOrderTestData(TestData testData) {
        Order testOrder = new Order();
        User orderOwner = new User();

        String[] parts = configs.getTestOrderInfo().split("/");
        testOrder.setOrderId(parts[0]);
        testOrder.setOrderNumber(parts[1]);
        orderOwner.setFullName(parts[2]);
        testOrder.setUser(orderOwner);
        testOrder.setFpName(configs.getTestFpName());
        testOrder.setPlacementDate(configs.getTestFpDate());

        Order retrievedOrder = new ControlRoomV2ApiClient(configs).getOrderById(
                testData.getAdminUser(),
                testData.getTestWarehouse(),
                testOrder.getPlacementDate(),
                testOrder.getOrderId());

        if (retrievedOrder != null) {
            testOrder = retrievedOrder;
        }

        testData.setTestOrder(testOrder);

        return testData;
    }

    public TestData buildTestLocationData(TestData testData) {
        testData.setTestDeviceLatitude(configs.getTestLatitude());
        testData.setTestDeviceLongitude(configs.getTestLongitude());
        return testData;
    }

    public TestData buildCustomerAppWarehousesTestData(TestData testData) {
        logger.info("Starting to create the customerApp test session data...");
        testData.setCustomerAppTestSession(new CustomerAppTestSession());
        testData.getCustomerAppTestSession().setTestOrder(new Order());
        updateStockForPredefinedProducts(testData, configs);
        testData.getCustomerAppTestSession().setTestWarehouse(warehousesDataFactory.buildTestWarehouseForCustomerAppTestSession());
        testData.getCustomerAppTestSession().setNowCategoriesInWarehouse(
                categoriesDataFactory.buildCategoriesListInWarehouse(
                        testData.getCustomerAppTestSession().getTestWarehouse(),
                        "now"));
        testData.getCustomerAppTestSession().setTomorrowCategoriesInWarehouse(
                categoriesDataFactory.buildCategoriesListInWarehouse(
                        testData.getCustomerAppTestSession().getTestWarehouse(),
                        "later"));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildRandomCategoryWithExclusiveDeliveryProductsInSubCategories(
                        testData.getCustomerAppTestSession(),
                        testData.getCustomerAppTestSession().getNowCategoriesInWarehouse(),
                        "now"));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildRandomCategoryWithExclusiveDeliveryProductsInSubCategories(
                        testData.getCustomerAppTestSession(),
                        testData.getCustomerAppTestSession().getTomorrowCategoriesInWarehouse(),
                        "later"));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildRandomCategoryWithOutOfStockProductInSubCategories(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildRandomCategoryWithNowProductThatHasPositiveStock(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildRandomCategoryWithLaterProductThatHasPositiveStock(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildRandomCategoryWithSingleProductInSubCategories(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildRandomCategoryWithCustomizableProductInSubCategories(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildRandomCategoryWithBundleProductInSubCategories(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildRandomCategoryWithSingleAndBundleProductsInSubCategories(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildRandomCategoryWithCustomizableAndNonCustomizableProductsInSubCategories(
                        testData.getCustomerAppTestSession()));
        testData.getCustomerAppTestSession().setSharedFacilityCategories(
                categoriesDataFactory.buildSharedFacilityCategoriesList(
                        testData.getCustomerAppTestSession().getNowCategoriesInWarehouse(),
                        configs.getSharedFacilityCategoryIds()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildPositiveStockListsAcrossWarehouse(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildSingleProductsWithoutDiscountList(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildSingleProductsWithSalePriceList(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildSingleProductsWithExtraSalePriceList(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildBundleProductsWithoutDiscountList(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildBundleProductsWithSalePriceList(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildSingleAndBundleProductsWithoutDiscount(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildSingleAndBundleProductsWithSalePrice(
                        testData.getCustomerAppTestSession()));
        testData.setCustomerAppTestSession(
                customerAppTestSessionFactory.buildSingleProductsWithoutDiscountList(
                        testData.getCustomerAppTestSession()));
        testData.getCustomerAppTestSession().setCarouselEnglishName(
                faker.name().name().replaceAll("[^a-zA-Z]", ""));
        testData.getCustomerAppTestSession().setCarouselArabicName(
                faker.name().name().replaceAll("[^a-zA-Z]", ""));
        logger.info("Finalized creating the customerApp test session data...");
        return testData;
    }

    public TestData buildCustomerAppShopsWarehouseTestData(TestData testData){
        logger.info("Starting to create the customerApp Shops test session data...");
        testData.setCustomerAppShopsTestSession(new CustomerAppShopsTestSession());
        testData.getCustomerAppShopsTestSession().setTestWarehouse(
                warehousesDataFactory.buildTestWarehouseForCustomerAppTestSession());
        testData.getCustomerAppShopsTestSession().setTestWarehouse(
                warehousesDataFactory.pointWarehouseObjectToShopsFp(
                        testData.getCustomerAppShopsTestSession().getTestWarehouse()));
        logger.info("Finalized creating the customerApp Shops test session data...");
        return testData;
    }

    public TestData buildStockTakeTestSession(TestData testData){
        testData.setStockTakerUser(
                new StockTakeApiClient(configs).loginAndGetAuthorizationToken(testData.getStockTakerUser()));
        testData.setStockTakerUser(
                new StockTakeApiClient(configs).getStockTakerUserDetails(testData.getStockTakerUser()));
        return testData;
    }

    public TestData buildPaymentCategoriesForCustomerAppTestSession(TestData testData) {
        if (testData.getRandomTestUser().getAuthorizationToken() == null) {
            logger.error("The random test user doesn't have an authorization token. Skipping the PaymentCategories " +
                    "data building");
            return testData;
        }
        testData.getCustomerAppTestSession().setPaymentCategories(new MobilePayServicesApiClient(configs)
                .getAllPaymentCategories(testData.getRandomTestUser().getAuthorizationToken()));
        return testData;
    }

    public PaymentCategory getPaymentCategoryByCertainCriteria(
            List<PaymentCategory> paymentCategories
            , String criteriaName
            , String criteriaValue) {
        logger.info("Searching the list of paymentCategories with count of " + paymentCategories.size() + " to find a "
                + "category with the criteriaName \"" + criteriaName + "\" and term is \"" + criteriaValue + "\"");
        boolean isCategoryFound;
        for (PaymentCategory pc : paymentCategories) {
            isCategoryFound = false;
            switch (criteriaName.toLowerCase()) {
                case "name", "enname" -> {
                    if (pc.getEnName().equalsIgnoreCase(criteriaValue))
                        isCategoryFound = true;
                    break;
                }
                case "id" -> {
                    if (pc.getCategoryId() == Integer.parseInt(criteriaValue))
                        isCategoryFound = true;
                    break;
                }
                default -> {
                    logger.error("Invalid PaymentCategory search criteriaName. Searching for \"" + criteriaName +
                            "\" and the available values are (name, enname, id)");
                }
            }
            if (isCategoryFound) {
                return pc;
            }
        }
        logger.error("Didn't find any category with \"" + criteriaName + "\" matching \"" + criteriaValue + "\"");
        return null;
    }

    public PaymentServiceProvider getPaymentServiceProviderByCertainCriteria(
            List<PaymentServiceProvider> paymentServiceProviders
            , String criteriaName
            , String criteriaValue) {
        logger.info("Searching the list paymentServiceProviders with count of " + paymentServiceProviders.size()
                + " to find a provider with the criteriaName \"" + criteriaName + "\" and criteriaValue \""
                + criteriaValue + "\"");
        boolean isPaymentServiceProviderFound;

        for (PaymentServiceProvider psp : paymentServiceProviders) {
            isPaymentServiceProviderFound = false;
            switch (criteriaName.toLowerCase()) {
                case "name", "enname" -> {
                    if (psp.getEnName().equalsIgnoreCase(criteriaValue))
                        isPaymentServiceProviderFound = true;
                    break;
                }
                case "id" -> {
                    if (psp.getServiceProviderId() == Integer.parseInt(criteriaValue))
                        isPaymentServiceProviderFound = true;
                    break;
                }
                default -> {
                    logger.error("Invalid PaymentServiceProvider search criteriaName. Searching for \"" + criteriaName +
                            "\" and the available values are (name, enname, id)");
                }
            }
            if (isPaymentServiceProviderFound) {
                logger.info("Found paymentServiceProvider with ID: " + psp.getServiceProviderId() + " and name \""
                        + psp.getEnName() + "\"");
                return psp;
            }
        }
        logger.error("Didn't find any paymentServiceProvider with \"" + criteriaName + "\" matching \""
                + criteriaValue + "\"");
        return null;
    }

    public PaymentService getPaymentServiceByCertainCriteria(
            List<PaymentService> paymentServices
            , String criteriaName
            , String criteriaValue) {
        logger.info("Searching the list paymentServices with count of " + paymentServices.size() + " to find a " +
                "service with the criteriaName \"" + criteriaName + "\" and criteriaValue \"" + criteriaValue + "\"");
        boolean isPaymentServiceFound;
        for (PaymentService ps : paymentServices) {
            isPaymentServiceFound = false;
            switch (criteriaName.toLowerCase()) {
                case "name", "enname" -> {
                    if (ps.getEnName().equalsIgnoreCase(criteriaValue))
                        isPaymentServiceFound = true;
                    break;
                }
                case "status" -> {
                    if (ps.isStatus() == Boolean.parseBoolean(criteriaValue.toLowerCase()))
                        isPaymentServiceFound = true;
                    break;
                }
                case "type" -> {
                    if (ps.getType().equalsIgnoreCase(criteriaValue))
                        isPaymentServiceFound = true;
                    break;
                }
                case "id" -> {
                    if (ps.getServiceId() == Integer.parseInt(criteriaValue))
                        isPaymentServiceFound = true;
                    break;
                }
                default -> {
                    logger.error("Invalid PaymentService search criteriaName. Searching for \"" + criteriaName +
                            "\" and the available values are (name, enname, status, type, id)");
                }
            }
            if (isPaymentServiceFound) {
                logger.info("Found service with ID: " + ps.getServiceId() + " and name \"" + ps.getEnName() + "\"");
                return ps;
            }
        }
        logger.error("Didn't find any paymentService with \"" + criteriaName + "\" matching \"" + criteriaValue + "\"");
        return null;
    }

    public AndroidDevice buildAndroidDeviceData(AndroidDevice androidDevice) {
        androidDevice.setPlatformName(configs.getAndroidPlatformName());
        androidDevice.setName(configs.getAndroidDeviceName());
        androidDevice.setPortNumber(String.valueOf(generateRandomPortNumber()));
        androidDevice.setAdbName("emulator-" + androidDevice.getPortNumber());
        androidDevice.setOsVersion(configs.getAndroidPlatformVersion());
        androidDevice.setAutomationName(configs.getAndroidAutomationName());

        return androidDevice;
    }

    public IosDevice buildIosDeviceData(IosDevice iosDevice) {
        iosDevice.setPlatformName(configs.getIosPlatformName());
        iosDevice.setDeviceName(configs.getiOSDeviceName());
        iosDevice.setDeviceUdid(configs.getIosUDID());
        iosDevice.setOsVersion(configs.getIosPlatformVersion());
        iosDevice.setAutomationName(configs.getIosAutomationName());

        return iosDevice;
    }

    //If the roleName is Admin, use what we have
    //Else, get user by role name
    //If no user found in sheet to match the needed role,
    ////get first record in CSV(burner), change its role to match what is needed
    public TestData buildBurnerUserData(TestData testData, String roleName, DataHelper dataHelper,
                                        LoginPage webLoginPage, HomePage homePage, UsersListPage webUsersListPage,
                                        List<Role> roles, SetUpHelper setUpHelper) {
        if (roleName.equalsIgnoreCase("admin")) {
            testData.getAdminUser().setRole(dataHelper.getRoleByRoleName(roleName, roles));
            testData.setBurnerUser(testData.getAdminUser());
        } else {
            User burnerUser = dataHelper.getUserByRoleCodeName(dataHelper.readUserRolesFromCSV(), roleName);
            testData.setBurnerUser(burnerUser);
            if (!burnerUser.getRole().getRoleName().equalsIgnoreCase(roleName)) {
                //Change role from wp-admin page to the needed role and save
                //Login as admin
                webLoginPage.loginWithByPassScript(testData.getAdminUser().getPhoneNumber()
                        , testData.getAdminUser().getBypassScriptPassword());
                homePage.isMoreBtnDisplayed();

                webUsersListPage.searchForUserAndChangeRole(
                        dataHelper.getRoleByRoleName(roleName, roles).getRoleCodeName(),
                        testData.getBurnerUser().getPhoneNumber());

                //Logout
                setUpHelper.openBaseURL();
                homePage.clickMoreBtn();
                homePage.clickLogoutBtn();

                //Update role on the burnerUserObject to match the target
                testData = changeUserKnownRole(testData, roles, dataHelper, roleName);
            }
        }
        return testData;
    }

    public TestData changeUserKnownRole(TestData testData, List<Role> roles, DataHelper dataHelper, String roleName) {
        Role role = dataHelper.getRoleByRoleName(roleName, roles);
        testData.getBurnerUser().setRole(role);
        return testData;
    }

    public TestData buildCardServiceTestData(TestData testData) {

        return testData;
    }

    public Order getOrderByCertainCriteria(List<Order> ordersPool, String criteriaName, String criteriaValue) {
        logger.info("Searching the list orders with count of " + ordersPool.size()
                + " to find order with the criteriaName \"" + criteriaName + "\" and criteriaValue \""
                + criteriaValue + "\"");
        boolean orderFound = false;
        Order targetOrder = new Order();
        switch (criteriaName.toLowerCase()) {
            case "dispatchervalue" -> {
                if (criteriaValue == null) {
                    for (Order e : ordersPool) {
                        if (e.getDispatcher() == null) {
                            orderFound = true;
                            targetOrder = e;
                            break;
                        }
                    }
                }
            }
            default -> {
                logger.error("Invalid order search criteriaName. Searching for \"" + criteriaName +
                        "\" and the available values are (dispatcherValue)");
            }
        }
        return targetOrder;
    }

    //A wrapper method to complete an order after being placed & paid for
    public void completeOrderCycleFromTheApis(TestData testData, float collectedAmount, Boolean markAsFree) {
        OrderApiClient orderApiClient = new OrderApiClient(configs);
        ControlRoomV2ApiClient controlRoomV2ApiClient = new ControlRoomV2ApiClient(configs);
        int maxAttempts = 30;
        int attempt = 0;

        //Mark order as packed
        controlRoomV2ApiClient.changeOrderStatus(testData.getAdminUser(),
                testData.getRandomTestUser().getTestOrder().getOrderId(),
                "packed",
                collectedAmount,
                markAsFree);

        //Mark order as picked up
        controlRoomV2ApiClient.changeOrderStatus(testData.getAdminUser(),
                testData.getRandomTestUser().getTestOrder().getOrderId(),
                "pickup",
                collectedAmount,
                markAsFree);

        //Mark order as in-route
        controlRoomV2ApiClient.changeOrderStatus(testData.getAdminUser(),
                testData.getRandomTestUser().getTestOrder().getOrderId(),
                "in-route",
                collectedAmount,
                markAsFree);

        //Mark order as delivering
        controlRoomV2ApiClient.changeOrderStatus(testData.getAdminUser(),
                testData.getRandomTestUser().getTestOrder().getOrderId(),
                "delivering",
                collectedAmount,
                markAsFree);

        //Mark order as delivered
        controlRoomV2ApiClient.changeOrderStatus(testData.getAdminUser(),
                testData.getRandomTestUser().getTestOrder().getOrderId(),
                "delivered",
                collectedAmount,
                markAsFree);

        //Don't exit until we try for MAX_ATTEMPTS to check if the order is completed
        while (attempt < maxAttempts) {
            logger.info("Attempt {} to check if the order {} is completed.", attempt + 1
                    , testData.getRandomTestUser().getTestOrder().getOrderId());
            testData.getRandomTestUser().setAllOrders(orderApiClient.listAllOrdersUser(
                    testData.getRandomTestUser()));
            if (testData.getRandomTestUser().getAllOrders().getFirst().getStatus()
                    .equalsIgnoreCase("completed")) {
                logger.info("Order {} is completed successfully in attempt {}. Exiting the loop..."
                        , testData.getRandomTestUser().getTestOrder().getOrderId(), attempt+1);
                break;
            } else {
                logger.warn("Order {} is not completed yet. Current status: {}"
                        , testData.getRandomTestUser().getTestOrder().getOrderId()
                        , testData.getRandomTestUser().getAllOrders().getFirst().getStatus());
            }
            // Wait for 2 seconds before the next attempt
            try {
                Thread.sleep(Duration.ofSeconds(2));
            } catch (Exception e) {
                logger.error("Error while waiting for the order to complete: {}", e.getMessage());
            }

            if (attempt+1 == maxAttempts) {
                logger.error("Reached maximum attempts of {} to check order completion. Order ID: {}"
                        , maxAttempts, testData.getRandomTestUser().getTestOrder().getOrderId());
                return;
            }
            logger.info("Finished attempt {} to check status for order {} to be completed. Retrying...", attempt + 1
                    , testData.getRandomTestUser().getTestOrder().getOrderId());
            attempt++;
        }
    }

    //Wrapper method to create & pay for an order or gratuity in payment service
    public void createAndPayForOrderInPaymentService(TestData testData, Boolean useBalance, String orderType) {
        logger.info("Creating and paying for an order in the payment service with order type: {} and orderID: {}"
                , orderType, testData.getRandomTestUser().getTestOrder().getOrderId());
        new OrderPaymentApiClient(configs).createOrderInPaymentService(testData.getRandomTestUser(),
                testData.getRandomTestUser().getTestOrder(),
                useBalance,
                orderType);
        new OrderPaymentApiClient(configs).payOrderInPaymentService(testData.getRandomTestUser(),
                testData.getRandomTestUser().getTestOrder(),
                orderType);
        logger.info("Completed creating and paying for an order in the payment service with order type: {} and " +
                "orderID: {}", orderType, testData.getRandomTestUser().getTestOrder().getOrderId());

        int maxAttempts = 30; // Maximum number of attempts
        int attempt = 0;

        do {
            logger.info("Waiting for the order {} to be processed in the payment service with current status as {}...",
                    testData.getRandomTestUser().getTestOrder().getOrderId(),
                    testData.getRandomTestUser().getTestOrder().getStatus());

            syncOrder(testData.getRandomTestUser().getTestOrder().getOrderId());

            testData.getRandomTestUser().getTestOrder().setStatus(new ControlRoomV2ApiClient(configs)
                    .getOrderDetailsById(
                            testData.getAdminUser(),
                            testData.getRandomTestUser().getTestOrder().getOrderId(),
                            testData.getRandomTestUser().getTestOrder())
                    .getStatus());

            logger.info("Retrieved status for order {}: {}",
                    testData.getRandomTestUser().getTestOrder().getOrderId(),
                    testData.getRandomTestUser().getTestOrder().getStatus());

            if (++attempt >= maxAttempts) {
                logger.error("Reached maximum attempts of {}. Exiting the loop of syncOrder after " +
                        "createAndPayForOrderInPaymentService.", maxAttempts);
                break;
            }
        } while (testData.getRandomTestUser().getTestOrder().getStatus().equalsIgnoreCase("pending"));
    }

    /**
     * Compares two lists of products to ensure that all products in the target list exist in the original list.
     *
     * @param originalList the source of truth list, which contains the complete set of products
     * @param targetList the list to verify, ensuring all its products exist in the original list
     * @return {@code true} if all products in the target list are found in the original list, {@code false} otherwise
     */
    public boolean compareTwoListsOfProducts(List<Product> originalList, List<Product> targetList){
        logger.info("Starting the products lists comparison...");
        for (Product p : targetList){
            boolean productFound = false;
            for (Product p2 : originalList){
                logger.info("Comparing product {} against product {} and result is {}"
                        , p.getMysqlId(), p2.getMysqlId(), Integer.compare(p.getMysqlId(), p2.getMysqlId()));
                if (p.getMysqlId() == p2.getMysqlId()){
                    logger.info("Found product with ID {}", p.getMysqlId());
                    productFound = true;
                    break;
                }
            }
            if (!productFound){
                logger.info("Product with ID {} isn't found in the original list with count of {} products"
                        , p.getMysqlId(), originalList.size());
                return productFound;
            }
        }
        return true;
    }

    // Fetch delete reasons from the API and set in TestData
    public void fetchDeleteReasons(User user, TestData testData) {
        logger.info("Fetching delete reasons from the API...");
        testData.setDeleteReasons(new DeleteAccountApiClient(configs).deleteContentUsingApi(user));
        logger.info("Completed the fetch of delete reasons from the API and count of reasons is {}"
                , testData.getDeleteReasons().size());
    }

    public String getFutureTimeStamp(String format, int hoursToAdd, int OneHour) {
        LocalTime currentTime = LocalTime.now().withMinute(0).withSecond(0).withNano(0);

        LocalTime futureTime = currentTime.plusHours(hoursToAdd);
        LocalTime futureOneTime = futureTime.plusHours(OneHour);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        String formattedFutureTime = futureTime.format(formatter);
        String formattedOneFutureTime = futureOneTime.format(formatter);

        return formattedFutureTime + " - " + formattedOneFutureTime;
    }

    public void updateStockForPredefinedProducts(TestData testData, Configs configs){
        if (!configs.isSkipAdminAuthorizationStep()){
            logger.info("Starting to update the stock for each predefined product. Current count is: {}"
                    , configs.getFallBackProductIds().size());
            for(int p : configs.getFallBackProductIds()){
                new ControlRoomV2ApiClient(configs).addStockToProduct(
                        testData.getAdminUser(),
                        new MobileWarehousesApiClient(configs).getWarehouseByLocation(
                                        String.valueOf(testData.getTestDeviceLatitude())
                                        , String.valueOf(testData.getTestDeviceLongitude()))
                                .getId()
                        , p);
            }
            logger.info("Completed updating the stock for predefined products.");
        } else {
            logger.info("Skipping updating the stock for the predefined products as the flag of " +
                    "skipAdminAuthorizationStep is {}"
                    , configs.isSkipAdminAuthorizationStep());
        }
    }

    /**
     * Skips the test execution if the current time is past the specified cut-off time.
     *
     * @param cutOffTime the cut-off time in 24-hour format (9 PM = 21)
     * @throws SkipException if the current time is past the cut-off time
     */
    public void skipTestsBasedOnCutOffTime(String cutOffTime){
        String currentTime = getCurrentTimeStamp("HH");
        if (currentTime.compareTo(cutOffTime) > 0){
            logger.warn("Skipping the test as the current time is past the cut off time of: " + cutOffTime);
            throw new SkipException("Skipping the test as the current time is past the cut off time of: " + cutOffTime);
        }
    }

    public TestData buildFoodAggregatorTestData(TestData testData){
        testData.setFoodAggregatorTestSession(new FoodAggregatorTestSession());
        testData= buildRestaurantsTestData(testData);
        return testData;
    }

    public TestData buildRestaurantsTestData(TestData testData){
        testData.getFoodAggregatorTestSession().setYeloRestaurantsList(new YeloApiClient(configs).getRestaurantsDataByLocation());
        testData.getFoodAggregatorTestSession().setRestaurantsList(
                new FoodAggregatorRestaurantsApiClient(configs).getRestaurantsDataByLocation());
        testData.getFoodAggregatorTestSession().setRestaurantsBusinessCategories(
                new FoodAggregatorRestaurantsApiClient(configs).getBusinessCategoriesData());

        return testData;
    }

    public void syncOrder(String orderId) {
        new OrderApiClient(configs).syncOrder(orderId);
    }
}
