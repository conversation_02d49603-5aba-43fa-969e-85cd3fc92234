package helpers;

import io.appium.java_client.AppiumBy;
import io.appium.java_client.android.AndroidDriver;
import modals.customerApp.androidNative.*;
import modals.customerApp.androidNative.AndroidNativeMoreScreen.AndroidNativeMoreScreen;
import modals.customerApp.androidNative.androidNativeHomePage.AndroidNativeHomeScreen;
import modals.customerApp.androidNative.foodAggregator.AndroidNativeFoodAggregatorHomeScreen;
import models.BusinessCategory;
import models.Restaurant;
import models.TestData;
import models.ValidationResults;
import org.openqa.selenium.WebElement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;
import java.util.stream.Collectors;

public class AndroidNativeTestsExecutionHelper extends AndroidTestsExecutionHelper {

    private static final Logger logger = LoggerFactory.getLogger(AndroidNativeTestsExecutionHelper.class);

    public void register(AndroidNativeCountriesSelectionScreen androidNativeCountriesSelectionScreen,
                         AndroidNativeLandingScreen androidNativeLandingScreen,
                         AndroidNativePhoneNumberScreen androidNativePhoneNumberScreen,
                         AndroidNativePhoneCountrySelectionDropdownScreen androidNativePhoneCountrySelectionDropdownScreen,
                         AndroidNativeOtpVerificationScreen androidNativeOtpVerificationScreen,
                         TestExecutionHelper testExecutionHelper,
                         TestData testData,
                         AndroidNativeCreateAccountScreen androidNativeCreateAccountScreen,
                         AndroidNativeRegisterSuccessScreen androidNativeRegisterSuccessScreen,
                         AndroidNativeHomeScreen androidNativeHomeScreen,
                         String phoneCountry) {

        // Choose a country and press login
        androidNativeCountriesSelectionScreen.selectCountryAndProceed(testData.getTestCountryCode());

        //Press the Login or signup button
        androidNativeLandingScreen.pressAuthBtn();

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidNativePhoneNumberScreen
                        , androidNativeOtpVerificationScreen, "register", phoneCountry);
            }
            default -> {
                changeCountry(androidNativePhoneNumberScreen, androidNativePhoneCountrySelectionDropdownScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidNativePhoneNumberScreen
                        , androidNativeOtpVerificationScreen, "register", phoneCountry);
            }
        }

        //Enter Account Information
        androidNativeCreateAccountScreen.fillInAccountInformationForm(testData);
        androidNativeCreateAccountScreen.pressSubmitBtn();

        //Proceed from the success screen
        if (androidNativeRegisterSuccessScreen.isPageDisplayed())
            androidNativeRegisterSuccessScreen.pressDoneBtn();

        //Validate that home screen is displayed
        androidNativeHomeScreen.isPageDisplayed();
    }

    public void login(TestData testData, TestExecutionHelper testExecutionHelper
            , AndroidNativePhoneNumberScreen androidNativePhoneNumberScreen
            , AndroidNativePhoneCountrySelectionDropdownScreen androidNativePhoneCountrySelectionDropdownScreen
            , AndroidNativeOtpVerificationScreen androidNativeOtpVerificationScreen
            , String phoneCountry) {

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidNativePhoneNumberScreen, androidNativeOtpVerificationScreen,
                        "login", phoneCountry);
            }
            default -> {
                changeCountry(androidNativePhoneNumberScreen, androidNativePhoneCountrySelectionDropdownScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, androidNativePhoneNumberScreen, androidNativeOtpVerificationScreen,
                        "login", phoneCountry);
            }
        }
    }

    private void enterPhoneNumberAndOTP(TestData testData, TestExecutionHelper testExecutionHelper,
                                        AndroidNativePhoneNumberScreen androidNativePhoneNumberScreen,
                                        AndroidNativeOtpVerificationScreen androidNativeOtpVerificationScreen,
                                        String method, String phoneCountry) {

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                if (androidNativePhoneNumberScreen.isPageDisplayed())
                    androidNativePhoneNumberScreen.enterPhoneNumberAndPresNext(
                            testData.getRandomTestUser().getLocalPhoneNumber());
            }
            default -> {
                if (androidNativePhoneNumberScreen.isPageDisplayed())
                    androidNativePhoneNumberScreen.enterPhoneNumberAndPresNext(
                            testData.getRandomTestUser().getForeignLocalPhoneNumber());
            }
        }

        //Fetch and Enter the OTP
        if (androidNativeOtpVerificationScreen.isPageDisplayed()) {
            while (androidNativeOtpVerificationScreen.isPageDisplayed()) {
                try {
                    testData.setRandomTestUser(testExecutionHelper.otpFactory
                            .fetchOtp(testData, method, testData.getRandomTestUser()));
                    if (androidNativeOtpVerificationScreen.isPageDisplayed()) {
                        androidNativeOtpVerificationScreen.enterOtp(testData.getRandomTestUser().getOtp());
                    }
                    if (androidNativeOtpVerificationScreen.isPageDismissed())
                        break;
                } catch (Exception e) {
                    break;
                }
            }
        }
    }

    public void logoutAndGoToPhoneInputScreen(AndroidDriver androidDriver, AndroidNativeHomeScreen androidNativeHomeScreen
            , AndroidNativeMoreScreen androidNativeMoreScreen) {
        androidNativeHomeScreen.pressMoreBtn();

        scrollUntilACertainElementIsFound(androidDriver,
                androidNativeMoreScreen.getScrollableContentContainer(),
                "down",
                androidNativeMoreScreen.getLogoutBtnContentDescription());

        androidNativeMoreScreen.pressLogoutBtn();

        androidNativeHomeScreen.isPageDisplayed();

        androidNativeHomeScreen.pressMoreBtn();
        androidNativeMoreScreen.pressContinueBtn();
    }

    private void changeCountry(AndroidNativePhoneNumberScreen androidNativePhoneNumberScreen
            , AndroidNativePhoneCountrySelectionDropdownScreen androidNativePhoneCountrySelectionDropdownScreen,
                               TestData testData) {
        androidNativePhoneNumberScreen.pressPhoneNumberCountryCode();
        if (androidNativePhoneCountrySelectionDropdownScreen.isDropdownDisplayed()) {
            androidNativePhoneCountrySelectionDropdownScreen
                    .searchAndSelectTheCountry(testData.getRandomTestUser().getForeignPhoneCountry()
                            , testData.getRandomTestUser().getForeignPhoneNumber()
                                    .replace(testData.getRandomTestUser().getForeignLocalPhoneNumber(), ""));
        }
    }
}
