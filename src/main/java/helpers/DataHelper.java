package helpers;

import models.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

public class DataHelper {
    private static final Logger logger = LoggerFactory.getLogger(DataHelper.class);

    //read configs
    public Configs readConfigs(Configs configs){
        logger.info("Initiating the main configs reading process...");

        File mainConfigs = new File("resources/environments/config_"
                + System.getProperty("env", "testing").toLowerCase()
                + ".properties");

        // Read Main Configs
        try {
            logger.info("Reading main configs...");
            FileReader fileReader = new FileReader(mainConfigs);
            Properties properties = new Properties();
            properties.load(fileReader);
            configs.setBaseURL(getPropertyValueOrDefault(properties, "baseURL", ""));
            configs.setControlRoomBaseURL(getPropertyValueOrDefault(properties, "controlRoomBaseURL", ""));
            configs.setBillingServicesBaseURL(getPropertyValueOrDefault(properties, "billingServicesBaseURL", ""));
            configs.setMidMileAppBaseURL(getPropertyValueOrDefault(properties, "midMileBaseURL", ""));
            configs.setStockTakeBaseURL(getPropertyValueOrDefault(properties, "stockTakeBaseURL",""));
            configs.setTransitBaseURL(getPropertyValueOrDefault(properties, "transitBaseURL", ""));
            configs.setLogisticsBaseURL(getPropertyValueOrDefault(properties, "logisticsBaseURL", ""));
            configs.setFleetAppBaseURL(getPropertyValueOrDefault(properties, "fleetAppBaseURL", ""));
            configs.setFleetServiceBaseURL(getPropertyValueOrDefault(properties, "fleetServiceBaseURL", ""));
            configs.setCardServicesBaseURL(getPropertyValueOrDefault(properties, "cardServicesBaseURL", ""));
            configs.setCardServicesAdminPanelBaseURL(getPropertyValueOrDefault(properties, "cardServicesAdminPanelBaseURL", ""));
            configs.setPickerServicesBaseURL(getPropertyValueOrDefault(properties, "pickerServicesBaseURL", ""));
            configs.setPaymentServiceBaseURL(getPropertyValueOrDefault(properties, "paymentServiceBaseURL",""));
            configs.setInaiBaseURL(getPropertyValueOrDefault(properties, "inaiBaseURL",""));
            configs.setInternalOrdersBaseURL(getPropertyValueOrDefault(properties, "internalOrdersBaseURL",""));
            configs.setFreshChatApiBaseUrl(getPropertyValueOrDefault(properties, "freshChatApiBaseURL", ""));
            configs.setCatalogBaseURL(getPropertyValueOrDefault(properties,"catalogBaseURL",""));
            configs.setOrderServiceBaseURL(getPropertyValueOrDefault(properties,"orderServiceBaseURL",""));
            configs.setFoodAggregatorServiceBaseURL(getPropertyValueOrDefault(properties,"foodAggregatorServiceBaseURL", ""));
            configs.setGridURL(getPropertyValueOrDefault(properties, "gridURL", ""));
            configs.setTestEmail(getPropertyValueOrDefault(properties, "testEmail", ""));
            configs.setTestPassword(getPropertyValueOrDefault(properties, "testPassword", ""));
            configs.setTestCountryCode(getPropertyValueOrDefault(properties, "testCountryCode", ""));
            configs.setTestMobileNumber(getPropertyValueOrDefault(properties, "testMobileNumber", ""));
            configs.setTestMobileCompany(getPropertyValueOrDefault(properties, "testMobileCompany", ""));
            configs.setTestCreditCard(getPropertyValueOrDefault(properties, "testCreditCard", ""));
            configs.setTestExpDate(getPropertyValueOrDefault(properties, "testExpDate", ""));
            configs.setTestCVC(getPropertyValueOrDefault(properties, "testCVC", ""));
            configs.setSecondaryTestCreditCard(getPropertyValueOrDefault(properties, "secondaryTestCreditCard", ""));
            configs.setSecondaryTestExpDate(getPropertyValueOrDefault(properties, "secondaryTestExpDate", ""));
            configs.setSecondaryTestCVC(getPropertyValueOrDefault(properties, "secondaryTestCVC", ""));
            configs.setDeclinedCreditCard(getPropertyValueOrDefault(properties, "declinedCreditCard", ""));
            configs.setWebRunMode(getPropertyValueOrDefault(properties, "webRunMode", ""));
            configs.setMobileRunMode(fetchMobileRunMode(properties));
            configs.setInternalAPIToken(getPropertyValueOrDefault(properties, "internalAPIToken", ""));
            configs.setNgrokAuthToken(getPropertyValueOrDefault(properties, "ngrokAuthToken", ""));
            configs.setSlackApiToken(getPropertyValueOrDefault(properties, "slackApiToken", ""));
            configs.setSlackApiBaseURL(getPropertyValueOrDefault(properties, "slackApiBaseURL", ""));
            configs.setBrowserStackBaseURL(getPropertyValueOrDefault(properties, "browserstackBaseURL", ""));
            configs.setWebBuildEnabled(Boolean.valueOf(getPropertyValueOrDefault(properties, "webBuildEnabled"
                    , "false")));
            configs.setMobileBuildEnabled(Boolean.valueOf(getPropertyValueOrDefault(properties, "mobileBuildEnabled"
                    , "false")));
            configs.setWebhookBuildEnabled(Boolean.valueOf(getPropertyValueOrDefault(properties, "webhookBuildEnabled"
                    , "false")));
            configs.setAndroidBuildEnabled(Boolean.valueOf(getPropertyValueOrDefault(properties, "androidBuildEnabled"
                    , "false")));
            configs.setiOSBuildEnabled(Boolean.valueOf(getPropertyValueOrDefault(properties, "iosBuildEnabled"
                    , "false")));
            configs.setAndroidDeviceName(getPropertyValueOrDefault(properties, "androidDeviceName", "Pixel_2"));
            configs.setiOSDeviceName(getPropertyValueOrDefault(properties, "iosDeviceName", "iPhone 14"));
            configs.setAdminLocalPhoneNumber(getPropertyValueOrDefault(properties, "adminLocalPhoneNumber", ""));
            configs.setAdminPhoneNumber(getPropertyValueOrDefault(properties, "adminPhoneCountryCode", ""));
            configs.setAdminPhoneCountryCode(getPropertyValueOrDefault(properties, "adminPhoneCountry", ""));
            configs.setAdminGmailAddress(getPropertyValueOrDefault(properties, "adminGmailAddress", ""));
            configs.setAdminGmailPassword(getPropertyValueOrDefault(properties, "adminGmailPassword", ""));
            configs.setBypassScriptPassword(getPropertyValueOrDefault(properties, "adminBypassScriptPassword", ""));
            configs.setAdminReferralCode(getPropertyValueOrDefault(properties, "adminReferralCode", ""));
            configs.setRolesTestsEnabled(Boolean.parseBoolean(getPropertyValueOrDefault(properties, "rolesTestsEnabled"
                    , "false")));
            configs.setTestLatitude(Double.parseDouble(getPropertyValueOrDefault(properties, "testLatitude", "")));
            configs.setTestLongitude(Double.parseDouble(getPropertyValueOrDefault(properties, "testLongitude", "")));
            configs.setTestFpName(getPropertyValueOrDefault(properties, "testFpName", ""));
            configs.setTestFpDate(getPropertyValueOrDefault(properties, "testFpDate", ""));
            configs.setTestOrderInfo(getPropertyValueOrDefault(properties, "testOrderInfo", ""));
            configs.setTestWpSecCookieName(getPropertyValueOrDefault(properties, "wpSecCookieName", ""));
            configs.setTestWpLoggedInCookieName(getPropertyValueOrDefault(properties, "wpLoggedInCookieName", ""));
            configs.setTestWpNodeAuthorizationCookieName(getPropertyValueOrDefault(properties, "wpNodeAuthorizationCookieName"
                    , ""));
            configs.setBuildMobileCategoriesAndProductsTestData(
                    Boolean.parseBoolean(getPropertyValueOrDefault(properties, "buildMobileCategoriesAndProductsTestData"
                            , "false")));
            configs.setBuildMobileShopsTestData(
                    Boolean.parseBoolean(getPropertyValueOrDefault(properties, "buildMobileShopsTestData"
                            , "false")));
            configs.setBuildWebControlRoomWarehousesAndOrders(
                    Boolean.parseBoolean(getPropertyValueOrDefault(properties, "buildWebControlRoomWarehousesAndOrders"
                            , "false")));
            configs.setRegisterUserUsingApi(Boolean.parseBoolean(getPropertyValueOrDefault(properties, "registerUserUsingApi"
                    , "false")));
            configs.setBuildMobileBillingTestData(
                    Boolean.parseBoolean(getPropertyValueOrDefault(properties, "buildMobileBillingTestData", "false")));
            configs.setCardServicesTestsEnabled(
                    Boolean.parseBoolean(getPropertyValueOrDefault(properties, "cardServicesTestsEnabled", "false")));
            configs.setTestExecutionVideoRecordingEnabled(
                    Boolean.parseBoolean(getPropertyValueOrDefault(properties, "testExecutionVideoRecordingEnabled"
                            , "false")));
            configs.setPickerPhoneNumber(getPropertyValueOrDefault(properties, "pickerPhoneNumber", ""));
            configs.setPickerPhoneCountryCode(getPropertyValueOrDefault(properties, "pickerPhoneCountryCode", "+20"));
            configs.setPickerPassword(getPropertyValueOrDefault(properties, "pickerPassword", ""));
            configs.setMidMilePhoneNumber(getPropertyValueOrDefault(properties, "midMilePhoneNumber", ""));
            configs.setMidMilePhoneCountryCode(getPropertyValueOrDefault(properties, "midMilePhoneCountryCode", "+20"));
            configs.setMidMilePassword(getPropertyValueOrDefault(properties, "midMilePassword", ""));
            configs.setChefPhoneNumber(getPropertyValueOrDefault(properties, "chefPhoneNumber", ""));
            configs.setChefCountryCode(getPropertyValueOrDefault(properties, "chefCountryCode", "+20"));
            configs.setChefPassword(getPropertyValueOrDefault(properties, "chefPassword", ""));
            configs.setdAPhoneNumber(getPropertyValueOrDefault(properties, "dAPhoneNumber", ""));
            configs.setdAPhoneCountryCode(getPropertyValueOrDefault(properties, "dAPhoneCountryCode", "+20"));
            configs.setdAPassword(getPropertyValueOrDefault(properties, "dAPassword", ""));
            configs.setFpManagerPhoneNumber(properties.getProperty("fpManagerPhoneNumber", ""));
            configs.setFpManagerCountryCode(properties.getProperty("fpManagerPhoneCountryCode", "+20"));
            configs.setFpManagerPassword(properties.getProperty("fpManagerPassword", ""));
            configs.setTargetApp(getPropertyValueOrDefault(properties, "targetApp", "customerAppReactNative"));
            configs.setPaymentServiceSecret(getPropertyValueOrDefault(properties, "paymentServiceSecret",""));
            configs.setPaymentShoppingKey(getPropertyValueOrDefault(properties, "paymentShoppingKey",""));
            configs.setPaymentTopUpKey(getPropertyValueOrDefault(properties, "paymentTopUpKey",""));
            configs.setPaymentBillingKey(getPropertyValueOrDefault(properties, "paymentBillingKey",""));
            configs.setPaymentGratuityKey(getPropertyValueOrDefault(properties, "paymentGratuityKey",""));
            configs.setInaiToken(getPropertyValueOrDefault(properties, "inaiToken",""));
            configs.setConnectToDB(Boolean.parseBoolean(System.getProperty("connect.to.db"
                    , getPropertyValueOrDefault(properties, "connectToDB", "false"))));
            configs.setMysqlHost(getPropertyValueOrDefault(properties, "mysqlHost", ""));
            configs.setMysqlUserName(getPropertyValueOrDefault(properties, "mysqlUserName", ""));
            configs.setMysqlUserPassword(getPropertyValueOrDefault(properties, "mysqlUserPassword", ""));
            configs.setMysqlDatabaseName(getPropertyValueOrDefault(properties, "mysqlDatabaseName", ""));
            configs.setMysqlServerPort(Integer.parseInt(getPropertyValueOrDefault(properties, "mysqlServerPort", "3306")));
            configs.setSshConnectionRequired(Boolean.parseBoolean(
                    getPropertyValueOrDefault(properties, "sshConnectionRequired", "false")));
            configs.setSshHost(getPropertyValueOrDefault(properties, "sshHost", ""));
            configs.setSshUserName(getPropertyValueOrDefault(properties, "sshUserName", ""));
            configs.setSshPortNumber(Integer.parseInt(getPropertyValueOrDefault(properties, "sshPort", "0")));
            configs.setSshKeyProtected(Boolean.parseBoolean(getPropertyValueOrDefault(properties, "isSshKeyProtected", "false")));
            configs.setSshPassphrase(getPropertyValueOrDefault(properties, "sshPassphrase", ""));
            configs.setSshKeyPath(getPropertyValueOrDefault(properties, "sshKeyPath", ""));
            configs.setBuildStockTakerTestData(Boolean.parseBoolean(getPropertyValueOrDefault(properties, "buildStockTakeTestData","false")));
            configs.setStockTakerPhoneNumber(getPropertyValueOrDefault(properties, "stockTakerPhoneNumber", ""));
            configs.setStockTakerCountryCode(getPropertyValueOrDefault(properties, "stockTakerPhoneCountryCode", "+20"));
            configs.setStockTakerPassword(getPropertyValueOrDefault(properties, "stockTakerPassword", ""));
            configs.setPaymentPanelEmail(getPropertyValueOrDefault(properties, "paymentPanelEmail",""));
            configs.setPaymentPanelPassword(getPropertyValueOrDefault(properties, "paymentPanelPassword",""));
            configs.setUseForeignCountryData(Boolean.parseBoolean(getPropertyValueOrDefault(properties, "setupForeignCountryData"
                    , "false")));
            configs.setApplePayInaiCallBackToken(getPropertyValueOrDefault(properties, "applePayInaiCallBackToken",""));
            configs.setFreshChatApiKey(getPropertyValueOrDefault(properties, "freshChatApiKey", ""));
            configs.setFreshChatChannelId(getPropertyValueOrDefault(properties, "freshChatChannelId", ""));
            configs.setSkipAdminAuthorizationStep(Boolean.parseBoolean(
                    getPropertyValueOrDefault(properties, "skipAdminAuthorizationStep", "false")));
            configs.setUseOrderApiV2(Boolean.parseBoolean(System.getProperty("use.order.api.v2"
                    , getPropertyValueOrDefault(properties, "useOrderApiV2", "false"))));
            configs.setRemoteProviderName(fetchRemoteProviderName(properties));
            configs.setTestMidMileOrdersDate(getPropertyValueOrDefault(properties, "testMidMileOrdersDate", ""));
            configs.setYeloBaseURL(getPropertyValueOrDefault(properties,"yeloBaseURL",""));
            configs.setYeloMarketplaceUserId(getPropertyValueOrDefault(properties,"yeloMarketplaceUserId",""));
            configs.setBuildFoodAggregatorTestData(Boolean.parseBoolean(getPropertyValueOrDefault(properties, "buildRestaurantsTestData","false")));
            configs.setSupplyChainStaticAuthToken(getPropertyValueOrDefault(
                    properties, "supplyChainStaticAuthToken",""));

            if(!getPropertyValueOrDefault(properties, "fallBackProductIds", "").isEmpty()
                && getPropertyValueOrDefault(properties, "fallBackProductIds", "").contains(",")){
                configs.setFallBackProductIds(
                        Arrays.stream(getPropertyValueOrDefault(properties, "fallBackProductIds", "")
                                        .split(","))
                            .map(String::trim)
                            .map(Integer::parseInt)
                            .collect(Collectors.toList()));
            }

            if (!getPropertyValueOrDefault(properties, "excludedCategoryIds", "").isEmpty()){
                if (getPropertyValueOrDefault(properties, "excludedCategoryIds", "").contains(",")){
                    configs.setExcludedCategoryIds(
                            Arrays.stream(getPropertyValueOrDefault(properties, "excludedCategoryIds", "")
                                    .split(","))
                                    .map(String::trim)
                                    .map(Integer::parseInt)
                                    .collect(Collectors.toList()));
                } else {
                    configs.setExcludedCategoryIds(
                            Collections.singletonList(Integer.parseInt(
                                    getPropertyValueOrDefault(properties, "excludedCategoryIds", ""))));
                }
            }

            if (!getPropertyValueOrDefault(properties, "sharedFacilityCategoryIds", "").isEmpty()){
                if (getPropertyValueOrDefault(properties, "sharedFacilityCategoryIds", "").contains(",")){
                    configs.setSharedFacilityCategoryIds(
                            Arrays.stream(getPropertyValueOrDefault(properties, "sharedFacilityCategoryIds", "")
                                            .split(","))
                                    .map(String::trim)
                                    .map(Integer::parseInt)
                                    .collect(Collectors.toList()));
                } else {
                    configs.setSharedFacilityCategoryIds(
                            Collections.singletonList(Integer.parseInt(
                                    getPropertyValueOrDefault(properties, "sharedFacilityCategoryIds", ""))));
                }
            }

            configs.setTargetQtyForEachFallBackProduct(
                    Integer.parseInt(getPropertyValueOrDefault(properties, "targetQtyForEachFallBackProduct", "10")));

            fileReader.close();
            logger.info("Finalised reading the main configs successfully.");

        } catch (Exception e){
            logger.error("Reading main config file failed. Killing the test...", e);
        }
        configs = readWebConfigs(configs);
        configs = readAndroidConfigs(configs);
        configs = readIosConfigs(configs);
        configs = readCardServiceConfigs(configs);
        configs = readBrowserStackConfigs(configs);
        configs = readLambdaTestConfigs(configs);

        return configs;
    }

    public Configs readWebConfigs(Configs configs){
        logger.info("Initiating the web configs reading process...");
        //Read Web Configs
        if (configs.getWebBuildEnabled()) {
            logger.info("Reading web configs...");
            File webConfigs = new File("resources/environments/webConfig.properties");
            try {
                FileReader fileReader = new FileReader(webConfigs);
                Properties properties = new Properties();
                properties.load(fileReader);
                configs.setChromeDriverPath(getPropertyValueOrDefault(properties
                        , "chromedriver"
                        , "resources/webDrivers/chromedriver"));
                configs.setBrowser(getPropertyValueOrDefault(properties, "browser", "chrome"));

                fileReader.close();
                logger.info("Finalised reading the web configs successfully.");
            } catch (Exception e) {
                logger.error("Reading web config file failed. Killing the test...", e);
            }
        } else {
            logger.error("Ignored reading the web configs as the buildEnabled flag is FALSE.");
        }
        return configs;
    }

    public Configs readAndroidConfigs(Configs configs){
        logger.info("Initiating the android configs reading process...");
        //Read android Configs
        if (configs.getAndroidBuildEnabled()) {
            logger.info("Reading android configs...");
            File androidDeviceConfigs = new File("resources/environments/"
                    + configs.getAndroidDeviceName() + ".properties");
            try {
                FileReader fileReader = new FileReader(androidDeviceConfigs);
                Properties properties = new Properties();
                properties.load(fileReader);
                configs.setAndroidPlatformName(getPropertyValueOrDefault(properties, "platformName", "Android"));
                configs.setAndroidPlatformVersion(getPropertyValueOrDefault(properties, "appium_platformVersion", "12"));
                configs.setAndroidADBName(getPropertyValueOrDefault(properties, "appium_deviceName", ""));
                configs.setAndroidAutomationName(getPropertyValueOrDefault(properties, "appium_automationName"
                        , "uiautomator2"));

                fileReader.close();
                logger.info("Finalised reading the android configs successfully.");
            } catch (Exception e) {
                logger.error("Reading android device config file failed. Killing the test...", e);
            }
        } else {
            logger.error("Ignored reading the android configs as the buildEnabled flag is FALSE.");
        }
        return configs;
    }

    public Configs readIosConfigs(Configs configs){
        logger.info("Initiating the iOS configs reading process...");
        //Read iOS configs
        if (configs.getiOSBuildEnabled()) {
            logger.info("Reading iOS configs...");
            File iosDeviceConfigs = new File("resources/environments/"
                    + configs.getiOSDeviceName().replace(" ", "_") + ".properties");
            try {
                FileReader fileReader = new FileReader(iosDeviceConfigs);
                Properties properties = new Properties();
                properties.load(fileReader);
                configs.setIosPlatformName(getPropertyValueOrDefault(properties, "platformName", "iOS"));
                configs.setIosPlatformVersion(getPropertyValueOrDefault(properties, "appium_platformVersion", ""));
                configs.setIosUDID(getPropertyValueOrDefault(properties, "appium_deviceUDID", ""));
                configs.setIosAutomationName(getPropertyValueOrDefault(properties, "appium_automationName", "xcuitest"));

                fileReader.close();
                logger.info("Finalised reading the iOS configs successfully.");
            } catch (Exception e) {
                logger.error("Reading iOS device config file failed. Killing the test...", e);
            }
        } else {
            logger.error("Ignored reading the iOS configs as the buildEnabled flag is FALSE.");
        }
        return configs;
    }

    public Configs readBrowserStackConfigs(Configs configs){
        logger.info("Initiating the browserStackConfigs reading process...");
        if (configs.getRemoteProviderName().equalsIgnoreCase("browserstack")) {
            //Reading General bStack properties
            File bStackConfigsFile = new File("resources/environments/browserStackConfigs.properties");
            try {
                FileReader fileReader = new FileReader(bStackConfigsFile);
                Properties properties = new Properties();
                properties.load(fileReader);

                configs.setBStackUserName(getPropertyValueOrDefault(properties, "userName", ""));
                configs.setBStackAccessKey(getPropertyValueOrDefault(properties, "accessKey", ""));
                configs.setBStackLocal(getPropertyValueOrDefault(properties, "", "false"));
                configs.setBStackDebug(getPropertyValueOrDefault(properties, "debug", "false"));
                configs.setbStackAppiumVersionForAndroidTests(
                        getPropertyValueOrDefault(properties, "appiumVersionForAndroidTests", ""));
                configs.setbStackAppiumVersionForIosTests(
                        getPropertyValueOrDefault(properties, "appiumVersionForIosTests", ""));
                configs.setBStackNetworkLogs(getPropertyValueOrDefault(properties, "networkLogs", "false"));
                configs.setBStackSelfHeal(getPropertyValueOrDefault(properties, "selfHeal", "true"));
                logger.info("Finalized reading the browserStack general configs successfully.");

                if (configs.getAndroidBuildEnabled()){
                    logger.info("Adjusting the android device configs to point to browserstack target device...");
                    configs = createDevicesPools(configs, "android", properties);
                    AndroidDevice targetAndroidDevice =
                            configs.getAndroidDevicesPool().get(
                                    new Random().nextInt(configs.getAndroidDevicesPool().size()));

                    configs.setAndroidDeviceName(targetAndroidDevice.getName());
                    configs.setAndroidPlatformVersion(targetAndroidDevice.getOsVersion());
                    configs.setbStackAndroidCustomerAppNativeAppId(
                            getPropertyValueOrDefault(properties, "bStackAndroidCustomerAppNativeApp", ""));
                    configs.setbStackAndroidCustomerAppNativeAppBuildNumber(
                            getPropertyValueOrDefault(properties
                                    , "bStackAndroidCustomerAppNativeAppBuildNumber"
                                    , ""));
                    configs.setbStackAndroidCustomerAppReactNativeId(
                            getPropertyValueOrDefault(properties, "bStackAndroidCustomerAppReactNativeApp", ""));
                    configs.setbStackAndroidCustomerAppReactNativeBuildNumber(
                            getPropertyValueOrDefault(properties
                                    , "bStackAndroidCustomerAppReactNativeAppBuildNumber"
                                    , ""));
                    configs.setbStackAndroidMidMileAppId(
                            getPropertyValueOrDefault(properties, "bStackAndroidMidMileApp", ""));
                    configs.setbStackAndroidMidMileAppBuildNumber(
                            getPropertyValueOrDefault(properties
                                    , "bStackAndroidMidMileAppBuildNumber"
                                    , ""));
                    configs.setbStackAndroidFleetAppId(
                            getPropertyValueOrDefault(properties, "bStackAndroidFleetApp", ""));
                    configs.setbStackAndroidFleetAppBuildNumber(
                            getPropertyValueOrDefault(properties
                                    , "bStackAndroidFleetAppBuildNumber"
                                    , ""));
                    logger.info("Finalized reading the android<>BrowserStack configs successfully.");
                }

                if (configs.getiOSBuildEnabled()){
                    logger.info("Adjusting the iOS device configs to point to browserstack target device...");
                    configs = createDevicesPools(configs, "ios", properties);
                    IosDevice targetIosDevice =
                            configs.getIosDevicesPool().get(
                                    new Random().nextInt(configs.getIosDevicesPool().size()));

                    configs.setiOSDeviceName(targetIosDevice.getDeviceName());
                    configs.setIosPlatformVersion(targetIosDevice.getOsVersion());
                    
                    configs.setbStackIosCustomerAppNativeAppId(
                            getPropertyValueOrDefault(properties, "bStackIosCustomerAppNativeApp", ""));
                    configs.setbStackIosCustomerAppNativeAppBuildNumber(
                            getPropertyValueOrDefault(properties
                                    , "bStackIosCustomerAppNativeAppBuildNumber"
                                    , ""));
                    configs.setbStackIosCustomerAppReactNativeId(
                            getPropertyValueOrDefault(properties, "bStackIosCustomerAppReactNativeApp", ""));
                    configs.setbStackIosCustomerAppReactNativeBuildNumber(
                            getPropertyValueOrDefault(properties
                                    , "bStackIosCustomerAppReactNativeAppBuildNumber"
                                    , ""));
                    configs.setbStackIosMidMileAppId(
                            getPropertyValueOrDefault(properties, "bStackIosMidMileApp", ""));
                    configs.setbStackIosMidMileAppBuildNumber(
                            getPropertyValueOrDefault(properties
                                    , "bStackIosMidMileAppBuildNumber"
                                    , ""));
                    configs.setbStackIosFleetAppId(
                            getPropertyValueOrDefault(properties, "bStackIosFleetApp", ""));
                    configs.setbStackIosFleetAppBuildNumber(
                            getPropertyValueOrDefault(properties
                                    , "bStackIosFleetAppBuildNumber"
                                    , ""));
                    logger.info("Finalized reading the iOS<>BrowserStack configs successfully.");
                }

            } catch (Exception e){
                logger.error("Reading android device config file failed. Killing the test...", e);
            }
        } else {
            logger.info("Ignoring browserStack configs. Mobile run mode is {} and remoteProviderName is {}"
                    , configs.getMobileRunMode(), configs.getRemoteProviderName());
        }
        return configs;
    }

    public Configs readLambdaTestConfigs(Configs configs){
        logger.info("Initiating the lambdaTestConfigs reading process...");
        if (configs.getRemoteProviderName().equalsIgnoreCase("lambdatest")) {
            //Reading General lambdaTest properties
            File lambdaTestConfigs = new File("resources/environments/lambdaTestConfigs.properties");
            try {
                FileReader fileReader = new FileReader(lambdaTestConfigs);
                Properties properties = new Properties();
                properties.load(fileReader);

                configs.setLambdaTestUserName(getPropertyValueOrDefault(properties, "userName", ""));
                configs.setLambdaTestAccessKey(getPropertyValueOrDefault(properties, "accessKey", ""));
                configs.setLambdaTestVisualKey(getPropertyValueOrDefault(properties, "visual", "false"));
                configs.setLambdaTestVideoKey(getPropertyValueOrDefault(properties, "video", "false"));
                configs.setLambdaTestNetworkKey(getPropertyValueOrDefault(properties, "network", "false"));
                configs.setLambdaTestConsoleKey(getPropertyValueOrDefault(properties, "console", "false"));
                logger.info("Finalized reading the lambdaTest general configs successfully.");

                if (configs.getAndroidBuildEnabled()){
                    logger.info("Adjusting the android device configs to point to lambdaTest target device...");
                    configs = createDevicesPools(configs, "android", properties);
                    AndroidDevice targetAndroidDevice =
                            configs.getAndroidDevicesPool().get(
                                    new Random().nextInt(configs.getAndroidDevicesPool().size()));

                    configs.setAndroidDeviceName(targetAndroidDevice.getName());
                    configs.setAndroidPlatformVersion(targetAndroidDevice.getOsVersion());
                    configs.setLambdaTestAndroidCustomerAppNativeAppId(
                            getPropertyValueOrDefault(properties, "lambdaTestAndroidCustomerAppNativeApp", ""));
                    configs.setLambdaTestAndroidCustomerAppNativeAppBuildNumber(
                            getPropertyValueOrDefault(properties
                                    , "lambdaTestAndroidCustomerAppNativeAppBuildNumber"
                                    , ""));
                    logger.info("Finalized reading the android<>lambdaTest configs successfully.");
                }

                if (configs.getiOSBuildEnabled()){
                    logger.info("Adjusting the iOS device configs to point to lambdaTest target device...");
                    configs = createDevicesPools(configs, "ios", properties);
                    IosDevice targetIosDevice =
                            configs.getIosDevicesPool().get(
                                    new Random().nextInt(configs.getIosDevicesPool().size()));

                    configs.setiOSDeviceName(targetIosDevice.getDeviceName());
                    configs.setIosPlatformVersion(targetIosDevice.getOsVersion());

                    configs.setLambdaTestIosCustomerAppNativeAppId(
                            getPropertyValueOrDefault(properties, "lambdaTestIosCustomerAppNativeApp", ""));
                    configs.setLambdaTestIosCustomerAppNativeAppBuildNumber(
                            getPropertyValueOrDefault(properties
                                    , "lambdaTestIosCustomerAppNativeAppBuildNumber"
                                    , ""));
                    logger.info("Finalized reading the iOS<>LambdaTest configs successfully.");
                }

            } catch (Exception e){
                logger.error("Reading android device config file failed. Killing the test...", e);
            }
        } else {
            logger.info("Ignoring LambdaTest configs. Mobile run mode is {} and remoteProviderName is {}"
                    , configs.getMobileRunMode(), configs.getRemoteProviderName());
        }
        return configs;
    }

    public Configs readCardServiceConfigs(Configs configs){
        logger.info("Initiating the cardService configs reading process...");
        if (configs.isCardServicesTestsEnabled()){
            logger.info("Reading cardService configs...");
            File cardsConfigs = new File("resources/environments/cardServiceConfigs_"
                    + System.getProperty("env", "testing").toLowerCase()
                    +".properties");
            try {
                FileReader cardsConfigsReader = new FileReader(cardsConfigs);
                Properties properties = new Properties();
                properties.load(cardsConfigsReader);

                // To login on card admin panel
                configs.setCardAdminPanelAdminUserName(getPropertyValueOrDefault(properties, "adminUserName", ""));
                configs.setCardAdminPanelAdminPassword(getPropertyValueOrDefault(properties, "adminPassword", ""));
                // To get the token that is used for Mobile Login
                configs.setCardMobileLoginSchemeUserName(getPropertyValueOrDefault(properties, "loginMobileSchemeUserName"
                        , ""));
                configs.setCardMobileLoginSchemePassword(getPropertyValueOrDefault(properties, "loginMobileSchemePassword"
                        , ""));
                configs.setCardLoginMobileUserPublicKey(new String(Files.readAllBytes(
                        Paths.get("resources/environments/cardServiceEncryptionPublicKey.pub")))
                        .replace("-----BEGIN PUBLIC KEY-----", "")
                        .replace("-----END PUBLIC KEY-----", "")
                        .replaceAll("\\s+", ""));
                configs.setCardServiceContractNumber(getPropertyValueOrDefault(properties, "cardServiceContractNumber",
                        ""));
                configs.setCardServiceTypeId(getPropertyValueOrDefault(properties, "cardServiceTypeId",
                        ""));
                configs.setCardServiceProductNumber(getPropertyValueOrDefault(properties, "cardServiceProductNumber",
                        ""));
                configs.setReceiverMobileNumber(getPropertyValueOrDefault(properties, "receiverMobileNumber",
                        ""));
                configs.setDefaultCardPasscode(getPropertyValueOrDefault(properties, "defaultCardPasscode",
                        ""));
                configs.setUpdatedCardPasscode(getPropertyValueOrDefault(properties, "updatedPasscode",
                        ""));
                configs.setCardUserMobileNumber(getPropertyValueOrDefault(properties,"cardUserMobileNumber",
                        ""));
                configs.setPickupLocationId(getPropertyValueOrDefault(properties,"pickupLocationId",
                        ""));
                configs.setCardUserNationalIdExpiryDate(getPropertyValueOrDefault(properties, "cardUserNationalIdExpiryDate",
                        ""));
                cardsConfigsReader.close();
                configs.setImagePath(getPropertyValueOrDefault(properties,"imagePath",""));
                logger.info("Finalised reading the cardService configs successfully.");
            } catch (Exception e){
                logger.error("Reading cards config file failed. Killing the test....", e);
            }
        }
        return configs;
    }

    public List<User> readUserRolesFromCSV(){
        Path filePath = Paths.get("resources/dataSets/rolesDataSets/userRolesMap.csv");
        List<User> users = new ArrayList<>();

        try {
            List<String> lines = Files.readAllLines(filePath);
            User user = new User();
            Role role = new Role();

            // Start from index 1 to skip the header
            for (int i = 1; i < lines.size(); i++) {
                String line = lines.get(i);
                String[] fields = line.split(",");
                user.setPhoneNumber(fields[0]);
                user.setBypassScriptPassword(fields[1]);
                role.setRoleName(fields[2]);
                user.setRole(role);

                users.add(user);
        }
        }catch (Exception e){
            logger.error("[ERROR] Reading the users list failed");
        }
        return users;
    }

    public User getUserByRoleCodeName(List<User> users, String roleCodeName){
        for (User user : users) {
            if (user.getRole().getRoleName().equalsIgnoreCase(roleCodeName)) {
                return user;
            }
        }
        //Default value is first record in the list/first user in CSV
        return users.get(0);
    }

    public List<Role> readRolesFromCSV() {
        List<Role> roles = new ArrayList<>();
        String filePath = "resources/dataSets/rolesDataSets/rolesList.csv";

        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            // Skip the header line
            br.readLine();

            while ((line = br.readLine()) != null) {
                String[] parts = line.split(",");
                if (parts.length == 2) {
                    Role role = new Role();
                    role.setRoleName(parts[0]);
                    role.setRoleCodeName(parts[1]);
                    roles.add(role);
                }
            }
        } catch (Exception e) {
            logger.error("Reading roles list failed.", e);
        }

        return roles;
    }

    public Role getRoleByRoleName(String roleName, List<Role> roles){
        for (Role role : roles){
            if (role.getRoleName().equalsIgnoreCase(roleName)){
                return role;
            }
        }
        return null;
    }

    public String getPropertyValueOrDefault(Properties properties, String keyName, String defaultValue){
        if (properties.getProperty( keyName, defaultValue).isEmpty())
            return defaultValue;
        else
            return properties.getProperty( keyName, defaultValue);
    }

    public Configs createDevicesPools(Configs configs, String targetPlatform, Properties properties){
        logger.info("Creating devices pools for target platform: {}", targetPlatform);
        switch(targetPlatform.toLowerCase()){
            case "android" -> {
                for (int i = 1; i <= 4; i++){
                    String platformVersionKey = i == 1 ? "androidPlatformVersion" : "androidPlatformVersionDevice" + i;
                    String deviceNameKey = i == 1 ? "androidDeviceName" : "androidDeviceName" + i;

                    String platformVersion = getPropertyValueOrDefault(properties, platformVersionKey, "");
                    String deviceName = getPropertyValueOrDefault(properties, deviceNameKey, "");

                    if (!platformVersion.isEmpty() && !deviceName.isEmpty()) {
                        AndroidDevice androidDevice = new AndroidDevice();
                        androidDevice.setOsVersion(platformVersion);
                        androidDevice.setName(deviceName);
                        configs.getAndroidDevicesPool().add(androidDevice);
                    }
                }
            }
            case "ios" -> {
                for (int i = 1; i <= 4; i++){
                    String platformVersionKey = i == 1 ? "iosPlatformVersion" : "iosPlatformVersionDevice" + i;
                    String deviceNameKey = i == 1 ? "iosDeviceName" : "iosDeviceName" + i;

                    String platformVersion = getPropertyValueOrDefault(properties, platformVersionKey, "");
                    String deviceName = getPropertyValueOrDefault(properties, deviceNameKey, "");

                    if (!platformVersion.isEmpty() && !deviceName.isEmpty()) {
                        IosDevice iosDevice = new IosDevice();
                        iosDevice.setOsVersion(platformVersion);
                        iosDevice.setDeviceName(deviceName);
                        configs.getIosDevicesPool().add(iosDevice);
                    }
                }
            }
            default -> {
                logger.error("Invalid target platform specified: {}", targetPlatform);
            }
        }
        logger.info("Devices pools created successfully for target platform: {}", targetPlatform);
        return configs;
    }

    private String fetchRemoteProviderName(Properties properties){
        logger.info("fetching remote provider name...");
        if (System.getProperty("browserstack.enabled", "false").equalsIgnoreCase("true")) {
            logger.info("BrowserStack is enabled via system properties.");
            return "browserstack";
        } else if (System.getProperty("remote.provider.name") != null
                && (System.getProperty("remote.provider.name").equalsIgnoreCase("browserstack")
                    || System.getProperty("remote.provider.name").equalsIgnoreCase("lambdaTest"))) {
            logger.info("Remote provider name is set via system properties.");
            return System.getProperty("remote.provider.name");
        } else {
            logger.info("Using default remote provider name from properties file.");
            return getPropertyValueOrDefault(properties, "remoteProviderName", "browserstack");
        }
    }

    private String fetchMobileRunMode(Properties properties) {
        logger.info("fetching mobile run mode...");
        if (System.getProperty("browserstack.enabled", "false").equalsIgnoreCase("true")) {
            logger.info("BrowserStack is enabled via system properties, setting the run mode to remote.");
            return "remote";
        } else if (System.getProperty("remote.provider.name") != null) {
            logger.info("Remote provider name is not null in system properties, setting the run mode to remote.");
            return "remote";
        }
        else {
            logger.info("Using default mobile run mode from properties file.");
            return getPropertyValueOrDefault(properties, "mobileRunMode", "local");
        }
    }
}
