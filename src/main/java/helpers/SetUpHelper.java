package helpers;

import models.Configs;
import org.openqa.selenium.WebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.ServerSocket;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class SetUpHelper {
    public SetUpHelper (WebDriver webDriver, Configs configs){
        this.webDriver = webDriver;
        this.configs = configs;
    }
    private final WebDriver webDriver;
    private static final Logger logger = LoggerFactory.getLogger(SetUpHelper.class);
    private final long timeoutInSeconds = 10; //Setup operations timeout in seconds
    private final Configs configs;

    //Return the local driver path based on the OS
    public String getDriverPath(){
        logger.info("Getting Driver Path");
        String driverPath;
        String osName = System.getProperty("os.name");
        if (osName.toLowerCase().contains("mac") || osName.toLowerCase().contains("linux")){
            driverPath = configs.getChromeDriverPath();
        } else {
            driverPath = System.getProperty("user.dir") + "/" + configs.getChromeDriverPath() + ".exe";
        }
        logger.info("DriverPath is: " + driverPath);
        return driverPath;
    }

    public void openBaseURL(){
        webDriver.get(configs.getBaseURL());
    }

    public static void captureScreenshot(WebDriver driver, String testCaseName) {
        try {
            if (driver == null) {
                logger.error("captureScreenshot skipped: driver is null for '{}'", testCaseName);
                return;
            }

            Path p = java.nio.file.Paths.get(
                    "resources", "screenshots",
                    (testCaseName == null ? "unnamed" : testCaseName.replaceAll("[^a-zA-Z0-9._-]", "_"))
                            + "_" + java.time.LocalDateTime.now()
                            .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss-SSS"))
                            + ".png");
            java.nio.file.Files.createDirectories(p.getParent());
            java.nio.file.Files.write(p, ((org.openqa.selenium.TakesScreenshot) driver)
                    .getScreenshotAs(org.openqa.selenium.OutputType.BYTES));
            logger.info("Saved screenshot in path {} for case : {}", p.toAbsolutePath(), testCaseName);
        } catch (Exception e) {
            logger.error("Failed to capture screenshot for '{}': {}", testCaseName, e.toString());
        }
    }
}
