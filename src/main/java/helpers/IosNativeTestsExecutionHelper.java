package helpers;

import io.appium.java_client.ios.IOSDriver;
import modals.customerApp.iosNative.*;
import modals.customerApp.iosNative.iosNativeHomePage.IosNativeHomeScreen;
import modals.customerApp.iosNative.iosNativeMorePage.IosNativeMoreScreen;
import models.TestData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class IosNativeTestsExecutionHelper extends IosTestsExecutionHelper {

    private static final Logger logger = LoggerFactory.getLogger(IosNativeTestsExecutionHelper.class);

    public void register(IosNativeCountriesSelectionScreen iosNativeCountriesSelectionScreen,
                         IosNativeLandingScreen iosNativeLandingScreen,
                         IosNativePhoneNumberScreen iosNativePhoneNumberScreen,
                         IosNativePhoneCountrySelectionDropdownScreen iosNativePhoneCountrySelectionDropdownScreen,
                         IosNativeOtpVerificationScreen iosNativeOtpVerificationScreen,
                         TestExecutionHelper testExecutionHelper,
                         TestData testData,
                         IosNativeCreateAccountScreen iosNativeCreateAccountScreen,
                         IosNativeRegisterSuccessScreen iosNativeRegisterSuccessScreen,
                         IosNativeHomeScreen iosNativeHomeScreen,
                         String phoneCountry) {

        // Choose a country and press login
        iosNativeCountriesSelectionScreen.selectCountryAndProceed(testData.getTestCountryCode());

        //Press the Login or signup button
        iosNativeLandingScreen.pressAuthBtn();

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosNativePhoneNumberScreen
                        , iosNativeOtpVerificationScreen, "register", phoneCountry);
            }
            default -> {
                changeCountry(iosNativePhoneNumberScreen, iosNativePhoneCountrySelectionDropdownScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosNativePhoneNumberScreen
                        , iosNativeOtpVerificationScreen, "register", phoneCountry);
            }
        }

        //Enter Account Information
        iosNativeCreateAccountScreen.fillInAccountInformationForm(testData);
        iosNativeCreateAccountScreen.pressSubmitBtn();

        //Proceed from the success screen
        if (iosNativeRegisterSuccessScreen.isPageDisplayed())
            iosNativeRegisterSuccessScreen.pressDoneBtn();

        //Validate that home screen is displayed
        iosNativeHomeScreen.isPageDisplayed();
    }

    public void login(TestData testData, TestExecutionHelper testExecutionHelper
            , IosNativePhoneNumberScreen iosNativePhoneNumberScreen
            , IosNativePhoneCountrySelectionDropdownScreen iosNativePhoneCountrySelectionDropdownScreen
            , IosNativeOtpVerificationScreen iosNativeOtpVerificationScreen
            , String phoneCountry) {

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosNativePhoneNumberScreen, iosNativeOtpVerificationScreen,
                        "login", phoneCountry);
            }
            default -> {
                changeCountry(iosNativePhoneNumberScreen, iosNativePhoneCountrySelectionDropdownScreen, testData);
                enterPhoneNumberAndOTP(testData, testExecutionHelper, iosNativePhoneNumberScreen, iosNativeOtpVerificationScreen,
                        "login", phoneCountry);
            }
        }
    }

    private void enterPhoneNumberAndOTP(TestData testData, TestExecutionHelper testExecutionHelper,
                                        IosNativePhoneNumberScreen iosNativePhoneNumberScreen,
                                        IosNativeOtpVerificationScreen iosNativeOtpVerificationScreen,
                                        String method, String phoneCountry) {

        switch (phoneCountry.toUpperCase()) {
            case "EG", "KSA" -> {
                if (iosNativePhoneNumberScreen.isPageDisplayed())
                    iosNativePhoneNumberScreen.enterPhoneNumberAndPresNext(
                            testData.getRandomTestUser().getLocalPhoneNumber());
            }
            default -> {
                if (iosNativePhoneNumberScreen.isPageDisplayed())
                    iosNativePhoneNumberScreen.enterPhoneNumberAndPresNext(
                            testData.getRandomTestUser().getForeignLocalPhoneNumber());
            }
        }

        //Fetch and Enter the OTP
        if (iosNativeOtpVerificationScreen.isPageDisplayed()) {
            while (iosNativeOtpVerificationScreen.isPageDisplayed()) {
                try {
                    testData.setRandomTestUser(testExecutionHelper.otpFactory
                            .fetchOtp(testData, method, testData.getRandomTestUser()));
                    if (iosNativeOtpVerificationScreen.isPageDisplayed()) {
                        iosNativeOtpVerificationScreen.enterOtp(testData.getRandomTestUser().getOtp());
                    }
                } catch (Exception e) {
                    break;
                }
                if (iosNativeOtpVerificationScreen.isPageHidden())
                    break;
            }
        }
    }

    public void logoutAndGoToPhoneInputScreen(IOSDriver iosDriver, IosNativeHomeScreen iosNativeHomeScreen
            , IosNativeMoreScreen iosNativeMoreScreen) {
        iosNativeHomeScreen.pressMoreBtn();

        scrollUntilACertainElementIsFound(iosDriver,
                "down",
                iosNativeMoreScreen.getScrollableContentContainer(),
                iosNativeMoreScreen.getLogoutNameSelector());

        iosNativeMoreScreen.pressLogoutBtn();

        iosNativeHomeScreen.isPageDisplayed();

        iosNativeHomeScreen.pressMoreBtn();
        iosNativeMoreScreen.pressContinueBtn();
    }

    private void changeCountry(IosNativePhoneNumberScreen iosNativePhoneNumberScreen
            , IosNativePhoneCountrySelectionDropdownScreen iosNativePhoneCountrySelectionDropdownScreen,
                               TestData testData) {
        iosNativePhoneNumberScreen.pressPhoneNumberCountryCode();
        if (iosNativePhoneCountrySelectionDropdownScreen.isDropdownDisplayed()) {
            iosNativePhoneCountrySelectionDropdownScreen.searchAndSelectTheCountry(testData.getRandomTestUser().getForeignPhoneCountry()
                    , testData.getRandomTestUser().getForeignPhoneNumber()
                            .replace(testData.getRandomTestUser().getForeignLocalPhoneNumber(), ""));
        }
    }
}
