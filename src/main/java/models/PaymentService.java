package models;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class PaymentService {
    private int serviceId;
    private String  arName;
    private String enName;
    private String arDescription;
    private String enDescription;
    private String feesType;
    private int feesValue;
    private int feesMinimum;
    private int feesMaximum;
    private boolean status;
    private String type;
    private String createdAt;
    private String updatedAt;
    private List<PaymentServiceInputParam> inputParamsList = new ArrayList<>();
    private int minimumAmountRange;
    private int maximumAmountRange;
    private int beeServiceId;
    private List<JSONObject> beeServiceChargeList = new ArrayList<>();

    public int getServiceId() {
        return serviceId;
    }

    public void setServiceId(int serviceId) {
        this.serviceId = serviceId;
    }

    public String getArName() {
        return arName;
    }

    public void setArName(String arName) {
        this.arName = arName;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getArDescription() {
        return arDescription;
    }

    public void setArDescription(String arDescription) {
        this.arDescription = arDescription;
    }

    public String getEnDescription() {
        return enDescription;
    }

    public void setEnDescription(String enDescription) {
        this.enDescription = enDescription;
    }

    public String getFeesType() {
        return feesType;
    }

    public void setFeesType(String feesType) {
        this.feesType = feesType;
    }

    public int getFeesValue() {
        return feesValue;
    }

    public void setFeesValue(int feesValue) {
        this.feesValue = feesValue;
    }

    public int getFeesMinimum() {
        return feesMinimum;
    }

    public void setFeesMinimum(int feesMinimum) {
        this.feesMinimum = feesMinimum;
    }

    public int getFeesMaximum() {
        return feesMaximum;
    }

    public void setFeesMaximum(int feesMaximum) {
        this.feesMaximum = feesMaximum;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<PaymentServiceInputParam> getInputParamsList() {
        return inputParamsList;
    }

    public void setInputParamsList(List<PaymentServiceInputParam> inputParamsList) {
        this.inputParamsList = inputParamsList;
    }

    public int getMinimumAmountRange() {
        return minimumAmountRange;
    }

    public void setMinimumAmountRange(int minimumAmountRange) {
        this.minimumAmountRange = minimumAmountRange;
    }

    public int getMaximumAmountRange() {
        return maximumAmountRange;
    }

    public void setMaximumAmountRange(int maximumAmountRange) {
        this.maximumAmountRange = maximumAmountRange;
    }

    public int getBeeServiceId() {
        return beeServiceId;
    }

    public void setBeeServiceId(int beeServiceId) {
        this.beeServiceId = beeServiceId;
    }

    public List<JSONObject> getBeeServiceChargeList() {
        return beeServiceChargeList;
    }

    public void setBeeServiceChargeList(List<JSONObject> beeServiceChargeList) {
        this.beeServiceChargeList = beeServiceChargeList;
    }
}
