package models;

import org.json.JSONObject;

import java.util.List;

public class Order {
    User user;
    String fpName;
    String fpId;
    String date;
    String orderId;
    String orderNumber;
    String status;
    String lastAction;
    String nowTomorrowType;
    String addressId;
    Address address;
    JSONObject notes;
    String totalAmount;
    String collectedAmount;
    String timeSlot;
    String deliveryDate;
    String area;
    String subArea;
    String placementDate;
    String expectedDeliveryTime;
    String actualDeliveryTime;
    User dispatcher;
    float deliveryFees;
    int customerId;
    boolean isNow;
    boolean giftReceipt;
    String customerName;
    String paymentMethod;
    Float subtotal;
    Float totalWithGratuity;
    String gratuityAmount;
    Float discount;
    Float total;
    Float totalInvoice;
    boolean isCancellable;
    JSONObject statuses;
    List<JSONObject> products;
    Warehouse warehouse;
    String referenceIn;
    String referenceOut;
    String internalCategory;
    String sourceLocation;
    String destination;
    String orderPaymentId;
    String gratuityOrderId;
    String billOrderId;
    String topUpOrderId;
    String providerOrderId;
    String gratuityProviderOrderId;
    String gratuityTransactionId;
    JSONObject orderWalletPaymentTransaction;
    JSONObject orderCCPaymentTransaction;
    String inaiRedirectionUrl;
    String ccTransactionId;
    String walletTransactionId;
    String ccGratuityTransactionId;
    String paymentTitle;
    boolean kitchenPickup;
    boolean pharmacyPickup;
    String refundTransactionId;
    int productId;
    String refundAmount;
    String refundType;
    boolean isScheduled;
    String orderCouponName;
    List<Product> orderProducts;
    double balanceUsedInOrder;
    double totalDueAmount;
    boolean isOrderTimeSlotShifted;
    String captureMethod;
    boolean isScheduledExpress;
    String promisedTime;
    float deliveryFeesInFeesObject;
    double discountFeesInFeesObject;
    double referralDiscountInFeesObject;
    double serviceFeesInFeesObject;
    String gratuityFundsUsed;
    int serviceFees;
    float totalToCollect;
    OrderPaymentTransactions orderPaymentTransactions;
    String deliveryNote;
    String currentStatus;
    boolean isPaidByInai;
    boolean isCredit;
    String apsRedirectionURL;
    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getFpName() {
        return fpName;
    }

    public void setFpName(String fpName) {
        this.fpName = fpName;
    }

    public String getFpId() {
        return fpId;
    }

    public void setFpId(String fpId) {
        this.fpId = fpId;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLastAction() {
        return lastAction;
    }

    public void setLastAction(String lastAction) {
        this.lastAction = lastAction;
    }

    public String getNowTomorrowType() {
        return nowTomorrowType;
    }

    public void setNowTomorrowType(String nowTomorrowType) {
        this.nowTomorrowType = nowTomorrowType;
    }

    public String getAddressId() {
        return addressId;
    }

    public void setAddressId(String addressId) {
        this.addressId = addressId;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public JSONObject getNotes() {
        return notes;
    }

    public void setNotes(JSONObject notes) {
        this.notes = notes;
    }

    public String getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(String totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getCollectedAmount() {
        return collectedAmount;
    }

    public void setCollectedAmount(String collectedAmount) {
        this.collectedAmount = collectedAmount;
    }

    public String getTimeSlot() {
        return timeSlot;
    }

    public void setTimeSlot(String timeSlot) {
        this.timeSlot = timeSlot;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getSubArea() {
        return subArea;
    }

    public void setSubArea(String subArea) {
        this.subArea = subArea;
    }

    public String getPlacementDate() {
        return placementDate;
    }

    public void setPlacementDate(String placementDate) {
        this.placementDate = placementDate;
    }

    public String getExpectedDeliveryTime() {
        return expectedDeliveryTime;
    }

    public void setExpectedDeliveryTime(String expectedDeliveryTime) {
        this.expectedDeliveryTime = expectedDeliveryTime;
    }

    public String getActualDeliveryTime() {
        return actualDeliveryTime;
    }

    public void setActualDeliveryTime(String actualDeliveryTime) {
        this.actualDeliveryTime = actualDeliveryTime;
    }

    public float getDeliveryFees() {
        return deliveryFees;
    }

    public void setDeliveryFees(float deliveryFees) {
        this.deliveryFees = deliveryFees;
    }

    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public boolean isNow() {
        return isNow;
    }

    public void setNow(boolean now) {
        isNow = now;
    }

    public boolean isGiftReceipt() {
        return giftReceipt;
    }

    public void setGiftReceipt(boolean giftReceipt) {
        this.giftReceipt = giftReceipt;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentTitle() {
        return paymentTitle;
    }
    public void setPaymentTitle(String paymentTitle) {
        this.paymentTitle = paymentTitle;
    }
    public Float getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(Float subtotal) {
        this.subtotal = subtotal;
    }

    public Float getDiscount() {
        return discount;
    }

    public void setDiscount(Float discount) {
        this.discount = discount;
    }

    public Float getTotal() {
        return total;
    }

    public void setTotal(Float total) {
        this.total = total;
    }

    public Float getTotalInvoice() {
        return totalInvoice;
    }

    public void setTotalInvoice(Float totalInvoice) {
        this.totalInvoice = totalInvoice;
    }

    public boolean isCancellable() {
        return isCancellable;
    }
    public void setCancellable(boolean cancellable) {
        this.isCancellable = cancellable;
    }

    public JSONObject getStatuses() {
        return statuses;
    }

    public void setStatuses(JSONObject statuses) {
        this.statuses = statuses;
    }

    public Warehouse getWarehouse() {
        return warehouse;
    }

    public void setWarehouse(Warehouse warehouse) {
        this.warehouse = warehouse;
    }

    public User getDispatcher() {
        return dispatcher;
    }

    public void setDispatcher(User dispatcher) {
        this.dispatcher = dispatcher;
    }

    public String getReferenceIn() {
        return referenceIn;
    }

    public void setReferenceIn(String referenceIn) {
        this.referenceIn = referenceIn;
    }

    public String getReferenceOut() {
        return referenceOut;
    }

    public void setReferenceOut(String referenceOut) {
        this.referenceOut = referenceOut;
    }

    public String getInternalCategory() {
        return internalCategory;
    }

    public void setInternalCategory(String internalCategory) {
        this.internalCategory = internalCategory;
    }

    public String getSourceLocation() {
        return sourceLocation;
    }

    public void setSourceLocation(String sourceLocation) {
        this.sourceLocation = sourceLocation;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getOrderPaymentId(){ return orderPaymentId;}

    public void setOrderPaymentId(String orderPaymentId){
        this.orderPaymentId=orderPaymentId;
    }

    public String getProviderOrderId(){return providerOrderId; }

    public void setProviderOrderId(String providerOrderId){this.providerOrderId=providerOrderId; }

    public String getGratuityProviderOrderId() {
        return gratuityProviderOrderId;
    }

    public void setGratuityProviderOrderId(String gratuityProviderOrderId) {
        this.gratuityProviderOrderId = gratuityProviderOrderId;
    }

    public String getGratuityOrderId() { return gratuityOrderId; }

    public void setGratuityOrderId(String gratuityOrderId) {
        this.gratuityOrderId = gratuityOrderId;
    }

    public String getGratuityTransactionId(){
        return gratuityTransactionId;
    }

    public void setGratuityTransactionId(String gratuityTransactionId){
        this.gratuityTransactionId=gratuityTransactionId;
    }

    public JSONObject getOrderWalletPaymentTransaction(){
        return orderWalletPaymentTransaction;
    }

    public void setOrderWalletPaymentTransaction(JSONObject orderWalletPaymentTransaction){
        this.orderWalletPaymentTransaction=orderWalletPaymentTransaction;
    }

    public JSONObject getOrderCCPaymentTransaction(){
        return orderCCPaymentTransaction;
    }

    public void setOrderCCPaymentTransaction(JSONObject orderCCPaymentTransaction){
        this.orderCCPaymentTransaction=orderCCPaymentTransaction;
    }

    public String getGratuityAmount() {
        return gratuityAmount;
    }

    public void setGratuityAmount(String gratuityAmount) {
        this.gratuityAmount = gratuityAmount;
    }

    public String getBillOrderId() {
        return billOrderId;
    }

    public void setBillOrderId(String billOrderId) {
        this.billOrderId = billOrderId;
    }

    public Float getTotalWithGratuity() {
        return totalWithGratuity;
    }

    public void setTotalWithGratuity(Float totalWithGratuity) {
        this.totalWithGratuity = totalWithGratuity;
    }

    public String getInaiRedirectionUrl() {return inaiRedirectionUrl;}

    public void setInaiRedirectionUrl(String inaiRedirectionUrl) {this.inaiRedirectionUrl = inaiRedirectionUrl;}

    public String getCcTransactionId() {return ccTransactionId;}

    public void setCcTransactionId(String ccTransactionId) {this.ccTransactionId = ccTransactionId;}

    public String getWalletTransactionId() {return walletTransactionId;}

    public void setWalletTransactionId(String walletTransactionId) {this.walletTransactionId = walletTransactionId;}

    public String getCcGratuityTransactionId() {return ccGratuityTransactionId;}

    public void setCcGratuityTransactionId(String ccGratuityTransactionId) {
        this.ccGratuityTransactionId = ccGratuityTransactionId;
    }

    public void setKitchenPickup(boolean kitchenPickup) {
        this.kitchenPickup = kitchenPickup;
    }

    public boolean isKitchenPickup() {
        return kitchenPickup;
    }

    public void setPharmacyPickup(boolean pharmacyPickup) {
        this.pharmacyPickup = pharmacyPickup;
    }

    public boolean isPharmacyPickup() {
        return pharmacyPickup;
    }

    public String getRefundTransactionId() {
        return refundTransactionId;
    }

    public void setRefundTransactionId(String refundTransactionId) {
        this.refundTransactionId = refundTransactionId;
    }

    public String getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public boolean isScheduled() {
        return isScheduled;
    }

    public void setScheduled(boolean scheduled) {
        isScheduled = scheduled;
    }

    public String getTopUpOrderId() {
        return topUpOrderId;
    }

    public void setTopUpOrderId(String topUpOrderId) {
        this.topUpOrderId = topUpOrderId;
    }

    public String getOrderCouponName() {return orderCouponName;}

    public void setOrderCouponName(String orderCouponName) {this.orderCouponName = orderCouponName;}

    public List<Product> getOrderProducts() {return orderProducts;}

    public void setOrderProducts(List<Product> orderProducts) {this.orderProducts = orderProducts;}

    public double getBalanceUsedInOrder() {return balanceUsedInOrder;}

    public void setBalanceUsedInOrder(double balanceUsedInOrder) {this.balanceUsedInOrder = balanceUsedInOrder;}

    public double getTotalDueAmount() {return totalDueAmount;}

    public void setTotalDueAmount(double totalDueAmount) {this.totalDueAmount = totalDueAmount;}

    public boolean getIsOrderTimeSlotShifted() {return isOrderTimeSlotShifted;}

    public void setIsOrderTimeSlotShifted(boolean isOrderTimeSlotShifted) {this.isOrderTimeSlotShifted = isOrderTimeSlotShifted;}

    public String getCaptureMethod() {return captureMethod;}

    public void setCaptureMethod(String captureMethod) {this.captureMethod = captureMethod;}

    public boolean getIsScheduledExpress() {return isScheduledExpress;}

    public void setIsScheduledExpress(boolean isScheduledExpress) {this.isScheduledExpress = isScheduledExpress;}

    public String getPromisedTime() {return promisedTime;}

    public void setPromisedTime(String promisedTime) {this.promisedTime = promisedTime;}

    public float getDeliveryFeesInFeesObject() {return deliveryFeesInFeesObject;}

    public void setDeliveryFeesInFeesObject(float deliveryFeesInFeesObject) {this.deliveryFeesInFeesObject
            = deliveryFeesInFeesObject;}

    public double getDiscountFeesInFeesObject() {return discountFeesInFeesObject;}

    public void setDiscountFeesInFeesObject(double discountFeesInFeesObject) {this.discountFeesInFeesObject
            = discountFeesInFeesObject;}

    public double getReferralDiscountInFeesObject() {
        return referralDiscountInFeesObject;
    }

    public void setReferralDiscountInFeesObject(double referralDiscountInFeesObject) {
        this.referralDiscountInFeesObject = referralDiscountInFeesObject;
    }

    public double getServiceFeesInFeesObject() {
        return serviceFeesInFeesObject;
    }

    public void setServiceFeesInFeesObject(double serviceFeesInFeesObject) {
        this.serviceFeesInFeesObject = serviceFeesInFeesObject;
    }

    public String getGratuityFundsUsed() {
        return gratuityFundsUsed;
    }

    public void setGratuityFundsUsed(String gratuityFundsUsed) {
        this.gratuityFundsUsed = gratuityFundsUsed;
    }

    public int getServiceFees() {return serviceFees;}

    public void setServiceFees(int serviceFees) {this.serviceFees = serviceFees;}

    public OrderPaymentTransactions getOrderPaymentTransactions() {
        return orderPaymentTransactions;
    }

    public void setOrderPaymentTransactions(OrderPaymentTransactions orderPaymentTransactions) {
        this.orderPaymentTransactions = orderPaymentTransactions;
    }

    public float getTotalToCollect() {
        return totalToCollect;
    }

    public void setTotalToCollect(float totalToCollect) {
        this.totalToCollect = totalToCollect;
    }

    public String getDeliveryNote() {
        return deliveryNote;
    }

    public void setDeliveryNote(String deliveryNote) {
        this.deliveryNote = deliveryNote;
    }
    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    public boolean isPaidByInai() {return isPaidByInai;}

    public void setPaidByInai(boolean paidByInai) {isPaidByInai = paidByInai;}

    public boolean isCredit() {return isCredit;}

    public void setCredit(boolean credit) {isCredit = credit;}
    public String getApsRedirectionURL() {
        return apsRedirectionURL;
    }

    public void setApsRedirectionURL(String apsRedirectionURL) {
        this.apsRedirectionURL = apsRedirectionURL;
    }
}
