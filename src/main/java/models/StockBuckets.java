package models;

public class StockBuckets {
    private int onApp;
    private int reserved;
    private int fpStock;
    private int notSellable;
    private int missing;
    private int totalLiability;

    public int getOnApp() {
        return onApp;
    }

    public void setOnApp(int onApp) {
        this.onApp = onApp;
    }

    public int getReserved() {
        return reserved;
    }

    public void setReserved(int reserved) {
        this.reserved = reserved;
    }

    public int getFpStock() {
        return fpStock;
    }

    public void setFpStock(int fpStock) {
        this.fpStock = fpStock;
    }

    public int getNotSellable() {
        return notSellable;
    }

    public void setNotSellable(int notSellable) {
        this.notSellable = notSellable;
    }

    public int getMissing() {
        return missing;
    }

    public void setMissing(int missing) {
        this.missing = missing;
    }

    public int getTotalLiability() {
        return totalLiability;
    }

    public void setTotalLiability(int totalLiability) {
        this.totalLiability = totalLiability;
    }
}
