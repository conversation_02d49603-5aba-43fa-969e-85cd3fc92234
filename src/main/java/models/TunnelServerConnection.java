package models;

import com.github.alexdlaird.ngrok.NgrokClient;
import com.github.alexdlaird.ngrok.protocol.Tunnel;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;

public class TunnelServerConnection {
    private NgrokClient client;
    private Tunnel tunnel;
    private SpringApplication server;
    private ConfigurableApplicationContext context;

    public NgrokClient getClient() {
        return client;
    }

    public void setClient(NgrokClient client) {
        this.client = client;
    }

    public Tunnel getTunnel() {
        return tunnel;
    }

    public void setTunnel(Tunnel tunnel) {
        this.tunnel = tunnel;
    }

    public SpringApplication getServer() {
        return server;
    }

    public void setServer(SpringApplication server) {
        this.server = server;
    }

    public ConfigurableApplicationContext getContext() {
        return context;
    }

    public void setContext(ConfigurableApplicationContext context) {
        this.context = context;
    }
}
