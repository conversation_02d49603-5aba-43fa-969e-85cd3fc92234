package models;

public class OrderPaymentTransactions {

    String orderPaymentTransactionId;
    String clientOrderId;
    String orderPaymentTransactionStatus;
    String orderPaymentTransactionAmount;
    String orderPaymentTransactionType;
    String gratuityTransactionId;
    String gratuityClientOrderId;
    String gratuityTransactionStatus;
    String refundGratuityTransactionId;
    String refundGratuityClientOrderId;
    String refundGratuityOrderTransactionStatus;
    String refundGratuityOrderTransactionType;
    String refundGratuityOrderTransactionAmount;
    String refundGratuityProviderName;
    String refundGratuityProviderAmount;
    String chargeGratuityProviderName;
    String chargeGratuityProviderAmount;
    String chargeGratuityOrderTransactionType;
    String chargeWalletPaymentTransactionId;
    String chargeWalletPaymentTransactionStatus;
    String chargeWalletPaymentTransactionType;
    float chargeWalletPaymentTransactionAmount;
    String chargeCCPaymentTransactionId;
    String chargeCCPaymentTransactionStatus;
    String chargeCCPaymentTransactionType;
    float chargeCCPaymentTransactionAmount;
    String chargeCCExternalTransactionId;
    String refundWalletPaymentTransactionId;
    String refundWalletPaymentTransactionStatus;
    String refundWalletPaymentTransactionType;
    float refundWalletPaymentTransactionAmount;
    String refundCCPaymentTransactionId;
    String refundCCPaymentTransactionStatus;
    String refundCCPaymentTransactionType;
    float refundCCPaymentTransactionAmount;

    public String getOrderPaymentTransactionId() {
        return orderPaymentTransactionId;
    }

    public void setOrderPaymentTransactionId(String orderPaymentTransactionId) {
        this.orderPaymentTransactionId = orderPaymentTransactionId;
    }

    public String getClientOrderId() {
        return clientOrderId;
    }

    public void setClientOrderId(String clientOrderId) {
        this.clientOrderId = clientOrderId;
    }

    public String getOrderPaymentTransactionStatus() {
        return orderPaymentTransactionStatus;
    }

    public void setOrderPaymentTransactionStatus(String orderPaymentTransactionStatus) {
        this.orderPaymentTransactionStatus = orderPaymentTransactionStatus;
    }

    public String getOrderPaymentTransactionAmount() {
        return orderPaymentTransactionAmount;
    }

    public void setOrderPaymentTransactionAmount(String orderPaymentTransactionAmount) {
        this.orderPaymentTransactionAmount = orderPaymentTransactionAmount;
    }

    public String getOrderPaymentTransactionType() {
        return orderPaymentTransactionType;
    }

    public void setOrderPaymentTransactionType(String orderPaymentTransactionType) {
        this.orderPaymentTransactionType = orderPaymentTransactionType;
    }

    public String getChargeWalletPaymentTransactionId() {
        return chargeWalletPaymentTransactionId;
    }

    public void setChargeWalletPaymentTransactionId(String chargeWalletPaymentTransactionId) {
        this.chargeWalletPaymentTransactionId = chargeWalletPaymentTransactionId;
    }

    public String getChargeWalletPaymentTransactionStatus() {
        return chargeWalletPaymentTransactionStatus;
    }

    public void setChargeWalletPaymentTransactionStatus(String chargeWalletPaymentTransactionStatus) {
        this.chargeWalletPaymentTransactionStatus = chargeWalletPaymentTransactionStatus;
    }

    public String getChargeWalletPaymentTransactionType() {
        return chargeWalletPaymentTransactionType;
    }

    public void setChargeWalletPaymentTransactionType(String chargeWalletPaymentTransactionType) {
        this.chargeWalletPaymentTransactionType = chargeWalletPaymentTransactionType;
    }

    public float getChargeWalletPaymentTransactionAmount() {
        return chargeWalletPaymentTransactionAmount;
    }

    public void setChargeWalletPaymentTransactionAmount(float chargeWalletPaymentTransactionAmount) {
        this.chargeWalletPaymentTransactionAmount = chargeWalletPaymentTransactionAmount;
    }

    public String getChargeCCPaymentTransactionId() {
        return chargeCCPaymentTransactionId;
    }

    public void setChargeCCPaymentTransactionId(String chargeCCPaymentTransactionId) {
        this.chargeCCPaymentTransactionId = chargeCCPaymentTransactionId;
    }

    public String getChargeCCPaymentTransactionStatus() {
        return chargeCCPaymentTransactionStatus;
    }

    public void setChargeCCPaymentTransactionStatus(String chargeCCPaymentTransactionStatus) {
        this.chargeCCPaymentTransactionStatus = chargeCCPaymentTransactionStatus;
    }

    public String getChargeCCPaymentTransactionType() {
        return chargeCCPaymentTransactionType;
    }

    public void setChargeCCPaymentTransactionType(String chargeCCPaymentTransactionType) {
        this.chargeCCPaymentTransactionType = chargeCCPaymentTransactionType;
    }

    public float getChargeCCPaymentTransactionAmount() {
        return chargeCCPaymentTransactionAmount;
    }

    public void setChargeCCPaymentTransactionAmount(float chargeCCPaymentTransactionAmount) {
        this.chargeCCPaymentTransactionAmount = chargeCCPaymentTransactionAmount;
    }

    public String getChargeCCExternalTransactionId() {
        return chargeCCExternalTransactionId;
    }

    public void setChargeCCExternalTransactionId(String chargeCCExternalTransactionId) {
        this.chargeCCExternalTransactionId = chargeCCExternalTransactionId;
    }

    public String getRefundWalletPaymentTransactionId() {
        return refundWalletPaymentTransactionId;
    }

    public void setRefundWalletPaymentTransactionId(String refundWalletPaymentTransactionId) {
        this.refundWalletPaymentTransactionId = refundWalletPaymentTransactionId;
    }

    public String getRefundWalletPaymentTransactionStatus() {
        return refundWalletPaymentTransactionStatus;
    }

    public void setRefundWalletPaymentTransactionStatus(String refundWalletPaymentTransactionStatus) {
        this.refundWalletPaymentTransactionStatus = refundWalletPaymentTransactionStatus;
    }

    public String getRefundWalletPaymentTransactionType() {
        return refundWalletPaymentTransactionType;
    }

    public void setRefundWalletPaymentTransactionType(String refundWalletPaymentTransactionType) {
        this.refundWalletPaymentTransactionType = refundWalletPaymentTransactionType;
    }

    public float getRefundWalletPaymentTransactionAmount() {
        return refundWalletPaymentTransactionAmount;
    }

    public void setRefundWalletPaymentTransactionAmount(float refundWalletPaymentTransactionAmount) {
        this.refundWalletPaymentTransactionAmount = refundWalletPaymentTransactionAmount;
    }

    public String getRefundCCPaymentTransactionId() {
        return refundCCPaymentTransactionId;
    }

    public void setRefundCCPaymentTransactionId(String refundCCPaymentTransactionId) {
        this.refundCCPaymentTransactionId = refundCCPaymentTransactionId;
    }

    public String getRefundCCPaymentTransactionStatus() {
        return refundCCPaymentTransactionStatus;
    }

    public void setRefundCCPaymentTransactionStatus(String refundCCPaymentTransactionStatus) {
        this.refundCCPaymentTransactionStatus = refundCCPaymentTransactionStatus;
    }

    public String getRefundCCPaymentTransactionType() {
        return refundCCPaymentTransactionType;
    }

    public void setRefundCCPaymentTransactionType(String refundCCPaymentTransactionType) {
        this.refundCCPaymentTransactionType = refundCCPaymentTransactionType;
    }

    public float getRefundCCPaymentTransactionAmount() {
        return refundCCPaymentTransactionAmount;
    }

    public void setRefundCCPaymentTransactionAmount(float refundCCPaymentTransactionAmount) {
        this.refundCCPaymentTransactionAmount = refundCCPaymentTransactionAmount;
    }

    public String getRefundGratuityTransactionId() {
        return refundGratuityTransactionId;
    }

    public void setRefundGratuityTransactionId(String refundGratuityTransactionId) {
        this.refundGratuityTransactionId = refundGratuityTransactionId;
    }

    public String getRefundGratuityClientOrderId() {
        return refundGratuityClientOrderId;
    }

    public void setRefundGratuityClientOrderId(String refundGratuityClientOrderId) {
        this.refundGratuityClientOrderId = refundGratuityClientOrderId;
    }

    public String getRefundGratuityOrderTransactionStatus() {
        return refundGratuityOrderTransactionStatus;
    }

    public void setRefundGratuityOrderTransactionStatus(String refundGratuityOrderTransactionStatus) {
        this.refundGratuityOrderTransactionStatus = refundGratuityOrderTransactionStatus;
    }

    public String getRefundGratuityOrderTransactionAmount() {
        return refundGratuityOrderTransactionAmount;
    }

    public void setRefundGratuityOrderTransactionAmount(String refundGratuityOrderTransactionAmount) {
        this.refundGratuityOrderTransactionAmount = refundGratuityOrderTransactionAmount;
    }

    public String getRefundGratuityProviderName() {
        return refundGratuityProviderName;
    }

    public void setRefundGratuityProviderName(String refundGratuityProviderName) {
        this.refundGratuityProviderName = refundGratuityProviderName;
    }

    public String getChargeGratuityProviderName() {
        return chargeGratuityProviderName;
    }

    public void setChargeGratuityProviderName(String chargeGratuityProviderName) {
        this.chargeGratuityProviderName = chargeGratuityProviderName;
    }

    public String getRefundGratuityProviderAmount() {
        return refundGratuityProviderAmount;
    }

    public void setRefundGratuityProviderAmount(String refundGratuityProviderAmount) {
        this.refundGratuityProviderAmount = refundGratuityProviderAmount;
    }

    public String getChargeGratuityProviderAmount() {
        return chargeGratuityProviderAmount;
    }

    public void setChargeGratuityProviderAmount(String chargeGratuityProviderAmount) {
        this.chargeGratuityProviderAmount = chargeGratuityProviderAmount;
    }

    public String getRefundGratuityOrderTransactionType() {
        return refundGratuityOrderTransactionType;
    }

    public void setRefundGratuityOrderTransactionType(String refundGratuityOrderTransactionType) {
        this.refundGratuityOrderTransactionType = refundGratuityOrderTransactionType;
    }

    public String getChargeGratuityOrderTransactionType() {
        return chargeGratuityOrderTransactionType;
    }

    public void setChargeGratuityOrderTransactionType(String chargeGratuityOrderTransactionType) {
        this.chargeGratuityOrderTransactionType = chargeGratuityOrderTransactionType;
    }

    public String getGratuityTransactionId() {
        return gratuityTransactionId;
    }

    public void setGratuityTransactionId(String gratuityTransactionId) {
        this.gratuityTransactionId = gratuityTransactionId;
    }

    public String getGratuityClientOrderId() {
        return gratuityClientOrderId;
    }

    public void setGratuityClientOrderId(String gratuityClientOrderId) {
        this.gratuityClientOrderId = gratuityClientOrderId;
    }

    public String getGratuityTransactionStatus() {
        return gratuityTransactionStatus;
    }

    public void setGratuityTransactionStatus(String gratuityTransactionStatus) {
        this.gratuityTransactionStatus = gratuityTransactionStatus;
    }
}
