package models;

public class Batch {

    private int id;
    private int batchId;
    private int qty;
    private int totalQty;
    private int available;
    private int reserved;
    private String name;
    private String expiryDate;
    private String productionDate;
    private String bestBefore;
    private String removalDate;
    private int shelfLife;
    private int daysForOOV;
    private int daysForExpiry;
    private boolean hasWrongBatch;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getBatchId() {
        return batchId;
    }

    public void setBatchId(int batchId) {
        this.batchId = batchId;
    }

    public int getQty() {
        return qty;
    }

    public void setQty(int qty) {
        this.qty = qty;
    }

    public int getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(int totalQty) {
        this.totalQty = totalQty;
    }

    public int getAvailable() {
        return available;
    }

    public void setAvailable(int available) {
        this.available = available;
    }

    public int getReserved() {
        return reserved;
    }

    public void setReserved(int reserved) {
        this.reserved = reserved;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(String productionDate) {
        this.productionDate = productionDate;
    }

    public String getBestBefore() {
        return bestBefore;
    }

    public void setBestBefore(String bestBefore) {
        this.bestBefore = bestBefore;
    }

    public String getRemovalDate() {
        return removalDate;
    }

    public void setRemovalDate(String removalDate) {
        this.removalDate = removalDate;
    }

    public int getShelfLife() {
        return shelfLife;
    }

    public void setShelfLife(int shelfLife) {
        this.shelfLife = shelfLife;
    }

    public int getDaysForOOV() {
        return daysForOOV;
    }

    public void setDaysForOOV(int daysForOOV) {
        this.daysForOOV = daysForOOV;
    }

    public int getDaysForExpiry() {
        return daysForExpiry;
    }

    public void setDaysForExpiry(int daysForExpiry) {
        this.daysForExpiry = daysForExpiry;
    }

    public boolean isHasWrongBatch() {
        return hasWrongBatch;
    }

    public void setHasWrongBatch(boolean hasWrongBatch) {
        this.hasWrongBatch = hasWrongBatch;
    }
}
