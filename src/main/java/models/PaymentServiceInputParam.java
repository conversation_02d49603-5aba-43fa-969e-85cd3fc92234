package models;

public class PaymentServiceInputParam {
    private int inputParamId;
    private String  arLabel;
    private String enLabel;
    private String arPlaceHolder;
    private String enPlaceHolder;
    private String type;
    private int minimumLength;
    private int maximumLength;
    private String createdAt;
    private String updatedAt;

    public int getInputParamId() {
        return inputParamId;
    }

    public void setInputParamId(int inputParamId) {
        this.inputParamId = inputParamId;
    }

    public String getArLabel() {
        return arLabel;
    }

    public void setArLabel(String arLabel) {
        this.arLabel = arLabel;
    }

    public String getEnLabel() {
        return enLabel;
    }

    public void setEnLabel(String enLabel) {
        this.enLabel = enLabel;
    }

    public String getArPlaceHolder() {
        return arPlaceHolder;
    }

    public void setArPlaceHolder(String arPlaceHolder) {
        this.arPlaceHolder = arPlaceHolder;
    }

    public String getEnPlaceHolder() {
        return enPlaceHolder;
    }

    public void setEnPlaceHolder(String enPlaceHolder) {
        this.enPlaceHolder = enPlaceHolder;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getMinimumLength() {
        return minimumLength;
    }

    public void setMinimumLength(int minimumLength) {
        this.minimumLength = minimumLength;
    }

    public int getMaximumLength() {
        return maximumLength;
    }

    public void setMaximumLength(int maximumLength) {
        this.maximumLength = maximumLength;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
}
