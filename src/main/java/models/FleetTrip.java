package models;

import java.util.List;
import java.util.Map;

public class FleetTrip {
    private int numberOfAssignedTrips;

    private int tripId;
    private String tripNumber;
    private int numberOfAssignedOrders;
    private List<FleetTripDetails> onGoing;
    private List<FleetTripDetails> upComing;

    private List<AssignedTrip> assignedTrips;
    private CurrentTrip currentTrip;

    public int getTripId() {
        return tripId;
    }

    public void setTripId(int tripId) {
        this.tripId = tripId;
    }

    public String getTripNumber() {
        return tripNumber;
    }

    public void setTripNumber(String tripNumber) {
        this.tripNumber = tripNumber;
    }
    public int getNumberOfAssignedTrips() {
        return numberOfAssignedTrips;
    }

    public void setNumberOfAssignedTrips(int numberOfAssignedTrips) {
        this.numberOfAssignedTrips = numberOfAssignedTrips;
    }

    public int getNumberOfAssignedOrders() {
        return numberOfAssignedOrders;
    }

    public void setNumberOfAssignedOrders(int numberOfAssignedOrders) {
        this.numberOfAssignedOrders = numberOfAssignedOrders;
    }

    public List<FleetTripDetails> getOnGoing() {
        return onGoing;
    }

    public void setOnGoing(List<FleetTripDetails> onGoing) {
        this.onGoing = onGoing;
    }

    public List<FleetTripDetails> getUpComing() {
        return upComing;
    }

    public void setUpComing(List<FleetTripDetails> upComing) {
        this.upComing = upComing;
    }

    public static class FleetTripDetails {
        private int tripId;
        private String tripNumber;
        private List<Order> orders;

        public int getTripId() {
            return tripId;
        }

        public void setTripId(int id) {
            this.tripId = id;
        }

        public String getTripNumber() {
            return tripNumber;
        }

        public void setTripNumber(String tripNumber) {
            this.tripNumber = tripNumber;
        }

        public List<Order> getOrders() {
            return orders;
        }

        public void setOrders(List<Order> orders) {
            this.orders = orders;
        }
    }

    public List<AssignedTrip> getAssignedTrips() {
        return assignedTrips;
    }

    public void setAssignedTrips(List<AssignedTrip> assignedTrips) {
        this.assignedTrips = assignedTrips;
    }

    public CurrentTrip getCurrentTrip() {
        return currentTrip;
    }

    public void setCurrentTrip(CurrentTrip currentTrip) {
        this.currentTrip = currentTrip;
    }

    public static class AssignedTrip {
        private String tripReference;
        private int statusId;

        private int tripId;
        private Action action;
        private List<String> areas;
        private List<Pickup> pickups;

        public String getTripReference() {
            return tripReference;
        }

        public void setTripReference(String tripReference) {
            this.tripReference = tripReference;
        }

        public int getStatusId() {
            return statusId;
        }

        public void setStatusId(int statusId) {
            this.statusId = statusId;
        }

        public int getTripId() {
            return tripId;
        }

        public void setTripId(int tripId) {
            this.tripId = tripId;
        }

        public Action getAction() {
            return action;
        }

        public void setAction(Action action) {
            this.action = action;
        }

        public List<String> getAreas() {
            return areas;
        }

        public void setAreas(List<String> areas) {
            this.areas = areas;
        }

        public List<Pickup> getPickups() {
            return pickups;
        }

        public void setPickups(List<Pickup> pickups) {
            this.pickups = pickups;
        }

        public static class Action {
            private String name;
            private String action;
            private boolean enabled;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getAction() {
                return action;
            }

            public void setAction(String action) {
                this.action = action;
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }
        }

        public static class Pickup {
            private String id;
            private String locationType;
            private String tripId;
            private String type;
            private Integer locationTypeId;
            private Integer statusId;
            private Action action;
            private PickupDetails details;
            private String status;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getLocationType() {
                return locationType;
            }

            public void setLocationType(String locationType) {
                this.locationType = locationType;
            }

            public String getTripId() {
                return tripId;
            }

            public void setTripId(String tripId) {
                this.tripId = tripId;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public Integer getLocationTypeId() {
                return locationTypeId;
            }

            public void setLocationTypeId(Integer locationTypeId) {
                this.locationTypeId = locationTypeId;
            }

            public Integer getStatusId() {
                return statusId;
            }

            public void setStatusId(Integer statusId) {
                this.statusId = statusId;
            }

            public Action getAction() {
                return action;
            }

            public void setAction(Action action) {
                this.action = action;
            }

            public PickupDetails getDetails() {
                return details;
            }

            public void setDetails(PickupDetails details) {
                this.details = details;
            }

            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public static class PickupDetails {
                private String eta;
                private String orderNumber;
                private String timeslot;
                private String deliveryDeadlineTs;
                private String status;
                private List<Product> products;

                public String getEta() {
                    return eta;
                }

                public void setEta(String eta) {
                    this.eta = eta;
                }

                public String getOrderNumber() {
                    return orderNumber;
                }

                public void setOrderNumber(String orderNumber) {
                    this.orderNumber = orderNumber;
                }

                public String getTimeslot() {
                    return timeslot;
                }

                public void setTimeslot(String timeslot) {
                    this.timeslot = timeslot;
                }

                public String getDeliveryDeadlineTs() {
                    return deliveryDeadlineTs;
                }

                public void setDeliveryDeadlineTs(String deliveryDeadlineTs) {
                    this.deliveryDeadlineTs = deliveryDeadlineTs;
                }

                public String getStatus() {
                    return status;
                }

                public void setStatus(String status) {
                    this.status = status;
                }

                public List<Product> getProducts() {
                    return products;
                }

                public void setProducts(List<Product> products) {
                    this.products = products;
                }

                public static class Product {
                    private String nameAr;
                    private int quantity;
                    private boolean outOfBags;
                    private int internalCategoryId;
                    private boolean isCoffeeProduct;
                    private int id;
                    private String nameEn;
                    private boolean isKitchenProduct;
                    private boolean isPharmacyProduct;

                    public String getNameAr() {
                        return nameAr;
                    }

                    public void setNameAr(String nameAr) {
                        this.nameAr = nameAr;
                    }

                    public int getQuantity() {
                        return quantity;
                    }

                    public void setQuantity(int quantity) {
                        this.quantity = quantity;
                    }

                    public boolean isOutOfBags() {
                        return outOfBags;
                    }

                    public void setOutOfBags(boolean outOfBags) {
                        this.outOfBags = outOfBags;
                    }

                    public int getInternalCategoryId() {
                        return internalCategoryId;
                    }

                    public void setInternalCategoryId(int internalCategoryId) {
                        this.internalCategoryId = internalCategoryId;
                    }

                    public boolean isCoffeeProduct() {
                        return isCoffeeProduct;
                    }

                    public void setCoffeeProduct(boolean isCoffeeProduct) {
                        this.isCoffeeProduct = isCoffeeProduct;
                    }

                    public int getId() {
                        return id;
                    }

                    public void setId(int id) {
                        this.id = id;
                    }

                    public String getNameEn() {
                        return nameEn;
                    }

                    public void setNameEn(String nameEn) {
                        this.nameEn = nameEn;
                    }

                    public boolean isKitchenProduct() {
                        return isKitchenProduct;
                    }

                    public void setKitchenProduct(boolean isKitchenProduct) {
                        this.isKitchenProduct = isKitchenProduct;
                    }

                    public boolean isPharmacyProduct() {
                        return isPharmacyProduct;
                    }

                    public void setPharmacyProduct(boolean isPharmacyProduct) {
                        this.isPharmacyProduct = isPharmacyProduct;
                    }
                }
            }
        }
    }

    public static class CurrentTrip {
        private String tripId;
        private String tripReference;
        private int statusId;
        private Action action;
        private List<Order> orders;
        private List<String> areas;
        private List<Pickup> pickups;

        public List<Delivery> getDeliveries() {
            return deliveries;
        }

        public void setDeliveries(List<Delivery> deliveries) {
            this.deliveries = deliveries;
        }

        private List<Delivery> deliveries;
        private String status;

        public String getTripId() {
            return tripId;
        }

        public void setTripId(String tripId) {
            this.tripId = tripId;
        }

        public String getTripReference() {
            return tripReference;
        }

        public void setTripReference(String tripReference) {
            this.tripReference = tripReference;
        }

        public int getStatusId() {
            return statusId;
        }

        public void setStatusId(int statusId) {
            this.statusId = statusId;
        }

        public Action getAction() {
            return action;
        }

        public void setAction(Action action) {
            this.action = action;
        }

        public List<Order> getOrders() {
            return orders;
        }

        public void setOrders(List<Order> orders) {
            this.orders = orders;
        }

        public List<String> getAreas() {
            return areas;
        }

        public void setAreas(List<String> areas) {
            this.areas = areas;
        }

        public List<Pickup> getPickups() {
            return pickups;
        }

        public void setPickups(List<Pickup> pickups) {
            this.pickups = pickups;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public static class Action {
            private String name;
            private String action;
            private boolean enabled;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getAction() {
                return action;
            }

            public void setAction(String action) {
                this.action = action;
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }
        }

        public static class Order {
            private boolean isPackaged;
            private String orderNumber;

            public boolean isPackaged() {
                return isPackaged;
            }

            public void setPackaged(boolean isPackaged) {
                this.isPackaged = isPackaged;
            }

            public String getOrderNumber() {
                return orderNumber;
            }

            public void setOrderNumber(String orderNumber) {
                this.orderNumber = orderNumber;
            }
        }

        public static class Pickup {
            private String id;
            private String locationType;
            private String tripId;
            private String type;
            private Integer locationTypeId;
            private Integer statusId;
            private Action action;
            private PickupDetails details;
            private String status;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getLocationType() {
                return locationType;
            }

            public void setLocationType(String locationType) {
                this.locationType = locationType;
            }

            public String getTripId() {
                return tripId;
            }

            public void setTripId(String tripId) {
                this.tripId = tripId;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public Integer getLocationTypeId() {
                return locationTypeId;
            }

            public void setLocationTypeId(Integer locationTypeId) {
                this.locationTypeId = locationTypeId;
            }

            public Integer getStatusId() {
                return statusId;
            }

            public void setStatusId(Integer statusId) {
                this.statusId = statusId;
            }

            public Action getAction() {
                return action;
            }

            public void setAction(Action action) {
                this.action = action;
            }

            public PickupDetails getDetails() {
                return details;
            }

            public void setDetails(PickupDetails details) {
                this.details = details;
            }

            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public static class PickupDetails {
                private String eta;
                private String orderNumber;
                private String timeslot;
                private String deliveryDeadlineTs;
                private String status;
                private List<Product> products;

                public String getEta() {
                    return eta;
                }

                public void setEta(String eta) {
                    this.eta = eta;
                }

                public String getOrderNumber() {
                    return orderNumber;
                }

                public void setOrderNumber(String orderNumber) {
                    this.orderNumber = orderNumber;
                }

                public String getTimeslot() {
                    return timeslot;
                }

                public void setTimeslot(String timeslot) {
                    this.timeslot = timeslot;
                }

                public String getDeliveryDeadlineTs() {
                    return deliveryDeadlineTs;
                }

                public void setDeliveryDeadlineTs(String deliveryDeadlineTs) {
                    this.deliveryDeadlineTs = deliveryDeadlineTs;
                }

                public String getStatus() {
                    return status;
                }

                public void setStatus(String status) {
                    this.status = status;
                }

                public List<Product> getProducts() {
                    return products;
                }

                public void setProducts(List<Product> products) {
                    this.products = products;
                }
            }
        }

        public static class Delivery {
            private boolean pendingOrder;
            private long orderId;
            private String locationType;
            private String tripId;
            private String type;
            private int donePickupTasksCount;
            private List<String> orderPickups;
            private int locationTypeId;
            private int statusId;
            private Long unreachableCustomerTimestamp;
            private Action action;
            private int typeId;
            private Details details;
            private String id;
            private int pickupTasksCount;
            private String status;
            private Action unreachableAction;

            public boolean isPendingOrder() {
                return pendingOrder;
            }

            public void setPendingOrder(boolean pendingOrder) {
                this.pendingOrder = pendingOrder;
            }

            public long getOrderId() {
                return orderId;
            }

            public void setOrderId(long orderId) {
                this.orderId = orderId;
            }

            public String getLocationType() {
                return locationType;
            }

            public void setLocationType(String locationType) {
                this.locationType = locationType;
            }

            public String getTripId() {
                return tripId;
            }

            public void setTripId(String tripId) {
                this.tripId = tripId;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public int getDonePickupTasksCount() {
                return donePickupTasksCount;
            }

            public void setDonePickupTasksCount(int donePickupTasksCount) {
                this.donePickupTasksCount = donePickupTasksCount;
            }

            public List<String> getOrderPickups() {
                return orderPickups;
            }

            public void setOrderPickups(List<String> orderPickups) {
                this.orderPickups = orderPickups;
            }

            public int getLocationTypeId() {
                return locationTypeId;
            }

            public void setLocationTypeId(int locationTypeId) {
                this.locationTypeId = locationTypeId;
            }

            public int getStatusId() {
                return statusId;
            }

            public void setStatusId(int statusId) {
                this.statusId = statusId;
            }

            public Long getUnreachableCustomerTimestamp() {
                return unreachableCustomerTimestamp;
            }

            public void setUnreachableCustomerTimestamp(Long unreachableCustomerTimestamp) {
                this.unreachableCustomerTimestamp = unreachableCustomerTimestamp;
            }

            public Action getAction() {
                return action;
            }

            public void setAction(Action action) {
                this.action = action;
            }

            public int getTypeId() {
                return typeId;
            }

            public void setTypeId(int typeId) {
                this.typeId = typeId;
            }

            public Details getDetails() {
                return details;
            }

            public void setDetails(Details details) {
                this.details = details;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public int getPickupTasksCount() {
                return pickupTasksCount;
            }

            public void setPickupTasksCount(int pickupTasksCount) {
                this.pickupTasksCount = pickupTasksCount;
            }

            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public Action getUnreachableAction() {
                return unreachableAction;
            }

            public void setUnreachableAction(Action unreachableAction) {
                this.unreachableAction = unreachableAction;
            }

            public static class Details {
                private String cxNotes;
                private String amount;
                private Address address;
                private String orderNumber;
                private List<Bag> bags;
                private int pickingStatus;
                private boolean isExternalKitchen;
                private String customerNotes;
                private boolean hasFragileItems;
                private boolean hasFrozenItems;
                private String eta;
                private String totalToCollect;
                private String fpId;
                private CashCollection cashCollection;
                private String timeslot;
                private String deliveryDeadlineTs;
                private int shelfNumber;
                private String subArea;
                private List<Location> locations;
                private long id;
                private String status;

                public String getCxNotes() {
                    return cxNotes;
                }

                public void setCxNotes(String cxNotes) {
                    this.cxNotes = cxNotes;
                }

                public String getAmount() {
                    return amount;
                }

                public void setAmount(String amount) {
                    this.amount = amount;
                }

                public Address getAddress() {
                    return address;
                }

                public void setAddress(Address address) {
                    this.address = address;
                }

                public String getOrderNumber() {
                    return orderNumber;
                }

                public void setOrderNumber(String orderNumber) {
                    this.orderNumber = orderNumber;
                }

                public List<Bag> getBags() {
                    return bags;
                }

                public void setBags(List<Bag> bags) {
                    this.bags = bags;
                }

                public int getPickingStatus() {
                    return pickingStatus;
                }

                public void setPickingStatus(int pickingStatus) {
                    this.pickingStatus = pickingStatus;
                }

                public boolean isExternalKitchen() {
                    return isExternalKitchen;
                }

                public void setExternalKitchen(boolean externalKitchen) {
                    isExternalKitchen = externalKitchen;
                }

                public String getCustomerNotes() {
                    return customerNotes;
                }

                public void setCustomerNotes(String customerNotes) {
                    this.customerNotes = customerNotes;
                }

                public boolean isHasFragileItems() {
                    return hasFragileItems;
                }

                public void setHasFragileItems(boolean hasFragileItems) {
                    this.hasFragileItems = hasFragileItems;
                }

                public boolean isHasFrozenItems() {
                    return hasFrozenItems;
                }

                public void setHasFrozenItems(boolean hasFrozenItems) {
                    this.hasFrozenItems = hasFrozenItems;
                }

                public String getEta() {
                    return eta;
                }

                public void setEta(String eta) {
                    this.eta = eta;
                }

                public String getTotalToCollect() {
                    return totalToCollect;
                }

                public void setTotalToCollect(String totalToCollect) {
                    this.totalToCollect = totalToCollect;
                }

                public String getFpId() {
                    return fpId;
                }

                public void setFpId(String fpId) {
                    this.fpId = fpId;
                }

                public CashCollection getCashCollection() {
                    return cashCollection;
                }

                public void setCashCollection(CashCollection cashCollection) {
                    this.cashCollection = cashCollection;
                }

                public String getTimeslot() {
                    return timeslot;
                }

                public void setTimeslot(String timeslot) {
                    this.timeslot = timeslot;
                }

                public String getDeliveryDeadlineTs() {
                    return deliveryDeadlineTs;
                }

                public void setDeliveryDeadlineTs(String deliveryDeadlineTs) {
                    this.deliveryDeadlineTs = deliveryDeadlineTs;
                }

                public int getShelfNumber() {
                    return shelfNumber;
                }

                public void setShelfNumber(int shelfNumber) {
                    this.shelfNumber = shelfNumber;
                }

                public String getSubArea() {
                    return subArea;
                }

                public void setSubArea(String subArea) {
                    this.subArea = subArea;
                }

                public List<Location> getLocations() {
                    return locations;
                }

                public void setLocations(List<Location> locations) {
                    this.locations = locations;
                }

                public long getId() {
                    return id;
                }

                public void setId(long id) {
                    this.id = id;
                }

                public String getStatus() {
                    return status;
                }

                public void setStatus(String status) {
                    this.status = status;
                }

                public Customer getCustomer() {
                    return customer;
                }

                public void setCustomer(Customer customer) {
                    this.customer = customer;
                }

                public boolean isHasCoffeeItems() {
                    return hasCoffeeItems;
                }

                public void setHasCoffeeItems(boolean hasCoffeeItems) {
                    this.hasCoffeeItems = hasCoffeeItems;
                }

                private Customer customer;
                private boolean hasCoffeeItems;

                public static class Address {
                    private String flatNumber;
                    private boolean confirmAddressLocation;
                    private String floorNumber;
                    private String fullAddress;
                    private Location location;

                    public String getFlatNumber() {
                        return flatNumber;
                    }

                    public void setFlatNumber(String flatNumber) {
                        this.flatNumber = flatNumber;
                    }

                    public boolean isConfirmAddressLocation() {
                        return confirmAddressLocation;
                    }

                    public void setConfirmAddressLocation(boolean confirmAddressLocation) {
                        this.confirmAddressLocation = confirmAddressLocation;
                    }

                    public String getFloorNumber() {
                        return floorNumber;
                    }

                    public void setFloorNumber(String floorNumber) {
                        this.floorNumber = floorNumber;
                    }

                    public String getFullAddress() {
                        return fullAddress;
                    }

                    public void setFullAddress(String fullAddress) {
                        this.fullAddress = fullAddress;
                    }

                    public Location getLocation() {
                        return location;
                    }

                    public void setLocation(Location location) {
                        this.location = location;
                    }

                    public String getDeliveryNotes() {
                        return deliveryNotes;
                    }

                    public void setDeliveryNotes(String deliveryNotes) {
                        this.deliveryNotes = deliveryNotes;
                    }

                    private String deliveryNotes;

                }

                public static class Location {
                    private double lng;
                    private double lat;

                    public double getLng() {
                        return lng;
                    }

                    public void setLng(double lng) {
                        this.lng = lng;
                    }

                    public double getLat() {
                        return lat;
                    }

                    public void setLat(double lat) {
                        this.lat = lat;
                    }

                    public String getId() {
                        return id;
                    }

                    public void setId(String id) {
                        this.id = id;
                    }

                    private String id;

                }

                public static class Bag {
                    private String nameAr;
                    private int quantity;
                    private int id;

                    public String getNameAr() {
                        return nameAr;
                    }

                    public void setNameAr(String nameAr) {
                        this.nameAr = nameAr;
                    }

                    public int getQuantity() {
                        return quantity;
                    }

                    public void setQuantity(int quantity) {
                        this.quantity = quantity;
                    }

                    public int getId() {
                        return id;
                    }

                    public void setId(int id) {
                        this.id = id;
                    }

                    public String getNameEn() {
                        return nameEn;
                    }

                    public void setNameEn(String nameEn) {
                        this.nameEn = nameEn;
                    }

                    private String nameEn;

                }

                public static class CashCollection {
                    private double maxAmountToCollect;
                    private double amountToCollect;
                    private double minSafeAmountToCollect;

                    public double getMaxAmountToCollect() {
                        return maxAmountToCollect;
                    }

                    public void setMaxAmountToCollect(double maxAmountToCollect) {
                        this.maxAmountToCollect = maxAmountToCollect;
                    }

                    public double getAmountToCollect() {
                        return amountToCollect;
                    }

                    public void setAmountToCollect(double amountToCollect) {
                        this.amountToCollect = amountToCollect;
                    }

                    public double getMinSafeAmountToCollect() {
                        return minSafeAmountToCollect;
                    }

                    public void setMinSafeAmountToCollect(double minSafeAmountToCollect) {
                        this.minSafeAmountToCollect = minSafeAmountToCollect;
                    }

                    public double getMaxSafeAmountToCollect() {
                        return maxSafeAmountToCollect;
                    }

                    public void setMaxSafeAmountToCollect(double maxSafeAmountToCollect) {
                        this.maxSafeAmountToCollect = maxSafeAmountToCollect;
                    }

                    private double maxSafeAmountToCollect;

                }

                public static class Customer {
                    public String getMobileNumber() {
                        return mobileNumber;
                    }

                    public void setMobileNumber(String mobileNumber) {
                        this.mobileNumber = mobileNumber;
                    }

                    public String getName() {
                        return name;
                    }

                    public void setName(String name) {
                        this.name = name;
                    }

                    private String mobileNumber;
                    private String name;

                }
            }
        }

    }

}
