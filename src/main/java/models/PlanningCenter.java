package models;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;

public class PlanningCenter {
    private int internalCategoryId;
    private String internalCategoryName;
    private boolean isAutomated;
    private int SchedulerId;
    private int statusId;
    private int readyToSync;
    private int triedToSendBefore;
    private int internalOrderId;
    private int internalOrderProductId;
    private int adjustedQuantity;
    private JSONArray payload;

    public int getInternalCategoryId() {
        return internalCategoryId;
    }

    public void setInternalCategoryId(int internalCategoryId) {
        this.internalCategoryId = internalCategoryId;
    }

    public String getInternalCategoryName() {
        return internalCategoryName;
    }

    public void setInternalCategoryName(String internalCategoryName) {
        this.internalCategoryName = internalCategoryName;
    }

    public boolean isAutomated() {
        return isAutomated;
    }

    public void setAutomated(boolean automated) {
        isAutomated = automated;
    }

    public int getSchedulerId() {
        return SchedulerId;
    }

    public void setSchedulerId(int schedulerId) {
        SchedulerId = schedulerId;
    }
    public int getStatusId() {
        return statusId;
    }

    public void setStatusId(int statusId) {
        this.statusId = statusId;
    }

    public int getReadyToSync() {
        return readyToSync;
    }

    public void setReadyToSync(int readyToSync) {
        this.readyToSync = readyToSync;
    }

    public int getTriedToSendBefore() {
        return triedToSendBefore;
    }

    public void setTriedToSendBefore(int triedToSendBefore) {
        this.triedToSendBefore = triedToSendBefore;
    }

    public int getInternalOrderId() {
        return internalOrderId;
    }

    public void setInternalOrderId(int internalOrderId) {
        this.internalOrderId = internalOrderId;
    }

    public int getInternalOrderProductId() {
        return internalOrderProductId;
    }

    public void setInternalOrderProductId(int internalOrderProductId) {
        this.internalOrderProductId = internalOrderProductId;
    }

    public int getAdjustedQuantity() {
        return adjustedQuantity;
    }

    public void setAdjustedQuantity(int adjustedQuantity) {
        this.adjustedQuantity = adjustedQuantity;
    }

    public JSONArray getPayload() {
        return payload;
    }

    public void setPayload(JSONArray payload) {
        this.payload = payload;
    }
}
