package models;

import java.util.List;

public class FoodAggregatorTestSession {

    private List<Restaurant> restaurantsList;
    private List<Restaurant> yeloRestaurantsList;
    private List<BusinessCategory> restaurantsBusinessCategories;

    public List<Restaurant> getRestaurantsList() {
        return restaurantsList;
    }

    public void setRestaurantsList(List<Restaurant> restaurantsList) {
        this.restaurantsList = restaurantsList;
    }

    public List<Restaurant> getYeloRestaurantsList() {
        return yeloRestaurantsList;
    }

    public void setYeloRestaurantsList(List<Restaurant> yeloRestaurantsList) {
        this.yeloRestaurantsList = yeloRestaurantsList;
    }

    public List<BusinessCategory> getRestaurantsBusinessCategories() {
        return restaurantsBusinessCategories;
    }

    public void setRestaurantsBusinessCategories(List<BusinessCategory> restaurantsBusinessCategories) {
        this.restaurantsBusinessCategories = restaurantsBusinessCategories;
    }
}
