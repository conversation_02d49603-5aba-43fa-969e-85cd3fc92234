package models;

import org.json.JSONObject;

import java.util.List;

public class Warehouse {
    private JSONObject location;
    private JSONObject nowStatus;
    private JSONObject featureFlags;
    private JSONObject settings;
    private List<JSONObject> supportedFps;
    private String type;
    private String timeZone;
    private String status;
    private String onFloorAssociatesCount;
    private String deliveryAssociateCapacityPerOrder;
    private String maxDeliveryHours;
    private String id;
    private String name;
    private List<String> supportManagers;
    private List<String> deliveryAssociates;
    private JSONObject manager;
    private List<JSONObject> areas;
    private String laterSupported;
    private String nowSupported;
    private String timeSlot;
    private String timeSlotId;
    private boolean isCurrentTimeSlot;
    private double deliveryFees;
    private boolean isNotifyMeFeatureEnabled;
    private String shiftId;
    private String areaName;
    private Area area;
    private JSONObject fullWarehouseJsonObject;
    private String warehouseName;
    private List<Product> nowProductsWithPositiveStock;
    private List<Product> laterProductsWithPositiveStock;

    public JSONObject getLocation() {
        return location;
    }

    public void setLocation(JSONObject location) {
        this.location = location;
    }

    public JSONObject getNowStatus() {
        return nowStatus;
    }

    public void setNowStatus(JSONObject nowStatus) {
        this.nowStatus = nowStatus;
    }

    public JSONObject getFeatureFlags() {
        return featureFlags;
    }

    public void setFeatureFlags(JSONObject featureFlags) {
        this.featureFlags = featureFlags;
    }

    public JSONObject getSettings() {
        return settings;
    }

    public void setSettings(JSONObject settings) {
        this.settings = settings;
    }

    public List<JSONObject> getSupportedFps() {
        return supportedFps;
    }

    public void setSupportedFps(List<JSONObject> supportedFps) {
        this.supportedFps = supportedFps;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOnFloorAssociatesCount() {
        return onFloorAssociatesCount;
    }

    public void setOnFloorAssociatesCount(String onFloorAssociatesCount) {
        this.onFloorAssociatesCount = onFloorAssociatesCount;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDeliveryAssociateCapacityPerOrder() {
        return deliveryAssociateCapacityPerOrder;
    }

    public void setDeliveryAssociateCapacityPerOrder(String deliveryAssociateCapacityPerOrder) {
        this.deliveryAssociateCapacityPerOrder = deliveryAssociateCapacityPerOrder;
    }

    public String getMaxDeliveryHours() {
        return maxDeliveryHours;
    }

    public void setMaxDeliveryHours(String maxDeliveryHours) {
        this.maxDeliveryHours = maxDeliveryHours;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getSupportManagers() {
        return supportManagers;
    }

    public void setSupportManagers(List<String> supportManagers) {
        this.supportManagers = supportManagers;
    }

    public List<String> getDeliveryAssociates() {
        return deliveryAssociates;
    }

    public void setDeliveryAssociates(List<String> deliveryAssociates) {
        this.deliveryAssociates = deliveryAssociates;
    }

    public JSONObject getManager() {
        return manager;
    }

    public void setManager(JSONObject manager) {
        this.manager = manager;
    }

    public List<JSONObject> getAreas() {
        return areas;
    }

    public void setAreas(List<JSONObject> areas) {
        this.areas = areas;
    }

    public String getLaterSupported() {
        return laterSupported;
    }

    public void setLaterSupported(String laterSupported) {
        this.laterSupported = laterSupported;
    }

    public String getNowSupported() {
        return nowSupported;
    }

    public void setNowSupported(String nowSupported) {
        this.nowSupported = nowSupported;
    }

    public String getTimeSlot() {
        return timeSlot;
    }

    public void setTimeSlot(String timeSlot) {
        this.timeSlot = timeSlot;
    }

    public String getTimeSlotId() {
        return timeSlotId;
    }

    public void setTimeSlotId(String timeSlotId) {
        this.timeSlotId = timeSlotId;
    }

    public boolean isCurrentTimeSlot() {
        return isCurrentTimeSlot;
    }

    public void setCurrentTimeSlot(boolean currentTimeSlot) {
        isCurrentTimeSlot = currentTimeSlot;
    }

    public double getDeliveryFees() {
        return deliveryFees;
    }

    public void setDeliveryFees(double deliveryFees) {
        this.deliveryFees = deliveryFees;
    }

    public boolean isNotifyMeFeatureEnabled() {
        return isNotifyMeFeatureEnabled;
    }

    public void setNotifyMeFeatureEnabled(boolean notifyMeFeatureEnabled) {
        isNotifyMeFeatureEnabled = notifyMeFeatureEnabled;
    }

    public String getShiftId() {
        return shiftId;
    }

    public void setShiftId(String shiftId) {
        this.shiftId = shiftId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Area getArea() {
        return area;
    }

    public void setArea(Area area) {
        this.area = area;
    }

    public void setFullWarehouseJsonObject(JSONObject fullWarehouseJsonObject){
        this.fullWarehouseJsonObject=fullWarehouseJsonObject;
    }

    public JSONObject getFullWarehouseJsonObject(){
        return fullWarehouseJsonObject;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public List<Product> getNowProductsWithPositiveStock() {
        return nowProductsWithPositiveStock;
    }

    public void setNowProductsWithPositiveStock(List<Product> nowProductsWithPositiveStock) {
        this.nowProductsWithPositiveStock = nowProductsWithPositiveStock;
    }

    public List<Product> getLaterProductsWithPositiveStock() {
        return laterProductsWithPositiveStock;
    }

    public void setLaterProductsWithPositiveStock(List<Product> laterProductsWithPositiveStock) {
        this.laterProductsWithPositiveStock = laterProductsWithPositiveStock;
    }
}
