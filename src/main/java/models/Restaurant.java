package models;

import java.util.List;

public class Restaurant {

    int yeloId;
    String name;
    String logo;
    List<String> businessCategories;
    String cover;
    String description;
    double minimumOrderAmount;
    String operatingStatus;
    boolean isDeliveredByBreadfast;
    double deliveryFee;
    int deliveryTime;
    double rating;
    int reviewCount;
    boolean IsAvailableYeloStatus;

    public int getYeloId() {
        return yeloId;
    }

    public void setYeloId(int yeloId) {
        this.yeloId = yeloId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public List<String> getBusinessCategories() {
        return businessCategories;
    }

    public void setBusinessCategories(List<String> businessCategories) {
        this.businessCategories = businessCategories;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getOperatingStatus() {return operatingStatus;}

    public void setOperatingStatus(String operatingStatus) {this.operatingStatus = operatingStatus;}

    public double getDeliveryFee() {
        return deliveryFee;
    }

    public void setDeliveryFee(double deliveryFee) {
        this.deliveryFee = deliveryFee;
    }

    public int getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(int deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public double getRating() {
        return rating;
    }

    public void setRating(double rating) {
        this.rating = rating;
    }

    public int getReviewCount() {
        return reviewCount;
    }

    public void setReviewCount(int reviewCount) {
        this.reviewCount = reviewCount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getMinimumOrderAmount() {
        return minimumOrderAmount;
    }

    public void setMinimumOrderAmount(double minimumOrderAmount) {
        this.minimumOrderAmount = minimumOrderAmount;
    }

    public boolean getIsDeliveredByBreadfast() {return isDeliveredByBreadfast;}

    public void setIsDeliveredByBreadfast(boolean isDeliveredByBreadfast) {this.isDeliveredByBreadfast = isDeliveredByBreadfast;}

    public boolean isAvailableYeloStatus() {return IsAvailableYeloStatus;}

    public void setAvailableYeloStatus(boolean availableYeloStatus) {IsAvailableYeloStatus = availableYeloStatus;}
}
