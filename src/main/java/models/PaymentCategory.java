package models;

import java.util.ArrayList;
import java.util.List;

public class PaymentCategory {
    private int categoryId;
    private String  arName;
    private String enName;
    private String arDescription;
    private String enDescription;
    private String imageUrl;
    private boolean status;
    private int orderIndex;
    private String createdAt;
    private String updatedAt;
    private String screenDeepLink;
    private String screenUrl;
    private List<PaymentServiceProvider> paymentServiceProviders = new ArrayList<>();

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public String getArName() {
        return arName;
    }

    public void setArName(String arName) {
        this.arName = arName;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getArDescription() {
        return arDescription;
    }

    public void setArDescription(String arDescription) {
        this.arDescription = arDescription;
    }

    public String getEnDescription() {
        return enDescription;
    }

    public void setEnDescription(String enDescription) {
        this.enDescription = enDescription;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public int getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(int orderIndex) {
        this.orderIndex = orderIndex;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getScreenDeepLink() {
        return screenDeepLink;
    }

    public void setScreenDeepLink(String screenDeepLink) {
        this.screenDeepLink = screenDeepLink;
    }

    public String getScreenUrl() {
        return screenUrl;
    }

    public void setScreenUrl(String screenUrl) {
        this.screenUrl = screenUrl;
    }

    public List<PaymentServiceProvider> getPaymentServiceProviders() {
        return paymentServiceProviders;
    }

    public void setPaymentServiceProviders(List<PaymentServiceProvider> paymentServiceProviders) {
        this.paymentServiceProviders = paymentServiceProviders;
    }
}
