package models;

import java.util.ArrayList;
import java.util.List;

public class FreshChatConversation {
    private String conversationId;
    private String conversationInternalId;
    private String appId;
    private String conversationUrl;
    private List<FreshChatMessage> messages = new ArrayList<>();

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getConversationInternalId() {
        return conversationInternalId;
    }

    public void setConversationInternalId(String conversationInternalId) {
        this.conversationInternalId = conversationInternalId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getConversationUrl() {
        return conversationUrl;
    }

    public void setConversationUrl(String conversationUrl) {
        this.conversationUrl = conversationUrl;
    }

    public List<FreshChatMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<FreshChatMessage> messages) {
        this.messages = messages;
    }
}
