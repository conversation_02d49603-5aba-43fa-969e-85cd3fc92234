package models;

import java.util.List;
import java.util.Map;
public class User {
    private String id;
    private String fullName;
    private String firstName;
    private String lastName;
    private String localPhoneNumber;
    private String phoneNumber;
    private String phoneCountry;
    private String foreignLocalPhoneNumber;
    private String foreignPhoneNumber;
    private String foreignPhoneCountryCode;
    private String foreignPhoneCountry;
    private String otp;
    private String emailAddress;
    private String emailPassword;
    private String bypassScriptPassword;
    private String registerToken;
    private String authorizationToken;
    private String referralCode = "";
    private Role role;
    private Map<String, String> adminAuthorisationCookies;
    private String currentBalance;
    private Address address;
    private List<Category> categoriesListInCurrentLocation;
    private boolean isInDeletionProcess;
    private String nationalId;
    private String nationalIdFrontImageIdentifier;
    private String nationalIdBackImageIdentifier;
    private String dateOfBirth;
    private String userName;
    private boolean isBlocked;
    private Order testOrder;
    private List<Order> allOrders;
    private Integer hrId;
    private String daFpNameAssigned;
    private String daStatus;
    private String daScore;
    private Integer daBalance;
    private boolean isDaLocked;
    private String chatbotJwtToken;
    private String fpId;
    private String freshChatId;

    public Integer getHrId() {
        return hrId;
    }

    public void setHrId(Integer hrId) {
        this.hrId = hrId;
    }

    public String getDaFpNameAssigned() {
        return daFpNameAssigned;
    }

    public void setDaFpNameAssigned(String daFpNameAssigned) {
        this.daFpNameAssigned = daFpNameAssigned;
    }

    public String getDaStatus() {
        return daStatus;
    }

    public void setDaStatus(String daStatus) {
        this.daStatus = daStatus;
    }

    public String getDaScore() {
        return daScore;
    }

    public void setDaScore(String daScore) {
        this.daScore = daScore;
    }

    public Integer getDaBalance() {
        return daBalance;
    }

    public void setDaBalance(Integer daBalance) {
        this.daBalance = daBalance;
    }

    public boolean isDaLocked() {
        return isDaLocked;
    }

    public void setDaLocked(boolean daLocked) {
        isDaLocked = daLocked;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getLocalPhoneNumber() {
        return localPhoneNumber;
    }

    public void setLocalPhoneNumber(String localPhoneNumber) {
        this.localPhoneNumber = localPhoneNumber;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneCountry() {
        return phoneCountry;
    }

    public void setPhoneCountry(String phoneCountry) {
        this.phoneCountry = phoneCountry;
    }

    public String getForeignLocalPhoneNumber() {
        return foreignLocalPhoneNumber;
    }

    public void setForeignLocalPhoneNumber(String foreignLocalPhoneNumber) {
        this.foreignLocalPhoneNumber = foreignLocalPhoneNumber;
    }

    public String getForeignPhoneNumber() {
        return foreignPhoneNumber;
    }

    public void setForeignPhoneNumber(String foreignPhoneNumber) {
        this.foreignPhoneNumber = foreignPhoneNumber;
    }

    public String getForeignPhoneCountryCode() {
        return foreignPhoneCountryCode;
    }

    public void setForeignPhoneCountryCode(String foreignPhoneCountryCode) {
        this.foreignPhoneCountryCode = foreignPhoneCountryCode;
    }

    public String getForeignPhoneCountry() {
        return foreignPhoneCountry;
    }

    public void setForeignPhoneCountry(String foreignPhoneCountry) {
        this.foreignPhoneCountry = foreignPhoneCountry;
    }

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getEmailPassword() {
        return emailPassword;
    }

    public void setEmailPassword(String emailPassword) {
        this.emailPassword = emailPassword;
    }

    public String getBypassScriptPassword() {
        return bypassScriptPassword;
    }

    public void setBypassScriptPassword(String bypassScriptPassword) {
        this.bypassScriptPassword = bypassScriptPassword;
    }

    public String getRegisterToken() {
        return registerToken;
    }

    public void setRegisterToken(String registerToken) {
        this.registerToken = registerToken;
    }

    public String getAuthorizationToken() {
        return authorizationToken;
    }

    public void setAuthorizationToken(String authorizationToken) {
        this.authorizationToken = authorizationToken;
    }

    public String getReferralCode() {
        return referralCode;
    }

    public void setReferralCode(String referralCode) {
        this.referralCode = referralCode;
    }

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
    }

    public Map<String, String> getAdminAuthorisationCookies() {
        return adminAuthorisationCookies;
    }

    public void setAdminAuthorisationCookies(Map<String, String> adminAuthorisationCookies) {
        this.adminAuthorisationCookies = adminAuthorisationCookies;
    }

    public String getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(String currentBalance) {
        this.currentBalance = currentBalance;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public List<Category> getCategoriesListInCurrentLocation() {
        return categoriesListInCurrentLocation;
    }

    public void setCategoriesListInCurrentLocation(List<Category> categoriesListInCurrentLocation) {
        this.categoriesListInCurrentLocation = categoriesListInCurrentLocation;
    }

    public boolean isInDeletionProcess() {
        return isInDeletionProcess;
    }

    public void setInDeletionProcess(boolean inDeletionProcess) {
        isInDeletionProcess = inDeletionProcess;
    }

    public String getNationalId() {
        return nationalId;
    }

    public void setNationalId(String nationalId) {
        this.nationalId = nationalId;
    }

    public String getNationalIdFrontImageIdentifier() {
        return nationalIdFrontImageIdentifier;
    }

    public void setNationalIdFrontImageIdentifier(String nationalIdFrontImageIdentifier) {
        this.nationalIdFrontImageIdentifier = nationalIdFrontImageIdentifier;
    }

    public String getNationalIdBackImageIdentifier() {
        return nationalIdBackImageIdentifier;
    }

    public void setNationalIdBackImageIdentifier(String nationalIdBackImageIdentifier) {
        this.nationalIdBackImageIdentifier = nationalIdBackImageIdentifier;
    }

    public String getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(String dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public boolean isBlocked() {
        return isBlocked;
    }

    public void setBlocked(boolean blocked) {
        isBlocked = blocked;
    }

    public Order getTestOrder() {
        return testOrder;
    }

    public void setTestOrder(Order testOrder) {
        this.testOrder = testOrder;
    }

    public List<Order> getAllOrders() {
        return allOrders;
    }

    public void setAllOrders(List<Order> allOrders) {
        this.allOrders = allOrders;
    }

    public String getChatbotJwtToken() {
        return chatbotJwtToken;
    }

    public void setChatbotJwtToken(String chatbotJwtToken) {
        this.chatbotJwtToken = chatbotJwtToken;
    }

    public String getFpId() {
        return fpId;
    }

    public void setFpId(String fpId) {
        this.fpId = fpId;
    }

    public String getFreshChatId() {
        return freshChatId;
    }

    public void setFreshChatId(String freshChatId) {
        this.freshChatId = freshChatId;
    }
}
