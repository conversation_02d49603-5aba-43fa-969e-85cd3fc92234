package models;

import java.sql.Connection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestData {
    private String testCountryCode;
    private String testCreditCard;
    private String testExpiryDate;
    private String testCVC;
    private String secondaryTestCreditCard;
    private String secondaryTestExpiryDate;
    private String secondaryTestCVC;
    private String declinedTestCreditCard;
    private User adminUser;
    private User burnerUser;
    private double testDeviceLatitude;
    private double testDeviceLongitude;
    private List<Warehouse> warehousesList;
    private Warehouse testWarehouse;
    private List<Order> ordersList;
    private Order testOrder;
    private Coupon testCoupon;
    private User randomTestUser;
    private User secondaryTestUser;
    private CustomerAppTestSession customerAppTestSession;
    private CustomerAppShopsTestSession customerAppShopsTestSession;
    private Bill testBill;
    private CardService cardService;
    private User pickerUser;
    private User midMileUser;
    private User chefUser;
    private User dAUser;
    private User fpManagerUser;
    private Connection dbConnection;
    private User stockTakerUser;
    private CustomerAppTestSession stockTakeTestSession;
    private FleetTrip fleetTrip;
    private FreshChatConversation testConversation;
    private FleetTrip assignedTrip;
    private FleetTrip tripTasks;
    private User paymentPanelUser;
    private PlanningCenter planningCenter;
    private Transfer transfer;
    private int sentTransfersCount;
    private int notSentTransfersCount;
    private List<String> deleteReasons;
    private Product productStockLog;
    private FoodAggregatorTestSession foodAggregatorTestSession;
    private Map<String, Object> deductResult;
    private Map<String, Object> orderResult = new HashMap<>();
    private Map<String, Object> manualAdditionResult = new java.util.HashMap<>();

    public String getTestCountryCode() {
        return testCountryCode;
    }

    public void setTestCountryCode(String testCountryCode) {
        this.testCountryCode = testCountryCode;
    }

    public String getTestCreditCard() {
        return testCreditCard;
    }

    public void setTestCreditCard(String testCreditCard) {
        this.testCreditCard = testCreditCard;
    }

    public String getTestExpiryDate() {
        return testExpiryDate;
    }

    public void setTestExpiryDate(String testExpiryDate) {
        this.testExpiryDate = testExpiryDate;
    }

    public String getTestCVC() {
        return testCVC;
    }

    public void setTestCVC(String testCVC) {
        this.testCVC = testCVC;
    }

    public String getSecondaryTestCreditCard() {
        return secondaryTestCreditCard;
    }

    public void setSecondaryTestCreditCard(String secondaryTestCreditCard) {
        this.secondaryTestCreditCard = secondaryTestCreditCard;
    }

    public String getSecondaryTestExpiryDate() {
        return secondaryTestExpiryDate;
    }

    public void setSecondaryTestExpiryDate(String secondaryTestExpiryDate) {
        this.secondaryTestExpiryDate = secondaryTestExpiryDate;
    }

    public String getSecondaryTestCVC() {
        return secondaryTestCVC;
    }

    public void setSecondaryTestCVC(String secondaryTestCVC) {
        this.secondaryTestCVC = secondaryTestCVC;
    }

    public User getAdminUser() {
        return adminUser;
    }

    public void setAdminUser(User adminUser) {
        this.adminUser = adminUser;
    }

    public User getBurnerUser() {
        return burnerUser;
    }

    public void setBurnerUser(User burnerUser) {
        this.burnerUser = burnerUser;
    }

    public double getTestDeviceLatitude() {
        return testDeviceLatitude;
    }

    public void setTestDeviceLatitude(double testDeviceLatitude) {
        this.testDeviceLatitude = testDeviceLatitude;
    }

    public double getTestDeviceLongitude() {
        return testDeviceLongitude;
    }

    public void setTestDeviceLongitude(double testDeviceLongitude) {
        this.testDeviceLongitude = testDeviceLongitude;
    }

    public List<Warehouse> getWarehousesList() {
        return warehousesList;
    }

    public void setWarehousesList(List<Warehouse> warehousesList) {
        this.warehousesList = warehousesList;
    }

    public Warehouse getTestWarehouse() {
        return testWarehouse;
    }

    public void setTestWarehouse(Warehouse testWarehouse) {
        this.testWarehouse = testWarehouse;
    }

    public List<Order> getOrdersList() {
        return ordersList;
    }

    public void setOrdersList(List<Order> ordersList) {
        this.ordersList = ordersList;
    }

    public Order getTestOrder() {
        return testOrder;
    }

    public void setTestOrder(Order testOrder) {
        this.testOrder = testOrder;
    }

    public Coupon getTestCoupon() {
        return testCoupon;
    }

    public void setTestCoupon(Coupon testCoupon) {
        this.testCoupon = testCoupon;
    }

    public User getRandomTestUser() {
        return randomTestUser;
    }

    public void setRandomTestUser(User randomTestUser) {
        this.randomTestUser = randomTestUser;
    }

    public User getSecondaryTestUser() {
        return secondaryTestUser;
    }

    public void setSecondaryTestUser(User secondaryTestUser) {
        this.secondaryTestUser = secondaryTestUser;
    }

    public CustomerAppTestSession getCustomerAppTestSession() {
        return customerAppTestSession;
    }

    public void setCustomerAppTestSession(CustomerAppTestSession customerAppTestSession) {
        this.customerAppTestSession = customerAppTestSession;
    }

    public CustomerAppShopsTestSession getCustomerAppShopsTestSession() {
        return customerAppShopsTestSession;
    }

    public void setCustomerAppShopsTestSession(CustomerAppShopsTestSession customerAppShopsTestSession) {
        this.customerAppShopsTestSession = customerAppShopsTestSession;
    }

    public Bill getTestBill() {
        return testBill;
    }

    public void setTestBill(Bill testBill) {
        this.testBill = testBill;
    }

    public CardService getCardService() {
        return cardService;
    }

    public void setCardService(CardService cardService) {
        this.cardService = cardService;
    }

    public User getPickerUser() {
        return pickerUser;
    }

    public void setPickerUser(User pickerUser) {
        this.pickerUser = pickerUser;
    }

    public User getMidMileUser() {
        return midMileUser;
    }

    public void setMidMileUser(User midMileUser) {
        this.midMileUser = midMileUser;
    }

    public User getChefUser() {
        return chefUser;
    }

    public void setChefUser(User chefUser) {
        this.chefUser = chefUser;
    }

    public User getDaUser() {
        return dAUser;
    }

    public void setDaUser(User dAUser) {
        this.dAUser = dAUser;
    }

    public User getFpManagerUser() {
        return fpManagerUser;
    }

    public void setFpManagerUser(User fpManagerUser) {
        this.fpManagerUser = fpManagerUser;
    }

    public Connection getDbConnection() {
        return dbConnection;
    }

    public void setDbConnection(Connection dbConnection) {
        this.dbConnection = dbConnection;
    }

    public FleetTrip getFleetTrip() {
        return fleetTrip;
    }

    public void setFleetTrip(FleetTrip fleetTrip) {
        this.fleetTrip = fleetTrip;
    }

    public User getStockTakerUser() {
        return stockTakerUser;
    }

    public void setStockTakerUser(User stockTakerUser) {
        this.stockTakerUser = stockTakerUser;
    }

    public CustomerAppTestSession getStockTakeTestSession() {
        return stockTakeTestSession;
    }

    public void setStockTakeTestSession(CustomerAppTestSession stockTakeTestSession) {
        this.stockTakeTestSession = stockTakeTestSession;
    }

    public User getPaymentPanelUser() {
        return paymentPanelUser;
    }

    public void setPaymentPanelUser(User paymentPanelUser) {
        this.paymentPanelUser = paymentPanelUser;
    }

    public FleetTrip getAssignedTrip() {
        return assignedTrip;
    }

    public void setAssignedTrip(FleetTrip assignedTrip) {
        this.assignedTrip = assignedTrip;
    }

    public PlanningCenter getPlanningCenter() {
        return planningCenter;
    }

    public void setPlanningCenter(PlanningCenter planningCenter) {
        this.planningCenter = planningCenter;

    }

    public FleetTrip getTripTasks() {
        return tripTasks;
    }

    public void setTripTasks(FleetTrip tripTasks) {
        this.tripTasks = tripTasks;
    }

    public Transfer getTransfer() {
        return transfer;
    }

    public void setTransfer(Transfer transfer) {
        this.transfer = transfer;
    }

    public int getSentTransfersCount() {
        return sentTransfersCount;
    }

    public void setSentTransfersCount(int sentTransfersCount) {
        this.sentTransfersCount = sentTransfersCount;
    }

    public int getNotSentTransfersCount() {
        return notSentTransfersCount;
    }

    public void setNotSentTransfersCount(int notSentTransfersCount) {
        this.notSentTransfersCount = notSentTransfersCount;
    }
    public void setDeleteReasons(List<String> deleteReasons) {
        this.deleteReasons = deleteReasons;
    }

    public List<String>  getDeleteReasons() {
        return deleteReasons;
    }

    public FreshChatConversation getTestConversation() {
        return testConversation;
    }

    public void setTestConversation(FreshChatConversation testConversation) {
        this.testConversation = testConversation;
    }

    public Product getProductStockLog() {
        return productStockLog;
    }

    public void setProductStockLog(Product productStockLog) {
        this.productStockLog = productStockLog;
    }

    public String getDeclinedTestCreditCard() {
        return declinedTestCreditCard;
    }

    public void setDeclinedTestCreditCard(String declinedTestCreditCard) {
        this.declinedTestCreditCard = declinedTestCreditCard;
    }

    public FoodAggregatorTestSession getFoodAggregatorTestSession() {
        return foodAggregatorTestSession;
    }

    public void setFoodAggregatorTestSession (FoodAggregatorTestSession foodAggregatorTestSession) {
        this.foodAggregatorTestSession = foodAggregatorTestSession;
    }

    public Map<String, Object> getDeductResult(){ 
      return deductResult;
    }

    public void setDeductResult(Map<String, Object> deductResult){
        this.deductResult = deductResult;
    }

    public Map<String, Object> getManualAdditionResult() {
        return manualAdditionResult;
    }
    public void setManualAdditionResult(Map<String, Object> manualAdditionResult) {
        this.manualAdditionResult = manualAdditionResult;
    }

    public void setOrderResult(Map<String, Object> result) {

        this.orderResult = result;
    }
    public Map<String, Object> getOrderResult() {
        return this.orderResult;
    }
}
