package models;

import java.util.List;

public class Product {
    private String mongoId;
    private int mysqlId;
    private String extraSalesPrice;
    private List<String> tags;
    private String productType;
    private int maxAmountPerNowOrder;
    private int maxAmountPerLaterOrder;
    private String name;
    private String arabicName;
    private int priority;
    private double salePrice;
    private String brandName;
    private String arabicBrandName;
    private String subBrandName;
    private String arabicSubBrandName;
    private double price;
    private int categoryId;
    private int subCategoryId;
    private double fpPrice;
    private double stock;
    private double nowStock;
    private double extraSalePriceInHouseDiscount;
    private double extraSalePriceVendorDiscount;
    private String automatedDiscountPrice;
    private String automatedDiscountStock;
    private List<Discount> automatedDiscounts;
    private boolean active;
    private List<String> barcodeList;
    private String barcode;
    private int quantity;
    private double subtotal;
    private double total;
    private boolean isFreeGift;
    private boolean isTopUp;
    private int delta;
    private int shopId;
    private int catalogProductId;
    private int optionSetId;
    private int optionPrice;
    private boolean isCustomizable;
    private List<OptionSets> optionSet;
    private List<Options> option;
    private int optionSetsCount;

    public String getMongoId() {
        return mongoId;
    }

    public void setMongoId(String mongoId) {
        this.mongoId = mongoId;
    }

    public int getMysqlId() {
        return mysqlId;
    }

    public void setMysqlId(int mysqlId) {
        this.mysqlId = mysqlId;
    }

    public String getExtraSalesPrice() {
        return extraSalesPrice;
    }

    public void setExtraSalesPrice(String extraSalesPrice) {
        this.extraSalesPrice = extraSalesPrice;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public int getMaxAmountPerNowOrder() {
        return maxAmountPerNowOrder;
    }

    public void setMaxAmountPerNowOrder(int maxAmountPerNowOrder) {
        this.maxAmountPerNowOrder = maxAmountPerNowOrder;
    }

    public int getMaxAmountPerLaterOrder() {
        return maxAmountPerLaterOrder;
    }

    public void setMaxAmountPerLaterOrder(int maxAmountPerLaterOrder) {
        this.maxAmountPerLaterOrder = maxAmountPerLaterOrder;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getArabicName() {
        return arabicName;
    }

    public void setArabicName(String arabicName) {
        this.arabicName = arabicName;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public double getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(double salePrice) {
        this.salePrice = salePrice;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getArabicBrandName() {
        return arabicBrandName;
    }

    public void setArabicBrandName(String arabicBrandName) {
        this.arabicBrandName = arabicBrandName;
    }

    public String getSubBrandName() {
        return subBrandName;
    }

    public void setSubBrandName(String subBrandName) {
        this.subBrandName = subBrandName;
    }

    public String getArabicSubBrandName() {
        return arabicSubBrandName;
    }

    public void setArabicSubBrandName(String arabicSubBrandName) {
        this.arabicSubBrandName = arabicSubBrandName;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public int getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(int subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

    public double getFpPrice() {
        return fpPrice;
    }

    public void setFpPrice(double fpPrice) {
        this.fpPrice = fpPrice;
    }

    public double getStock() {
        return stock;
    }

    public void setStock(double stock) {
        this.stock = stock;
    }

    public double getNowStock() {
        return nowStock;
    }

    public void setNowStock(double nowStock) {
        this.nowStock = nowStock;
    }

    public double getExtraSalePriceInHouseDiscount() {
        return extraSalePriceInHouseDiscount;
    }

    public void setExtraSalePriceInHouseDiscount(double extraSalePriceInHouseDiscount) {
        this.extraSalePriceInHouseDiscount = extraSalePriceInHouseDiscount;
    }

    public double getExtraSalePriceVendorDiscount() {
        return extraSalePriceVendorDiscount;
    }

    public void setExtraSalePriceVendorDiscount(double extraSalePriceVendorDiscount) {
        this.extraSalePriceVendorDiscount = extraSalePriceVendorDiscount;
    }

    public String getAutomatedDiscountPrice() {
        return automatedDiscountPrice;
    }

    public void setAutomatedDiscountPrice(String automatedDiscountPrice) {
        this.automatedDiscountPrice = automatedDiscountPrice;
    }

    public String getAutomatedDiscountStock() {
        return automatedDiscountStock;
    }

    public void setAutomatedDiscountStock(String automatedDiscountStock) {
        this.automatedDiscountStock = automatedDiscountStock;
    }

    public List<Discount> getAutomatedDiscounts() {
        return automatedDiscounts;
    }

    public void setAutomatedDiscounts(List<Discount> automatedDiscounts) {
        this.automatedDiscounts = automatedDiscounts;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public List<String> getBarcodeList() {
        return barcodeList;
    }

    public void setBarcodeList(List<String> barcodeList) {
        this.barcodeList = barcodeList;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public int getQuantity() {return quantity;}

    public void setQuantity(int quantity) {this.quantity = quantity;}

    public double getSubtotal() {return subtotal;}

    public void setSubtotal(double subtotal) {this.subtotal = subtotal;}

    public double getTotal() {return total;}

    public void setTotal(double total) {this.total = total;}

    public boolean getIsFreeGift() {return isFreeGift;}

    public void setIsFreeGift(boolean isFreeGift) {this.isFreeGift = isFreeGift;}

    public boolean getIsTopUp() {return isTopUp;}

    public void setIsTopUp(boolean isTopUp) {this.isTopUp = isTopUp;}

    public int getDelta() {
        return delta;
    }

    public void setDelta(int delta) {
        this.delta = delta;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public int getCatalogProductId() {
        return catalogProductId;
    }

    public void setCatalogProductId(int catalogProductId) {
        this.catalogProductId = catalogProductId;
    }

    public int getOptionPrice() {
        return optionPrice;
    }

    public void setOptionPrice(int optionPrice) {
        this.optionPrice = optionPrice;
    }

    public int getOptionSetId() {
        return optionSetId;
    }

    public void setOptionSetId(int optionSetId) {
        this.optionSetId = optionSetId;
    }

    public boolean isCustomizable() {
        return isCustomizable;
    }

    public void setCustomizable(boolean customizable) {
        isCustomizable = customizable;
    }

    public List<Options> getOption() {
        return option;
    }

    public void setOption(List<Options> option) {
        this.option = option;
    }

    public List<OptionSets> getOptionSet() {
        return optionSet;
    }

    public void setOptionSet(List<OptionSets> optionSet) {
        this.optionSet = optionSet;
    }

    public int getOptionSetsCount() {
        return optionSetsCount;
    }

    public void setOptionSetsCount(int optionSetsCount) {
        this.optionSetsCount = optionSetsCount;
    }
}
