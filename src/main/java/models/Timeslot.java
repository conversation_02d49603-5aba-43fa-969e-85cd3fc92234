package models;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

public class Timeslot {

    String warehouseStatus;
    String timeslot;
    List<Integer> timeslotIds;
    String timeslotStatus;
    List<Integer> timeslotCapacity;
    int updatedTimeslotCapacity;
    String currentTimeslot;
    String currentTimeslot24hr;
    int currentTimeslotId;
    String currentTimeslotStatus;
    int currentTimeslotCapacity;
    int workingTime;
    boolean instantFlag;

    public String getWarehouseStatus() {
        return warehouseStatus;
    }
    public void setWarehouseStatus(String warehouseStatus) {
        this.warehouseStatus = warehouseStatus;
    }
    public boolean getInstantFlag() {
        return instantFlag;
    }

    public void setInstantFlag(boolean instantFlag) {
        this.instantFlag = instantFlag;
    }

    public String getCurrentTimeslot() {
        return currentTimeslot;
    }

    public void setCurrentTimeslot(String currentTimeslot) {
        this.currentTimeslot = currentTimeslot;
    }

    public int getCurrentTimeslotId() {
        return currentTimeslotId;
    }

    public void setCurrentTimeslotId(int currentTimeslotId) {
        this.currentTimeslotId = currentTimeslotId;
    }

    public String getCurrentTimeslotStatus() {
        return currentTimeslotStatus;
    }

    public void setCurrentTimeslotStatus(String currentTimeslotStatus) {
        this.currentTimeslotStatus = currentTimeslotStatus;
    }

    public int getCurrentTimeslotCapacity() {
        return currentTimeslotCapacity;
    }

    public void setCurrentTimeslotCapacity(int currentTimeslotCapacity) {
        this.currentTimeslotCapacity = currentTimeslotCapacity;
    }

    public int getWorkingTime() {
        return workingTime;
    }

    public void setWorkingTime(int workingTime) {
        this.workingTime = workingTime;
    }

    public String getTimeslot() {
        return timeslot;
    }

    public void setTimeslot(String timeslot) {
        this.timeslot = timeslot;
    }

    public String getTimeslotStatus() {
        return timeslotStatus;
    }

    public void setTimeslotStatus(String timeslotStatus) {
        this.timeslotStatus = timeslotStatus;
    }

    public int getUpdatedTimeslotCapacity() {
        return updatedTimeslotCapacity;
    }

    public void setUpdatedTimeslotCapacity(int updatedTimeslotCapacity) {
        this.updatedTimeslotCapacity = updatedTimeslotCapacity;
    }
    public List<Integer> getTimeslotIds() {
        return timeslotIds;
    }

    public void setTimeslotIds(List<Integer> timeslotIds) {
        this.timeslotIds = timeslotIds;
    }

    public List<Integer> getTimeslotCapacity() {
        return timeslotCapacity;
    }

    public void setTimeslotCapacity(List<Integer> timeslotCapacity) {
        this.timeslotCapacity = timeslotCapacity;
    }
    public String getCurrentTimeslot24hr() {
        return currentTimeslot24hr;
    }
    public void setCurrentTimeslot24hr(String currentTimeslot24hr) {
        this.currentTimeslot24hr = currentTimeslot24hr;
    }

}
