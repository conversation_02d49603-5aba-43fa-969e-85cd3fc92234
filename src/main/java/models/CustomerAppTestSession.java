package models;

import java.util.List;

public class CustomerAppTestSession {
    private Warehouse testWarehouse;
    private List<Category> nowCategoriesInWarehouse;
    private List<Category> tomorrowCategoriesInWarehouse;
    private Category nowOnlyCategory;
    private Category nowOnlySubCategory;
    private Product nowOnlyProduct;
    private Category tomorrowOnlyCategory;
    private Category tomorrowOnlySubcategory;
    private Product tomorrowOnlyProduct;
    private String outOfStockServeType;
    private Category outOfStockOnlyCategory;
    private Category outOfStockOnlySubcategory;
    private Product outOfStockOnlyProduct;
    private Category nowCategoryWithPositiveStock;
    private Category nowSubcategoryWithPositiveStock;
    private Product nowProductWithPositiveStock;
    private Category tomorrowCategoryWithPositiveStock;
    private Category tomorrowSubCategoryWithPositiveStock;
    private Product tomorrowProductWithPositiveStock;
    private List<PaymentCategory> paymentCategories;
    private Category testCategory;
    private List<Area> areasList;
    private Order testOrder;
    private List<Product> singleAndBundleProductsList;
    private List<Product> customizableAndNonCustomizableProducts;
    private Product singleProduct;
    private Product bundleProduct;
    private List<Product> bundleOnlyProducts;
    private List<Product> singleOnlyProducts;
    private List<Product> customizableOnlyProducts;
    private List<Product> singleProductsWithoutDiscount;
    private List<Product> singleProductsWithSalePrice;
    private List<Product> singleProductsWithExtraSalePrice;
    private List<Product> bundleProductsWithoutDiscount;
    private List<Product> bundleProductsWithSalePrice;
    private List<Product> singleAndBundleProductsWithoutDiscount;
    private List<Product> singleAndBundleProductsWithSalePrice;
    private List<Category> stockTakeCategories;
    private List<Category> sharedFacilityCategories;
    private List<Product> nowProductOnSharedFacilityWithPositiveStock;
    private List<Product> tomorrowProductOnSharedFacilityWithPositiveStock;
    private String carouselEnglishName;
    private String carouselArabicName;
    private OptionSets optionSets;
    private Product catalogProduct;

    public Warehouse getTestWarehouse() {
        return testWarehouse;
    }

    public void setTestWarehouse(Warehouse testWarehouse) {
        this.testWarehouse = testWarehouse;
    }

    public List<Category> getNowCategoriesInWarehouse() {
        return nowCategoriesInWarehouse;
    }

    public void setNowCategoriesInWarehouse(List<Category> nowCategoriesInWarehouse) {
        this.nowCategoriesInWarehouse = nowCategoriesInWarehouse;
    }

    public List<Category> getTomorrowCategoriesInWarehouse() {
        return tomorrowCategoriesInWarehouse;
    }

    public void setTomorrowCategoriesInWarehouse(List<Category> tomorrowCategoriesInWarehouse) {
        this.tomorrowCategoriesInWarehouse = tomorrowCategoriesInWarehouse;
    }

    public Category getNowOnlyCategory() {
        return nowOnlyCategory;
    }

    public void setNowOnlyCategory(Category nowOnlyCategory) {
        this.nowOnlyCategory = nowOnlyCategory;
    }

    public Category getNowOnlySubCategory() {
        return nowOnlySubCategory;
    }

    public void setNowOnlySubCategory(Category nowOnlySubCategory) {
        this.nowOnlySubCategory = nowOnlySubCategory;
    }

    public Product getNowOnlyProduct() {
        return nowOnlyProduct;
    }

    public void setNowOnlyProduct(Product nowOnlyProduct) {
        this.nowOnlyProduct = nowOnlyProduct;
    }

    public Category getTomorrowOnlyCategory() {
        return tomorrowOnlyCategory;
    }

    public void setTomorrowOnlyCategory(Category tomorrowOnlyCategory) {
        this.tomorrowOnlyCategory = tomorrowOnlyCategory;
    }

    public Category getTestCategory() {
        return testCategory;
    }

    public void setTestCategory(Category testCategory) {
        this.testCategory = testCategory;
    }

    public Category getTomorrowOnlySubcategory() {
        return tomorrowOnlySubcategory;
    }

    public void setTomorrowOnlySubcategory(Category tomorrowOnlySubcategory) {
        this.tomorrowOnlySubcategory = tomorrowOnlySubcategory;
    }

    public Product getTomorrowOnlyProduct() {
        return tomorrowOnlyProduct;
    }

    public void setTomorrowOnlyProduct(Product tomorrowOnlyProduct) {
        this.tomorrowOnlyProduct = tomorrowOnlyProduct;
    }

    public String getOutOfStockServeType() {
        return outOfStockServeType;
    }

    public void setOutOfStockServeType(String outOfStockServeType) {
        this.outOfStockServeType = outOfStockServeType;
    }

    public Category getOutOfStockOnlyCategory() {
        return outOfStockOnlyCategory;
    }

    public void setOutOfStockOnlyCategory(Category outOfStockOnlyCategory) {
        this.outOfStockOnlyCategory = outOfStockOnlyCategory;
    }

    public Category getOutOfStockOnlySubcategory() {
        return outOfStockOnlySubcategory;
    }

    public void setOutOfStockOnlySubcategory(Category outOfStockOnlySubcategory) {
        this.outOfStockOnlySubcategory = outOfStockOnlySubcategory;
    }

    public Product getOutOfStockOnlyProduct() {
        return outOfStockOnlyProduct;
    }

    public void setOutOfStockOnlyProduct(Product outOfStockOnlyProduct) {
        this.outOfStockOnlyProduct = outOfStockOnlyProduct;
    }

    public Category getNowCategoryWithPositiveStock() {
        return nowCategoryWithPositiveStock;
    }

    public void setNowCategoryWithPositiveStock(Category nowCategoryWithPositiveStock) {
        this.nowCategoryWithPositiveStock = nowCategoryWithPositiveStock;
    }

    public Category getNowSubcategoryWithPositiveStock() {
        return nowSubcategoryWithPositiveStock;
    }

    public void setNowSubcategoryWithPositiveStock(Category nowSubcategoryWithPositiveStock) {
        this.nowSubcategoryWithPositiveStock = nowSubcategoryWithPositiveStock;
    }

    public Product getNowProductWithPositiveStock() {
        return nowProductWithPositiveStock;
    }

    public void setNowProductWithPositiveStock(Product nowProductWithPositiveStock) {
        this.nowProductWithPositiveStock = nowProductWithPositiveStock;
    }

    public Category getTomorrowCategoryWithPositiveStock() {
        return tomorrowCategoryWithPositiveStock;
    }

    public void setTomorrowCategoryWithPositiveStock(Category tomorrowCategoryWithPositiveStock) {
        this.tomorrowCategoryWithPositiveStock = tomorrowCategoryWithPositiveStock;
    }

    public Category getTomorrowSubCategoryWithPositiveStock() {
        return tomorrowSubCategoryWithPositiveStock;
    }

    public void setTomorrowSubCategoryWithPositiveStock(Category tomorrowSubCategoryWithPositiveStock) {
        this.tomorrowSubCategoryWithPositiveStock = tomorrowSubCategoryWithPositiveStock;
    }

    public Product getTomorrowProductWithPositiveStock() {
        return tomorrowProductWithPositiveStock;
    }

    public void setTomorrowProductWithPositiveStock(Product tomorrowProductWithPositiveStock) {
        this.tomorrowProductWithPositiveStock = tomorrowProductWithPositiveStock;
    }

    public List<PaymentCategory> getPaymentCategories() {
        return paymentCategories;
    }

    public void setPaymentCategories(List<PaymentCategory> paymentCategories) {
        this.paymentCategories = paymentCategories;
    }

    public List<Area> getAreasList() {
        return areasList;
    }

    public void setAreasList(List<Area> areasList) {
        this.areasList = areasList;
    }

    public Order getTestOrder() {
        return testOrder;
    }

    public void setTestOrder(Order testOrder) {
        this.testOrder = testOrder;
    }

    public List<Product> getSingleAndBundleProductsList() {
        return singleAndBundleProductsList;
    }

    public void setSingleAndBundleProductsList(List<Product> singleAndBundleProductsList) {
        this.singleAndBundleProductsList = singleAndBundleProductsList;
    }

    public Product getSingleProduct() {
        return singleProduct;
    }

    public void setSingleProduct(Product singleProduct) {
        this.singleProduct = singleProduct;
    }

    public Product getBundleProduct() {
        return bundleProduct;
    }

    public void setBundleProduct(Product bundleProduct) {
        this.bundleProduct = bundleProduct;
    }

    public List<Product> getBundleOnlyProducts() {
        return bundleOnlyProducts;
    }

    public void setBundleOnlyProducts(List<Product> bundleOnlyProducts) {
        this.bundleOnlyProducts = bundleOnlyProducts;
    }

    public List<Product> getSingleOnlyProducts() {
        return singleOnlyProducts;
    }

    public void setSingleOnlyProducts(List<Product> singleOnlyProducts) {
        this.singleOnlyProducts = singleOnlyProducts;
    }

    public List<Category> getStockTakeCategories() {
        return stockTakeCategories;
    }

    public void setStockTakeCategories(List<Category> stockTakeCategories) {
        this.stockTakeCategories = stockTakeCategories;
    }

    public List<Category> getSharedFacilityCategories() {
        return sharedFacilityCategories;
    }

    public void setSharedFacilityCategories(List<Category> sharedFacilityCategories) {
        this.sharedFacilityCategories = sharedFacilityCategories;
    }

    public List<Product> getNowProductOnSharedFacilityWithPositiveStock() {
        return nowProductOnSharedFacilityWithPositiveStock;
    }

    public void setNowProductOnSharedFacilityWithPositiveStock(List<Product> nowProductOnSharedFacilityWithPositiveStock) {
        this.nowProductOnSharedFacilityWithPositiveStock = nowProductOnSharedFacilityWithPositiveStock;
    }

    public List<Product> getTomorrowProductOnSharedFacilityWithPositiveStock() {
        return tomorrowProductOnSharedFacilityWithPositiveStock;
    }

    public void setTomorrowProductOnSharedFacilityWithPositiveStock(List<Product> tomorrowProductOnSharedFacilityWithPositiveStock) {
        this.tomorrowProductOnSharedFacilityWithPositiveStock = tomorrowProductOnSharedFacilityWithPositiveStock;
    }

    public List<Product> getSingleProductsWithoutDiscount() {
        return singleProductsWithoutDiscount;
    }

    public void setSingleProductsWithoutDiscount(List<Product> singleProductsWithoutDiscount) {
        this.singleProductsWithoutDiscount = singleProductsWithoutDiscount;
    }

    public List<Product> getSingleProductsWithSalePrice() {
        return singleProductsWithSalePrice;
    }

    public void setSingleProductsWithSalePrice(List<Product> singleProductsWithSalePrice) {
        this.singleProductsWithSalePrice = singleProductsWithSalePrice;
    }

    public List<Product> getSingleProductsWithExtraSalePrice() {
        return singleProductsWithExtraSalePrice;
    }

    public void setSingleProductsWithExtraSalePrice(List<Product> singleProductsWithExtraSalePrice) {
        this.singleProductsWithExtraSalePrice = singleProductsWithExtraSalePrice;
    }

    public List<Product> getBundleProductsWithoutDiscount() {
        return bundleProductsWithoutDiscount;
    }

    public void setBundleProductsWithoutDiscount(List<Product> bundleProductsWithoutDiscount) {
        this.bundleProductsWithoutDiscount = bundleProductsWithoutDiscount;
    }

    public List<Product> getBundleProductsWithSalePrice() {
        return bundleProductsWithSalePrice;
    }

    public void setBundleProductsWithSalePrice(List<Product> bundleProductsWithSalePrice) {
        this.bundleProductsWithSalePrice = bundleProductsWithSalePrice;
    }

    public List<Product> getSingleAndBundleProductsWithoutDiscount() {
        return singleAndBundleProductsWithoutDiscount;
    }

    public void setSingleAndBundleProductsWithoutDiscount(List<Product> singleAndBundleProductsWithoutDiscount) {
        this.singleAndBundleProductsWithoutDiscount = singleAndBundleProductsWithoutDiscount;
    }

    public List<Product> getSingleAndBundleProductsWithSalePrice() {
        return singleAndBundleProductsWithSalePrice;
    }

    public void setSingleAndBundleProductsWithSalePrice(List<Product> singleAndBundleProductsWithSalePrice) {
        this.singleAndBundleProductsWithSalePrice = singleAndBundleProductsWithSalePrice;
    }

    public void setCarouselEnglishName(String carouselEnglishName) {
        this.carouselEnglishName = carouselEnglishName;
    }

    public String getCarouselEnglishName() {
        return carouselEnglishName;
    }

    public void setCarouselArabicName(String carouselArabicName) {
        this.carouselArabicName = carouselArabicName;
    }

    public String getCarouselArabicName() {
        return carouselArabicName;
    }

    public List<Product> getCustomizableOnlyProducts() {
        return customizableOnlyProducts;
    }

    public void setCustomizableOnlyProducts(List<Product> customizableOnlyProducts) {
        this.customizableOnlyProducts = customizableOnlyProducts;
    }

    public List<Product> getCustomizableAndNonCustomizableProducts() {
        return customizableAndNonCustomizableProducts;
    }

    public void setCustomizableAndNonCustomizableProducts(List<Product> customizableAndNonCustomizableProducts) {
        this.customizableAndNonCustomizableProducts = customizableAndNonCustomizableProducts;
    }

    public OptionSets getOptionSets() {
        return optionSets;
    }

    public void setOptionSets(OptionSets optionSets) {
        this.optionSets = optionSets;
    }

    public Product getCatalogProduct() {
        return catalogProduct;
    }

    public void setCatalogProduct(Product catalogProduct) {
        this.catalogProduct = catalogProduct;
    }
}
