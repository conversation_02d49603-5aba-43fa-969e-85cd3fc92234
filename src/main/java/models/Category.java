package models;

import org.json.JSONObject;

import java.util.List;

public class Category {
    private JSONObject tag;
    private boolean isClosed;
    private int sort;
    private String publishedAt;
    private int id;
    private String name;
    private String arabicName;
    private String description;
    private String arabicDescription;
    private int countOfProducts;
    private List<Category> subCategories;
    private List<Product> nowProductsInclusive;
    private List<Product> laterProductsInclusive;
    private List<Product> nowProductsExclusive;
    private List<Product> laterProductsExclusive;
    private List<Product> nowProductsWithPositiveStocks;
    private List<Product> laterProductsWithPositiveStocks;
    private List<Product> bundleProducts;
    private List<Product> singleProducts;
    private List<Product> customizableProducts;
    private List<Product> singleAndBundleProducts;
    private String categoryId;
    private String subCategoryId;
    private String subCategoryName;
    private String subCategoryArabicName;
    private List<Product> stockTakeProducts;
    public JSONObject getTag() {
        return tag;
    }

    public void setTag(JSONObject tag) {
        this.tag = tag;
    }

    public boolean getIsClosed() {
        return isClosed;
    }

    public void setIsClosed(boolean isClosed) {
        this.isClosed = isClosed;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public String getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(String publishedAt) {
        this.publishedAt = publishedAt;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getArabicName() {
        return arabicName;
    }

    public void setArabicName(String arabicName) {
        this.arabicName = arabicName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getArabicDescription() {
        return arabicDescription;
    }

    public void setArabicDescription(String arabicDescription) {
        this.arabicDescription = arabicDescription;
    }

    public int getCountOfProducts() {
        return countOfProducts;
    }

    public void setCountOfProducts(int countOfProducts) {
        this.countOfProducts = countOfProducts;
    }

    public List<Category> getSubCategories() {
        return subCategories;
    }

    public void setSubCategories(List<Category> subCategories) {
        this.subCategories = subCategories;
    }

    public List<Product> getNowProductsInclusive() {
        return nowProductsInclusive;
    }

    public void setNowProductsInclusive(List<Product> nowProductsInclusive) {
        this.nowProductsInclusive = nowProductsInclusive;
    }

    public List<Product> getLaterProductsInclusive() {
        return laterProductsInclusive;
    }

    public void setLaterProductsInclusive(List<Product> laterProductsInclusive) {
        this.laterProductsInclusive = laterProductsInclusive;
    }

    public List<Product> getNowProductsExclusive() {
        return nowProductsExclusive;
    }

    public void setNowProductsExclusive(List<Product> nowProductsExclusive) {
        this.nowProductsExclusive = nowProductsExclusive;
    }

    public List<Product> getLaterProductsExclusive() {
        return laterProductsExclusive;
    }

    public void setLaterProductsExclusive(List<Product> laterProductsExclusive) {
        this.laterProductsExclusive = laterProductsExclusive;
    }

    public List<Product> getNowProductsWithPositiveStocks() {
        return nowProductsWithPositiveStocks;
    }

    public void setNowProductsWithPositiveStocks(List<Product> nowProductsWithPositiveStocks) {
        this.nowProductsWithPositiveStocks = nowProductsWithPositiveStocks;
    }

    public List<Product> getLaterProductsWithPositiveStocks() {
        return laterProductsWithPositiveStocks;
    }

    public void setLaterProductsWithPositiveStocks(List<Product> laterProductsWithPositiveStocks) {
        this.laterProductsWithPositiveStocks = laterProductsWithPositiveStocks;
    }

    public List<Product> getBundleProducts() {
        return bundleProducts;
    }

    public void setBundleProducts(List<Product> bundleProducts) {
        this.bundleProducts = bundleProducts;
    }

    public List<Product> getSingleProducts() {
        return singleProducts;
    }

    public void setSingleProducts(List<Product> singleProducts) {
        this.singleProducts = singleProducts;
    }

    public List<Product> getSingleAndBundleProducts() {
        return singleAndBundleProducts;
    }

    public void setSingleAndBundleProducts(List<Product> singleAndBundleProducts) {
        this.singleAndBundleProducts = singleAndBundleProducts;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getSubCategoryId() {
        return subCategoryId;
    }

    public void setSubCategoryId(String subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

    public String getSubCategoryName() {
        return subCategoryName;
    }

    public void setSubCategoryName(String subCategoryName) {
        this.subCategoryName = subCategoryName;
    }

    public String getSubCategoryArabicName() {
        return subCategoryArabicName;
    }

    public void setSubCategoryArabicName(String subCategoryArabicName) {
        this.subCategoryArabicName = subCategoryArabicName;
    }

    public List<Product> getStockTakeProducts() {
        return stockTakeProducts;
    }

    public void setStockTakeProducts(List<Product> stockTakeProducts) {
        this.stockTakeProducts = stockTakeProducts;
    }

    public List<Product> getCustomizableProducts() {
        return customizableProducts;
    }

    public void setCustomizableProducts(List<Product> customizableProducts) {
        this.customizableProducts = customizableProducts;
    }
}
