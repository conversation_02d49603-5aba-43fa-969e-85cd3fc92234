package models;

import java.util.ArrayList;
import java.util.List;

public class ValidationResults {
    String role;
    String page;
    List<String> validationResults = new ArrayList<>();
    boolean result = true;

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }

    public List<String> getValidationResults() {
        return validationResults;
    }

    public void setValidationResults(List<String> validationResults) {
        this.validationResults = validationResults;
    }

    public boolean isResult() {
        return result;
    }

    public void setResult(boolean result) {
        this.result = result;
    }

    public void addALogToValidationResults(String log){
        validationResults.add(log);
    }
}
