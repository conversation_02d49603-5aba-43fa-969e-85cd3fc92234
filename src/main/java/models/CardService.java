package models;

public class CardService {
    String cardServiceToken; // token fetched from LoginMobile Scheme endpoint
    String passCode; // To be used in the endpoint Login using encrypted mobileNumber and passCode (6 digits)
    String encryptedUserPassCodeObject; // result from encrypting Full MobileNumber and passcode
    String userToken; // Response from the loginWithPassCode endpoint
    String pinCode; // Card pin code (4 digits)
    String breadfastCardId; // Fetched from the cardsPoolApi
    String adminToken; // Fetched from the Admin Panel Login endpoint
    String bcid; //Unique Identifier of physical card
    String nationalId; //Card user national ID
    String requestRefNum; // Response from the checkSender endpoint
    String batchId; //Batch ID for the invitation code
    String invitationCode; //Invitation code for exclusive launch
    String walletUserId; // Unique Identifier of card user
    String packageNumber; // Package number to change card status from registered to received
    String cardLastFourDigits; // Last four digits of card user

    public String getPayScreenToken() {
        return cardServiceToken;
    }

    public void setPayScreenToken(String cardServiceToken) {
        this.cardServiceToken = cardServiceToken;
    }

    public String getUserToken() {
        return userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

    public String getPassCode() {
        return passCode;
    }

    public void setPassCode(String passCode) {
        this.passCode = passCode;
    }

    public String getCardServiceToken() {
        return cardServiceToken;
    }

    public void setCardServiceToken(String cardServiceToken) {
        this.cardServiceToken = cardServiceToken;
    }

    public String getEncryptedUserPassCodeObject() {
        return encryptedUserPassCodeObject;
    }

    public void setEncryptedUserPassCodeObject(String encryptedUserPassCodeObject) {
        this.encryptedUserPassCodeObject = encryptedUserPassCodeObject;
    }

    public String getPinCode() {
        return pinCode;
    }

    public void setPinCode(String pinCode) {
        this.pinCode = pinCode;
    }

    public String getBreadfastCardId() {
        return breadfastCardId;
    }

    public void setBreadfastCardId(String breadfastCardId) {
        this.breadfastCardId = breadfastCardId;
    }

    public String getAdminToken() {
        return adminToken;
    }

    public void setAdminToken(String adminToken) {
        this.adminToken = adminToken;
    }

    public String getBcid() {
        return bcid;
    }

    public void setBcid(String bcid) {
        this.bcid = bcid;
    }

    public String getNationalId() {
        return nationalId;
    }
    public void setNationalId(String nationalId) {
        this.nationalId = nationalId;
    }
    public String getRequestRefNum() {
        return requestRefNum;
    }
    public void setRequestRefNum(String requestRefNum) {
        this.requestRefNum = requestRefNum;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getInvitationCode() {
        return invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }
    public String getWalletUserId() {
        return walletUserId;
    }
    public void setWalletUserId(String walletUserId) {
        this.walletUserId = walletUserId;
    }
    public String getPackageNumber() {
        return packageNumber;
    }
    public void setPackageNumber(String packageNumber) {
        this.packageNumber = packageNumber;
    }
    public String getCardLastFourDigits() {
        return cardLastFourDigits;
    }
    public void setCardLastFourDigits(String cardLastFourDigits) {
        if (cardLastFourDigits != null && cardLastFourDigits.length() >= 4) {
            this.cardLastFourDigits = cardLastFourDigits.substring(cardLastFourDigits.length() - 4);
        } else {
            this.cardLastFourDigits = cardLastFourDigits;
        }
    }
}
