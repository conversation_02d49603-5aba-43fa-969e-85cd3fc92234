package modals.fleetApp.android.bfShops;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidMapViewScreen extends BaseAndroidScreen {
    public AndroidMapViewScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"1, #2716-27313632, Porto Cairo, جاري التوصيل, work, Porto cairo\"]")
    WebElement orderCard;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"تفاصيل الطلب\"]")
    WebElement orderDetailsButton;
    public void clickOnOrderCard(){
        wait.until(ExpectedConditions.elementToBeClickable(orderCard)).click();
    }
    public void clickOnOrderDetailsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(orderDetailsButton)).click();
    }
}

