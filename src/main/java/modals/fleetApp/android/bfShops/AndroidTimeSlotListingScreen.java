package modals.fleetApp.android.bfShops;
import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
public class AndroidTimeSlotListingScreen extends BaseAndroidScreen {
    public AndroidTimeSlotListingScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(id ="deliver_orders_txt")
    WebElement deliverOrdersTxt;
    @FindBy(id ="trip_$tripeId")
    WebElement tripeId;
    @FindBy(id ="bck_btn")
    WebElement backBtn;
    @FindBy(id ="display_btn")
    WebElement displayBtn;
    public boolean isDeliverOrdersTxtAppeared(){
        return isElementDisplayed(deliverOrdersTxt);
    }
    public boolean isTripeIdAppeared(){
        return isElementDisplayed(tripeId);
    }
    public void clickOnBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backBtn)).click();}
    public void clickOnDisplayBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(displayBtn)).click();}
}

