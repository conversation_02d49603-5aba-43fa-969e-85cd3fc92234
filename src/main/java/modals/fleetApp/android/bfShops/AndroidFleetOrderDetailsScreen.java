package modals.fleetApp.android.bfShops;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidFleetOrderDetailsScreen extends BaseAndroidScreen {
    public AndroidFleetOrderDetailsScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.view.ViewGroup[@resource-id=\"START_DELIVERY_TASK_btn\"]")
    WebElement startDeliverySlider;
    @FindBy(xpath = "//android.view.ViewGroup[@resource-id=\"CONFIRM_DELIVERY_TASK_btn\"]")
    WebElement confirmDeliverySlider;
    @FindBy(xpath = "//android.view.ViewGroup[@resource-id=\"COMPLETE_DELIVERY_TASK_btn\"]")
    WebElement completeDeliverySlider;
    public WebElement getStartDeliverySlider() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath("//android.view.ViewGroup[@resource-id=\"START_DELIVERY_TASK_btn\"]")));
        } catch (Exception e) {
            return null;
        }
    }
    public WebElement getConfirmDeliverySlider() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath("//android.view.ViewGroup[@resource-id=\"CONFIRM_DELIVERY_TASK_btn\"]")));
        } catch (Exception e) {
            return null;
        }
    }
    public WebElement getCompleteDeliverySlide() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath("//android.view.ViewGroup[@resource-id=\"COMPLETE_DELIVERY_TASK_btn\"]")));
        } catch (Exception e) {
            return null;
        }
    }

}
