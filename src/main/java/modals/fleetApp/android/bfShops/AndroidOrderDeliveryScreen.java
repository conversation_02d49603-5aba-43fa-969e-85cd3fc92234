package modals.fleetApp.android.bfShops;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidOrderDeliveryScreen extends BaseAndroidScreen {
    public AndroidOrderDeliveryScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(id ="deliver_orders_txt")
    WebElement deliverOrdersTxt;
    @FindBy(xpath =" @FindBy(id =\"deliver_orders_txt\")\n" +
            "    WebElement deliverOrdersTxt;")
    WebElement backBtn;
    @FindBy(id ="display_btn")
    WebElement displayBtn;
    public boolean isDeliverTxtAppeared(){
        return isElementDisplayed(deliverOrdersTxt);
    }
    public void clickOnBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backBtn)).click();
    }
    public void clickOnDisplayBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(displayBtn)).click();
    }

}
