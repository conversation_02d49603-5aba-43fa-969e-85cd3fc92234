package modals.fleetApp.android.bfShops;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
public class AndroidCashCollectionScreen extends BaseAndroidScreen {
    public AndroidCashCollectionScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.widget.TextView[@text=\"218\"]")
    WebElement orderValue;
    @FindBy(xpath = "//android.widget.EditText[@text=\"0\"]")
    WebElement enteredAmountField;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"تأكيد\"]")
    WebElement confirmBtn;
    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"cashCollect_BlockModal_txt\"]")
    WebElement collectionBlockModule;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"الرجوع و المراجعه\"]")
    WebElement backAndReviewBtn;
    @FindBy(xpath = "//android.view.ViewGroup[@resource-id=\"COMPLETE_DELIVERY_TASK_btn\"]")
    WebElement completeDeliverySlider;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"حسنا\"]")
    WebElement okBtn;
    @FindBy(className = "android.widget.EditText")
    WebElement reenterValuTxtBox;
    public int extractCashOrderValue() {
        String valueText = orderValue.getText(); // e.g. "218"
        int value = Integer.parseInt(valueText.trim());
        System.out.println("Extracted Order Value: " + value);
        return value;
    }
    public void enterAmountPlus150() {
        int originalValue = extractCashOrderValue();
        int newAmount = originalValue + 150;
        enteredAmountField.clear(); // clear any existing text
        enteredAmountField.sendKeys(String.valueOf(newAmount));
        System.out.println("Entered Amount: " + newAmount);
    }
    public void inputAmountBasedOnOrderValue(int baseAmount) {
        int amountToEnter = baseAmount +150;
       wait.until(ExpectedConditions.elementToBeClickable( reenterValuTxtBox)).clear();
        reenterValuTxtBox.sendKeys(String.valueOf(amountToEnter));
    }
    public void clickOnConfirmBtn(){
        wait.until(ExpectedConditions.elementToBeClickable( confirmBtn)).click();
    }
    public void clickOnBackAndReviewBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backAndReviewBtn)).click();
    }
    public void clickOnOkBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(okBtn)).click();
    }
    public void enterAmountPlus300() {
        int originalValue = extractCashOrderValue();
        int newAmount = originalValue + 300;
        enteredAmountField.clear(); // clear any existing text
        enteredAmountField.sendKeys(String.valueOf(newAmount));
        System.out.println("Entered Amount: " + newAmount);
    }
    public void enterAmountLessThanTotal() {
        int originalValue = extractCashOrderValue();
        int newAmount = originalValue - 10;
        enteredAmountField.clear(); // clear any existing text
        enteredAmountField.sendKeys(String.valueOf(newAmount));
        System.out.println("Entered Amount: " + newAmount);
    }
    public boolean isCollectionBlockModuleAppeared(){
        return isElementDisplayed(collectionBlockModule);
    }
    public WebElement CompleteDeliverySlider() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath("//android.view.ViewGroup[@resource-id=\"COMPLETE_DELIVERY_TASK_btn\"]")));
        } catch (Exception e) {
            return null;
        }
}}

