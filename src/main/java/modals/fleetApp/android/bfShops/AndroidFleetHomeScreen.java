package modals.fleetApp.android.bfShops;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class AndroidFleetHomeScreen extends BaseAndroidScreen {
    public AndroidFleetHomeScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(id ="locationBottomSheet_id")
    WebElement locationBottomSheet;
    @FindBy(id ="confirmLocation_accept_btn")
    WebElement confirmLocationBtn;
    @FindBy(xpath = "//android.view.ViewGroup[contains(@content-desc,'TRP-')]")
    List<WebElement> tripCards;
    @FindBy(xpath ="//android.widget.TextView[@text=\"الرحلة الحالية\"]")
    WebElement currentTrip;
    public boolean isLocationBottomSheetAppeared(){
        return isElementDisplayed(locationBottomSheet);
    }
    public void clickOnConfirmLocationBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmLocationBtn)).click();
    }
    public void clickOnTripCard() {
        wait.until(ExpectedConditions.visibilityOfAllElements(tripCards));
        tripCards.get(0).click();
    }
    public boolean isCurrentTripAppeared(){
        return isElementDisplayed(currentTrip);
    }
}

