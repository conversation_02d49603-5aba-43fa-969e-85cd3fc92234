package modals.fleetApp.android.bfShops;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class AndroidTripDetailsScreen extends BaseAndroidScreen {
    public AndroidTripDetailsScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(id ="areaPanel_${areaIndex}")
    WebElement areaPanel;
    @FindBy(id ="addressLabel_txt")
    WebElement addressLabelTxt;
    @FindBy(id ="pickup_enabled_btn")
    WebElement scanAWBBtn;
    @FindBy(id ="pickup_dimmed_btn")
    WebElement dimmedPickupBtn;
    @FindBy(xpath = "//android.view.ViewGroup[contains(@content-desc, ', ')]")
    List<WebElement> order_dropdown;
    @FindBy(xpath ="//android.widget.FrameLayout[@resource-id=\"android:id/content\"]/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[3]")
    WebElement pickupButtonOfPickupModule;
    @FindBy(xpath = "//android.widget.TextView[@text=\"إستلام\"]")
    WebElement receive_txt;
    @FindBy(id ="pickUpModule_title_orderNumber")
    WebElement orderNumberOfPickupModule;
    @FindBy(xpath ="//android.widget.FrameLayout[@resource-id=\"android:id/content\"]/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[1]")
    WebElement closeIconOfPickupModule;
    @FindBy(id ="Order_display_btn")
    WebElement displayBtn;
    @FindBy(id ="start_delivery_btn")
    WebElement startDeliveryBtn;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"لا أستطيع مسح الرمز\"]")
    WebElement cantScanCodeBtn;
    @FindBy(xpath ="//android.widget.TextView[contains(@text,'AWB-')]")
    WebElement productCode;
    String codeValue;
    String awbNumber ;
    @FindBy(xpath = "//android.widget.EditText[@resource-id=\"barCode_number\"]")
    WebElement awbTxtField;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"تأكيد\"]")
    WebElement confirmBtn;
    @FindBy(xpath = "//android.widget.TextView[@text=\"تاكيد الاستلام من المخزن\"]")
    WebElement confirmPickupBtn;
    public boolean isPickupModuleAppeared(){
        return isElementDisplayed(receive_txt);
    }
    public boolean isAreaPanelAppeared(){
        return isElementDisplayed(areaPanel);
    }
    public boolean isPickupButtonEnabled(){
        return  isElementDisplayed(scanAWBBtn);
    }
    public boolean isPickupButtonDisabled(){
        return  isElementDisplayed(dimmedPickupBtn);
    }
    public void clickOnScanAWBBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(scanAWBBtn)).click();
    }
    public boolean isOrderNumberOfPickupModule(){
        return isElementDisplayed(orderNumberOfPickupModule);
    }
    public void clickOnCloseIcon(){
        wait.until(ExpectedConditions.elementToBeClickable(closeIconOfPickupModule)).click();
    }
    public void clickOnOrderDisplayBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(displayBtn)).click();
    }
    public boolean isDisplayBtnAppeared(){
        return  isElementDisplayed(displayBtn);
    }
    public void clickOnPickupBtnInModule(){
        wait.until(ExpectedConditions.elementToBeClickable( pickupButtonOfPickupModule)).click();
    }
    public void clickOnStartDeliveryBtn(){
        wait.until(ExpectedConditions.elementToBeClickable( startDeliveryBtn)).click();
    }
    public WebElement getStratDelivryElemnt() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.id("start_delivery_btn")));
        } catch (Exception e) {
            return null;
        }
    }
    public WebElement getPickupBtnInModule() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath("//android.widget.FrameLayout[@resource-id=\"android:id/content\"]/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[3]")));
        } catch (Exception e) {
            return null;
        }
    }
    public void clickONOrderDropDown() {
        wait.until(ExpectedConditions.visibilityOfAllElements(order_dropdown));
        order_dropdown.get(0).click();
    }
    public WebElement getStratTripElemnt() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath("(//android.widget.TextView[@text=\"ابدأ الرحلة\"])[2]")));
        } catch (Exception e) {
            return null;
        }
    }
    public boolean isStartDeliveryBtnDisplayed(){
        return  isElementDisplayed(startDeliveryBtn);
    }
    public void clickOnCantScanCodeBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(cantScanCodeBtn)).click();
    }
    //Method 1 → Get AWB number dynamically
    public void getAWBValue() {
        WebElement awbElement = wait.until(
                ExpectedConditions.visibilityOf(productCode)
        );
        codeValue = awbElement.getText();               // e.g. "AWB-89058306-1"
        awbNumber = codeValue.replace("AWB-", "");      // e.g. "89058306-1"
        System.out.println("Extracted AWB Number: " + awbNumber);
    }
    // Method 2 → Enter AWB number into text field
    public void clickOnAWBTxtField() {
        wait.until(ExpectedConditions.elementToBeClickable(awbTxtField)).click();}
    public void enterAWB() {
        wait.until(ExpectedConditions.elementToBeClickable(awbTxtField)).sendKeys(awbNumber);
    }
    public void clickOnConfirmBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmBtn)).click();
    }
    public WebElement getConfirmPickupBtn() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath("//android.widget.TextView[@text=\"تاكيد الاستلام من المخزن\"]")));
        } catch (Exception e) {
            return null;
        }
    }
}
