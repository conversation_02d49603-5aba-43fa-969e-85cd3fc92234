package modals.fleetApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidSideMenuScreen extends BaseAndroidScreen {

    public AndroidSideMenuScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(id ="side_menu_btn")
    WebElement sideMenuBtn;
    @FindBy(id ="close_btn")
    WebElement closeBtn;
    @FindBy(id ="da_name_txt")
    WebElement daNameTxt;
    @FindBy(id = "hrid_txt")
    WebElement daHrIdTxt;
    @FindBy(id="score_txt")
    WebElement daScoreTxt;
    @FindBy(id="fp_name_txt")
    WebElement fpNameAssigned;
    @FindBy(id="daStatus_warning_txt")
    WebElement changeStatusWarnTxt;
    @FindBy(id="balance_txt")
    WebElement daBalanceTxt;
    @FindBy(id="status_btn")
    WebElement statusToggle;
    @FindBy(id="order_history_btn")
    WebElement orderHistoryBtn;
    @FindBy(id="notificaiton center_btn")
    WebElement notificationCenterBtn;
    @FindBy(id="logout_btn")
    WebElement logoutBtn;

    public void openSideMenu(){
        wait.until(ExpectedConditions.elementToBeClickable(sideMenuBtn)).click();
    }
    public boolean isSideMenuLoaded(){
        return isElementDisplayed(closeBtn);

    }
    public boolean arePlaceHolderButtonsDisplayed(){
        return isElementDisplayed(orderHistoryBtn) && isElementDisplayed(notificationCenterBtn) && isElementDisplayed(statusToggle);

    }

    public String getDaName(){
        wait.until(ExpectedConditions.visibilityOf(daNameTxt));
        return daNameTxt.getText();
    }
    public String getDaHrId(){
        wait.until(ExpectedConditions.visibilityOf(daHrIdTxt));
        return daHrIdTxt.getText();
    }
    public String getDaScore(){
        wait.until(ExpectedConditions.visibilityOf(daScoreTxt));
        return daScoreTxt.getText();
    }
    public String getfpNameAssigned(){
        wait.until(ExpectedConditions.visibilityOf(fpNameAssigned));
        return fpNameAssigned.getText();
    }

    public String getBalance(){
        wait.until(ExpectedConditions.visibilityOf(daBalanceTxt));
        return daBalanceTxt.getText();
    }
    public void closeSideMenu(){
        wait.until(ExpectedConditions.elementToBeClickable(closeBtn)).click();
    }
    
}
