package modals.fleetApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidSplashScreen extends BaseAndroidScreen {
    public AndroidSplashScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(id ="login_landing_txt")
    WebElement landingPageHeader;
    @FindBy(id ="login_landing_btn")
    WebElement loginBtn;
    @FindBy(id = "login_back_btn")
    WebElement backBtn;
    @FindBy(id="login_title")
    WebElement loginTitle;
    @FindBy(id="mobileNumber_cell")
    WebElement mobileNumberField;
    @FindBy(xpath="//android.view.ViewGroup[@content-desc=\"تأكيد\"]")
    WebElement confirmBtn;
    @FindBy(id="password_cell")
    WebElement passwordField;
    @FindBy(xpath="//android.view.ViewGroup[@content-desc=\"تسجيل الدخول\"]")
    WebElement passwordLoginBtn;
    @FindBy(id = "login_useAnotherNumber_btn")
    WebElement useAnotherNumberBtn;
    @FindBy(id="login_numberNotInRightDigits_txt")
    WebElement wrongNumberTxt;
    @FindBy(xpath = "//android.widget.TextView[@text=\"ليس لديك شحنات لتوصيلها\"]")
    WebElement emptyTxtScreen;
    @FindBy(xpath ="//android.widget.TextView[@text=\"الشحنات المطلوب منك توصيلها سوف تظهر هنا\"]")
    WebElement emptySecondTxtScreen;
    @FindBy(id="wrongPass_txt")
    WebElement wrongPassTxt;
    @FindBy(xpath = "//android.widget.FrameLayout[@resource-id=\"android:id/content\"]/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[1]/android.view.ViewGroup[2]/android.view.ViewGroup[1]/android.view.ViewGroup")
    WebElement menuTab;
    @FindBy(xpath = "//android.widget.TextView[@text=\"تسجيل الخروج\"]")
    WebElement logoutBtn;
    @FindBy(id = "login_numberNotRegistered_txt")
    WebElement unAuthorizedNumTxt;

    public String getLandingScreenText(){
        wait.until(ExpectedConditions.visibilityOf(landingPageHeader));
        return landingPageHeader.getText();
    }
    public boolean isLoginButtonDisplayed(){
        return isElementDisplayed(loginBtn);
    }
    public void pressLoginBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(loginBtn))
                .click();
    }
    public void pressBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backBtn))
                .click();
    }
    public boolean isBackButtonDisplayed(){
        return isElementDisplayed(backBtn);
    }
    public String getLoginTitle(){
        return loginTitle.getText();
    }
    public boolean isMobileNumberFieldDisplayed(){
        return isElementDisplayed(mobileNumberField);
    }
    public boolean isConfirmButtonDisplayed(){
        return isElementDisplayed(confirmBtn);
    }
    public boolean isConfirmBtnDisabled() {
        return !confirmBtn.isEnabled();
    }
    public void setMobileNumber(String mobileNumber) {
        wait.until(ExpectedConditions.elementToBeClickable(mobileNumberField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(mobileNumberField))
                .sendKeys(mobileNumber);
    }
    public String getMobileNumber(){
        return mobileNumberField.getText();
    }
    public void pressConfirmBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmBtn))
                .click();
    }
    public boolean isPasswordLoginBtnDisabled() {
        return !passwordLoginBtn.isEnabled();
    }
    public boolean isPasswordFieldDisplayed(){
        return isElementDisplayed(passwordField);
    }
    public void setPassword(String password) {
        wait.until(ExpectedConditions.elementToBeClickable(passwordField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(passwordField))
                .sendKeys(password);
    }
    public String getPassword(){
        return passwordField.getText();
    }
    public boolean isPasswordLoginBtnDisplayed(){
        return isElementDisplayed(passwordLoginBtn);
    }
    public void pressPasswordLoginBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(passwordLoginBtn))
                .click();
    }
    public Boolean isUseAnotherNumberBtnDisplayed(){
        return isElementDisplayed(useAnotherNumberBtn);
    }
    public boolean isUseAnotherNumberBtnEnabled(){
        return useAnotherNumberBtn.isEnabled();
    }
    public String getWrongNumberTxt(){
        wait.until(ExpectedConditions.visibilityOf(wrongNumberTxt));
        return wrongNumberTxt.getText();
    }
    public String getEmptyScreenTxt(){
        wait.until(ExpectedConditions.visibilityOf(emptyTxtScreen));
        return emptyTxtScreen.getText();
    }
    public String getEmptySecondScreenTxt(){
        wait.until(ExpectedConditions.visibilityOf(emptySecondTxtScreen));
        return emptySecondTxtScreen.getText();
    }
    public String getWrongPasswordTxt(){
        wait.until(ExpectedConditions.visibilityOf(wrongPassTxt));
        return wrongPassTxt.getText();
    }
    public void pressMenuTab(){
        wait.until(ExpectedConditions.elementToBeClickable(menuTab))
                .click();
    }
    public void pressLogoutBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(logoutBtn))
                .click();
    }
    public String getUnauthorizedNumberTxt(){
        wait.until(ExpectedConditions.visibilityOf(unAuthorizedNumTxt));
        return unAuthorizedNumTxt.getText();
    }
    public void setTempMobileNumber(String mobileNumber) {
        wait.until(ExpectedConditions.elementToBeClickable(mobileNumberField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(mobileNumberField))
                .sendKeys("01152166887");
    }
    public void setTempPassword(String password) {
        wait.until(ExpectedConditions.elementToBeClickable(passwordField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(passwordField))
                .sendKeys("lihj50rq"); }
    }
