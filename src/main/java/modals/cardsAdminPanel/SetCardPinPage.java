package modals.cardsAdminPanel;

import modals.BaseWebPage;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.testng.Assert;

public class SetCardPinPage extends BaseWebPage {

    public SetCardPinPage(WebDriver webDriver)
    {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }
    String activePlaceHolderXpath = "//span[%s][@class='pin-placeholder active']";
    String setPlaceHolderXpath = "//span[%s][@class='pin-placeholder setted']";
    @FindBy(xpath = "//b[text()='Create PIN']")
    WebElement setPinPageHeader;
    @FindBy(xpath = "//b[text()='Confirm PIN']")
    WebElement confirmPinPageHeader;
    @FindBy(xpath = "//b[text()='Success!']")
    WebElement successPageHeader;
    @FindBy(xpath = "//div[@data-value='1']")
    WebElement digit_1;
    public boolean isSetPinPageDisplayed()
    {
        return (isElementDisplayed(setPinPageHeader));
    }
    public boolean isConfirmPinPageDisplayed()
    {
        return (isElementDisplayed(confirmPinPageHeader));
    }
    public void enterCardPin () {
        // Enter digit '1' four times to set pin
        for (int i = 1; i < 5; i++) {
            WebElement activePlaceholder = webDriver.findElement(By.xpath(String.format(activePlaceHolderXpath, i)));
            wait.until(ExpectedConditions.visibilityOf(activePlaceholder));
            wait.until(ExpectedConditions.elementToBeClickable(digit_1)).click();
            WebElement setPlaceholder = webDriver.findElement(By.xpath(String.format(setPlaceHolderXpath, i)));
            wait.until(ExpectedConditions.visibilityOf(setPlaceholder));
        }
    }
    public void confirmCardPin () {
        // Enter digit '1' four times to confirm pin
        Assert.assertTrue(isConfirmPinPageDisplayed());
        for (int i = 1; i < 5; i++) {
            WebElement activePlaceholder = webDriver.findElement(By.xpath(String.format(activePlaceHolderXpath, i)));
            wait.until(ExpectedConditions.visibilityOf(activePlaceholder));
            wait.until(ExpectedConditions.elementToBeClickable(digit_1)).click();
            WebElement setPlaceholder = webDriver.findElement(By.xpath(String.format(setPlaceHolderXpath, i)));
            wait.until(ExpectedConditions.visibilityOf(setPlaceholder));
        }
    }
    public boolean isSuccessPageDisplayed()
    {
        return (isElementDisplayed(successPageHeader));
    }
}
