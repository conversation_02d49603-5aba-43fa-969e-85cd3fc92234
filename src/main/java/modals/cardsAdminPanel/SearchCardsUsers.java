package modals.cardsAdminPanel;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class SearchCardsUsers extends BaseWebPage {
    public SearchCardsUsers(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }
    @FindBy(xpath = "//app-dashboard//ng-component//div[1]/div/div")
    WebElement searchCardsPageTitle;
    @FindBy(xpath = "//input[@id='mat-input-0']")
    WebElement mobileNumberTextFiled;
    @FindBy(xpath = "//input[@id='mat-input-2' and @data-placeholder='14 digits']")
    WebElement nationalIdTextFiled;
    @FindBy(xpath = "//input[@id='mat-input-1' and @data-placeholder='12 digits']")
    WebElement bcidTextFiled;
    @FindBy(xpath = "//input[@id='mat-input-3' and @data-placeholder='4 digits']")
    WebElement lastFourDigitsTextFiled;
    @FindBy(xpath = "//span[normalize-space()='Search']")
    WebElement searchBtn;
    @FindBy(xpath = "//form/div[2]/div/app-bf-button[2]/button/span[1]")
    WebElement moreOptionsBtn;
    @FindBy(xpath = "//span[normalize-space()='More Details']")
    WebElement moreDetailsBtn;
    @FindBy(xpath = "//div[@class='chip success']")
    WebElement userStatus;
    @FindBy(xpath = "//*[@label='Status']")
    WebElement statusTextField;

    public boolean searchCardPageTitleIsDisplayed() {
        return isElementDisplayed(searchCardsPageTitle);
    }
    public boolean moreDetailsBtnIsDisplayed() {
        return isElementDisplayed(moreDetailsBtn);
    }
    public boolean userStatusIsDisplayed(){
        return isElementDisplayed(userStatus);
    }
    public boolean statusTextFieldIsDisplayed(){
        return isElementDisplayed(statusTextField);
    }
    public void enterMobileNumber(String mobileNumber) {
        enterStringIntoTextField(mobileNumberTextFiled, mobileNumber);
    }
    public void enterNationalId(String nationalId) {
        enterStringIntoTextField(nationalIdTextFiled, nationalId);
    }
    public void enterBcidNumber(String bcidNumber) {
        enterStringIntoTextField(bcidTextFiled, bcidNumber);
    }
    public void enterLastFourDigits(String lastFourDigits) {
        enterStringIntoTextField(lastFourDigitsTextFiled,lastFourDigits );
    }
    public void clickOnSearchBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(searchBtn))
                .click();
    }
    public void clickOnMoreOptionsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(moreOptionsBtn))
                .click();
    }
    public void clickOnMoreDetailsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(moreDetailsBtn))
                .click();
    }
    public void searchByCardUserMobileNumber(String mobileNumber){
        enterMobileNumber(mobileNumber);
        clickOnSearchBtn();
    }
    public void searchByCardUserNationalId(String nationalId){
        enterNationalId(nationalId);
        clickOnSearchBtn();
    }
    public void searchByCardUserBcidNumber(String bcidNumber){
        enterBcidNumber(bcidNumber);
        clickOnSearchBtn();
    }
    public void searchByCardUserLastFourDigits(String lastFourDigits){
        enterLastFourDigits(lastFourDigits);
        clickOnSearchBtn();
    }
    public void searchByRegisteredCardUserMobileNumber(String mobileNumber)
    {
        // Remove +2 from the mobile number
        if (mobileNumber.startsWith("+2")) {
            mobileNumber = mobileNumber.substring(2);}
        enterMobileNumber(mobileNumber);
        clickOnSearchBtn();
    }
    public String getSearchCardsPageTitleText(){
        return wait.until(ExpectedConditions.visibilityOf(searchCardsPageTitle))
                .getText();
    }
}
