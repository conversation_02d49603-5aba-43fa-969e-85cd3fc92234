package modals.cardsAdminPanel;

import modals.BaseWebPage;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class ViewCardUsersDetails extends BaseWebPage {
    public ViewCardUsersDetails(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }
    @FindBy(xpath = "//*[@class='mat-menu-trigger']")
    WebElement threeDotsBtn;
    @FindBy(xpath = "//button[normalize-space()='Replace Breadfast Card']")
    WebElement replaceCardBtn;
    @FindBy(xpath = "//span[normalize-space()='OK, replace card']")
    WebElement confirmReplaceCardBtn;
    @FindBy(xpath = "//mat-card-title[@class='mat-card-title card-title flex-container']")
    WebElement cardUsersDetailsPageTitle;
    @FindBy(xpath = "//mat-card[@class='mat-card mat-focus-indicator replace-card-dialog mat-elevation-z0']")
    WebElement replaceCardModal;
    @FindBy(xpath = "//span[normalize-space()='Card Collected']")
    WebElement cardCollectedBtn;
    @FindBy(xpath = "//snack-bar-container[contains(@class, 'mat-snack-bar-container') and contains(@class, 'success') and contains(@class, 'mat-snack-bar-center')]")
    WebElement replacementRequestToaster;
    @FindBy(xpath = "//*[text()=\" Documents \"]")
    WebElement documentsBtn;
    @FindBy(xpath = "//button[text()=\" Close Card \"]")
    WebElement closeCardBtn;
    @FindBy(xpath = "//span[@class=\"mat-button-wrapper\" and text()=\" Close Card \"]")
    WebElement confirmCloseCardBtn;
    @FindBy(xpath = "//div[@class=\"chip error\"]")
    WebElement statusLabel;
    @FindBy(xpath = "//button[normalize-space()='Unblock Passcode']")
    WebElement unblockPasscodeBtn;
    @FindBy(xpath = "//mat-card-content[@class='mat-card-content unblock-dialog__content']")
    WebElement unblockPasscodeModal;
    @FindBy(xpath = "//span[normalize-space()='OK, unblock']")
    WebElement confirmUnblockPasscodeBtn;
    @FindBy(xpath = "//snack-bar-container[contains(@class, 'mat-snack-bar-container') and contains(@class, 'success') and contains(@class, 'mat-snack-bar-center')]")
    WebElement unblockPasscodeSuccessMessage;
    @FindBy(xpath = "//button[span[mat-icon[@data-mat-icon-name='upload']] and span[contains(text(),'Upload Documents')]]")
    WebElement uploadDoc;
    @FindBy(xpath = "//input[@type=\"file\"]")
    WebElement uploadDocImage;
    @FindBy(xpath = "//h4[text()=\"No Documents Uploaded yet\"]")
    WebElement noDoc;
    @FindBy(xpath = "//button[text()=\" Schedule to SFTP \"]")
    WebElement scheduleToSFTPBtn;
    @FindBy(xpath = "//button[normalize-space(.)='Schedule']")
    WebElement scheduleBtn;
    @FindBy(xpath = "//p[text()=\" Scheduled \"]")
    WebElement scheduleStatusLabel;
    @FindBy(xpath = "//button[normalize-space()='Reset PIN Count']")
    WebElement resetPinCountBtn;
    @FindBy(xpath = "//mat-card[@class='mat-card mat-focus-indicator reset-pin-dialog mat-elevation-z0']")
    WebElement resetPinCountModal;
    @FindBy(xpath = "//span[normalize-space()='OK, reset PIN count']")
    WebElement confirmResetPinCountBtn;
    @FindBy(xpath = "//simple-snack-bar[@class='mat-simple-snackbar ng-star-inserted']")
    WebElement customerPinUnblockedToaster;
    @FindBy(xpath = "//button[text()=\" Delete \"]")
    WebElement deleteDocBtn;
    @FindBy(xpath = "//mat-card-title[@class='mat-card-title confirm-dialog__header__title']")
    WebElement deleteDocModal;
    @FindBy(xpath = "//span[normalize-space()='Delete Document']")
    WebElement confirmDeleteDocBtn;
    @FindBy(xpath = "//snack-bar-container[contains(@class, 'mat-snack-bar-container') and contains(@class, 'success') and contains(@class, 'mat-snack-bar-center')]")
    WebElement deleteDocSuccessMessage;
    @FindBy(xpath = "//span[@class='mat-button-wrapper' and text()=' Cancel ']")
    WebElement cancelDocBtn;
    @FindBy(xpath = "//mat-card-title[@class='mat-card-title confirm-dialog__header__title']")
    WebElement cancelDocModal;
    @FindBy(xpath = "//span[normalize-space()='Yes']")
    WebElement confirmCancelDocBtn;
    @FindBy(xpath = "//snack-bar-container[contains(@class, 'mat-snack-bar-container') and contains(@class, 'success') and contains(@class, 'mat-snack-bar-center')]")
    WebElement cancelDocSuccessMessage;
    @FindBy(xpath = "//app-user-data/mat-card[1]/mat-card-title/a")
    WebElement editCustomerDetailsBtn;

    public boolean cardUsersDetailsPageTitleIsDisplayed(){
        return isElementDisplayed(cardUsersDetailsPageTitle);
    }
    public boolean cardCollectedBtnIsDisplayed(){
        return isElementDisplayed(cardCollectedBtn);
    }
    public boolean replacementRequestToasterIsDisplayed(){
        return isElementDisplayed(replacementRequestToaster);
    }
    public boolean replaceCardModalIsDisplayed(){
        return isElementDisplayed(replaceCardModal);
    }
    public boolean unblockPasscodeSuccessMessageIsDisplayed(){
        return isElementDisplayed(unblockPasscodeSuccessMessage);
    }
    public boolean unblockPasscodeModalIsDisplayed(){
        return isElementDisplayed(unblockPasscodeModal);
    }
    public boolean noFileUploaded(){
        return  isElementDisplayed(noDoc);
    }
    public boolean displayedOfScheduledStatus(){
        return isElementDisplayed(scheduleStatusLabel);
    }
    public boolean resetPinCountModalIsDisplayed(){
        return isElementDisplayed(resetPinCountModal);
    }
    public boolean customerPinUnblockedToasterIsDisplayed(){
        return isElementDisplayed(customerPinUnblockedToaster);
    }
    public boolean deleteDocModalIsDisplayed(){
        return isElementDisplayed(deleteDocModal);
    }
    public boolean cancelDocModalIsDisplayed(){
        return isElementDisplayed(cancelDocModal);
    }
    public boolean deleteDocSuccessMessageIsDisplayed(){
        return isElementDisplayed(deleteDocSuccessMessage);
    }
    public boolean cancelDocSuccessMessageIsDisplayed(){
        return isElementDisplayed(cancelDocSuccessMessage);
    }
    public void clickOnThreeDotsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(threeDotsBtn))
                .click();
    }
    public void clickOnReplaceCardBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(replaceCardBtn))
                .click();
    }
    public void clickOnConfirmReplaceCardBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmReplaceCardBtn))
                .click();
    }
    public void clickOnUnblockPasscodeBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(unblockPasscodeBtn))
                .click();
    }
    public void clickOnConfirmUnblockPasscodeBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmUnblockPasscodeBtn))
                .click();
    }
    public void clickOnCloseBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(closeCardBtn)).click();
    }
    public void clickOnConfirmCloseBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmCloseCardBtn)).click();
    }
    public void clickOnResetPinCountBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(resetPinCountBtn))
                .click();
    }
    public void clickOnConfirmResetPinCountBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmResetPinCountBtn))
                .click();
    }
    public void clickOnDocTab(){
        wait.until(ExpectedConditions.elementToBeClickable(documentsBtn)).click();}
    public void clickOnUploadDocs(){
        wait.until(ExpectedConditions.elementToBeClickable(uploadDoc)).click();}
    public void uploadDocImage(String imagePath) {
        wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//input[@type=\"file\"]")));
        uploadDocImage.sendKeys(imagePath);}
    public void clickOnThreeDotBtn() {
        wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//button[@class=\"mat-focus-indicator bf-button mat-flat-button mat-button-base bf-button-size-sm bf-button-type-primary mat-primary\"]")))
            .click();}
    public void clickOnScheduleToSFTPBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(scheduleToSFTPBtn)).click();}
    public void clickOnScheduleBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(scheduleBtn)).click();}
    public void clickOnDeleteDocBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(deleteDocBtn))
                .click();
    }
    public void clickOnCancelDocBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(cancelDocBtn))
                .click();
    }
    public void clickOnConfirmDeleteDocBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmDeleteDocBtn))
                .click();
    }
    public void clickOnConfirmCancelDocBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmCancelDocBtn))
                .click();
    }
    public void clickOnEditCustomerDetailsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(editCustomerDetailsBtn))
                .click();
    }
    public String getClosedStatusText(){
        return wait.until(ExpectedConditions.visibilityOf(statusLabel))
                .getText();}
    public String getReplacementRequestToasterText(){
        return wait.until(ExpectedConditions.visibilityOf(replacementRequestToaster))
                .getText();
    }
    public String getUnblockPasscodeSuccessMessageText(){
        return wait.until(ExpectedConditions.visibilityOf(unblockPasscodeSuccessMessage))
                .getText();
    }
    public String getResetPinCountToasterText(){
        return wait.until(ExpectedConditions.visibilityOf(customerPinUnblockedToaster))
                .getText();
    }
    public String getDeleteDocSuccessMessageText(){
        return wait.until(ExpectedConditions.visibilityOf(deleteDocSuccessMessage))
                .getText();
    }
    public String getCancelDocSuccessMessageText(){
        return wait.until(ExpectedConditions.visibilityOf(cancelDocSuccessMessage))
                .getText();
    }
}
