package modals.cardsAdminPanel;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class CardPanelDashboard extends BaseWebPage {
    public CardPanelDashboard(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }
    @FindBy(xpath = "//h1[@class='centered-text']")
    WebElement dashboardTitle;
    @FindBy(xpath = "//a[contains(@class, 'nav-link') and contains(@class, 'nav-dropdown-toggle') and text()='Card Users']")
    WebElement cardUsersDropDownList;
    @FindBy(xpath = "//a[text()=' Search Cards']")
    WebElement searchCardsTab;
    public boolean dashboardTitleIsDisplayed(){
        return isElementDisplayed(dashboardTitle);
    }
    public boolean cardUsersListIsDisplayed() {return isElementDisplayed(cardUsersDropDownList);}
    public void clickOnCardUsersDropDownList(){
        wait.until(ExpectedConditions.elementToBeClickable(cardUsersDropDownList))
                .click();
    }
    public void clickOnSearchCardsTab(){
        wait.until(ExpectedConditions.elementToBeClickable(searchCardsTab))
                .click();
    }

    public String getPageTitleText(){
        return wait.until(ExpectedConditions.visibilityOf(dashboardTitle))
                .getText();
    }
}
