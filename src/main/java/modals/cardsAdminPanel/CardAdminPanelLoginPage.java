package modals.cardsAdminPanel;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class CardAdminPanelLoginPage extends BaseWebPage {
    public CardAdminPanelLoginPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    private final String pageUrlPath = "/#/pages/login";

    @FindBy(xpath = "//input[@placeholder='Username Name']")
    WebElement userNameField;

    @FindBy(xpath = "//input[@type=\"password\"]")
    WebElement passwordField;

    @FindBy(xpath = "//button[@type=\"submit\"]")
    WebElement loginBtn;

    @FindBy(xpath = "//h4[ text()='Login']")
    WebElement pageTitle;

    public void goToPage(){
        visitASubPage(pageUrlPath);
    }

    public void enterUserName(String userName) {
        enterStringIntoTextField(userNameField, userName);
    }

    public void enterPassword(String password){
        enterStringIntoTextField(passwordField, password);
    }

    public void clickLoginBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(loginBtn))
                .click();
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void fillLoginFormAndSubmit(String username, String password){
        enterUserName(username);
        enterPassword(password);
        clickLoginBtn();
    }

    public String getPageTitleText(){
        return wait.until(ExpectedConditions.visibilityOf(pageTitle))
                .getText();
    }
}
