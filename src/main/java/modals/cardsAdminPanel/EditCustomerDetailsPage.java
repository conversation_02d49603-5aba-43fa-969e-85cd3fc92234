package modals.cardsAdminPanel;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
public class EditCustomerDetailsPage extends BaseWebPage {
    public EditCustomerDetailsPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }
    @FindBy(xpath = "//h3[text()='Edit Customer Details']")
    WebElement editCustomerDetailsPageTitle;
    @FindBy(xpath = "//input[@placeholder='Address']")
    WebElement customerAddressTextField;
    @FindBy(xpath = "//select[@formcontrolname='city']/option[@value='Cairo']")
    WebElement customerCityDropDownList;
    @FindBy(xpath = "//select[@formcontrolname='gender']/option[@value='female']")
    WebElement customerGenderDropDownList;
    @FindBy(xpath = "//button[contains(text(), 'Confirm')]")
    WebElement confirmBtn;
    @FindBy(xpath = "//snack-bar-container[contains(@class, 'mat-snack-bar-container') and contains(@class, 'success') and contains(@class, 'mat-snack-bar-center')]")
    WebElement editDetailsSuccessMessage;

    public boolean editCustomerDetailsPageTitleIsDisplayed() {
        return isElementDisplayed(editCustomerDetailsPageTitle);
    }
    public void enterCustomerAddress(String address) {
        wait.until(ExpectedConditions.visibilityOf(customerAddressTextField)).sendKeys(address);
    }
    public void selectCustomerCity() {
        wait.until(ExpectedConditions.elementToBeClickable(customerCityDropDownList)).click();
    }
    public void selectCustomerGender() {
        wait.until(ExpectedConditions.elementToBeClickable(customerGenderDropDownList)).click();
    }
    public void clickOnConfirmBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmBtn)).click();
    }
    public String getEditDetailsSuccessMessage(){
        return wait.until(ExpectedConditions.visibilityOf(editDetailsSuccessMessage))
                .getText();
    }
}
