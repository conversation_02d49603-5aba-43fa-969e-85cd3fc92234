package modals.wordpressAdmin;

import modals.BaseWebPage;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.Select;

public class UsersListPage extends BaseWebPage {
    public UsersListPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "wp-admin/users.php";

    @FindBy(id = "user-search-input")
    WebElement searchTxtField;

    @FindBy(id = "search-submit")
    WebElement searchSubmitBtn;

    @FindBy(id = "the-list")
    WebElement usersTable;

    @FindBy(xpath = "//h1[text()='Users']")
    WebElement pageTitle;

    @FindBy(id = "new_role")
    WebElement rolesDropDown;

    @FindBy(id = "changeit")
    WebElement changeRoleBtn;

    String pageSubtitleXpath = "//span[@class='subtitle'][text() = 'Search results for “%s”']";

    String targetUserRowId = "user_%s";

    String userRowCheckBox = "//input[@type='checkbox' and @id='%s']";

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public void enterPhoneNumberInSearchTxtField(String searchString){
        wait.until(ExpectedConditions.visibilityOf(searchTxtField)).sendKeys(searchString);
        wait.until(ExpectedConditions.textToBePresentInElementValue(searchTxtField, searchString));
    }

    public void clickSearchBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(searchSubmitBtn))
                .click();
    }

    public String getRowIDForPhoneNumber(String phoneNumber){
        // Find all rows in the table
        java.util.List<WebElement> rows = usersTable.findElements(By.tagName("tr"));
        String foundID = null;

        // Iterate through each row to find the matching phone number
        for (WebElement row : rows) {
            WebElement phoneNumberCell = row.findElement(By.xpath(".//td[@data-colname='Phone Number']"));
            String rowPhoneNumber = phoneNumberCell.getText().trim();

            if (rowPhoneNumber.contains(phoneNumber)) {
                // Get the id attribute value of the matched row
                foundID = row.getAttribute("id").replaceAll("\\D+", "");
                break; // Stop searching after finding the match
            }
        }
        return foundID;
    }

    public void selectUserRecordFromTable(String phoneNumber){
        String userID = String.format(targetUserRowId, getRowIDForPhoneNumber(phoneNumber));
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(userRowCheckBox, userID)))).click();
    }

    public void selectRoleFromDropdown(String roleCodeName){
        Select dropdown = new Select(rolesDropDown);
        dropdown.selectByValue(roleCodeName);
    }

    public void clickChangeRoleApplyBtn(String phoneNumber){
        wait.until(ExpectedConditions.elementToBeClickable(changeRoleBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOfElementLocated(
                By.xpath(String.format(pageSubtitleXpath, phoneNumber))));
    }

    public void searchForUserAndChangeRole(String roleCodeName, String phoneNumber){
        goToPage();
        isPageDisplayed();
        enterPhoneNumberInSearchTxtField(phoneNumber);
        clickSearchBtn();
        selectUserRecordFromTable(phoneNumber);
        selectRoleFromDropdown(roleCodeName);
        clickChangeRoleApplyBtn(phoneNumber);
    }
}
