package modals.wordpressAdmin;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class PostsAdminPage extends BaseWebPage {
    public PostsAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "wp-admin/edit.php";

    @FindBy(xpath = "//h1[@class='wp-heading-inline' and text()='Posts']")
    WebElement pageTitle;

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }
}
