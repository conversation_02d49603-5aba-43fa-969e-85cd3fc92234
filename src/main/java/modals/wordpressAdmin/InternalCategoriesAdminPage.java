package modals.wordpressAdmin;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class InternalCategoriesAdminPage extends BaseWebPage {
    public InternalCategoriesAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "wp-admin/edit-tags.php?taxonomy=internal_categories&post_type=product";

    @FindBy(xpath = "//h1[@class='wp-heading-inline' and text()='Internal Categories']")
    WebElement pageTitle;

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }
}
