package modals.wordpressAdmin;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class CssSettingsAdminPage extends BaseWebPage {
    public CssSettingsAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "wp-admin/admin.php?page=css-settings";

    @FindBy(xpath = "//h1[text()='CSS Settings']")
    WebElement pageTitle;

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }
}
