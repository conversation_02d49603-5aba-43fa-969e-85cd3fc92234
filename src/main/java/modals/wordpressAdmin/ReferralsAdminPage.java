package modals.wordpressAdmin;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class ReferralsAdminPage extends BaseWebPage {
    public ReferralsAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }
    String pagePath = "wp-admin/admin.php?page=referrals";
    @FindBy(xpath = "//h1[text()='Referrals']")
    WebElement pageTitle;
    @FindBy (id="acf-referrer_incentive-referrer_incentive_type")
    WebElement referrerFixedAmountType;
    @FindBy (id="acf-referrer_incentive-referrer_amount")
    WebElement referrerAmount;
    @FindBy (id="acf-referrer_incentive-referrer_amount_orders")
    WebElement referrerAmountOrdersNumber;
    @FindBy (id="acf-referrer_incentive-referrer_incentive_type-cashback_percentage")
    WebElement referrerCashbackPercentageType;
    @FindBy (id="acf-referrer_incentive-referrer_cashback_percent")
    WebElement referrerCashbackPercent;
    @FindBy (id="acf-referrer_incentive-referrer_cashback_cap")
    WebElement referrerCashbackCapAmount;
    @FindBy (id="acf-referrer_incentive-referrer_cashback_orders")
    WebElement referrerCashbackOrdersNumber;
    @FindBy (id="acf-referred_incentive-first_referred_reward-referred_incentive_type")
    WebElement firstReferredRewardDiscountPercentageType;
    @FindBy (id="acf-referred_incentive-first_referred_reward-referred_discount_percent")
    WebElement firstReferredRewardDiscountPercent;
    @FindBy (id="acf-referred_incentive-first_referred_reward-referred_discount_cap")
    WebElement firstReferredRewardDiscountPercentCap;
    @FindBy (id="acf-referred_incentive-first_referred_reward-referred_discount_orders")
    WebElement firstReferredRewardDiscountPercentOrdersNumber;
    @FindBy (id="acf-referred_incentive-first_referred_reward-referred_incentive_type-cashback_percentage")
    WebElement firstReferredRewardCashbackPercentageType;
    @FindBy (id="acf-referred_incentive-first_referred_reward-referred_cashback_percent")
    WebElement firstReferredRewardCashbackPercent;
    @FindBy (id="acf-referred_incentive-first_referred_reward-referred_cashback_cap")
    WebElement firstReferredRewardCashbackPercentCapAmount;
    @FindBy (id="acf-referred_incentive-first_referred_reward-referred_cashback_orders")
    WebElement firstReferredRewardCashbackPercentOrdersNumber;
    @FindBy (id="acf-referred_incentive-second_referred_reward-referred_sec_incentive_type")
    WebElement secondReferredRewardFreeDeliveryType;
    @FindBy (id="acf-referred_incentive-second_referred_reward-referred_free_delivery_orders")
    WebElement secondReferredRewardFreeDeliveryOrdersNumber;
    @FindBy (id="acf-referred_incentive-second_referred_reward-referred_sec_incentive_type-cashback_percentage")
    WebElement secondReferredRewardCashbackPercentageType;
    @FindBy (id="acf-referred_incentive-second_referred_reward-referred_sec_cashback_percent")
    WebElement secondReferredRewardCashbackPercent;
    @FindBy (id="acf-referred_incentive-second_referred_reward-referred_sec_cashback_cap")
    WebElement secondReferredRewardCashbackPercentCapAmount;
    @FindBy (id="acf-referred_incentive-second_referred_reward-referred_sec_cashback_orders")
    WebElement secondReferredRewardCashbackPercentOrdersNumbers;
    @FindBy (id="acf-referred_incentive-second_referred_reward-referred_sec_incentive_type-none")
    WebElement secondReferredRewardNoneType;
    @FindBy (id="acf-field_5d1e74c0c46eccrpo")
    WebElement numberPlacedOrdersToBeAbleToUseReferralDropDown;
    @FindBy (id="acf-field_5d1e74c0c46eccrta")
    WebElement totalAmountPlacedOrdersToBeAbleToUseReferral;
    @FindBy (xpath="//input[@type='submit' and @value='Update']")
    WebElement updateBtn;

    public void goToPage(){
        visitASubPage(pagePath);
    }
    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }
    public void clickReferrerFixedAmountType() {
        wait.until(ExpectedConditions.elementToBeClickable(referrerFixedAmountType)).click();
    }
    public void enterReferrerAmount(String amount) {
        wait.until(ExpectedConditions.elementToBeClickable(referrerAmount)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(referrerAmount)).sendKeys(amount);
    }
    public void enterReferrerAmountOrdersNumber(String ordersNumber) {
        wait.until(ExpectedConditions.elementToBeClickable(referrerAmountOrdersNumber)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(referrerAmountOrdersNumber)).sendKeys(ordersNumber);
    }
    public void clickReferrerCashbackPercentageType() {
        wait.until(ExpectedConditions.elementToBeClickable(referrerCashbackPercentageType)).click();
    }
    public void enterReferrerCashbackPercent(String cashbackPercent) {
        wait.until(ExpectedConditions.elementToBeClickable(referrerCashbackPercent)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(referrerCashbackPercent)).sendKeys(cashbackPercent);
    }
    public void enterReferrerCashbackCapAmount(String cashbackCapAmount) {
        wait.until(ExpectedConditions.elementToBeClickable(referrerCashbackCapAmount)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(referrerCashbackCapAmount)).sendKeys(cashbackCapAmount);
    }
    public void enterReferrerCashbackOrdersNumber(String cashbackOrdersNumber) {
        wait.until(ExpectedConditions.elementToBeClickable(referrerCashbackOrdersNumber)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(referrerCashbackOrdersNumber)).sendKeys(cashbackOrdersNumber);
    }
    public void clickFirstReferredRewardDiscountPercentageType() {
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardDiscountPercentageType)).click();
    }
    public void enterFirstReferredRewardDiscountPercent(String discountPercent) {
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardDiscountPercent)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardDiscountPercent)).sendKeys(discountPercent);
    }
    public void enterFirstReferredRewardDiscountPercentCap(String discountCapAmount) {
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardDiscountPercentCap)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardDiscountPercentCap)).sendKeys(discountCapAmount);
    }
    public void enterFirstReferredRewardDiscountPercentOrdersNumber(String discountOrdersNumber) {
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardDiscountPercentOrdersNumber)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardDiscountPercentOrdersNumber)).sendKeys(discountOrdersNumber);
    }
    public void clickFirstReferredRewardCashbackPercentageType() {
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardCashbackPercentageType)).click();
    }
    public void enterFirstReferredRewardCashbackPercent(String cashbackPercent) {
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardCashbackPercent)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardCashbackPercent)).sendKeys(cashbackPercent);
    }
    public void enterFirstReferredRewardCashbackPercentCap(String cashbackCapAmount) {
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardCashbackPercentCapAmount)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardCashbackPercentCapAmount)).sendKeys(cashbackCapAmount);
    }
    public void enterFirstReferredRewardCashbackPercentOrdersNumber(String cashbackOrdersNumber) {
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardCashbackPercentOrdersNumber)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(firstReferredRewardCashbackPercentOrdersNumber)).sendKeys(cashbackOrdersNumber);
    }
    public void clickSecondReferredRewardFreeDeliveryType() {
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardFreeDeliveryType)).click();
    }
    public void enterSecondReferredRewardFreeDeliveryOrdersNumber(String ordersNumber) {
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardFreeDeliveryOrdersNumber)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardFreeDeliveryOrdersNumber)).sendKeys(ordersNumber);
    }
    public void clickSecondReferredRewardCashbackPercentageType() {
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardCashbackPercentageType)).click();
    }
    public void enterSecondReferredRewardCashbackPercent(String cashbackPercent) {
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardCashbackPercent)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardCashbackPercent)).sendKeys(cashbackPercent);
    }
    public void enterSecondReferredRewardCashbackCapAmount(String cashbackCapAmount) {
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardCashbackPercentCapAmount)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardCashbackPercentCapAmount)).sendKeys(cashbackCapAmount);
    }
    public void enterSecondReferredRewardCashbackPercentOrdersNumber(String cashbackOrdersNumber) {
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardCashbackPercentOrdersNumbers)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardCashbackPercentOrdersNumbers)).sendKeys(cashbackOrdersNumber);
    }
    public void clickNoneSecondReferredRewardType() {
        wait.until(ExpectedConditions.elementToBeClickable(secondReferredRewardNoneType)).click();
    }
    public void selectNumberPlacedOrdersToBeAbleToUseReferral(String placedOrderNumbers) {
        selectTheDropDownList(numberPlacedOrdersToBeAbleToUseReferralDropDown, placedOrderNumbers);
    }
    public void enterTotalAmountPlacedOrdersToBeAbleToUseReferral(String ordersNumber) {
        wait.until(ExpectedConditions.elementToBeClickable(totalAmountPlacedOrdersToBeAbleToUseReferral)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(totalAmountPlacedOrdersToBeAbleToUseReferral)).sendKeys(ordersNumber);
    }
    public void clickUpdateBtn() {
        scrollUpToTop();
        wait.until(ExpectedConditions.visibilityOf(updateBtn)).click();
    }
}
