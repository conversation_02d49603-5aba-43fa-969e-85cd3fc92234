package modals.wordpressAdmin;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class VodafoneCouponAdminPage extends BaseWebPage {
    public VodafoneCouponAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "wp-admin/admin.php?page=vodafone-coupon";

    @FindBy(xpath = "//h1[text()='Vodafone coupon']")
    WebElement pageTitle;

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }
}
