package modals.wordpressAdmin;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class PagesAdminPage extends BaseWebPage {
    public PagesAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "wp-admin/edit.php?post_type=page";

    @FindBy(xpath = "//h1[@class='wp-heading-inline' and text()='Pages']")
    WebElement pageTitle;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void goToPage(){
        visitASubPage(pagePath);
    }
}
