package modals.wordpressAdmin;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class ProductCategoriesAdminPage extends BaseWebPage {
    public ProductCategoriesAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "wp-admin/edit-tags.php?taxonomy=product_cat&post_type=product";

    @FindBy(xpath = "//h1[@class='wp-heading-inline' and text()='Product categories']")
    WebElement pageTitle;

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }
}
