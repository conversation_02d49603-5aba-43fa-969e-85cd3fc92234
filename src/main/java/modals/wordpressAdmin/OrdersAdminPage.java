package modals.wordpressAdmin;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class OrdersAdminPage extends BaseWebPage {
    public OrdersAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "wp-admin/edit.php?post_type=shop_order";

    @FindBy(xpath = "//h1[@class='wp-heading-inline' and text()='Orders']")
    WebElement pageTitle;

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        try {
            wait.until(ExpectedConditions.textToBePresentInElement(pageTitle, "orders"));
            return true;
        } catch (Exception e){
            return false;
        }
    }
}
