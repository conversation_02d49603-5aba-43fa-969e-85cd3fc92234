package modals.midMilePage;

import helpers.apiClients.webApiClients.ControlRoomV2ApiClient;
import modals.BaseWebPage;
import org.openqa.selenium.*;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.slf4j.LoggerFactory;
import org.testng.annotations.Test;

import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;
@Test
public class OrdersPage extends BaseWebPage {
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(ControlRoomV2ApiClient.class);
    private JavascriptExecutor js;
    Actions actions = new Actions(webDriver);

    public OrdersPage(WebDriver webDriver) {
        super(webDriver);
        this.js = (JavascriptExecutor) webDriver;
        PageFactory.initElements(webDriver, this);
    }

    String pathPage = "dashboard/mid-mile/orders";
    String datePickerTargetDateSelector = "//td[@title='%s']";
    @FindBy(xpath = "//div[@class='ant-select-selection-overflow']")
    WebElement locationSourceDropDownlist;
    @FindBy(xpath = "//div[contains(text(),'All source locations')]")
    WebElement sourceLocationOption;
    @FindBy(xpath = "//span[@class='ant-select-selection-item-content']")
    WebElement sourceLocationOption2;
    @FindBy(id = "orders_entryPoint_date_container")
    WebElement calendarDate;
    @FindBy(id = "entryPoint_sourceLocation_cell_list")
    List<WebElement> fpOptionsList;
    @FindBy(id = "entryPoint_show_btn")
    WebElement showButton;
    @FindBy(id = "listing_search_cell")
    WebElement searchTextBox;
    @FindBy(xpath = "//span[contains(text(), 'Select source location')]")
    WebElement srcLocationModuleTitle;
    @FindBy(xpath = "//span[@title='Orders']")
    WebElement orderTitle;
    @FindBy(id = "listing_search_button")
    WebElement applyFilterBtn;
    @FindBy(xpath = "//th[@id='listing_order_column_text']")
    WebElement orderTableTitle;
    @FindBy(xpath = "//*[name()='path' and contains(@class,'ant-empty-')]")
    WebElement dataTest;
    @FindBy(xpath = "//div[@class='ant-empty-description']\n" + "\n")
    WebElement dataInfo;
    String itemDDL = "//div[@class='rc-virtual-list-holder-inner']//div[contains(@title, '%s')]";
    @FindBy(xpath = "//strong[contains(text(), 'Assign Truck')]")
    WebElement assignTruckBtn;
    @FindBy(xpath = "//span[(text()='Assign')]")
    WebElement assignBtnOfTruckModal;
    @FindBy(id = "orders_list_assignTruckModal_search_container")
    WebElement searchFieldOfTruckModal;
    @FindBy(xpath = "//label[contains(@class,'ant-radio-wrapper')]")
    WebElement truckOptionOfTruckModal;
    @FindBy(xpath = "//label[contains(@class,'ant-radio-wrapper')]")
    List<WebElement> truckOptions;
    @FindBy(xpath = "//div[contains(text(), 'Truck assigned successfully')]")
    WebElement successMessageOfTruckGotAssigned;
    @FindBy(id = "orders_list_statusColumn_%s_txt")
    WebElement statusOfOrderAtStatusColumn;
    @FindBy(xpath = "//*[contains(@id, 'editTruck_') and contains(@id, '_btn')]")
    WebElement editButtonOfTruckColumn ;
    @FindBy(id = "orders_list_assignDispatcher_%s_btn")
    WebElement assignDispatcherBtn;
    @FindBy(xpath = "//div[contains(text(), 'Assign dispatcher')]")
    WebElement assignDispatcherModalTitle;
    @FindBy(xpath = "//span[contains(text(), 'No truck assigned yet')]")
    WebElement emptyTruckScreenTxt;
    @FindBy(xpath = "//span[contains(text(), 'Assigned Truck')]")
    WebElement assignedTruckTitle;
    @FindBy(xpath = "//span[contains(text(), 'Assign Truck')]")
    WebElement titleOfTruckAssignModal;
    @FindBy(xpath = "//strong[contains(text(), 'Date')]")
    WebElement dateLabel;
    @FindBy(xpath = "//input[@id='status_cell']")
    WebElement statusFilter;
    @FindBy(xpath = "//span[@class='ant-typography' and text()='-']")
    WebElement emptyTruck;
    @FindBy(xpath = "(//span[contains(text(), 'Canceled')])")
    WebElement canceledStatus;
    @FindBy(xpath = "//th[contains(text(), 'Truck details')]")
    WebElement truckDetailsLabelOfAssignTruckModal;
    @FindBy(xpath = "//th[contains(text(), '# assigned orders')]")
    WebElement assignedOrderLabelOfAssignTruckModal;
    @FindBy(xpath = "(//span[contains(text(), 'Arrived')])")
    WebElement arrivedStatus;
    @FindBy(xpath = "(//span[contains(text(), 'Completed')])")
    WebElement completedStatus;
    @FindBy(xpath = "(//span[contains(text(), 'Placed')])")
    WebElement placedStatus;
    public void goToPage() {
        visitASubPage(pathPage);
    }
    public void clickShowButton() {
        wait.until(ExpectedConditions.elementToBeClickable(showButton)).click();
    }

    public void searchForOrder(String searchString) {
        wait.until(ExpectedConditions.elementToBeClickable(searchTextBox)).click();
        wait.until(ExpectedConditions.elementToBeClickable(searchTextBox))
                .sendKeys(searchString);
    }

    public boolean isOrderModuleOpen() {
        wait.until(ExpectedConditions.visibilityOf(orderTitle));
        return isElementDisplayed(orderTitle);
    }

    public void clickFirstOption() {
        wait.until(ExpectedConditions.elementToBeClickable(locationSourceDropDownlist)).click();
        wait.until(ExpectedConditions.elementToBeClickable(sourceLocationOption)).click();
        closeDropDown();
    }

    public boolean isApplyFilterDisplay() {
        wait.until(ExpectedConditions.visibilityOf(applyFilterBtn));
        return isElementDisplayed(applyFilterBtn);
    }
    public boolean isDataDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(dataInfo));
        return isElementDisplayed(dataInfo);
    }
    public void clickApply() throws InterruptedException {
        wait.until(ExpectedConditions.elementToBeClickable(applyFilterBtn)).click();
        Thread.sleep(2000);
    }
    public void clickAssignTruck() {
        js.executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", assignTruckBtn);
        logger.info("Assign Truck button found and scrolled into view.");
        wait.until(ExpectedConditions.elementToBeClickable(assignTruckBtn)).click();
    }

    public boolean isAssignOfTruckModalDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(assignBtnOfTruckModal));
        return isElementDisplayed(assignBtnOfTruckModal);
    }

    public void clickAssignOfTruckModal() {
        wait.until(ExpectedConditions.elementToBeClickable(assignBtnOfTruckModal)).click();
    }
    public void chooseTargetDate(String targetDate, String dateFormat) {
        wait.until(ExpectedConditions.visibilityOf(srcLocationModuleTitle));
        wait.until(ExpectedConditions.visibilityOf(dateLabel));
        enterDateIntoTextField(changeDateFormatToTargetFormat(targetDate, dateFormat, "dd-MM-yyyy"));
        pressTargetDayFromDatePicker(changeDateFormatToTargetFormat(targetDate, dateFormat, "yyyy-MM-dd"));
    }
    public void enterDateIntoTextField(String selectedDate) {
        while (!calendarDate.getAttribute("value").equalsIgnoreCase(selectedDate)) {
            wait.until(ExpectedConditions.elementToBeClickable(calendarDate)).click();

            // Determine the appropriate key based on the platform
            Keys selectAllKey =
                    System.getProperty("os.name").toLowerCase().contains("mac") ? Keys.COMMAND : Keys.CONTROL;

            // Send the appropriate key to the text field
            calendarDate.sendKeys(selectAllKey + "a");
            calendarDate.sendKeys(Keys.DELETE);

            wait.until(ExpectedConditions.elementToBeClickable(calendarDate)).sendKeys(selectedDate);
            calendarDate.sendKeys(Keys.ENTER);
        }
    }
    public void pressTargetDayFromDatePicker(String targetDate) {
        String targetDateSelector = String.format(datePickerTargetDateSelector, targetDate);
        wait.until(ExpectedConditions.elementToBeClickable(calendarDate)).click();
        wait.until(ExpectedConditions.elementToBeClickable(By.xpath(targetDateSelector))).click();
    }
    public boolean isAssignBtnDisplayed() {
        return !isElementDisplayed(assignBtnOfTruckModal);
    }
    public void selectFirstTruck() {
        wait.until(ExpectedConditions.elementToBeClickable(truckOptionOfTruckModal)).click();
    }
    public String getSuccessMssg() {
        wait.until(ExpectedConditions.visibilityOf(successMessageOfTruckGotAssigned));
        return successMessageOfTruckGotAssigned.getText();
    }
    public void filterByStatus(String status) throws InterruptedException {
        Thread.sleep(2000);
        actions.moveToElement(statusFilter).click().perform();
        WebElement statusOption = wait.until(ExpectedConditions.visibilityOfElementLocated(
            By.xpath("//div[contains(text(), '" + status + "')]")));
            statusOption.click();
            closeDropDown();
        }
    public boolean isCanceledStatusDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(canceledStatus));
        return isElementDisplayed(canceledStatus);
    }
    public boolean isEmptyTruckDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(emptyTruck));
        return isElementDisplayed(emptyTruck);
    }
    public String getEmptyTruckAssigned(){
        return emptyTruck.getText();
    }
    public boolean isAssignTruckModalTitleDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(titleOfTruckAssignModal));
        return isElementDisplayed(titleOfTruckAssignModal);
    }
    public String getAssignTruckModalTitle() {
        return titleOfTruckAssignModal.getText();
    }
    public String getSourceLocationModuleTitle() {
        wait.until(ExpectedConditions.visibilityOf(srcLocationModuleTitle));
        return srcLocationModuleTitle.getText();
    }
    public String getOrdersPageTitle() {
        return orderTitle.getText();
    }
    public boolean isAssignTruckBtnDisplayed() {
        int maxScrolls = 10; // Maximum scroll attempts
        int scrollCount = 0;
        int scrollStep = 1; // Increase scrolling step for faster movement

        while (scrollCount < maxScrolls) {
            try {
                WebElement element = wait.until(ExpectedConditions.visibilityOf(assignTruckBtn));

                if (element.isDisplayed()) {
                    // Scroll smoothly to bring the element into full view
                    js.executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element);
                    logger.info("Element found and scrolled into view.");
                    return true;
                }
            } catch (TimeoutException e) {
                // Faster scrolling by increasing the pixel step
                js.executeScript("window.scrollBy(0, " + scrollStep + ");");
                logger.info("Scrolling down faster...");
            }
            scrollCount++;
        }
        logger.info("Element not found after scrolling.");
        return false;
    }
    public boolean isEditTruckBtnDisplayed() {
        int maxScrolls = 10; // Maximum scroll attempts
        int scrollCount = 0;
        int scrollStep = 1; // Increase scrolling step for faster movement

        while (scrollCount < maxScrolls) {
            try {
                WebElement element = wait.until(ExpectedConditions.visibilityOf(editButtonOfTruckColumn));

                if (element.isDisplayed()) {
                    // Scroll smoothly to bring the element into full view
                    js.executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element);
                    logger.info("Element found and scrolled into view.");
                    return true;
                }
            } catch (TimeoutException e) {
                // Faster scrolling by increasing the pixel step
                js.executeScript("window.scrollBy(0, " + scrollStep + ");");
                logger.info("Scrolling down faster...");
            }
            scrollCount++;
        }
        logger.info("Element not found after scrolling.");
        return false;
    }
    public void clickEditTruckBtn() throws InterruptedException {
        js.executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", editButtonOfTruckColumn);
        logger.info("Assign Truck button found and scrolled into view.");
        wait.until(ExpectedConditions.elementToBeClickable(editButtonOfTruckColumn)).click();
        Thread.sleep(2000);
    }
    public boolean isEditTruckBtnDisabled() {
    return !editButtonOfTruckColumn.isEnabled();
    }
    public String getAssignTruckBtnTxt() {
        return assignTruckBtn.getText();
    }
    public boolean isTruckDetailsColumnDisplayed(){
        return isElementDisplayed(truckDetailsLabelOfAssignTruckModal);
    }
    public String getTruckDetailsLabelAtTruckAssignationModal(){
        return truckDetailsLabelOfAssignTruckModal.getText();
    }
    public boolean isAssignedOrdersColumnDisplayed(){
        return isElementDisplayed(assignedOrderLabelOfAssignTruckModal);
    }
    public String getAssignOrdersLabelAtTruckAssignationModal(){
        return assignedOrderLabelOfAssignTruckModal.getText();
    }
    public boolean isSearchFieldDisplayed(){
        return isElementDisplayed(searchFieldOfTruckModal);
    }
    public void checkAndClickAssignDispatcher() {
        int maxScrolls = 10; // Maximum scroll attempts
        int scrollCount = 0;
        int scrollStep = 3; // Adjust step size for better scrolling

        while (scrollCount < maxScrolls) {
            try {
                // Locate "Assign Truck" button dynamically in the same row
                WebElement assignTruckBtn = wait.until(ExpectedConditions.presenceOfElementLocated(
                        By.xpath("//tr[.//strong[contains(text(), 'Assign Truck')]]//strong[contains(text(), 'Assign Truck')]")
                ));

                if (assignTruckBtn.isDisplayed()) {
                    logger.info("Assign Truck button is visible. Stopping scroll.");

                    // Scroll smoothly to the "Assign Truck" button
                    js.executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", assignTruckBtn);
                    logger.info("Assign Truck button scrolled into view.");

                    // Find the "Assign Dispatcher" button in the same row and click it
                    WebElement assignDispatcherBtn = wait.until(ExpectedConditions.elementToBeClickable(
                            By.xpath("//tr[.//strong[contains(text(), 'Assign Truck')]]//strong[contains(text(), 'Assign Dispatcher')]")
                    ));

                    assignDispatcherBtn.click();
                    logger.info("Assign Dispatcher button clicked.");
                    return; // Exit after clicking
                }
            } catch (NoSuchElementException | TimeoutException e) {
                // Scroll down if Assign Truck button is not found yet
                js.executeScript("window.scrollBy(0, " + scrollStep + ");");
                logger.info("Scrolling down... Attempt: " + (scrollCount + 1));
            }

            scrollCount++;
        }

        logger.info("Assign Truck button not found after " + maxScrolls + " scroll attempts.");
    }

    public String getEmptyAssignedTruckText() throws InterruptedException {
        Thread.sleep(2000);
        return emptyTruckScreenTxt.getText();
    }
    public String getAssignedTruckTitle(){
        return assignedTruckTitle.getText();
    }
    public String getAssignDispatcherModalTitle(){
        return assignDispatcherModalTitle.getText();
    }
    public boolean isArrivedStatusDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(arrivedStatus));
        return isElementDisplayed(arrivedStatus);
    }
    public boolean isCompletedStatusDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(completedStatus));
        return isElementDisplayed(completedStatus);
    }
    public boolean isPlacedStatusDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(placedStatus));
        return isElementDisplayed(placedStatus);
    }
    public WebElement getRadioButton() {
        return truckOptionOfTruckModal;
    }
public void selectAnotherTruck() {
    for (WebElement radioButton : truckOptions) { // Loop through each button
        if (!radioButton.getAttribute("class").contains("ant-radio-wrapper-checked")) {
            radioButton.click();
            logger.info("Found an unselected radio button and clicked it.");
            return;
        }
    }
    logger.warn("No unchecked radio button found.");
}
}
