package modals;

import helpers.BaseHelper;
import io.appium.java_client.android.AndroidDriver;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Wait;

import java.time.Duration;

public class BaseAndroidScreen extends BaseHelper {
    public AndroidDriver androidDriver;
    public Wait<AndroidDriver> wait;

    public BaseAndroidScreen(AndroidDriver androidDriver) {
        this.androidDriver = androidDriver;
        this.wait = new FluentWait<>(androidDriver).withTimeout(Duration.ofSeconds(30))
                .pollingEvery(Duration.ofSeconds(2)).ignoring(NoSuchElementException.class);
    }

    public boolean isElementDisplayed(WebElement webElement){
        try {
            wait.until(ExpectedConditions.visibilityOf(webElement));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public boolean isElementHidden(WebElement webElement){
        try {
            wait.until(ExpectedConditions.invisibilityOf(webElement));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public void hideKeyboardIfDisplayed(){
        if (androidDriver.isKeyboardShown())
            androidDriver.hideKeyboard();
    }

    public void enterValueInTextField(WebElement txtField, String value){
        wait.until(ExpectedConditions.visibilityOf(txtField)).click();
        wait.until(ExpectedConditions.visibilityOf(txtField)).sendKeys(value);
        //wait.until(ExpectedConditions.textToBePresentInElement(txtField, value));
    }
}
