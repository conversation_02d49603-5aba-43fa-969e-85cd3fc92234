package modals;

import helpers.BaseHelper;
import org.openqa.selenium.*;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.support.ui.Wait;

import java.time.Duration;
import java.util.ArrayList;

public class BaseWebPage extends BaseHelper {
    public WebDriver webDriver;
    public Wait<WebDriver> wait;

    public BaseWebPage(WebDriver webDriver) {
        this.webDriver = webDriver;
        this.wait = new FluentWait<>(webDriver).withTimeout(Duration.ofSeconds(60))
                .pollingEvery(Duration.ofSeconds(1)).ignoring(NoSuchElementException.class);
    }

    public void switchToWindow(int index) {
        ArrayList<String> tabs = new ArrayList<>(webDriver.getWindowHandles());
        webDriver.switchTo().window(tabs.get(index));
    }

    public boolean isElementDisplayed(WebElement webElement) {
        try {
            wait.until(ExpectedConditions.visibilityOf(webElement));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void goToUrl(String url) {
        webDriver.get(url);
    }

    public void visitASubPage(String subURL) {
        webDriver.get(webDriver.getCurrentUrl() + subURL);
    }

    public void executeJavaScriptCode(String script) {
        JavascriptExecutor jsExecutor = (JavascriptExecutor) webDriver;
        jsExecutor.executeScript(script);
    }

    public void scrollUpToTop() {
        JavascriptExecutor js = (JavascriptExecutor) webDriver;
        js.executeScript("window.scrollTo(0, 0);");
    }

    public String capitalizeFirstLetter(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;

        for (char c : input.toCharArray()) {
            if (Character.isWhitespace(c)) {
                capitalizeNext = true;
                result.append(c); // Preserve whitespace
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }

        return result.toString();
    }

    public void moveToElement(WebElement element) {
        new Actions(webDriver).moveToElement(element);
        new Actions(webDriver).pause(Duration.ofSeconds(4));
    }

    public void enterStringIntoTextField(WebElement txtField, String text) {
        while (!txtField.getAttribute("value").equalsIgnoreCase(text)) {
            if (!wait.until(ExpectedConditions.visibilityOf(txtField)).getText().isEmpty())
                txtField.clear();
            txtField.sendKeys(text);
            wait.until(ExpectedConditions.textToBePresentInElementValue(txtField, text));
        }
    }

    public String getElementText(WebElement webElement) {
        return wait.until(ExpectedConditions.visibilityOf(webElement)).getText();
    }

    public void closeDropDown() {
        Actions actions = new Actions(webDriver);
        actions.sendKeys(Keys.ESCAPE).build().perform();
    }

    public void switchToTheOtherTab(){
        String originalWindow = webDriver.getWindowHandle();
        for (String window : webDriver.getWindowHandles()){
            if (!window.equals(originalWindow)){
                webDriver.switchTo().window(window);
                break;
            }
        }
    }
    public void selectTheDropDownList(WebElement dropDown,String text){
        Select select = new Select(dropDown);
        select.selectByVisibleText(text);
        wait.until(ExpectedConditions.textToBePresentInElementValue(dropDown, text));
    }

    public void performDoubleClickOnElement(WebElement element){
        new Actions(webDriver).doubleClick(element).perform();
    }
}
