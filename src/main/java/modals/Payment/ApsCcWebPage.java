package modals.Payment;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class ApsCcWebPage extends BaseWebPage {

    public ApsCcWebPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    @FindBy (id = "card_number")
    WebElement cardNumberTextField;

    @FindBy (id = "expiry_date_masked")
    WebElement expiryDateTextField;

    @FindBy (id = "card_security_code")
    WebElement cVVTextField;

    @FindBy (id = "card_holder_name")
    WebElement cardHolderNameTextField;

    @FindBy (id = "save-card")
    WebElement saveCardCheckBox;

    @FindBy (id = "card-label")
    WebElement cardLabelTextField;

    @FindBy (id = "check-form-submit")
    WebElement addCardBtn;

    public void payOrderInAps(String cardNumber, String expiryDate, String cVV, String cardHolderName)
    {
        //Check if elements are displayed in the web page and enter card details
        isElementDisplayed(cardNumberTextField);
        cardNumberTextField.sendKeys(cardNumber);

        moveToElement(expiryDateTextField);
        expiryDateTextField.sendKeys(expiryDate);

        moveToElement(cVVTextField);
        cVVTextField.sendKeys(cVV);

        moveToElement(cardHolderNameTextField);
        cardHolderNameTextField.sendKeys(cardHolderName);

        //Click on add card button
        isElementDisplayed(addCardBtn);
        addCardBtn.click();

        // Wait until the page title is "Payment Successful"
        wait.until(ExpectedConditions.titleIs("Payment Successful"));
    }

}
