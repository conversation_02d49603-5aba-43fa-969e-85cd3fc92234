package modals.chatbotSdk;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class ChatbotSdkHostPage extends BaseWebPage {
    public ChatbotSdkHostPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    @FindBy(id = "token")
    WebElement tokenTxtField;

    @FindBy(id = "name")
    WebElement fullNameTxtField;

    @FindBy(id = "externalId")
    WebElement userIdTxtField;

    @FindBy(id = "lang")
    WebElement languageTxtField;

    @FindBy(xpath = "/html/body/label/button")
    WebElement submitBtn;

    @FindBy(id = "fc_frame")
    WebElement chatbotFrameContainer;

    @FindBy(id = "fc_widget")
    WebElement chatbotFrame;

    @FindBy(id = "chat-icon")
    WebElement chatbotIcon;

    @FindBy(id = "btn-answer-yes")
    WebElement proceedToPageBtn;

    public void enterTokenIntoTxtField(String token){
        enterStringIntoTextField(tokenTxtField, token);
    }

    public void enterNameIntoTxtField(String fullName){
        enterStringIntoTextField(fullNameTxtField, fullName);
    }

    public void enterUserIdIntoTxtField(String userId){
        enterStringIntoTextField(userIdTxtField, userId);
    }

    public void enterLanguageIntoTxtField(String language){
        enterStringIntoTextField(languageTxtField, language);
    }

    public void pressSubmitBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(submitBtn))
                .click();
    }

    public void fillInHostPageFormAndSubmit(String token, String fullName, String userId, String language){
        enterTokenIntoTxtField(token);
        enterNameIntoTxtField(fullName);
        enterUserIdIntoTxtField(userId);
        enterLanguageIntoTxtField(language);
        pressSubmitBtn();
    }

    public void pressChatbotBtn(){
        wait.until(ExpectedConditions.visibilityOf(chatbotFrameContainer));
        wait.until(ExpectedConditions.visibilityOf(chatbotFrame));
        switchDriverContextToChatbotIframe();
        pressChatbotIcon();
    }

    public boolean isChatbotIconDisplayed(){
        return isElementDisplayed(chatbotFrame);
    }

    public void switchDriverContextToChatbotIframe(){
        webDriver.switchTo().frame(chatbotFrame);
    }

    public void pressProceedAnywayBtn(){
        wait.until(ExpectedConditions.visibilityOf(proceedToPageBtn))
                .click();
    }

    public void pressChatbotIcon(){
        wait.until(ExpectedConditions.visibilityOf(chatbotIcon))
                .click();
    }
}
