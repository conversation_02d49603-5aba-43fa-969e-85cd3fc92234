package modals.chatbotSdk;

import modals.BaseWebPage;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class WebChatbotSdk extends BaseWebPage {
    public WebChatbotSdk(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    @FindBy(xpath = "//div[@class='channel-content']")
    WebElement chatWithUsBtn;

    @FindBy(xpath = "//*[@id=\"app-conversation-editor\"]")
    WebElement txtFieldContainer;

    @FindBy(xpath = "//div[@class='agent-name' and text()='Breadfast Support']")
    WebElement breadfastSupport;

    public void openChatDialogueView(){
        wait.until(ExpectedConditions.elementToBeClickable(chatWithUsBtn)).click();
        wait.until(ExpectedConditions.visibilityOf(txtFieldContainer));
    }

    public void enterMessageIntoChatTextField(String message){
        wait.until(ExpectedConditions.elementToBeClickable(txtFieldContainer)).click();
        new Actions(webDriver).sendKeys(txtFieldContainer, message).sendKeys(Keys.ENTER).perform();
    }

    public boolean isBreadfastSupportDisplayed(){
        return isElementDisplayed(breadfastSupport);
    }
}
