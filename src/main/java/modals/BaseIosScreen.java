package modals;

import helpers.BaseHelper;
import io.appium.java_client.ios.IOSDriver;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Wait;

import java.time.Duration;

public class BaseIosScreen extends BaseHelper {
    public IOSDriver iosDriver;
    public Wait<IOSDriver> wait;
    private static final int MAX_RETRIES = 3;

    public BaseIosScreen(IOSDriver iosDriver) {
        this.iosDriver = iosDriver;
        this.wait = new FluentWait<>(iosDriver).withTimeout(Duration.ofSeconds(30))
                .pollingEvery(Duration.ofSeconds(2)).ignoring(NoSuchElementException.class);
    }

    public boolean isElementDisplayed(WebElement webElement){
        try {
            wait.until(ExpectedConditions.visibilityOf(webElement));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public boolean isElementHidden(WebElement webElement){
        try {
            wait.until(ExpectedConditions.invisibilityOf(webElement));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public String getTextDisplayed(WebElement webElement)
    {
        return webElement.getText();
    }

    public void enterValueInTextField(WebElement txtField, String txtValue){
        wait.until(ExpectedConditions.visibilityOf(txtField)).click();
        wait.until(ExpectedConditions.visibilityOf(txtField)).sendKeys(txtValue + "\n");
        try{
            wait.until(ExpectedConditions.textToBePresentInElement(txtField, txtValue));
        } catch (Exception e){
            // Do nothing
        }
    }

    public void dismissKeyboardIfDisplayed(IOSDriver iosDriver){
        try {
            iosDriver.hideKeyboard();
        } catch (Exception e){
            // do nothing
        }
    }

    public int getMaxRetries() {
        return MAX_RETRIES;
    }
}
