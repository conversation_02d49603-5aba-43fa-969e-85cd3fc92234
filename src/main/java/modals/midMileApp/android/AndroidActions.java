package modals.midMileApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidActions extends BaseAndroidScreen {
    public AndroidActions(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(id="listing_ordersToReceiveName_txt")
    WebElement ordersToReceiveSection;
    @FindBy(id="listing_ordersToDeliverName_txt")
    WebElement ordersToDeliverSection;
    @FindBy(id = "listing_arrowReceiveSection_btn")
    WebElement receiveSectionArrowBtn;
    @FindBy(id="listing_sourceLocationName_txt")
    WebElement sourceLocationTxt;
    @FindBy(id="loading_confirmReceiving_btn")
    WebElement confirmArrivalReceiveBtn;
    @FindBy(id="listing_orderNumber_value")
    WebElement orderNumber;
    @FindBy(id ="listing_outReferenceNumber_value")
    WebElement outRefNumber;
    @FindBy(id="listing_destinationLocationName_value")
    WebElement destinationLocationTxt;
    @FindBy(id="listing_internalCategoryName_txt")
    WebElement internalCategoryTxt;
    @FindBy(id ="loading_startLoading_btn")
    WebElement startLoadingReceiveBtn;
    @FindBy(id="loading_endLoading_btn")
    WebElement endLoadingReceiveBtn;
    @FindBy(id="loading_successMssg_txt")
    WebElement endLoadingSuccessTxt;
    @FindBy(id="loading_doneSuccessMssg_btn")
    WebElement endLoadingDoneBtn;
    @FindBy(id="loading_confirmReceiving_btn")
    WebElement startDeliveryBtn;
    @FindBy(id="listing_inReferenceNumber_value")
    WebElement inRefNumber;
    @FindBy(id="listing_deliveryDestinationName_value")
    WebElement destinationLocationDeliveryTxt;
    @FindBy(id="listing_numberOfOrdersToDeliver_txt")
    WebElement ordersToDeliverNumber;
    @FindBy(id="loading_confirmReceiving_btn")
    WebElement confirmArrivalDeliveryBtn;
    @FindBy(id="listing_numberOfOrdersToReceive_txt")
    WebElement ordersToReceiveNumber;
    @FindBy(id="deloading_startDeloading_btn")
    WebElement startDeloadingBtn;
    @FindBy(id="deloading_endDeloading_btn")
    WebElement endDeloadingBtn;
    @FindBy(id="deloading_successMssg_txt")
    WebElement endDeloadingSuccessTxt;
    @FindBy(id="deloading_doneSuccessMssg_btn")
    WebElement endDeloadingDoneBtn;
    @FindBy(xpath = "//android.widget.TextView[@text=\"ليس لديك شحنات لتوصيلها\"]")
    WebElement emptyTxtScreen;
    @FindBy(xpath ="//android.widget.TextView[@text=\"الشحنات المطلوب منك توصيلها سوف تظهر هنا\"]")
    WebElement emptySecondTxtScreen;
    public String getOrdersToReceiveText(){
        wait.until(ExpectedConditions.visibilityOf(ordersToReceiveSection));
        return ordersToReceiveSection.getText();
    }
    public String getOrdersToDeliverText(){
        wait.until(ExpectedConditions.visibilityOf(ordersToDeliverSection));
        return ordersToDeliverSection.getText();
    }
    public void pressReceiveSectionArrowBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(receiveSectionArrowBtn))
                .click();
    }
    public boolean isSourceLocationDisplayed(){
        return isElementDisplayed(sourceLocationTxt);
    }
    public boolean isOrderNumberDisplayed(){
        return isElementDisplayed(orderNumber);
    }
    public boolean isOutRefNumberDisplayed(){
        return isElementDisplayed(outRefNumber);
    }
    public boolean isDestinationLocationDisplayed(){
        return isElementDisplayed(destinationLocationTxt);
    }
    public boolean isInternalCategoryDisplayed(){
        return isElementDisplayed(internalCategoryTxt);
    }
    public boolean isConfirmArrivalActionBtnDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(confirmArrivalReceiveBtn));
        return isElementDisplayed(confirmArrivalReceiveBtn);
    }
    public void pressConfirmArrivalReceiveBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmArrivalReceiveBtn))
                .click();
    }
    public boolean isStartLoadingActionBtnDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(startLoadingReceiveBtn));
        return isElementDisplayed(startLoadingReceiveBtn);
    }
    public void pressStartLoadingReceiveBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(startLoadingReceiveBtn))
                .click();
    }
    public boolean isEndLoadingActionBtnDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(endLoadingReceiveBtn));
        return isElementDisplayed(endLoadingReceiveBtn);
    }
    public void pressEndLoadingActionBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(endLoadingReceiveBtn))
                .click();
    }
    public boolean isDoneLoadingSuccessMssgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(endLoadingSuccessTxt));
        return isElementDisplayed(endLoadingSuccessTxt);
    }
    public boolean isDoneLoadingSuccessMssgBtnDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(endLoadingDoneBtn));
        return isElementDisplayed(endLoadingDoneBtn);
    }
    public String getSuccessMssgLoadingText(){
        wait.until(ExpectedConditions.visibilityOf(endLoadingSuccessTxt));
        return endLoadingSuccessTxt.getText();
    }
    public String getOrderNumber(){
        wait.until(ExpectedConditions.visibilityOf(endLoadingSuccessTxt));
        return endLoadingSuccessTxt.getText();
    }
    public void pressDoneLoadingSuccessMssgBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(endLoadingDoneBtn))
                .click();
    }
    public boolean isStartDeliveryActionBtnDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(startDeliveryBtn));
        return isElementDisplayed(startDeliveryBtn);
    }
    public boolean isDestinationLocationDeliveryDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(destinationLocationDeliveryTxt));
        return isElementDisplayed(destinationLocationDeliveryTxt);
    }
    public boolean isInRefNumberDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(inRefNumber));
        return isElementDisplayed(inRefNumber);
    }
    public String getOrdersNumberToDeliver(){
        wait.until(ExpectedConditions.visibilityOf(ordersToDeliverNumber));
        return ordersToDeliverNumber.getText();
    }
    public void pressStartDeliveryBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(startDeliveryBtn))
                .click();
    }
    public boolean isConfirmArrivalDeliveryBtnDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(confirmArrivalDeliveryBtn));
        return isElementDisplayed(confirmArrivalDeliveryBtn);
    }
    public String getOrdersNumberToReceive(){
        wait.until(ExpectedConditions.visibilityOf(ordersToReceiveNumber));
        return ordersToReceiveNumber.getText();
    }
    public void pressConfirmArrivalDeliveryBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(confirmArrivalDeliveryBtn))
                .click();
    }
    public boolean isStartDeloadingBtnDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(startDeloadingBtn));
        return isElementDisplayed(startDeloadingBtn);
    }
    public void pressStartDeloadingBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(startDeloadingBtn))
                .click();
    }
    public boolean isEndDeloadingBtnDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(endDeloadingBtn));
        return isElementDisplayed(endDeloadingBtn);
    }
    public void pressEndDeloadingBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(endDeloadingBtn))
                .click();
    }
    public boolean isDoneDeloadingSuccessMssgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(endDeloadingSuccessTxt));
        return isElementDisplayed(endDeloadingSuccessTxt);
    }
    public boolean isDoneDeloadingSuccessMssgBtnDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(endDeloadingDoneBtn));
        return isElementDisplayed(endDeloadingDoneBtn);
    }
    public void pressDoneDeloadingSuccessMssgBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(endDeloadingDoneBtn))
                .click();
    }
    public String getSuccessMssgDeloadingText(){
        wait.until(ExpectedConditions.visibilityOf(endDeloadingSuccessTxt));
        return endDeloadingSuccessTxt.getText();
    }
    public String getOrderNumberDeloading(){
        wait.until(ExpectedConditions.visibilityOf(endDeloadingSuccessTxt));
        return endDeloadingSuccessTxt.getText();
    }
    public boolean isEmptyScreenFirstTxtDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(emptyTxtScreen));
        return isElementDisplayed(emptyTxtScreen);
    }
    public boolean isEmptyScreenSecondTxtDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(emptySecondTxtScreen));
        return isElementDisplayed(emptySecondTxtScreen);
    }
    public boolean isOrderToReceiveSectionDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(ordersToReceiveSection));
        return isElementDisplayed(ordersToReceiveSection);
    }
    public boolean isOrderToDeliverSectionDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(ordersToDeliverSection));
        return isElementDisplayed(ordersToDeliverSection);
    }
}
