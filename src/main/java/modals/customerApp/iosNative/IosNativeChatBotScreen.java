package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeChatBotScreen extends BaseIosScreen {
    public IosNativeChatBotScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy (xpath = "//XCUIElementTypeButton[@name='freshchat send icon']")
    WebElement sendMsgBtn;

    @FindBy (xpath = "")
    WebElement haveAQuestionTitle;

    public boolean isChatBotScreenDisplayed() {
        return wait.until(ExpectedConditions.visibilityOf(sendMsgBtn)).isDisplayed();
    }
}
