package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativePhoneNumberScreen extends BaseIosScreen {
    public IosNativePhoneNumberScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "phoneNumberScreen_header")
    WebElement pageTitle;

    @FindBy(name = "phoneNumberScreen_phoneCountryBtn")
    WebElement phoneNumberCountryCode;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='phoneNumberScreen_txtField']")
    WebElement phoneNumberTxtField;

    @FindBy(name = "submitBtn")
    WebElement nextBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='This field is required.']")
    WebElement requiredMobileNumberErrorMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Enter a valid mobile number.']")
    WebElement invalidFormatErrorMsg;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void pressPhoneNumberCountryCode(){
        wait.until(ExpectedConditions.visibilityOf(phoneNumberCountryCode))
                .click();
    }

    public void pressNextBtnWithoutRetry(){
        wait.until(ExpectedConditions.visibilityOf(nextBtn)).click();
    }

    public void pressNextBtn(int retryCounterInitialValue){
        wait.until(ExpectedConditions.visibilityOf(nextBtn)).click();
        try {
            wait.until(ExpectedConditions.invisibilityOf(nextBtn));
        } catch (Exception e){
            if (retryCounterInitialValue < getMaxRetries()){
                pressNextBtn(retryCounterInitialValue + 1);
            } else {
                throw e;
            }
        }
    }

    public boolean isNextBtnEnabled(){
        try {
            wait.until(ExpectedConditions.attributeToBe(nextBtn, "enabled", "true"));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public void enterPhoneNumber(String phoneNumber){
        wait.until(ExpectedConditions.visibilityOf(phoneNumberTxtField)).sendKeys(phoneNumber);
    }

    public void enterPhoneNumberAndPresNext(String phoneNumber){
        enterPhoneNumber(phoneNumber);
        isNextBtnEnabled();
        pressNextBtn(0);
    }

    public boolean isRequiredMobileNumberErrorMsgDisplayed(){
        return isElementDisplayed(requiredMobileNumberErrorMsg);
    }

    public boolean isInvalidFormatErrorMsgDisplayed(){
        return isElementDisplayed(invalidFormatErrorMsg);
    }

    public String getCurrentValueInTxtField(){
        return wait.until(ExpectedConditions.visibilityOf(phoneNumberTxtField)).getText();
    }
}
