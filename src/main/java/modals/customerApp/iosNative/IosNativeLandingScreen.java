package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeLandingScreen extends BaseIosScreen {

    public IosNativeLandingScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "onBoardingScreen_loginBtn")
    WebElement authBtn;

    @FindBy(name = "onBoardingScreen_exploreBtn")
    WebElement exploreBtn;

    public void pressAuthBtn(){
        wait.until(ExpectedConditions.visibilityOf(authBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(authBtn));
    }

    public void pressExploreBtn(){
        wait.until(ExpectedConditions.visibilityOf(exploreBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(exploreBtn));
    }
}
