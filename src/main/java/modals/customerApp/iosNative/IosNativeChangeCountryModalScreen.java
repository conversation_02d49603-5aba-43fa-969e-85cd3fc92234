package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeChangeCountryModalScreen extends BaseIosScreen {
    public IosNativeChangeCountryModalScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy (name = "changeCountry_confirmationModal_title")
    WebElement modalTitle;

    @FindBy(name = "changeCountry_confirmationModal_confirmBtn")
    WebElement changeCountryBtn;

    @FindBy(name = "changeCountry_confirmationModal_dismissBtn")
    WebElement keepCountryBtn;

    public boolean isModalDisplayed() {
       return wait.until(ExpectedConditions.visibilityOf(modalTitle))
               .isDisplayed();
    }

    public void pressChangeBtn() {
        wait.until(ExpectedConditions.visibilityOf(changeCountryBtn))
                .click();
    }

    public void pressCancelBtn () {
        wait.until(ExpectedConditions.visibilityOf(keepCountryBtn))
                .click();
    }
}

