package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class IosNativeUpdateCustomizedProductScreen extends BaseIosScreen {

    public IosNativeUpdateCustomizedProductScreen(IOSDriver iosDriver) {
        super(iosDriver);
    }

    @FindBy(xpath = "//XCUIElementTypeButton[@name=\"Update\"]")
    WebElement updateBtn;

    @FindBy(xpath = "//XCUIElementTypeImage[contains(@name, 'productDetailsProductCustomizationQtyAddBtn-')]")
    WebElement plusOptionBtn;

    @FindBy(xpath = "//XCUIElementTypeImage[contains(@name, 'productDetailsProductCustomizationQtyMinsBtn-')]")
    WebElement minusOptionBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[contains(@name, 'productDetailsQtyMinusBtn')]")
    WebElement increaseProductQtyBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[contains(@name, 'productDetailsQtyAddBtn')]")
    WebElement decreaseProductQtyBtn;

}
