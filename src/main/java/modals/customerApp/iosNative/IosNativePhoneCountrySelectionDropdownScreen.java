package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativePhoneCountrySelectionDropdownScreen extends BaseIosScreen {
    public IosNativePhoneCountrySelectionDropdownScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeSearchField[@name='Search Your Country']")
    WebElement searchCountryTextField;

    String countryPhoneCodeSelector = "//XCUIElementTypeStaticText[@name='(%s)']";

    public boolean isDropdownDisplayed(){
        return isElementDisplayed(searchCountryTextField);
    }

    public void searchForCountryByName(String countryName){
        wait.until(ExpectedConditions.visibilityOf(searchCountryTextField))
                .sendKeys(countryName);
    }

    public void selectCountryByPhoneCode(String phoneCode){
        wait.until(ExpectedConditions
                .visibilityOfElementLocated(By.xpath(String.format(countryPhoneCodeSelector, phoneCode))))
            .click();
    }

    public void searchAndSelectTheCountry(String countryName, String countryCode){
        searchForCountryByName(countryName);
        selectCountryByPhoneCode(countryCode);
    }
}
