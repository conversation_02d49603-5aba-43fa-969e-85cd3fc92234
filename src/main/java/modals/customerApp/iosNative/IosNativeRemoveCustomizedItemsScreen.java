package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;

public class IosNativeRemoveCustomizedItemsScreen extends BaseIosScreen {
    public IosNativeRemoveCustomizedItemsScreen(IOSDriver iosDriver) {
        super(iosDriver);
    }

    @FindBy(id = "Select items to remove")
    WebElement screenTitleElement;

    @FindBy(xpath = "//XCUIElementTypeStaticText[starts-with(@name, 'product_') " +
            "and substring(@name, string-length(@name) - 5) = '_increaseQty_btn']")
    WebElement increaseQtyBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[starts-with(@name, 'product_') " +
            "and substring(@name, string-length(@name) - 15) = '_decreaseQty_btn' and @label='ic_minus']")
    WebElement decreaseQtyBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[starts-with(@name, 'product_')" +
            " and substring(@name, string-length(@name) - 15) = '_decreaseQty_btn' and @label='ic_delete']")
    WebElement deleteBtn;

    @FindBy(id = "Close")
    WebElement closeBtn;

}
