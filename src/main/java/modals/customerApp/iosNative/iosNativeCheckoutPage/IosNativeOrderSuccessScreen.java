package modals.customerApp.iosNative.iosNativeCheckoutPage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeOrderSuccessScreen extends BaseIosScreen {
    public IosNativeOrderSuccessScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Order details']")
    WebElement pageTitle;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Back']")
    WebElement backBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='orderDetails_orderStatus']")
    WebElement orderStatus;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='orderDetails_cancelOrderBtn']")
    WebElement cancelBtn;

    @FindBy(xpath = "//XCUIElementTypeScrollView")
    WebElement scrollableContentContainer;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void pressBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backBtn))
                .click();
    }
    public String getOrderStatus() {
        return wait.until(ExpectedConditions.visibilityOf(orderStatus))
                .getAttribute("value");
    }

    public boolean isCancelBtnDisplayed(){
        return isElementDisplayed(cancelBtn);
    }

    public void pressCancelBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(cancelBtn))
                .click();
    }

    public WebElement getScrollableContentContainer(){
        return scrollableContentContainer;
    }
}
