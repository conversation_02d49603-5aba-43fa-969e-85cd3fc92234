package modals.customerApp.iosNative.iosNativeCheckoutPage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeCheckoutScreen extends BaseIosScreen {
    public IosNativeCheckoutScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Checkout']")
    WebElement pageHeaderContainer;

    @FindBy(xpath = "//XCUIElementTypeButton[@name=\"checkoutScreen_promoCodeSection_applyBtn\"]")
    WebElement applyCouponCodeBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='checkoutScreen_placeOrderBtn']")
    WebElement placeOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Schedule']")
    WebElement ScheduleBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Delivery Time Updated']")
    WebElement DeliveryTimeUpdatedPopUp;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Place Order']")
    WebElement DeliveryTimeUpdatedPopUpPlaceOrder;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Pick Another Time']")
    WebElement DeliveryTimeUpdatedPopUpPickAnotherTime;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Or schedule another time']")
    WebElement ScheduleAnotherTimeSheet;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='timeSlot_Instant']")
    WebElement InstantInScheduleSheet;

    @FindBy(xpath = "(//XCUIElementTypeImage[@name='RadioButtonEmpty'])")
    WebElement FirstAvailableScheduleSlot;

    @FindBy(xpath = "(//XCUIElementTypeStaticText[@name='Fully booked'])")
    WebElement FirstFullyBookedScheduleSlot;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='This is the closest slot due to high demand.']")
    WebElement ClosestSlotDueToHighDemandMsg;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Book this slot']")
    WebElement BookThisSlotBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Pick another time']")
    WebElement PickAnotherTimeBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Deliver at the earliest, if possible']")
    WebElement DeliverAtTheEarliestToggle;

    @FindBy(xpath = "//XCUIElementTypeApplication[@name=\"Breadfast Testing\"]" +
            "/XCUIElementTypeWindow/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther" +
            "/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther" +
            "/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeScrollView" +
            "/XCUIElementTypeOther/XCUIElementTypeSwitch")
    WebElement DeliverAtTheEarliestToggleBtn;

    @FindBy(xpath = "(//XCUIElementTypeStaticText[@name='Deliver at the earliest, if possible'])[2]")
    WebElement DeliverAtTheEarliestPopupSheet;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Got it']")
    WebElement GotItBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='AddNewCard_changeCard_btn']")
    WebElement changeCardBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='cardSelectionModal_container']")
    WebElement cardSelectionModal;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='AddNewCard_changeCard_btn'])[2]")
    WebElement changeCardBtnInCardSelectionModal;

    @FindBy(id = "paymentSection_cashOnDelivery_rowContainer")
    WebElement cashOnDeliveryBtn;

    @FindBy(xpath = "//XCUIElementTypeScrollView")
    WebElement scrollableContentContainer;

    String promoCodeText = "Promo code";

    @FindBy(id = "checkoutScreen_promoCodeSection_txtField")
    WebElement promoCodeTxtField;

    @FindBy(id = "checkoutScreen_promoCodeSection_applyBtn")
    WebElement promoCodeApplyBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@name, 'tipValue_')]")
    WebElement tipValueBtn;

    String tipValueSelector = "tipValue_%s";

    String promoCodeTxtFieldSelector = "//XCUIElementTypeTextField[@name='%s']";

    String promoCodeTxtFieldContentDescription = "checkoutScreen_promoCodeSection_txtField";

    String scrollableContentContainerSelector = "//XCUIElementTypeScrollView";

    public String getTipValueSelector (String tipValue){
        return String.format(tipValueSelector, tipValue);
    }

    public void pressTipValueBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(tipValueBtn))
                .click();
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageHeaderContainer);
    }

    public void pressPlaceOrderBtn(){
        wait.until(ExpectedConditions.visibilityOf(placeOrderBtn)).click();
        try {
            wait.until(ExpectedConditions.invisibilityOf(placeOrderBtn));
        } catch (Exception e){
            // Do nothing
        }
    }

    public boolean isScheduleBtnDisplayed(){
        return isElementDisplayed(ScheduleBtn);
    }

    public boolean DeliveryTimeUpdatedPopUpIsDisplayed(){
        return isElementDisplayed(DeliveryTimeUpdatedPopUp);
    }

    public void PressDeliveryTimeUpdatedPopUpPlaceOrder(){
        wait.until(ExpectedConditions.elementToBeClickable(DeliveryTimeUpdatedPopUpPlaceOrder))
                .click();
    }

    public WebElement getScrollableContentContainer(){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(scrollableContentContainerSelector)));
    }

    public void PressDeliveryTimeUpdatedPopUpPickAnotherTime(){
        wait.until(ExpectedConditions.elementToBeClickable(DeliveryTimeUpdatedPopUpPickAnotherTime))
                .click();
    }

    public String getPromoCodeTxtFieldContentDescription() {
        return promoCodeTxtFieldContentDescription;
    }

    public WebElement getPromoCodeTxtFieldUiElement() {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(
                String.format(promoCodeTxtFieldSelector, getPromoCodeTxtFieldContentDescription()))));
    }

    public void enterCouponCode(String couponCode) {
        wait.until(ExpectedConditions.visibilityOf(applyCouponCodeBtn));
        enterValueInTextField(getPromoCodeTxtFieldUiElement(), couponCode);
        dismissKeyboardIfDisplayed(iosDriver);

    }

    public void pressApplyCouponCodeBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(applyCouponCodeBtn))
                .click();
    }

    public void PressScheduleBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(ScheduleBtn))
                .click();
    }

    public boolean ScheduleAnotherTimeSheetIsDisplayed(){
        return isElementDisplayed(ScheduleAnotherTimeSheet);
    }

    public void PressFirstAvailableScheduleSlot(){
        wait.until(ExpectedConditions.elementToBeClickable(FirstAvailableScheduleSlot))
                .click();
    }

    public boolean InstantInScheduleSheetIsDisplayed(){
        return isElementDisplayed(InstantInScheduleSheet);
    }

    public boolean FirstFullyBookedScheduleSlotIsDisplayed(){
        return isElementDisplayed(FirstFullyBookedScheduleSlot);
    }

    public boolean ClosestSlotDueToHighDemandMsgIsDisplayed(){
        return isElementDisplayed(ClosestSlotDueToHighDemandMsg);
    }

    public boolean BookThisSlotBtnIsDisplayed(){
        return isElementDisplayed(BookThisSlotBtn);
    }

    public void PressBookThisSlotBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(BookThisSlotBtn))
                .click();
    }

    public boolean PickAnotherTimeBtnIsDisplayed(){
        return isElementDisplayed(PickAnotherTimeBtn);
    }

    public void PressPickAnotherTimeBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(PickAnotherTimeBtn))
                .click();
    }

    public boolean DeliverAtTheEarliestToggleIsDisplayed(){
        return isElementDisplayed(DeliverAtTheEarliestToggle);
    }

    public void PressDeliverAtTheEarliestToggleBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(DeliverAtTheEarliestToggleBtn))
                .click();
    }

    public boolean DeliverAtTheEarliestPopupSheetIsDisplayed(){
        return isElementDisplayed(DeliverAtTheEarliestPopupSheet);
    }

    public void PressGotItBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(GotItBtn))
                .click();
    }

    public void pressChangeCardBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(changeCardBtn))
                .click();
    }

    public boolean isCardSelectionModalDisplayed(){
        return isElementDisplayed(cardSelectionModal);
    }

    public void pressChangeCardBtnInCardSelectionModal() {
        wait.until(ExpectedConditions.elementToBeClickable(changeCardBtnInCardSelectionModal))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(cardSelectionModal));
    }

    public void pressCashOnDeliveryBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(cashOnDeliveryBtn))
                .click();
    }

    public String getPromoCodeText(){
        return promoCodeText;
    }

    public void enterPromoCode(String promoCode) {
        wait.until(ExpectedConditions.elementToBeClickable(promoCodeTxtField)).click();
        enterValueInTextField(promoCodeTxtField, promoCode);
        dismissKeyboardIfDisplayed(iosDriver);
    }

    public void pressPromoCodeApplyBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(promoCodeApplyBtn))
                .click();
    }
}
