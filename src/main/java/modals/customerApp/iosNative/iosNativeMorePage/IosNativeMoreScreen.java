package modals.customerApp.iosNative.iosNativeMorePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeMoreScreen extends BaseIosScreen {
    public IosNativeMoreScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    String logoutNameSelector = "moreScreen_logout";

    @FindBy(xpath = "//XCUIElementTypeButton[@name='moreScreen_logout']")
    WebElement logoutBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Continue']")
    WebElement continueBtn;

    String scrollableContentContainer = "//XCUIElementTypeScrollView/XCUIElementTypeOther/XCUIElementTypeOther";

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='moreScreen_userName']")
    WebElement userName;

    String fullNameXpath = "//XCUIElementTypeStaticText[@label='moreScreen_userName']";

    @FindBy (name = "country_egypt_name")
    WebElement countryEgypt;

    @FindBy (name = "country_ksa_name")
    WebElement countryKSA;

    @FindBy(name = "moreScreen_freshChat")
    WebElement chatBotTitle;

    @FindBy(name ="MoreUnSelectedState")
    WebElement moreTabButton;

    @FindBy(name = "moreScreen_activityHistory")
    WebElement activityHistoryBtn;

    @FindBy(name = "moreScreen_favorites")
    WebElement favoritesBtn;

    @FindBy(name = "moreScreen_rewards")
    WebElement breadfastRewardsBtn;

    @FindBy(name = "moreScreen_freeCredits")
    WebElement freeCreditBtn;

    @FindBy(name = "moreScreen_accountSettings")
    WebElement accountSettingsBtn;

    @FindBy(name = "moreScreen_help")
    WebElement helpBtn;

    @FindBy(xpath = "//XCUIElementTypeNavigationBar[@name='Help']")
    WebElement helpTitle;

    @FindBy (xpath = "//XCUIElementTypeStaticText[@name='Change country']")
    WebElement changeCountrySheetTitle;

    @FindBy(name = "moreScreen_language")
    WebElement languageBtn;

    @FindBy(xpath = "(//XCUIElementTypeButton[@name='moreScreen_language']//XCUIElementTypeStaticText)[2]")
    WebElement currentLanguage;

    @FindBy(name = "moreScreen_country")
    WebElement countryBtn;

    @FindBy(xpath = "(//XCUIElementTypeButton[@name='moreScreen_country']//XCUIElementTypeStaticText)[2]")
    WebElement currentCountry;

    @FindBy(name = "moreScreen_freshChat")
    WebElement talkToUsBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Get Started with Breadfast']")
    WebElement getStartedWithBreadfastTxt;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='المزيد']")
    WebElement MoreArLang;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Change']")
    WebElement changeBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='moreScreen_language']")
    WebElement moreScreenUpdateLangBtn;

    @FindBy(xpath = "//XCUIElementTypeCell[@name='Language, English']")
    WebElement changeLangSettingsDevice;

    @FindBy (xpath = "//XCUIElementTypeCell[@name='العربية']")
    WebElement selectArFromDeviceSettings;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Saudi Arabia']")
    WebElement kSACountrySelected;

    public void pressLogoutBtn(){
        wait.until(ExpectedConditions.visibilityOf(logoutBtn))
                .click();
    }

    public void pressContinueBtn(){
        wait.until(ExpectedConditions.visibilityOf(continueBtn))
                .click();
    }

    public WebElement getScrollableContentContainer(){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(scrollableContentContainer)));
    }

    public String getLogoutNameSelector(){
        return logoutNameSelector;
    }

    public String getDisplayedUserFullName(){
        return wait.until(ExpectedConditions.visibilityOf(userName))
                .getText();
    }

    public void pressMoreTabBtn()  {
        wait.until(ExpectedConditions.visibilityOf(moreTabButton)).click();
    }

    public boolean isFullNameDisplayed(String firstName, String lastName) {
        WebElement fullName = wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(fullNameXpath, (firstName + " " + lastName)))));
        return isElementDisplayed(fullName);
    }

    public void pressActivityHistoryBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(activityHistoryBtn))
                .click();
    }

    public void pressFavoritesBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(favoritesBtn))
                .click();
    }

    public void pressHelpBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(helpBtn))
                .click();
    }

    public void pressRewardsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(breadfastRewardsBtn))
                .click();
    }

    public void pressCountryBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(countryBtn))
                .click();
    }

    public String getCurrentlySelectedCountryName(){
        return wait.until(ExpectedConditions.visibilityOf(currentCountry))
                .getText();
    }

    public void pressTalkToUsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(talkToUsBtn))
                .click();
    }

    public void pressAccountSettingsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(accountSettingsBtn))
                .click();
    }

    public boolean isHelpPageDisplayed () {
        return isElementDisplayed(helpTitle);
    }

    public boolean isCountrySheetDisplayed () {
        return isElementDisplayed(changeCountrySheetTitle);
    }

    public void pressOnTalkToUs ()
    {
        wait.until(ExpectedConditions.elementToBeClickable(talkToUsBtn)).click();
    }

    public boolean isGetStartedTxtDisplayed() {
        return isElementDisplayed(getStartedWithBreadfastTxt);
    }

    public boolean isLangChangedToAr () {
        return isElementDisplayed(MoreArLang);
    }

    public void pressToSelectKSA () {
        wait.until(ExpectedConditions.visibilityOf(countryKSA)).click();
    }

    public void pressToSelectEgypt () {
        wait.until(ExpectedConditions.visibilityOf(countryEgypt)).click();
    }

    public void pressChangeBtn() {
        wait.until(ExpectedConditions.visibilityOf(changeBtn)).click();
    }

    public void pressUpdateLangFromMoreScreen() {
        wait.until(ExpectedConditions.visibilityOf(moreScreenUpdateLangBtn)).click();
    }

    public void pressChangeLanFromDeviceSettings() {
        wait.until(ExpectedConditions.visibilityOf(changeLangSettingsDevice)).click();
    }

    public void pressArLagDeviceSettings() {
        wait.until(ExpectedConditions.visibilityOf(selectArFromDeviceSettings)).click();
    }

    public boolean isCountryChangedToKSA() {
        return isElementDisplayed(kSACountrySelected);
    }
}
