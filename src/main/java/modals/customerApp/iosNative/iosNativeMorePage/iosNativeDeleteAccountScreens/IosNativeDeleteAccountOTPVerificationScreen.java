package modals.customerApp.iosNative.iosNativeMorePage.iosNativeDeleteAccountScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeDeleteAccountOTPVerificationScreen extends BaseIosScreen {
    public IosNativeDeleteAccountOTPVerificationScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "otpScreen_submitBtn")
    WebElement deleteAccountBtn;

    @FindBy(xpath = "//XCUIElementTypeSwitch[@name='otpScreen_termsContainer']")
    WebElement termsAndConditionsCheckbox;

    @FindBy(xpath = "(//XCUIElementTypeStaticText[@name=\" \"])[1]")
    WebElement deleteAccountOTPVerificationTextField;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Resend code']")
    WebElement resendCodeBtn;

    @FindBy(xpath = "//XCUIElementTypeAlert")
    WebElement errorAlert;

    @FindBy(name = "otpScreen_submitBtn")
    WebElement  deleteAccountConfirmOtpBtn;

    public boolean isVerifyDeletingAccountScreenDisplayed () {
       return wait.until(ExpectedConditions.visibilityOf(termsAndConditionsCheckbox))
               .isDisplayed();
    }

    public void pressOnDeleteAccount () {
        wait.until(ExpectedConditions.visibilityOf(deleteAccountBtn)).click();
    }

    public void pressTermsAndConditionsCheckbox () {
        wait.until(ExpectedConditions.visibilityOf(termsAndConditionsCheckbox))
                .click();
    }

    public WebElement getOtpTextField(){
        wait.until(ExpectedConditions.visibilityOf(deleteAccountOTPVerificationTextField)).click();
        return (deleteAccountOTPVerificationTextField);
    }

    public void resendCodeBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(resendCodeBtn))
                .click();
    }

    public void enterOtp(String otp){
        wait.until(ExpectedConditions.elementToBeClickable(deleteAccountOTPVerificationTextField))
                .sendKeys(otp);
    }

    public boolean isDeleteAccountConfirmOtpBtnEnabled(){
        return wait.until(ExpectedConditions.visibilityOf(deleteAccountConfirmOtpBtn))
                .isEnabled();
    }
}
