package modals.customerApp.iosNative.iosNativeMorePage.iosNativeDeleteAccountScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeDeleteAccountScreen extends BaseIosScreen {

    public IosNativeDeleteAccountScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "deleteAccount_description")
    WebElement deletePageDescription;

    @FindBy(name = "deleteAccount_confirmReason_btn")
    WebElement continueBtn;

    @FindBy(xpath = "")
    WebElement deleteHintMsg;

    @FindBy(name = "deleteConfirm_verifyBtn")
    WebElement verifyMobileNumberBtn;

    @FindBy(xpath = "")
    WebElement closeBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Welcome back to Breadfast!\"]")
    WebElement welcomeBackTxtMsg;

    String deleteReasonSelector = "//XCUIElementTypeStaticText[@name='%s']";

    String scrollableContentContainerSelector = "//XCUIElementTypeScrollView";

    String continueBtnNameSelector = "deleteAccount_confirmReason_btn";

    public boolean isPageDisplayed () {
        return isElementDisplayed(deletePageDescription);
    }

    public WebElement getScrollableContentContainer() {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(scrollableContentContainerSelector)));
    }

    public String getContinueBtnNameSelector(){
        return continueBtnNameSelector;
    }

    public void pressContinueBtnToDelete () {
        wait.until(ExpectedConditions.visibilityOf(continueBtn)).click();
    }

    public boolean isDeleteAccountConfirmationScreenDisplayed () {
        return isElementDisplayed(verifyMobileNumberBtn);
    }

    public void pressVerifyMobileNumber () {
        wait.until(ExpectedConditions.visibilityOf(verifyMobileNumberBtn))
                .click();
    }

    public void pressCloseBtn () {
        wait.until(ExpectedConditions.visibilityOf(closeBtn))
                .click();
    }

    public boolean isWelcomeBackToBreadfastDisplayed () {
        return isElementDisplayed(welcomeBackTxtMsg);
    }

    //Click on the delete reason
    public void pressDeleteFirstReason(String reason) {
            wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(
                            String.format(deleteReasonSelector, reason))))
                    .click();
    }
}
