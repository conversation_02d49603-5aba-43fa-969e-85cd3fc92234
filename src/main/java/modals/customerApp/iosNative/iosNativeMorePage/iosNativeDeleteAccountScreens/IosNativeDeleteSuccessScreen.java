package modals.customerApp.iosNative.iosNativeMorePage.iosNativeDeleteAccountScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeDeleteSuccessScreen extends BaseIosScreen {
    public IosNativeDeleteSuccessScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "deleteResult_title")
    WebElement deleteSuccessScreenTitle;

    @FindBy(name = "deleteResult_dateConfrimation")
    WebElement deletionDateConfirmation;

    @FindBy(name = "deleteResult_closeBtn")
    WebElement deleteSuccessCloseBtn;

    public boolean isPageDisplayed () {
        return isElementDisplayed(deleteSuccessScreenTitle);
    }

    public String getDeletionDateConfirmation() {
        return wait.until(ExpectedConditions.visibilityOf(deletionDateConfirmation))
                .getText();
    }

    public void pressCloseBtn() {
        wait.until(ExpectedConditions.visibilityOf(deleteSuccessCloseBtn))
                .click();
    }
}
