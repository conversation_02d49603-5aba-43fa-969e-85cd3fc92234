package modals.customerApp.iosNative.iosNativeMorePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeSavedAddressScreen extends BaseIosScreen {
    public IosNativeSavedAddressScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }
    @FindBy (xpath = "//XCUIElementTypeStaticText[@name=\"savedAddresses_title\"]")
    WebElement savedAddressTitle;

    @FindBy (xpath = "//XCUIElementTypeButton[@name=\"savedAddresses_addNewAddress_btn\"]")
    WebElement addAddressBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"defaultAddress_tag\"]")
    WebElement defaultAddressTag;

    public boolean isDefaultAddressDisplayed()
    {
        return isElementDisplayed(defaultAddressTag);
    }

    public boolean isAddressesPageDisplayed() {
        return isElementDisplayed(savedAddressTitle);
    }

    public void pressOnDefaultAddress() {
        wait.until(ExpectedConditions.visibilityOf(defaultAddressTag))
                .click();
    }
}
