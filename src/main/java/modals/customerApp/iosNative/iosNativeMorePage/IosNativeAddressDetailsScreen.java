package modals.customerApp.iosNative.iosNativeMorePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeAddressDetailsScreen extends BaseIosScreen {
    public IosNativeAddressDetailsScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "addressDetails_deleteAddress_btn")
    WebElement deleteBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='You must set a new default address before deleting this one.']")
    WebElement cantDeleteDefaultAddressTag;

    public void pressOnDeleteBtn() {
        wait.until(ExpectedConditions.visibilityOf(deleteBtn))
                .click();
    }

    public boolean isDeleteTagDisplayed() {
        return isElementDisplayed(cantDeleteDefaultAddressTag);
    }
}
