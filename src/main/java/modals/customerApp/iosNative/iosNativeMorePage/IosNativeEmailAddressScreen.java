package modals.customerApp.iosNative.iosNativeMorePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeEmailAddressScreen extends BaseIosScreen {
    public IosNativeEmailAddressScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='The email is already used. Try another one.']")
    WebElement emailExistingHint;

    @FindBy(name = "emailScreen_submitBtn")
    WebElement updateEmailBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Your email was updated successfully']")
    WebElement mailUpdatedTag;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='emailScreen_txtField']")
    WebElement emailTxtFieldEdit;

    public boolean isEmailPageDisplayed () {
        return isElementDisplayed(emailTxtFieldEdit);
    }

    public void updateUserMail(String UserUpdatedMail) {
        wait.until(ExpectedConditions.visibilityOf(emailTxtFieldEdit)).click();
        wait.until(ExpectedConditions.visibilityOf(emailTxtFieldEdit)).clear();
        enterValueInTextField(emailTxtFieldEdit, UserUpdatedMail);
    }

    public void pressOnUpdateMailBtn() {
        wait.until(ExpectedConditions.visibilityOf(updateEmailBtn))
                .click();
    }

    public boolean isMailUpdatedTagDisplayed()
    {
        return isElementDisplayed(mailUpdatedTag);
    }

    public boolean isMailExistingHintDisplayed()
    {
        return isElementDisplayed(emailExistingHint);
    }
}
