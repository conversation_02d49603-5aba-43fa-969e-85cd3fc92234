package modals.customerApp.iosNative.iosNativeMorePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeAccountSettingsScreen extends BaseIosScreen {
    public IosNativeAccountSettingsScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "accountSettings_personalInformation_btn")
    WebElement personalInfoTab;

    @FindBy(name = "accountSettings_savedAddresses_btn")
    WebElement savedAddress;

    @FindBy(name = "accountSettings_emailAddress_btn")
    WebElement emailAddresses;

    @FindBy(name = "accountSettings_deleteAccount_btn")
    WebElement deleteAccountTab;

    public Boolean isPageDisplayed() {
        return isElementDisplayed(personalInfoTab);
    }

    public void pressManageAddressesTab(){
        wait.until(ExpectedConditions.elementToBeClickable(savedAddress))
                .click();
    }

    public void pressPersonalInfoTab()
    {
        wait.until(ExpectedConditions.visibilityOf(personalInfoTab))
                .click();
    }

    public void pressMailAddress() {
        wait.until(ExpectedConditions.visibilityOf(emailAddresses))
                .click();
    }
    public void pressDeleteAccountTab() {
        wait.until(ExpectedConditions.visibilityOf(deleteAccountTab))
                .click();
    }
}
