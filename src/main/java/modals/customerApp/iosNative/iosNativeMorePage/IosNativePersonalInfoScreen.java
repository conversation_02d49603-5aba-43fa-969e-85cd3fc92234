package modals.customerApp.iosNative.iosNativeMorePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativePersonalInfoScreen extends BaseIosScreen {
    public IosNativePersonalInfoScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='personalInformation_firstName_']")
    WebElement firstNameTxtField;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='personalInformation_lastName_']")
    WebElement lastNameTxtField;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='personalInformation_phoneNumber_']")
    WebElement phoneNumberTxtField;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Save']")
    WebElement saveChangeBtn;

    @FindBy (xpath = "//XCUIElementTypeStaticText[@name='The changes were saved successfully.']")
    WebElement savedSuccessfullyMsg;

    String userName = "%s";

    public void pressOnFirstNameFiled() {
        wait.until(ExpectedConditions.visibilityOf(firstNameTxtField)).click();
    }

    public void pressOnLastNameFiled() {
        wait.until(ExpectedConditions.visibilityOf(lastNameTxtField)).click();
    }

    public void pressOnPhoneNumberTxtField() {
        wait.until(ExpectedConditions.visibilityOf(phoneNumberTxtField)).click();
    }

    public void pressOnSaveChangesBtn() {
        wait.until(ExpectedConditions.visibilityOf(saveChangeBtn)).click();
    }

    public boolean isPersonalInfoSaved() {
        return isElementDisplayed(savedSuccessfullyMsg);
    }

    public boolean isPersonalPageDisplayed() {
        return wait.until(ExpectedConditions.visibilityOf(firstNameTxtField))
                .isDisplayed();
    }

    public void UpdateUserFName(String MyUpdatedName) {
        wait.until(ExpectedConditions.visibilityOf(firstNameTxtField)).clear();
        firstNameTxtField.click();
        firstNameTxtField.sendKeys(MyUpdatedName);
    }

    public void UpdateUserLName(String MyUpdatedName){
            wait.until(ExpectedConditions.visibilityOf(lastNameTxtField)).clear();
            lastNameTxtField.click();
            lastNameTxtField.sendKeys(MyUpdatedName);
    }

    public void enterNameAndPressSaveBtn(String firstName, String lastName) {
        UpdateUserFName(firstName);
        UpdateUserLName(lastName);
        pressOnSaveChangesBtn();
    }

    public void UpdateUserMobileNumber(String updatedPhoneNumber){
        wait.until(ExpectedConditions.visibilityOf(phoneNumberTxtField)).clear();
        phoneNumberTxtField.click();
        phoneNumberTxtField.sendKeys(updatedPhoneNumber);
    }

    public String getTheUserName(String UserFName){
        return String.format(this.userName, UserFName);
    }
}
