package modals.customerApp.iosNative.iosNativeMorePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeFavouriteScreen extends BaseIosScreen {

    public IosNativeFavouriteScreen(IOSDriver iosDriver){
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "")
    WebElement pageContainerEmptyView;

    @FindBy (xpath = "")
    WebElement emptyViewTitle;

    @FindBy (xpath = "")
    WebElement emptyViewDescription;

    @FindBy(xpath = "")
    WebElement exploreBtn;

    @FindBy(xpath = "")
    WebElement favBtn;

    @FindBy(xpath = "")
    WebElement increaseQtyBtn;

    @FindBy(xpath = "")
    WebElement cartBtn;

    @FindBy(xpath = "")
    WebElement qtyTxtOfOne;

    public boolean isPageDisplayedWithEmptyView(){
        return isElementDisplayed(pageContainerEmptyView);
    }

    public boolean isFavListEmpty()
    {
        return isElementDisplayed(exploreBtn);
    }

    public void pressExploreBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(exploreBtn)).click();
    }

    public void pressFavBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(favBtn)).click();
    }
    public  void pressCartBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(cartBtn)).click();
    }

    public boolean isQtyAdded ()
    {
        return isElementDisplayed(qtyTxtOfOne);
    }
}
