package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import models.TestData;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeCreateAccountScreen extends BaseIosScreen {
    public IosNativeCreateAccountScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "createAccount_header")
    WebElement pageTitle;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='createAccount_firstName_txtField']")
    WebElement firstNameField;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='createAccount_lastName_txtField']")
    WebElement lastNameField;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='createAccount_email_txtField']")
    WebElement emailField;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='createAccount_referralCode_txtField']")
    WebElement referralCodeField;

    @FindBy(name = "createAccount_submitBtn")
    WebElement submitBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='This field is required.']")
    WebElement emptyFieldErrorMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='First name must include letters only.']")
    WebElement invalidFirstNameErrorMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Last name must include letters only.']")
    WebElement invalidLastNameErrorMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Enter a valid email address.']")
    WebElement invalidEmailErrorMsg;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void enterFirstName(String firstName){
        wait.until(ExpectedConditions.visibilityOf(firstNameField)).sendKeys(firstName);
    }

    public void enterLastName(String lastName){
        wait.until(ExpectedConditions.visibilityOf(lastNameField)).sendKeys(lastName);
    }

    public void enterEmail(String email){
        wait.until(ExpectedConditions.visibilityOf(emailField)).sendKeys(email);
    }

    public void enterReferralCode(String refCode){
        wait.until(ExpectedConditions.visibilityOf(referralCodeField)).sendKeys(refCode);
    }

    public void fillInAccountInformationForm(TestData testData){
        enterFirstName(testData.getRandomTestUser().getFirstName());
        enterLastName(testData.getRandomTestUser().getLastName());
        enterEmail(testData.getRandomTestUser().getEmailAddress());
        wait.until(ExpectedConditions.attributeToBe(submitBtn, "enabled", "true"));
    }

    public void pressSubmitBtn(){
        wait.until(ExpectedConditions.visibilityOf(submitBtn))
                .click();
    }

    public boolean isEmptyFirstNameErrorMsgDisplayed(){
        return isElementDisplayed(emptyFieldErrorMsg);
    }

    public boolean isEmptyLastNameErrorMsgDisplayed(){
        return isElementDisplayed(emptyFieldErrorMsg);
    }

    public boolean isInvalidFirstNameErrorMsgDisplayed(){
        return isElementDisplayed(invalidFirstNameErrorMsg);
    }

    public boolean isInvalidLastNameErrorMsgDisplayed(){
        return isElementDisplayed(invalidLastNameErrorMsg);
    }

    public boolean isInvalidEmailErrorMsgDisplayed(){
        return isElementDisplayed(invalidEmailErrorMsg);
    }

    public boolean isSubmitBtnEnabled(){
        try {
            wait.until(ExpectedConditions.attributeToBe(submitBtn, "enabled", "true"));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public void clearTxtField(String fieldName){
        switch (fieldName.toLowerCase()) {
            case "firstname" -> wait.until(ExpectedConditions.visibilityOf(firstNameField)).clear();
            case "lastname" -> wait.until(ExpectedConditions.visibilityOf(lastNameField)).clear();
            case "email" -> wait.until(ExpectedConditions.visibilityOf(emailField)).clear();
            case "referralcode" -> wait.until(ExpectedConditions.visibilityOf(referralCodeField)).clear();
            default -> { /* do nothing */ }
        }
    }
}
