package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeCategoryDetailsScreen extends BaseIosScreen {
    public IosNativeCategoryDetailsScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "(//XCUIElementTypeScrollView)[1]")
    WebElement subCategoriesScrollableContentContainer;

    @FindBy(xpath = "//XCUIElementTypeOther")
    WebElement productsScrollableContentContainer;

    @FindBy(name = "currentCategory_title")
    WebElement pageTitle;

    String subCategoryNameSelector = "subCategory_%s_name";

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Back']")
    WebElement backBtn;

    @FindBy(name = "cartBtn")
    WebElement cartBtn;

    String addToFavoritesBtnNameSelector = "addToFavorites_product_%s";

    String productImgSelector = "product_%s_img";

    String addToCartBtnSelector = "product_%s_increaseBtn";

    String currentStockSelector = "product_%s_currentCartStock";

    String removeFromCartBtnSelector = "product_%s_decreaseBtn";

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public WebElement getSubCategoriesScrollableContentContainer(){
        return subCategoriesScrollableContentContainer;
    }

    public WebElement getProductsScrollableContentContainer(){
        return productsScrollableContentContainer;
    }

    public void pressBackBtn(){
        wait.until(ExpectedConditions.visibilityOf(backBtn))
                .click();
    }

    public String getSubCategoryNameSelector(String subCategoryId){
        return String.format(subCategoryNameSelector, subCategoryId);
    }

    public void pressSubCategoryById(String subCategoryId){
        wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(getSubCategoryNameSelector(subCategoryId))))
                .click();
    }

    public void pressAddToCartBtnByProductId(String productId){
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.name(String.format(addToCartBtnSelector, productId))))
                .click();
    }

    public String getProductImgNameSelectorById(String productId){
        return String.format(productImgSelector, productId);
    }

    public WebElement getProductCardElementById(String productId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.name(getProductImgNameSelectorById(productId))));
    }

    public void pressProductCardById(String productId){
        getProductCardElementById(productId)
                .click();
    }

    public void pressCartBtn(){
        wait.until(ExpectedConditions.visibilityOf(cartBtn))
                .click();
    }
}
