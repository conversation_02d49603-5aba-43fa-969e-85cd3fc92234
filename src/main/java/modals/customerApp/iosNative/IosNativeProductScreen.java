package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeProductScreen extends BaseIosScreen {
    public IosNativeProductScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "")
    WebElement addToFavBtn;

    @FindBy (xpath = "")
    WebElement viewFavListBtn;

    @FindBy (xpath = "")
    WebElement backBtn;

    public void clickFavoriteBtn() {
        wait.until(ExpectedConditions.visibilityOf(addToFavBtn)).click();
    }

    public void clickViewFavList() {
        wait.until(ExpectedConditions.visibilityOf(viewFavListBtn)).click();
    }

    public void clickBack() {
        wait.until(ExpectedConditions.visibilityOf(backBtn)).click();
    }

}
