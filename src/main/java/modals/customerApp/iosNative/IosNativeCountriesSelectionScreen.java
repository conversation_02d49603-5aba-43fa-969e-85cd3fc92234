package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeCountriesSelectionScreen extends BaseIosScreen {
    public IosNativeCountriesSelectionScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    String countrySelector = "countryName_%s";

    @FindBy(name = "continueButton")
    WebElement continueBtn;

    public void selectCountryByName(String countryName){
        switch (countryName.toLowerCase()){
            case "egypt", "ksa" ->
                    wait.until(ExpectedConditions.visibilityOfElementLocated(
                            By.name(String.format(countrySelector, countryName)))).click();
            case "eg" ->
                    wait.until(ExpectedConditions.visibilityOfElementLocated(
                            By.name(String.format(countrySelector, "egypt")))).click();
            case "saudi arabia", "saudi", "saudiarabia" ->
                    wait.until(ExpectedConditions.visibilityOfElementLocated(
                            By.name(String.format(countrySelector, "ksa")))).click();
        }
        wait.until(ExpectedConditions.attributeToBe(continueBtn, "enabled", "true"));
    }

    public void pressContinueBtn(){
        wait.until(ExpectedConditions.visibilityOf(continueBtn)).click();
    }

    public void selectCountryAndProceed(String countryName){
        selectCountryByName(countryName);
        pressContinueBtn();
    }
}
