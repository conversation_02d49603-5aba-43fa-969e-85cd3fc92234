package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeOtpVerificationScreen extends BaseIosScreen {
    public IosNativeOtpVerificationScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "otpScreen_header")
    WebElement pageTitle;

    @FindBy(name = "otpScreen_subHeader")
    WebElement pageSubTitle;

    @FindBy(xpath = "(//XCUIElementTypeStaticText)[3]")
    WebElement firstOtpDigitField;

    @FindBy(name = "otpTextField")
    WebElement otpTextField;

    @FindBy(name = "otpScreen_submitBtn")
    WebElement verifyBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='otpScreen_resendBtn']")
    WebElement didNotGetCodeBtn;

    @FindBy(name = "Resend Code by SMS")
    WebElement resendCodeBySmsBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@name, \"Wrong verification code\")]")
    WebElement wrongOtpErrorMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@name, 'ran out of verification attempts')]")
    WebElement outOfVerificationAttemptsErrorMsg;

    @FindBy(name = "Welcome back to Breadfast!")
    WebElement loginAfterDeletedAccountModalTitle;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public boolean isPageHidden(){
        return isElementHidden(pageTitle);
    }

    public void enterOtp(String otp){
        wait.until(ExpectedConditions.attributeToBe(otpTextField, "enabled", "true"));
        if (!otpTextField.getText().equalsIgnoreCase(otp)) {
            otpTextField.clear();
            otpTextField.sendKeys(otp);
        }
        wait.until(ExpectedConditions.attributeToBe(verifyBtn, "enabled", "true"));
    }

    public void submitOtpBtn(){
        wait.until(ExpectedConditions.visibilityOf(verifyBtn))
                .click();
    }

    public String getDidNotGetCodeBtnText(){
        return wait.until(ExpectedConditions.visibilityOf(didNotGetCodeBtn))
                .getText();
    }

    public void pressDidNotGetCodeBtn(){
        wait.until(ExpectedConditions.visibilityOf(didNotGetCodeBtn))
                .click();
    }

    public void pressResendCodeBySmsBtn(){
        wait.until(ExpectedConditions.visibilityOf(resendCodeBySmsBtn))
                .click();
    }

    public boolean isWrongOtpErrorMsgDisplayed(){
        return isElementDisplayed(wrongOtpErrorMsg);
    }

    public String getWrongOtpErrorMsgText(){
        return wait.until(ExpectedConditions.visibilityOf(wrongOtpErrorMsg)).getText();
    }

    public boolean isOutOfVerificationAttemptsErrorMsgDisplayed(){
        return isElementDisplayed(outOfVerificationAttemptsErrorMsg);
    }

    public void pressFirstOtpDigitField(){
        wait.until(ExpectedConditions.visibilityOf(firstOtpDigitField))
                .click();
    }
}
