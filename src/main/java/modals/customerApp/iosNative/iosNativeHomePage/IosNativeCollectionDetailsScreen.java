package modals.customerApp.iosNative.iosNativeHomePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeCollectionDetailsScreen extends BaseIosScreen {
    public IosNativeCollectionDetailsScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeButton[contains(@name, '_increaseBtn')]")
    WebElement firstAddToCartBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='smallClose']")
    WebElement dismissCollectionBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='CartFilled']")
    WebElement cartBtn;

    public void pressDismissCollectionBtn(){
        wait.until(ExpectedConditions.visibilityOf(dismissCollectionBtn))
                .click();
    }

    public void pressFirstAddToCartBtn(){
        wait.until(ExpectedConditions.visibilityOf(firstAddToCartBtn))
                .click();
    }

    public void pressCartBtn(){
        wait.until(ExpectedConditions.visibilityOf(cartBtn))
                .click();
    }
}
