package modals.customerApp.iosNative.foodAggregator;
import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.time.Duration;
import java.util.*;

public class IosNativeFoodAggregatorHomeScreen extends BaseIosScreen {
    public IosNativeFoodAggregatorHomeScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    private static final Logger logger = LoggerFactory.getLogger(IosNativeFoodAggregatorHomeScreen.class);

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Delivering to\"]")
    WebElement deliveringToTitle;

    @FindBy(name = "Back")
    WebElement homeBackBtn;

    @FindBy(name = "CartFilled")
    WebElement cartIcon;

    @FindBy(name = "homeRestaurantsTitle")
    WebElement allRestaurantsTitle;

    @FindBy(name = "restaurants_banner_english")
    WebElement restaurantEntryPoint;

    String categoryFilterIdSelector = "homeCategoryCard[%s]";

    String categoryFilterHeaderIdSelector ="homeCategoryHeader[%s]";

    String restaurantCardId = "homeRestaurantCard[%s]";

    String restaurantCardNameId = "homeRestaurantName[%s]";

    String homeRestaurantRatingId = "homeRestaurantRating[%s]";

    String homeRestaurantDeliveryTimeId = "homeRestaurantDeliveryTime[%s]";

    String homeRestaurantDeliveryFeesId = "homeRestaurantDeliveryFee[%s]";

    String homeRestaurantClosedStatusId = "homeRestaurantCLOSEDStatus[%s]";

    String homeRestaurantBusyStatusId = "homeRestaurantBUSYStatus[%s]";

    String restaurantsScrollableContainer= "homeRestaurantsSection";

    String categoryFilterScrollableContainer = "homeCategoryContainer";

    String categoryFilterNameId = "homeCategoryName[%s]";

    String ratingElementId = "homeRestaurantRating[%s]";

    String restaurantEntryPointContentDesc = "restaurants_banner_english";

    //Main Screen Elements Methods
    public void pressRestaurantsEntryPoint() {
        wait.until(ExpectedConditions.visibilityOf(restaurantEntryPoint))
                .click();
    }
    public String getRestaurantEntryPointContentDescription() {
        return restaurantEntryPointContentDesc;
    }

    public boolean isRestaurantHomeScreenDisplayed() {
        return isElementDisplayed(allRestaurantsTitle);
    }

    public boolean isBackToGroceryBtnDisplayed() {
        return isElementDisplayed(homeBackBtn);
    }

    public boolean isCartDisplayed() {
        return isElementDisplayed(cartIcon);
    }

    public void pressBackBtn() {
        wait.until(ExpectedConditions.visibilityOf(homeBackBtn))
                .click();
    }

    public void pressCartIcon() {
        wait.until(ExpectedConditions.visibilityOf(cartIcon))
                .click();
    }

    //Filter Methods
    public boolean isCategoryFilterRowDisplayed() {
        return isElementDisplayed(getFilterScrollableContainer());
    }

    public boolean isCategoryFilterDisplayed(String categoryId){
        return isElementDisplayed(getCategoryFilterUiElement(categoryId));
    }

    public boolean isCategoryFilterHeaderDisplayed(String categoryId){
        return isElementDisplayed(getCategoryFilterHeaderUiElement(categoryId));
    }

    public void pressCategoryFilterById(String categoryId){
        getCategoryFilterUiElement(categoryId)
                .click();
        wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(getCategoryFilterHeaderIdSelector(categoryId))));
    }

    public void deselectCategoryFilterById(String categoryId){
        getCategoryFilterUiElement(categoryId)
                .click();
        wait.until(ExpectedConditions.invisibilityOfElementLocated(By.name(getCategoryFilterHeaderIdSelector(categoryId))));
    }

    public String extractCategoryFilterName(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.name(getCategoryFilterNameIdSelector(categoryId)))).getAttribute("label");
    }

    public String getCategoryFilterNameIdSelector(String categoryId){
        return String.format(categoryFilterNameId,categoryId);
    }

    public WebElement getCategoryFilterNameUiElement(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(
                getCategoryFilterNameIdSelector(categoryId))));
    }

    public boolean isCategoryFilterNameDisplayed(String categoryId) {
        return isElementDisplayed(getCategoryFilterNameUiElement(categoryId));
    }

    public String getCategoryFilterIdSelector(String categoryId){
        return String.format(categoryFilterIdSelector,categoryId);
    }

    public WebElement getCategoryFilterUiElement(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(
                getCategoryFilterIdSelector(categoryId))));
    }

    public boolean isCategoryFilterRowScrollable(){
        try {
            return  getFilterScrollableContainer().getAttribute("type")
                    .equalsIgnoreCase("XCUIElementTypeScrollView");
        } catch (Exception e){
            return false;
        }
    }

    public WebElement getFilterScrollableContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(categoryFilterScrollableContainer)));
        } catch (Exception e) {
            return null;
        }
    }

    public List<WebElement> getAllVisibleCategoryCards() {
        try {
            // Match the homeCategoryCard[*] pattern
            return iosDriver.findElements(By.xpath("//*[contains(@name, 'homeCategoryCard[')]"));
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    public void captureCurrentlyVisibleCategories(Set<String> discoveredIds) {

        try {
            // Get all category cards currently visible in viewport
            List<WebElement> visibleCards = getAllVisibleCategoryCards();

            for (WebElement card : visibleCards) {
                try {
                    String categoryId = extractCategoryIdFromCard(card);
                    if (categoryId != null && !categoryId.isEmpty()) {
                        discoveredIds.add(categoryId);
                    }
                } catch (Exception e) {
                    // Continue processing other cards if one fails
                }
            }
        } catch (Exception e) {
            // Don't fail the test if capture fails, just log
            logger.error("Could not capture visible categories: {}", e.getMessage());
        }
    }

    private String extractCategoryIdFromCard(WebElement card) {
        try {
            // Get the resource-id attribute
            String accessibilityId = card.getAttribute("name");

            // Pattern: homeCategoryCard[123] -> "123"
            if (accessibilityId != null && accessibilityId.contains("homeCategoryCard[")) {
                int startIndex = accessibilityId.indexOf("homeCategoryCard[") + "homeCategoryCard[".length();
                int endIndex = accessibilityId.indexOf("]", startIndex);

                if (startIndex > 0 && endIndex > startIndex) {
                    return accessibilityId.substring(startIndex, endIndex);
                }
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    //Header Methods
    public String extractCategoryHeaderName(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.name(getCategoryFilterHeaderIdSelector(categoryId)))).getAttribute("label");
    }

    public String getCategoryFilterHeaderIdSelector(String categoryId){
        return String.format(categoryFilterHeaderIdSelector,categoryId);
    }

    public WebElement getCategoryFilterHeaderUiElement(String categoryId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(
                getCategoryFilterHeaderIdSelector(categoryId))));
    }

    //Restaurant Cards Methods
    public boolean isRestaurantCardDisplayed(String restaurantId) {
        return isElementDisplayed(getRestaurantCardUiElement(restaurantId));
    }

    //Check here if at least one restaurant is shown.
    public boolean areRestaurantsListed(String restaurantId) {
        return isElementDisplayed(getRestaurantCardUiElement(restaurantId));
    }

    public void pressRestaurantCardById(String restaurantId){
        getRestaurantCardUiElement(restaurantId)
                .click();
        wait.until(ExpectedConditions.invisibilityOfElementLocated(By.name(
                getRestaurantCardIdSelector(restaurantId))));
    }

    public String getRestaurantName(String restaurantId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                        By.name(String.format(restaurantCardNameId, restaurantId))))
                .getText();
    }

    public WebElement getRestaurantNameUiElement(String restaurantId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(
                getRestaurantNameIdSelector(restaurantId))));
    }

    public String getRestaurantNameIdSelector(String categoryId){
        return String.format(restaurantCardNameId,categoryId);
    }

    public boolean isRestaurantNameDisplayed(String restaurantId) {
        return isElementDisplayed(getRestaurantNameUiElement(restaurantId));
    }

    public String getRestaurantDeliveryTime(String restaurantId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                        By.name(String.format(homeRestaurantDeliveryTimeId, restaurantId))))
                .getText();
    }

    public String getRestaurantDeliveryFees(String restaurantId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                        By.name(String.format(homeRestaurantDeliveryFeesId, restaurantId))))
                .getText();
    }

    public boolean isRestaurantClosed(String restaurantId) {
        return isElementDisplayed(getRestaurantClosedStatusUiElement(restaurantId));
    }

    public boolean isRestaurantBusy(String restaurantId) {
        return isElementDisplayed(getRestaurantBusyStatusUiElement(restaurantId));
    }

    public String getRestaurantClosedStatusIdSelector(String restaurantId){
        return String.format(homeRestaurantClosedStatusId,restaurantId);
    }

    public String getRestaurantBusyStatusIdSelector(String restaurantId){
        return String.format(homeRestaurantBusyStatusId,restaurantId);
    }

    public WebElement getRestaurantBusyStatusUiElement(String restaurantId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(
                getRestaurantBusyStatusIdSelector(restaurantId))));
    }

    public WebElement getRestaurantClosedStatusUiElement(String restaurantId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(
                getRestaurantClosedStatusIdSelector(restaurantId))));
    }

    public WebElement getRestaurantsScrollableContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(restaurantsScrollableContainer)));
        } catch (Exception e) {
            return null;
        }
    }

    public String getRestaurantCardIdSelector(String restaurantId){
        return String.format(restaurantCardId,restaurantId);
    }

    public WebElement getRestaurantCardUiElement(String restaurantId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.name(
                getRestaurantCardIdSelector(restaurantId))));
    }

    public List<WebElement> getAllVisibleRestaurantCards() {
        try {
            By locator = By.xpath("//*[contains(@name, 'homeRestaurantCard[')]");

            List<WebElement> allCards = iosDriver.findElements(locator);
            logger.debug("Found {} total restaurant cards (regardless of visibility)", allCards.size());

            for (WebElement card : allCards) {
                try {
                    logger.debug("Card → name: {}, isDisplayed: {}", card.getAttribute("name"), card.isDisplayed());
                } catch (Exception e) {
                    logger.warn("Error inspecting card: {}", e.getMessage());
                }
            }

            return allCards;

        } catch (Exception e) {
            logger.warn("Error in getAllVisibleRestaurantCards(): {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    public void captureCurrentlyVisibleRestaurants(Set<String> discoveredIds) {

        try {
            // Get all restaurant cards currently visible in viewport
            List<WebElement> visibleCards = getAllVisibleRestaurantCards();

            for (WebElement card : visibleCards) {
                try {

                    String restaurantId = extractRestaurantIdFromCard(card);
                    if (restaurantId != null && !restaurantId.isEmpty()) {
                        discoveredIds.add(restaurantId);
                    }
                } catch (Exception e) {
                    logger.error("EXCEPTION CAUGHT AND SWALLOWED: {}", e.getMessage(), e);
                    // Continue processing other cards if one fails
                }
            }
        } catch (Exception e) {
            // Don't fail the test if capture fails, just log
            logger.error("Could not capture visible restaurants: {}", e.getMessage());
        }
    }

    private String extractRestaurantIdFromCard(WebElement card) {
        try {
            // Get the accessibility id attribute
            String accessibilityId = card.getAttribute("name");

            // Pattern: homeRestaurantCard[123] -> "123"
            if (accessibilityId != null && accessibilityId.contains("homeRestaurantCard[")) {
                int startIndex = accessibilityId.indexOf("homeRestaurantCard[") + "homeRestaurantCard[".length();
                int endIndex = accessibilityId.indexOf("]", startIndex);

                if (startIndex > 0 && endIndex > startIndex) {
                    return accessibilityId.substring(startIndex, endIndex);
                }
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public String getUnifiedRestaurantStatusFromUI(String restaurantId) {
        try {
            String closedStatusId = String.format(homeRestaurantClosedStatusId, restaurantId);
            String busyStatusId = String.format(homeRestaurantBusyStatusId, restaurantId);

            // Check for closed status
            try {
                WebDriverWait shortWait = new WebDriverWait(iosDriver, Duration.ofSeconds(3));
                WebElement closedElement = shortWait.until(ExpectedConditions.presenceOfElementLocated(By.id(closedStatusId)));
                if (closedElement.isDisplayed()) {
                    logger.info("Found closed status for restaurant ID: " + restaurantId);
                    return "closed";
                }
            } catch (TimeoutException e) {
                logger.debug("Closed status not found for restaurant ID: " + restaurantId);
            }

            // Check for busy status
            try {
                WebDriverWait shortWait = new WebDriverWait(iosDriver, Duration.ofSeconds(3));
                WebElement busyElement = shortWait.until(ExpectedConditions.presenceOfElementLocated(By.id(busyStatusId)));
                if (busyElement.isDisplayed()) {
                    logger.info("Found busy status for restaurant ID: " + restaurantId);
                    return "busy";
                }
            } catch (TimeoutException e) {
                logger.debug("Busy status not found for restaurant ID: " + restaurantId);
            }

            return "open";
        } catch (Exception e) {
            logger.warn("Error checking restaurant status for ID: " + restaurantId + ". Error: " + e.getMessage());
            return "open";
        }
    }
    public String verifyRestaurantStatus(String operatingStatus, String restaurantId) {
        String uiStatus = getUnifiedRestaurantStatusFromUI(restaurantId);

        operatingStatus = operatingStatus.toLowerCase();

        switch (operatingStatus) {
            case "closed":
                logger.info("Status is CLOSED in API, UI shows: " + uiStatus + " for restaurant ID: " + restaurantId);
                break;
            case "busy":
                logger.info("Status is BUSY in API. UI shows: " + uiStatus + " for restaurant ID: " + restaurantId);
                break;
            case "open":
                logger.info("Status is OPEN in API. UI shows: " + uiStatus + " for restaurant ID: " + restaurantId);
                break;
            default:
                logger.info("Unknown API status '" + operatingStatus + "'. UI shows: " + uiStatus + " for restaurant ID: " + restaurantId);
        }
        return uiStatus;
    }

    public boolean checkIfRatingIsDisplayed(String restaurantId) {
        try {
            WebElement ratingElement = wait.until(ExpectedConditions.presenceOfElementLocated(
                    By.id(String.format(ratingElementId, restaurantId))
            ));
            return ratingElement.isDisplayed();
        } catch (TimeoutException e) {
            return false;
        }
    }

    public Set<String> getCurrentlyVisibleRestaurants() {
        Set<String> visibleRestaurants = new HashSet<>();

        try {
            List<WebElement> visibleCards = getAllVisibleRestaurantCards();
            logger.info("Found {} visible restaurant cards", visibleCards.size());

            for (WebElement card : visibleCards) {
                String restaurantId = extractRestaurantIdFromCard(card);
                logger.debug("🔍 Extracted restaurant ID: {}", restaurantId);

                if (restaurantId != null && !restaurantId.isEmpty()) {
                    visibleRestaurants.add(restaurantId);
                } else {
                    logger.warn("Skipping card with null or empty restaurant ID");
                }
            }
        } catch (Exception e) {
            logger.warn("Error getting currently visible restaurants: {}", e.getMessage());
        }

        logger.info("Total visible restaurant IDs collected: {}", visibleRestaurants.size());
        return visibleRestaurants;
    }

    public boolean waitForDeliveryFeeToBeVisible(IOSDriver driver, String restaurantId) {
        String feeAccessibilityId = "homeRestaurantDeliveryFee[" + restaurantId + "]";
        int maxAttempts = 5;

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                WebElement feeElement = driver.findElement(By.id(feeAccessibilityId));
                if (feeElement.isDisplayed()) {
                    logger.info("✅ Delivery fee for restaurant ID {} is visible: {}", restaurantId, feeElement.getText());
                    return true;
                }
            } catch (Exception e) {
                logger.warn("⏳ Attempt {}/{} - Delivery fee not yet visible for ID: {}", attempt, maxAttempts, restaurantId);
            }

            // Perform a small scroll
            Map<String, Object> args = new HashMap<>();
            args.put("direction", "down");
            args.put("distance", 0.2);
            ((JavascriptExecutor) driver).executeScript("mobile: scroll", args);

            try {
                Thread.sleep(500);
            } catch (InterruptedException ignored) {}
        }

        logger.error("Delivery fee element NOT found after {} retries for restaurant ID: {}", maxAttempts, restaurantId);
        return false;
    }
}
