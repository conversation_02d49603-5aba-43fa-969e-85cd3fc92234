package modals.customerApp.iosNative.foodAggregator;
import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeFoodAggregatorRestaurantDetailsScreen extends BaseIosScreen {
    public IosNativeFoodAggregatorRestaurantDetailsScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "")
    WebElement restaurantName;

    @FindBy(name = "restaurantDetailsBtn")
    WebElement expandRestaurantDetailsBtn;

    @FindBy(name = "restaurantDetailsBackBtn")
    WebElement backBtn;

    @FindBy(name = "restaurantDetailsCartBtn")
    WebElement cartIcon;

    @FindBy(xpath = "")
    WebElement restaurantCategory;

    @FindBy(xpath = "")
    WebElement restaurantRating;

    @FindBy(xpath = "")
    WebElement restaurantDetailsSection;

    @FindBy(xpath = "")
    WebElement restaurantDeliveryFees;

    @FindBy(xpath = "")
    WebElement restaurantDeliverySlot;

    @FindBy(xpath = "")
    WebElement restaurantDeliveredByTag;

    @FindBy(xpath = "")
    WebElement restaurantCategories;

    @FindBy(xpath = "")
    WebElement restaurantProductName;

    @FindBy(xpath = "")
    WebElement restaurantProductPrice;

    @FindBy(xpath = "")
    WebElement restaurantProductAddToCartBtn;

    @FindBy(xpath = "")
    WebElement restaurantProductTag;

    @FindBy(xpath = "//XCUIElementTypeButton[contains(@name, 'restaurantDetailsProductQtyAddBtn')]")
    WebElement firstAddToCartBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name=\"Add\"]")
    WebElement firstItemAddToCartBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[contains(@name, 'restaurantDetailsProductQtyMinusBtn')]")
    WebElement firstDecreaseQtyBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@name, 'This item requires a separate order.')]")
    WebElement cartTypeErrorMsg;

    @FindBy(xpath = "//XCUIElementTypeButton[contains(@name, 'Start new order')]")
    WebElement startNewOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[contains(@name, 'Keep current cart')]")
    WebElement keepCurrentCartBtn;

    String firstProductCartQtySelector = "(//XCUIElementTypeOther[contains(@name, 'restaurantDetailsProductQtyTxt')])[1]" +
            "//XCUIElementTypeStaticText[@name='%s']";

    public boolean isPageDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(expandRestaurantDetailsBtn)).isDisplayed();
    }

    public boolean isRestaurantBackBtnDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(backBtn)).isDisplayed();
    }

    //Assert here on categories
    public boolean isRestaurantCategoriesDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantCategories)).isDisplayed();
    }

    public boolean isRestaurantCategoryDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantCategory)).isDisplayed();
    }

    public boolean isRestaurantSlotDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantDeliverySlot)).isDisplayed();
    }

    public boolean isRestaurantFessDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantDeliveryFees)).isDisplayed();
    }

    public boolean isRestaurantDeliveredByTagDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantDeliveredByTag)).isDisplayed();
    }

    public boolean isRestaurantCartIconDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(cartIcon)).isDisplayed();
    }

    public boolean isRestaurantRatingDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantRating)).isDisplayed();
    }

    public boolean isRestaurantDetailsSectionDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(restaurantDetailsSection)).isDisplayed();
    }

    public void pressRestaurantDetailsSection()
    {
         wait.until(ExpectedConditions.visibilityOf(restaurantDetailsSection)).click();
    }

    public void pressBackBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(backBtn)).click();
    }

    public void goToCartScreen()
    {
        wait.until(ExpectedConditions.visibilityOf(cartIcon)).click();
    }

    public void pressFirstAddToCartBtn() {
        wait.until(ExpectedConditions.visibilityOf(firstAddToCartBtn)).click();
    }
    public void pressFirstItemAddToCartBtn() {
        wait.until(ExpectedConditions.visibilityOf(firstItemAddToCartBtn)).click();
    }
    public void pressFirstDecreaseQtyBtn() {
        wait.until(ExpectedConditions.visibilityOf(firstDecreaseQtyBtn)).click();
    }

    public boolean isFirstDecreaseQtyBtnEnabled(){
        try {
            return wait.until(ExpectedConditions.visibilityOf(firstDecreaseQtyBtn)).isEnabled();
        } catch (Exception e){
            return false;
        }
    }

    public boolean isFirstProductAddedToCartCorrectly(String expectedQty) {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(
                    String.format(firstProductCartQtySelector, expectedQty)))).isDisplayed();
        } catch (Exception e){
            return false;
        }
    }

    public boolean isCartTypeErrorMsgDisplayed() {
        return isElementDisplayed(cartTypeErrorMsg);
    }

    public void pressStartNewOrderBtn() {
        wait.until(ExpectedConditions.visibilityOf(startNewOrderBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(startNewOrderBtn));
    }

    public void pressKeepCurrentCartBtn() {
        wait.until(ExpectedConditions.visibilityOf(keepCurrentCartBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(keepCurrentCartBtn));
    }
}

