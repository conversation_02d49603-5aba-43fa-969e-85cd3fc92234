package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeInternalCustomizedProductScreen extends BaseIosScreen {

    public IosNativeInternalCustomizedProductScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeImage[contains(@name, 'productDetailsProductCustomizationQtyAddBtn-')]")
    WebElement plusOptionBtn;

    @FindBy(xpath = "//XCUIElementTypeImage[contains(@name, 'productDetailsProductCustomizationQtyMinusBtn-')]")
    WebElement minusOptionBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[contains(@name, 'productDetailsQtyMinusBtn')]")
    WebElement increaseProductQtyBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[contains(@name, 'productDetailsQtyAddBtn')]")
    WebElement decreaseProductQtyBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Add']")
    WebElement addProductToCartBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Required']")
    WebElement requiredTextLabel;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Customize your order']")
    WebElement customizationTextLabel;

    @FindBy(xpath = "//XCUIElementTypeImage[contains(@name, 'productDetailsProductCustomizations-')]")
    WebElement radioOptionBtn;

    @FindBy(xpath = "//XCUIElementTypeScrollView")
    WebElement scrollableContentContainer;

    String relatedProductsContentDescription = "Related products";

    public void clickOnOptionBtn() {
        try {
            // Try clicking on the first element if it's visible
            if (radioOptionBtn.isDisplayed()) {
                wait.until(ExpectedConditions.elementToBeClickable(radioOptionBtn)).click();
            } else {
                wait.until(ExpectedConditions.elementToBeClickable(plusOptionBtn)).click();
            }
        } catch (NoSuchElementException | TimeoutException e) {
            // If first element is not found or not clickable, click on the other one
            wait.until(ExpectedConditions.elementToBeClickable(plusOptionBtn)).click();
        }
    }

    public void clickOnAddProductBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(addProductToCartBtn)).click();
    }

    public WebElement getScrollableContentContainer() {
        return scrollableContentContainer;
    }

    public String getRelatedProductsContentDescription(){
        return relatedProductsContentDescription;
    }

    public boolean isCustomizationLabelDisplayed(){
        return isElementDisplayed(customizationTextLabel);
    }
}
