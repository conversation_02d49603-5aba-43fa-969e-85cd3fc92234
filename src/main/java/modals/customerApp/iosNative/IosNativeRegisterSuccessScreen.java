package modals.customerApp.iosNative;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNativeRegisterSuccessScreen extends BaseIosScreen {
    public IosNativeRegisterSuccessScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "completeRegisterScreen_header")
    WebElement pageTitle;

    @FindBy(name = "completeRegisterScreen_continueBtn")
    WebElement doneBtn;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void pressDoneBtn(){
        wait.until(ExpectedConditions.visibilityOf(doneBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(doneBtn));
    }
}
