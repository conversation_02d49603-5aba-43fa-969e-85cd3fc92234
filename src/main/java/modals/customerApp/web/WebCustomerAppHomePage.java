package modals.customerApp.web;

import modals.BaseWebPage;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WebCustomerAppHomePage extends BaseWebPage {
    private final Logger logger = LoggerFactory.getLogger(WebCustomerAppHomePage.class);

    public WebCustomerAppHomePage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    // Page elements
    @FindBy(xpath = "//div[contains(@class, 'home-page')]")
    WebElement homePageContainer;

    @FindBy(xpath = "//div[contains(@class, 'categories-section')]")
    WebElement categoriesSection;

    @FindBy(xpath = "//div[contains(@class, 'scrollable-content')]")
    WebElement scrollableContentContainer;

    @FindBy(xpath = "//button[contains(@class, 'cart-btn')]")
    WebElement cartBtn;

    @FindBy(xpath = "//div[contains(@class, 'mini-tracking')]")
    WebElement miniTrackingContainer;

    // Dynamic locators
    public By getCategoryById(String categoryId) {
        return By.xpath("//div[@data-category-id='" + categoryId + "']");
    }

    public By getCategoryContentDescription(String categoryId) {
        return By.xpath("//div[@data-category-id='" + categoryId + "' or contains(@aria-label, '" + categoryId + "')]");
    }

    // Page actions
    public boolean isPageDisplayed() {
        logger.info("Checking if home page is displayed");
        try {
            wait.until(ExpectedConditions.visibilityOf(homePageContainer));
            return true;
        } catch (Exception e) {
            logger.error("Home page is not displayed", e);
            return false;
        }
    }

    public boolean isPageDismissed() {
        logger.info("Checking if home page is dismissed");
        try {
            wait.until(ExpectedConditions.invisibilityOf(homePageContainer));
            return true;
        } catch (Exception e) {
            logger.error("Home page is still visible", e);
            return false;
        }
    }

    public void pressCategoryById(String categoryId) {
        logger.info("Pressing category with ID: {}", categoryId);
        WebElement categoryElement = wait.until(ExpectedConditions.elementToBeClickable(getCategoryById(categoryId)));
        categoryElement.click();
    }

    public WebElement getScrollableContentContainer() {
        return scrollableContentContainer;
    }

    public boolean placeOrderMiniTrackingIsDisplayed() {
        logger.info("Checking if mini tracking is displayed");
        try {
            wait.until(ExpectedConditions.visibilityOf(miniTrackingContainer));
            return true;
        } catch (Exception e) {
            logger.error("Mini tracking is not displayed", e);
            return false;
        }
    }

    public void pressCartBtn() {
        logger.info("Pressing cart button");
        wait.until(ExpectedConditions.elementToBeClickable(cartBtn)).click();
    }
}
