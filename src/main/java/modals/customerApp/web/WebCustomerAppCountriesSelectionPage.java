package modals.customerApp.web;

import modals.BaseWebPage;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WebCustomerAppCountriesSelectionPage extends BaseWebPage {
    private final Logger logger = LoggerFactory.getLogger(WebCustomerAppCountriesSelectionPage.class);

    public WebCustomerAppCountriesSelectionPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    // Page elements
    @FindBy(xpath = "//div[contains(@class, 'countries-selection')]")
    WebElement countriesSelectionContainer;

    @FindBy(xpath = "//button[contains(@class, 'proceed-btn') or contains(text(), 'Proceed') or contains(text(), 'Continue')]")
    WebElement proceedBtn;

    @FindBy(xpath = "//div[contains(@class, 'country-list')]")
    WebElement countryList;

    // Dynamic locators
    public By getCountryByCode(String countryCode) {
        return By.xpath("//div[@data-country-code='" + countryCode + "'] | //button[@data-country-code='" + countryCode + "'] | //div[contains(text(), '" + countryCode + "')]");
    }

    // Page actions
    public boolean isPageDisplayed() {
        logger.info("Checking if countries selection page is displayed");
        try {
            wait.until(ExpectedConditions.visibilityOf(countriesSelectionContainer));
            return true;
        } catch (Exception e) {
            logger.error("Countries selection page is not displayed", e);
            return false;
        }
    }

    public void selectCountryAndProceed(String countryCode) {
        logger.info("Selecting country: {} and proceeding", countryCode);
        
        // Select country
        WebElement countryElement = wait.until(ExpectedConditions.elementToBeClickable(getCountryByCode(countryCode)));
        countryElement.click();
        
        // Proceed
        wait.until(ExpectedConditions.elementToBeClickable(proceedBtn)).click();
    }

    public void selectCountry(String countryCode) {
        logger.info("Selecting country: {}", countryCode);
        WebElement countryElement = wait.until(ExpectedConditions.elementToBeClickable(getCountryByCode(countryCode)));
        countryElement.click();
    }

    public void pressProceedBtn() {
        logger.info("Pressing proceed button");
        wait.until(ExpectedConditions.elementToBeClickable(proceedBtn)).click();
    }
}
