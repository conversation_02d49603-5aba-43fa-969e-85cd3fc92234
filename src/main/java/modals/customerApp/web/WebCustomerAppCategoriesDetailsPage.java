package modals.customerApp.web;

import modals.BaseWebPage;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WebCustomerAppCategoriesDetailsPage extends BaseWebPage {
    private final Logger logger = LoggerFactory.getLogger(WebCustomerAppCategoriesDetailsPage.class);

    public WebCustomerAppCategoriesDetailsPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    // Page elements
    @FindBy(xpath = "//div[contains(@class, 'category-details')]")
    WebElement categoryDetailsContainer;

    @FindBy(xpath = "//div[contains(@class, 'products-grid')]")
    WebElement productsGrid;

    @FindBy(xpath = "//div[contains(@class, 'scrollable-content')]")
    WebElement scrollableContentContainer;

    @FindBy(xpath = "//button[contains(@class, 'cart-btn')]")
    WebElement cartBtn;

    // Dynamic locators
    public By getProductCardByProductId(String productId) {
        return By.xpath("//div[@data-product-id='" + productId + "']");
    }

    public By getProductCardContentDescription(String productId) {
        return By.xpath("//div[@data-product-id='" + productId + "' or contains(@aria-label, '" + productId + "')]");
    }

    public By getAddToCartBtnByProductId(String productId) {
        return By.xpath("//div[@data-product-id='" + productId + "']//button[contains(@class, 'add-to-cart') or contains(text(), 'Add to Cart')]");
    }

    // Page actions
    public boolean isPageDisplayed(String categoryName) {
        logger.info("Checking if category details page is displayed for category: {}", categoryName);
        try {
            wait.until(ExpectedConditions.visibilityOf(categoryDetailsContainer));
            // Optionally check for category name in page title or header
            return true;
        } catch (Exception e) {
            logger.error("Category details page is not displayed", e);
            return false;
        }
    }

    public void pressAddToCartBtnByProductId(String productId) {
        logger.info("Pressing add to cart button for product ID: {}", productId);
        WebElement addToCartBtn = wait.until(ExpectedConditions.elementToBeClickable(getAddToCartBtnByProductId(productId)));
        addToCartBtn.click();
    }

    public void pressCartBtn() {
        logger.info("Pressing cart button");
        wait.until(ExpectedConditions.elementToBeClickable(cartBtn)).click();
    }

    public WebElement getScrollableContentContainer() {
        return scrollableContentContainer;
    }

    public boolean isProductDisplayed(String productId) {
        logger.info("Checking if product is displayed: {}", productId);
        try {
            wait.until(ExpectedConditions.visibilityOfElementLocated(getProductCardByProductId(productId)));
            return true;
        } catch (Exception e) {
            logger.error("Product is not displayed: {}", productId, e);
            return false;
        }
    }
}
