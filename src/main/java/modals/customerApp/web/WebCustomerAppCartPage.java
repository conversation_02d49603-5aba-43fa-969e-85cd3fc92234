package modals.customerApp.web;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WebCustomerAppCartPage extends BaseWebPage {
    private final Logger logger = LoggerFactory.getLogger(WebCustomerAppCartPage.class);

    public WebCustomerAppCartPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    // Page elements
    @FindBy(xpath = "//div[contains(@class, 'cart-page') or contains(@class, 'shopping-cart')]")
    WebElement cartPageContainer;

    @FindBy(xpath = "//div[contains(@class, 'cart-items')]")
    WebElement cartItemsContainer;

    @FindBy(xpath = "//button[contains(@class, 'checkout-btn') or contains(text(), 'Go to Checkout') or contains(text(), 'Checkout')]")
    WebElement goToCheckoutBtn;

    @FindBy(xpath = "//div[contains(@class, 'cart-summary')]")
    WebElement cartSummary;

    @FindBy(xpath = "//span[contains(@class, 'total-amount')]")
    WebElement totalAmount;

    @FindBy(xpath = "//button[contains(@class, 'continue-shopping')]")
    WebElement continueShoppingBtn;

    // Page actions
    public boolean isCartScreenDisplayed() {
        logger.info("Checking if cart screen is displayed");
        try {
            wait.until(ExpectedConditions.visibilityOf(cartPageContainer));
            return true;
        } catch (Exception e) {
            logger.error("Cart screen is not displayed", e);
            return false;
        }
    }

    public void pressGoToCheckoutBtn() {
        logger.info("Pressing go to checkout button");
        wait.until(ExpectedConditions.elementToBeClickable(goToCheckoutBtn)).click();
    }

    public boolean isGoToCheckoutBtnEnabled() {
        logger.info("Checking if go to checkout button is enabled");
        try {
            WebElement btn = wait.until(ExpectedConditions.visibilityOf(goToCheckoutBtn));
            return btn.isEnabled();
        } catch (Exception e) {
            logger.error("Go to checkout button is not enabled", e);
            return false;
        }
    }

    public String getTotalAmount() {
        logger.info("Getting total amount from cart");
        try {
            return wait.until(ExpectedConditions.visibilityOf(totalAmount)).getText();
        } catch (Exception e) {
            logger.error("Could not get total amount", e);
            return "";
        }
    }

    public boolean isCartEmpty() {
        logger.info("Checking if cart is empty");
        try {
            // Check if cart items container is empty or has no items
            return cartItemsContainer.findElements(org.openqa.selenium.By.xpath(".//div[contains(@class, 'cart-item')]")).isEmpty();
        } catch (Exception e) {
            logger.error("Could not determine if cart is empty", e);
            return false;
        }
    }
}
