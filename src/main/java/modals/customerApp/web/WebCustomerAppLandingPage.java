package modals.customerApp.web;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WebCustomerAppLandingPage extends BaseWebPage {
    private final Logger logger = LoggerFactory.getLogger(WebCustomerAppLandingPage.class);

    public WebCustomerAppLandingPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    // Page elements
    @FindBy(xpath = "//div[contains(@class, 'landing-page')]")
    WebElement landingPageContainer;

    @FindBy(xpath = "//button[contains(@class, 'auth-btn') or contains(text(), 'Login') or contains(text(), 'Sign In')]")
    WebElement authBtn;

    @FindBy(xpath = "//button[contains(@class, 'guest-btn') or contains(text(), 'Continue as Guest')]")
    WebElement guestBtn;

    @FindBy(xpath = "//div[contains(@class, 'country-selector')]")
    WebElement countrySelector;

    // Page actions
    public boolean isPageDisplayed() {
        logger.info("Checking if landing page is displayed");
        try {
            wait.until(ExpectedConditions.visibilityOf(landingPageContainer));
            return true;
        } catch (Exception e) {
            logger.error("Landing page is not displayed", e);
            return false;
        }
    }

    public void pressAuthBtn() {
        logger.info("Pressing authentication button");
        wait.until(ExpectedConditions.elementToBeClickable(authBtn)).click();
    }

    public void pressGuestBtn() {
        logger.info("Pressing guest button");
        wait.until(ExpectedConditions.elementToBeClickable(guestBtn)).click();
    }

    public boolean isAuthBtnDisplayed() {
        logger.info("Checking if auth button is displayed");
        try {
            wait.until(ExpectedConditions.visibilityOf(authBtn));
            return true;
        } catch (Exception e) {
            logger.error("Auth button is not displayed", e);
            return false;
        }
    }
}
