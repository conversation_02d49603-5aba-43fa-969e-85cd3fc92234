package modals.customerApp.web;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WebCustomerAppPhoneNumberPage extends BaseWebPage {
    private final Logger logger = LoggerFactory.getLogger(WebCustomerAppPhoneNumberPage.class);

    public WebCustomerAppPhoneNumberPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    // Page elements
    @FindBy(xpath = "//div[contains(@class, 'phone-number-page')]")
    WebElement phoneNumberPageContainer;

    @FindBy(xpath = "//input[@type='tel' or contains(@class, 'phone-input') or @name='phone' or @id='phone']")
    WebElement phoneNumberInput;

    @FindBy(xpath = "//button[contains(@class, 'continue-btn') or contains(text(), 'Continue') or contains(text(), 'Next')]")
    WebElement continueBtn;

    @FindBy(xpath = "//div[contains(@class, 'country-code-selector')]")
    WebElement countryCodeSelector;

    @FindBy(xpath = "//button[contains(@class, 'country-dropdown')]")
    WebElement countryDropdownBtn;

    // Page actions
    public boolean isPageDisplayed() {
        logger.info("Checking if phone number page is displayed");
        try {
            wait.until(ExpectedConditions.visibilityOf(phoneNumberPageContainer));
            return true;
        } catch (Exception e) {
            logger.error("Phone number page is not displayed", e);
            return false;
        }
    }

    public void enterPhoneNumber(String phoneNumber) {
        logger.info("Entering phone number: {}", phoneNumber);
        WebElement phoneInput = wait.until(ExpectedConditions.visibilityOf(phoneNumberInput));
        phoneInput.clear();
        phoneInput.sendKeys(phoneNumber);
    }

    public void pressContinueBtn() {
        logger.info("Pressing continue button");
        wait.until(ExpectedConditions.elementToBeClickable(continueBtn)).click();
    }

    public void pressCountryDropdownBtn() {
        logger.info("Pressing country dropdown button");
        wait.until(ExpectedConditions.elementToBeClickable(countryDropdownBtn)).click();
    }

    public boolean isContinueBtnEnabled() {
        logger.info("Checking if continue button is enabled");
        try {
            WebElement btn = wait.until(ExpectedConditions.visibilityOf(continueBtn));
            return btn.isEnabled();
        } catch (Exception e) {
            logger.error("Continue button is not enabled", e);
            return false;
        }
    }
}
