package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeChatBotScreen extends BaseAndroidScreen {
    public AndroidNativeChatBotScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy (xpath = "//android.widget.ImageView[@content-desc=\"Send message\"]")
    WebElement sendMsgBtn;

    @FindBy (xpath = "//android.widget.TextView[@text=\"I have a question\"]")
    WebElement haveAQuestionTitle;

    public boolean isPageDisplayed()
    {
        return wait.until(ExpectedConditions.visibilityOf(sendMsgBtn)).isDisplayed();
    }
}
