package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeCountriesSelectionScreen extends BaseAndroidScreen {
    public AndroidNativeCountriesSelectionScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    String countrySelector = "//android.view.View[@content-desc='country-%s']";

    @FindBy(xpath = "//android.view.View[@content-desc='continue_btn']")
    WebElement continueBtn;

    public void selectCountryByName(String countryName){
        switch (countryName.toLowerCase()){
            case "egypt", "eg" ->
                    wait.until(ExpectedConditions.visibilityOfElementLocated(
                            By.xpath(String.format(countrySelector, "egypt")))).click();

            case "saudi arabia", "saudi", "saudiarabia", "ksa" ->
                    wait.until(ExpectedConditions.visibilityOfElementLocated(
                            By.xpath(String.format(countrySelector, "saudiArabia")))).click();
        }
        wait.until(ExpectedConditions.attributeToBe(continueBtn, "enabled", "true"));
    }

    public void pressContinueBtn(){
        wait.until(ExpectedConditions.visibilityOf(continueBtn)).click();
    }

    public void selectCountryAndProceed(String countryName){
        selectCountryByName(countryName);
        pressContinueBtn();
    }
}

