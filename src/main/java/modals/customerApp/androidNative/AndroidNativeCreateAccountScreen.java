package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import models.TestData;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeCreateAccountScreen extends BaseAndroidScreen {
    public AndroidNativeCreateAccountScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Let’s finish up your account']")
    WebElement pageTitle;

    @FindBy(xpath = "//android.view.View[@content-desc='registerAccount_firstName_txtField']/android.widget.EditText")
    WebElement firstNameField;

    @FindBy(xpath = "//android.view.View[@content-desc='registerAccount_lastName_txtField']/android.widget.EditText")
    WebElement lastNameField;

    @FindBy(xpath = "//android.view.View[@content-desc='registerAccount_email_txtField']/android.widget.EditText")
    WebElement emailField;

    @FindBy(xpath = "//android.view.View[@content-desc='registerAccount_referralCode_txtField']/android.widget.EditText")
    WebElement referralCodeField;

    @FindBy(xpath = "//android.view.View[@content-desc='registerAccount_submitBtn']")
    WebElement submitBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='This field is required.']")
    WebElement emptyTxtFieldErrorMsg;

    @FindBy(xpath = "//android.widget.TextView[@text='First name must include letters only.']")
    WebElement invalidFirstNameErrorMsg;

    @FindBy(xpath = "//android.widget.TextView[@text='Last name must include letters only.']")
    WebElement invalidLastNameErrorMsg;

    @FindBy(xpath = "//android.widget.TextView[@text='Enter a valid email address.']")
    WebElement invalidEmailErrorMsg;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void enterFirstName(String firstName){
        wait.until(ExpectedConditions.visibilityOf(firstNameField)).sendKeys(firstName);
    }

    public void enterLastName(String lastName){
        wait.until(ExpectedConditions.visibilityOf(lastNameField)).sendKeys(lastName);
    }

    public void enterEmail(String email){
        wait.until(ExpectedConditions.visibilityOf(emailField)).sendKeys(email);
    }

    public void enterReferralCode(String refCode){
        wait.until(ExpectedConditions.visibilityOf(referralCodeField)).sendKeys(refCode);
    }

    public void fillInAccountInformationForm(TestData testData){
        enterFirstName(testData.getRandomTestUser().getFirstName());
        enterLastName(testData.getRandomTestUser().getLastName());
        enterEmail(testData.getRandomTestUser().getEmailAddress());
        wait.until(ExpectedConditions.attributeToBe(submitBtn, "enabled", "true"));
    }

    public void pressSubmitBtn(){
        wait.until(ExpectedConditions.visibilityOf(submitBtn))
                .click();
    }

    public boolean isEmptyTxtFieldErrorMsgDisplayed(){
        return isElementDisplayed(emptyTxtFieldErrorMsg);
    }

    public boolean isInvalidFirstNameErrorMsgDisplayed(){
        return isElementDisplayed(invalidFirstNameErrorMsg);
    }

    public boolean isInvalidLastNameErrorMsgDisplayed(){
        return isElementDisplayed(invalidLastNameErrorMsg);
    }

    public boolean isInvalidEmailErrorMsgDisplayed(){
        return isElementDisplayed(invalidEmailErrorMsg);
    }

    public boolean isSubmitBtnEnabled(){
        try {
            wait.until(ExpectedConditions.attributeToBe(submitBtn, "enabled", "true"));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public void clearTxtField(String fieldName){
        switch (fieldName.toLowerCase()) {
            case "firstname" -> wait.until(ExpectedConditions.visibilityOf(firstNameField)).clear();
            case "lastname" -> wait.until(ExpectedConditions.visibilityOf(lastNameField)).clear();
            case "email" -> wait.until(ExpectedConditions.visibilityOf(emailField)).clear();
            case "referralcode" -> wait.until(ExpectedConditions.visibilityOf(referralCodeField)).clear();
            default -> { /* do nothing */ }
        }
    }

    public void pressTxtField(String fieldName){
        switch (fieldName.toLowerCase()) {
            case "firstname" -> wait.until(ExpectedConditions.visibilityOf(firstNameField)).click();
            case "lastname" -> wait.until(ExpectedConditions.visibilityOf(lastNameField)).click();
            case "email" -> wait.until(ExpectedConditions.visibilityOf(emailField)).click();
            case "referralcode" -> wait.until(ExpectedConditions.visibilityOf(referralCodeField)).click();
            default -> { /* do nothing */ }
        }
    }

    public void outFocusCursorFromTxtField(){
        wait.until(ExpectedConditions.visibilityOf(pageTitle)).click();
    }
}
