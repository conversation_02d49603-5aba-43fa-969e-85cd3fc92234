package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeLandingScreen extends BaseAndroidScreen {
    public AndroidNativeLandingScreen (AndroidDriver androidDriver){
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.view.View[@content-desc='onBoardingScreen_loginOrSignUp_btn']")
    WebElement authBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='onBoardingScreen_exploreBreadfast_btn']")
    WebElement exploreBtn;

    public void pressAuthBtn(){
        wait.until(ExpectedConditions.visibilityOf(authBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(authBtn));
    }

    public void pressExploreBtn(){
        wait.until(ExpectedConditions.visibilityOf(exploreBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(exploreBtn));
    }
}
