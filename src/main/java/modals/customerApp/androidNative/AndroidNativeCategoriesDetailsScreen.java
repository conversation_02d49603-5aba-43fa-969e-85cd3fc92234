package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeCategoriesDetailsScreen extends BaseAndroidScreen {

    public AndroidNativeCategoriesDetailsScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "(//android.widget.Button)[1]")
    WebElement backBtn;

    String subCategoryTabContentDescription = "subcategory_%s";

    String subCategoryTabXpathSelector = "//android.view.View[@content-desc='%s']";

    String productCardContentDescription = "productCard_%s_container";

    String productCardXpathSelector = "//android.view.View[@content-desc='%s']";

    @FindBy(xpath="(//android.view.View/android.view.View/android.view.View/android.view.View/android.widget.TextView)[1]")
    WebElement categoryTitle;

    String scrollableContentContainerSelector =
            "//androidx.compose.ui.platform.ComposeView/android.view.View/android.view.View/android.view.View[3]";

    String addToCartBtnXpathSelector = "//android.view.View[@content-desc='%s']" +
            "/ancestor::android.view.View[1]" +
            "//android.view.View[@content-desc='product_incrementQtyBtn']";

    @FindBy(xpath = "(//android.view.View[2])[1]")
    WebElement cartBtn;

    String addToFavoritesBtnXpathSelector = "/../android.view.View[@content-desc='product_favoriteBtn']";
    
    @FindBy(xpath = "//android.widget.TextView[@text='Product added to favorites.']")
    WebElement productAddedToFavoritesSuccessMsg;
    
    @FindBy(xpath = "//android.widget.TextView[@text='View list']")
    WebElement viewFavoritesListBtn;
    
    String subCategoriesScrollableContainerSelector =
            "//android.view.View[@content-desc='category_%s_subCategories_listContainer']";

    @FindBy(xpath = "//android.view.View[@content-desc='back_btn']")
    WebElement categoriesBottomSheetDismissBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Explore Breadfast']")
    WebElement categoriesBottomSheetTitle;

    String categoriesBottomSheetScrollableContentContainerSelector =
            "//android.view.ViewGroup/android.view.View/android.view.View" +
                    "/android.view.View/android.view.View[2]/android.view.View[2]";
    String decreaseBtnXpathSelector = "//android.view.View[@content-desc='%s']" + "/ancestor::android.view.View[1]" +
            "//android.view.View[@content-desc='product_decrementQtyBtn']";

    public boolean isBackBtnDisplayed() {
        return isElementDisplayed(backBtn);
    }

    public void pressBackBtn() {
        wait.until(ExpectedConditions.visibilityOf(backBtn))
                .click();
    }

    public String getProductCardContentDescription(String productId) {
        return (String.format(productCardContentDescription, productId));
    }

    public String getProductCardXpathSelector(String productId) {
        return (String.format(productCardXpathSelector, getProductCardContentDescription(productId)));
    }

    public WebElement getProductCardUiElement(String productId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(productCardXpathSelector
                        , getProductCardContentDescription(productId)))));
    }

    public boolean isProductCardDisplayed(String productId) {
        return isElementDisplayed(getProductCardUiElement(productId));
    }

    public WebElement getAddToCartUiElementByProductId(String productId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(addToCartBtnXpathSelector, getProductCardContentDescription(productId)))
        ));
    }

    public void pressProductCardByProductId(String productId) {
        getProductCardUiElement(productId)
                .click();
    }

    public void pressAddToCartBtnByProductId(String productId) {
        getAddToCartUiElementByProductId(productId)
                .click();
    }

    public String getSubCategoryTabContentDescription(String subCategoryId) {
        return (String.format(subCategoryTabContentDescription, subCategoryId));
    }

    public WebElement getSubCategoryTabUiElement(String tabId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(subCategoryTabXpathSelector, getSubCategoryTabContentDescription(tabId)))));
    }

    public void pressSubCategoryTab(String subCategoryId){
        wait.until(ExpectedConditions.visibilityOf(getSubCategoryTabUiElement(subCategoryId))).click();
    }

    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public WebElement getSubCategoriesScrollableContentContainer(String categoryId){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(subCategoriesScrollableContainerSelector, categoryId))));
        } catch (Exception e){
            return null;
        }
    }

    public void pressCartBtn(){
        wait.until(ExpectedConditions.visibilityOf(cartBtn))
                .click();
    }

    public void pressAddToFavoritesBtnByProductId(String productId) {
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath((getProductCardXpathSelector(productId) + addToFavoritesBtnXpathSelector))
        )).click();
    }

    public boolean isProductAddedToFavoritesSuccessMsgDisplayed(){
        return isElementDisplayed(productAddedToFavoritesSuccessMsg);
    }

    public void pressViewFavoritesListBtn(){
        wait.until(ExpectedConditions.visibilityOf(viewFavoritesListBtn))
                .click();
    }

    public boolean isPageDisplayed(String categoryName){
        return isElementDisplayed(categoryTitle)
                && wait.until(ExpectedConditions.visibilityOf(categoryTitle)).getText().equalsIgnoreCase(categoryName);
    }

    public void openCategoriesBottomSheet(){
        wait.until(ExpectedConditions.visibilityOf(categoryTitle))
                .click();
    }

    public boolean isCategoriesBottomSheetDisplayed(){
        return isElementDisplayed(categoriesBottomSheetDismissBtn) && isElementDisplayed(categoriesBottomSheetTitle);
    }

    public WebElement getCategoriesBottomSheetScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(categoriesBottomSheetScrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }
    public void pressDecreaseBtnByProductId(String productId) {
        getDecreaseBtnUiElementByProductId(productId)
                .click();
    }
    public WebElement getDecreaseBtnUiElementByProductId(String productId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(decreaseBtnXpathSelector, getProductCardContentDescription(productId)))
        ));
    }
}
