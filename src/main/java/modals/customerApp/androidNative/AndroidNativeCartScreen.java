package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class AndroidNativeCartScreen extends BaseAndroidScreen {
    public AndroidNativeCartScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@content-desc=\"checkout_btn\"]")
    WebElement goToCheckoutBtn;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Cart\"]")
    WebElement cartTitle;

    @FindBy(xpath = "//android.view.View[@content-desc=\"back_btn\"]")
    WebElement backBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"clearAll_btn\"]")
    WebElement clearAllBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"emptyView_title\"]")
    WebElement cartEmptyView;

    @FindBy(xpath = "//android.view.View[starts-with(@content-desc, 'cartItem_')]")
    List<WebElement> cartItems;

    @FindBy(xpath = "//android.widget.TextView[starts-with(@content-desc, 'product_') " +
            "and substring(@content-desc, string-length(@content-desc) - 3) = '_qty']")
    List<WebElement> productQtyElements;

    public boolean isCheckoutBtnDisplayed() {
        return wait.until(ExpectedConditions.visibilityOf(goToCheckoutBtn)).isDisplayed();
    }

    public boolean isCartEmpty ()
    {
        return isElementDisplayed(cartEmptyView);
    }

    public boolean isCartScreenDisplayed () {
        return wait.until(ExpectedConditions.visibilityOf(cartTitle)).isDisplayed();
    }

    public void pressGoToCheckoutBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(goToCheckoutBtn))
                .click();
    }

    public boolean isClearAllBtnDisplayed ()
    {
        return isElementDisplayed(clearAllBtn);
    }

    public int getCountOfCartItems(){
        return cartItems.size();
    }

    public int getTotalCountOfItemsInCart(){
        int count = 0;
        for (WebElement element : productQtyElements){
            try {
                count += Integer.parseInt(wait.until(ExpectedConditions.visibilityOf(element)).getText());
            } catch (Exception e){
                continue;
            }
        }
        return count;
    }
}
