package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeOtpVerificationScreen extends BaseAndroidScreen {
    public AndroidNativeOtpVerificationScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@content-desc='otpScreen_subHeader_txt']")
    WebElement pageSubTitle;

    @FindBy(xpath = "//android.widget.EditText")
    WebElement otpTextField;

    @FindBy(xpath = "//android.view.View[@content-desc='verify_btn']")
    WebElement verifyBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='otpScreen_didntGetCode_btn']")
    WebElement didNotGetCodeBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='resendOtp_btn']")
    WebElement resendCodeBySmsBtn;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Wrong verification code')]")
    WebElement wrongOtpErrorMsg;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'ran out of verification attempts')]")
    WebElement outOfVerificationAttemptsErrorMsg;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageSubTitle);
    }

    public void enterOtp(String otp){
        wait.until(ExpectedConditions.attributeToBe(otpTextField, "enabled", "true"));
        if (!otpTextField.getText().equalsIgnoreCase(otp)) {
            otpTextField.clear();
            otpTextField.sendKeys(otp);
        }
        try {
            wait.until(ExpectedConditions.attributeToBe(verifyBtn, "enabled", "true"));
        } catch (Exception e){
            // do nothing
        }

    }

    public void isErrorMsgDismissed(){
        wait.until(ExpectedConditions.invisibilityOf(wrongOtpErrorMsg));
    }

    public void submitOtpBtn(){
        wait.until(ExpectedConditions.visibilityOf(verifyBtn))
                .click();
    }

    public String getDidNotGetCodeBtnText(){
        return wait.until(ExpectedConditions.visibilityOf(didNotGetCodeBtn))
                .getText();
    }

    public void pressDidNotGetCodeBtn(){
        wait.until(ExpectedConditions.visibilityOf(didNotGetCodeBtn))
                .click();
    }

    public void pressResendCodeBySmsBtn(){
        wait.until(ExpectedConditions.visibilityOf(resendCodeBySmsBtn))
                .click();
    }

    public boolean isWrongOtpErrorMsgDisplayed(){
        return isElementDisplayed(wrongOtpErrorMsg);
    }

    public String getWrongOtpErrorMsgText(){
        return wait.until(ExpectedConditions.visibilityOf(wrongOtpErrorMsg)).getText();
    }

    public boolean isOutOfVerificationAttemptsErrorMsgDisplayed(){
        return isElementDisplayed(outOfVerificationAttemptsErrorMsg);
    }

    public boolean isPageDismissed(){
        return isElementHidden(pageSubTitle);
    }
}
