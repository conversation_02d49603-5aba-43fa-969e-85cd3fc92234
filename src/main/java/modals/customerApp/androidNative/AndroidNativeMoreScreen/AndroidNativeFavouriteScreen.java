package modals.customerApp.androidNative.AndroidNativeMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeFavouriteScreen extends BaseAndroidScreen {

    public AndroidNativeFavouriteScreen(AndroidDriver androidDriver){
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@content-desc='emptyView_container']")
    WebElement pageContainerEmptyView;

    @FindBy(xpath = "//android.view.View[@content-desc='explore_btn']")
    WebElement exploreBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='cart_btn']")
    WebElement cartBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='favoriteProducts_list_container']")
    WebElement favoriteProductsListContainer;

    String productCardContentDescription = "productCard_%s_innerContainer";

    String productCardXpathSelector = "//android.view.View[@content-desc='%s']";

    String addToCartBtnXpathSelector = "//android.view.View[@content-desc='%s']" +
            "/ancestor::android.view.View[1]" +
            "//android.view.View[@content-desc='product_incrementQtyBtn']";

    public boolean isFavListEmpty() {
        return isElementDisplayed(exploreBtn);
    }

    public boolean isFavoriteProductsListDisplayed() {
        return isElementDisplayed(favoriteProductsListContainer);
    }

    public void pressExploreBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(exploreBtn))
                .click();
    }

    public  void pressCartBtn() {
        wait.until(ExpectedConditions.visibilityOf(cartBtn))
                .click();
    }

    public String getProductCardContentDescription (String productId) {
        return String.format(productCardContentDescription, productId);
    }

    public String getProductCardXpathSelector(String productId) {
        return String.format(productCardXpathSelector, getProductCardContentDescription(productId));
    }

    public WebElement getProductCardUiElement(String productId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(getProductCardXpathSelector(productId))));
    }

    public boolean isProductCardDisplayed(String productId){
        try {
            return getProductCardUiElement(productId).isDisplayed();
        } catch (Exception e){
            return false;
        }
    }

    public WebElement getAddToCartUiElementByProductId(String productId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(addToCartBtnXpathSelector, getProductCardContentDescription(productId)))
        ));
    }

    public void pressAddToCartBtnByProductId(String productId) {
        getAddToCartUiElementByProductId(productId)
                .click();
    }
}
