package modals.customerApp.androidNative.AndroidNativeMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeMoreScreen extends BaseAndroidScreen {
    public AndroidNativeMoreScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    String fullNameXpath = "//android.widget.TextView[@text='%s']";

    @FindBy(xpath = "//android.widget.TextView[@content-desc='loggedIn_userName']")
    WebElement fullName;

    @FindBy(xpath = "//android.view.View[@content-desc='moreScreen_container']")
    WebElement moreScreenContainer;

    @FindBy(xpath = "//android.widget.TextView[@text='العربية']")
    WebElement arabicLang;

    @FindBy(xpath = "//android.widget.TextView[@text='Egypt']")
    WebElement countryEgyptBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Saudi Arabia']")
    WebElement countryKSABtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I have a question']")
    WebElement chatBotTitle;

    @FindBy(xpath = "//android.view.View[@content-desc='bottomBar_more_btn']")
    WebElement moreTabButton;

    @FindBy(xpath = "//android.view.View[@content-desc='continue_btn']")
    WebElement continueBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='breadfast_topAppBar']")
    WebElement bottomActionSheetHeader;

    @FindBy(xpath = "//android.view.View[@content-desc='morescreen_activityHistory']")
    WebElement activityHistoryBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='morescreen_favorites']")
    WebElement favoritesBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='morescreen_loyaltyProgram']")
    WebElement breadfastRewardsBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='morescreen_freeCredit']")
    WebElement freeCreditBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='morescreen_accountSettings']")
    WebElement accountSettingsBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='morescreen_help']")
    WebElement helpBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Help']")
    WebElement helpTitle;

    @FindBy(xpath = "//android.view.View[@content-desc='morescreen_lanugage']")
    WebElement languageBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='morescreen_subtitle_selectedLanguage']")
    WebElement currentLanguage;

    @FindBy(xpath = "//android.view.View[@content-desc='morescreen_country']")
    WebElement countryBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='morescreen_subtitle_countryName']")
    WebElement currentlySelectedCountry;

    @FindBy(xpath = "//android.view.View[@content-desc='morescreen_talkToUs']")
    WebElement talkToUsBtn;

    String logoutBtnContentDescription = "morescreen_logout";

    String logoutBtnSelector = "//android.view.View[@content-desc='%s']";

    @FindBy(xpath = "//android.widget.TextView[@content-desc='morescreen_getStarted_btn']")
    WebElement getStartedWithBreadfastTxt;

    @FindBy(xpath = "//android.widget.TextView[@text='Change']")
    WebElement changeBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='morescreen_lanugage']")
    WebElement moreScreenUpdateLangBtn;

    String scrollableContentContainerSelector =
            "//android.view.View[@content-desc='moreScreen_innerContainer']";

    public boolean isPageDisplayed() {
        return isElementDisplayed(moreScreenContainer);
    }

    public void pressMoreTabBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(moreTabButton)).click();
    }

    public String getDisplayedUserFullName(){
        return wait.until(ExpectedConditions.visibilityOf(fullName)).getText();
    }

    public boolean isFullNameDisplayed(String firstName, String lastName) {
        WebElement fullName = wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(fullNameXpath, (firstName + " " + lastName)))));
        return isElementDisplayed(fullName);
    }

    public void pressActivityHistoryBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(activityHistoryBtn))
                .click();
    }

    public void pressFavoritesBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(favoritesBtn))
                .click();
    }

    public void pressHelpBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(helpBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(helpBtn));
    }

    public void pressRewardsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(breadfastRewardsBtn))
                .click();
    }

    public void pressLanguageBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(languageBtn)).click();
    }

    public void pressCountryBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(countryBtn)).click();
    }

    public void pressTalkToUsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(talkToUsBtn))
                .click();
    }

    public String getLogoutBtnContentDescription(){
        return logoutBtnContentDescription;
    }

    public String getLogoutBtnSelector(){
        return String.format(logoutBtnSelector, getLogoutBtnContentDescription());
    }

    public void pressLogoutBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath(getLogoutBtnSelector())))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(fullName));
    }

    public WebElement getScrollableContentContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e) {
            return null;
        }
    }

    public void pressAccountSettingsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(accountSettingsBtn))
                .click();
    }

    public boolean isHelpPageDisplayed() {
        return isElementDisplayed(helpTitle);
    }

    public void pressArabicLanguageBtn() {
        wait.until(ExpectedConditions.visibilityOf(arabicLang)).click();
    }

    public void pressChangeBtn() {
        wait.until(ExpectedConditions.visibilityOf(changeBtn)).click();
        try {
            wait.until(ExpectedConditions.invisibilityOf(changeBtn));
        } catch (Exception e){
            // do nothing
        }
    }

    public String getCurrentlySelectedLanguage(){
        return wait.until(ExpectedConditions.visibilityOf(currentLanguage))
                .getText();
    }

    public void pressCountryOptionByCountryName(String countryName) {
        switch (countryName.toLowerCase()) {
            case "egypt", "eg", "مصر" -> wait.until(ExpectedConditions.visibilityOf(countryEgyptBtn)).click();
            case "ksa", "saudi arabia", "السعودية" ->
                    wait.until(ExpectedConditions.visibilityOf(countryKSABtn)).click();
            default -> {
            }
        }
    }

    public void pressContinueBtn(){
        wait.until(ExpectedConditions.visibilityOf(continueBtn)).click();
    }

    public boolean isBottomActionSheetDisplayed(){
        return isElementDisplayed(bottomActionSheetHeader);
    }

    public String getCurrentlySelectedCountryName(){
        return wait.until(ExpectedConditions.visibilityOf(currentlySelectedCountry))
                .getText();
    }

    public boolean isUserLoggedOut(){
        return isElementDisplayed(getStartedWithBreadfastTxt)
                && isElementDisplayed(continueBtn);
    }
}
