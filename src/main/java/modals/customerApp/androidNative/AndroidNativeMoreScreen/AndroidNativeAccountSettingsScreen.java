package modals.customerApp.androidNative.AndroidNativeMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeAccountSettingsScreen extends BaseAndroidScreen
{
    public AndroidNativeAccountSettingsScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"accountSettings_personalInformation\"]")
    WebElement personalInfoTab;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"accountSettings_savedAddresses\"]")
    WebElement savedAddress;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"accountSettings_emailAddress\"]")
    WebElement emailAddresses;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"accountSettings_savedCards\"]")
    WebElement savedCardsTab;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Change Language\"]")
    WebElement changeLanguageTab;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"accountSettings_notifications\"]")
    WebElement notificationsAndSmsTab;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"accountSettings_deleteAccount\"]")
    WebElement deleteAccountTab;

    @FindBy (xpath = "//android.widget.Button")
    WebElement backBtn;

    public void pressBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backBtn)).click();
    }

    public Boolean isPageDisplayed() {
        return isElementDisplayed(personalInfoTab);
    }

    public void pressManageAddressesTab(){
        wait.until(ExpectedConditions.visibilityOf(savedAddress))
                .click();
    }

    public void pressPersonalInfoTab()
    {
        wait.until(ExpectedConditions.visibilityOf(personalInfoTab)).click();
    }

    public void pressEMailAddressBtn() {
        wait.until(ExpectedConditions.visibilityOf(emailAddresses)).click();
    }

    public void pressDeleteAccountTab()
    {
        wait.until(ExpectedConditions.visibilityOf(deleteAccountTab)).click();
    }

}
