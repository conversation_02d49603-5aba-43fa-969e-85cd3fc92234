package modals.customerApp.androidNative.AndroidNativeMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativePersonalInfoScreen extends BaseAndroidScreen {
    public String myUsername;

    public AndroidNativePersonalInfoScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    
    @FindBy(xpath = "//android.view.View[@content-desc='personalInnformation_header']")
    WebElement pageHeader;

    @FindBy(xpath = "//android.view.View[@content-desc='firstName_txtField']/android.widget.EditText")
    WebElement firstNameTxtField;

    @FindBy(xpath = "//android.view.View[@content-desc='lastName_txtField']/android.widget.EditText")
    WebElement lastNameTxtField;

    @FindBy(xpath = "//android.view.View[@content-desc='phoneNumber_countryCode']")
    WebElement phoneNumberCountryCode;

    @FindBy(xpath = "//android.view.View[@content-desc='phoneNumber_txtField']/android.widget.EditText")
    WebElement phoneNumberTxtField;

    @FindBy(xpath = "//android.view.View[@content-desc='saveChanges_btn']")
    WebElement saveChangeBtn;

    @FindBy (xpath = "//android.widget.TextView[@text='The changes were saved successfully.']")
    WebElement infoUpdatedSuccessMsg;

    String editFieldUISelector= "//android.widget.EditText[@text='%s']";
    String userName = "%s";

    public boolean isPageDisplayed() {
        return isElementDisplayed(pageHeader);
    }

    public void pressOnSubmitBtn() {
        wait.until(ExpectedConditions.visibilityOf(saveChangeBtn))
                .click();
    }

    public boolean isPersonalInfoSaved()
    {
        return isElementDisplayed(infoUpdatedSuccessMsg);
    }

    public void updateFirstName(String firstName) {
        wait.until(ExpectedConditions.visibilityOf(firstNameTxtField)).clear();
        enterValueInTextField(firstNameTxtField, firstName);
    }

    public void updateLastName(String lastName){
        wait.until(ExpectedConditions.visibilityOf(lastNameTxtField)).clear();
        enterValueInTextField(lastNameTxtField, lastName);
    }

    public void enterNameAndPressSaveBtn(String firstName, String lastName){
        updateFirstName(firstName);
        updateLastName(lastName);
        pressOnSubmitBtn();
    }

    public String getTheUserName(String UserFName){
        return String.format(this.userName, UserFName);
    }
}
