package modals.customerApp.androidNative.AndroidNativeMoreScreen.AndroidNativeAddressManagementScreens;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeSavedAddressScreen extends BaseAndroidScreen {
    public AndroidNativeSavedAddressScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy (xpath = "//android.view.View[@content-desc='breadfast_topAppBar']" +
            "//android.widget.TextView[@text='Saved addresses']")
    WebElement savedAddressTitle;

    @FindBy (xpath = "//android.view.View[@content-desc='breadfast_topAppBar']/android.widget.Button")
    WebElement backBtn;

    @FindBy (xpath = "//android.view.View[@content-desc='addNewAddress_btn']")
    WebElement addAddressBtn;

    String addressCardContentDescription = "address_%s";
    
    String addressCardSelector = "//android.view.View[@content-desc='%s']";
    
    String defaultAddressLabelSelector = "//android.view.View[@content-desc='default_address_tag']";
    
    String scrollableContentContainer = "//androidx.compose.ui.platform.ComposeView" +
            "/android.view.View/android.view.View/android.view.View[2]";

    public boolean isAddressesPageDisplayed() {
        return isElementDisplayed(savedAddressTitle);
    }

    public String getAddressCardContentDescription(int addressId){
        return String.format(addressCardContentDescription, addressId);
    }

    public String getAddressCardXpathSelector(int addressId){
        return String.format(addressCardSelector, getAddressCardContentDescription(addressId));
    }

    public boolean isAddressCardDisplayed(int addressId){
        return isElementDisplayed(wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getAddressCardXpathSelector(addressId)))));
    }

    public void pressAddressCard(int addressId){
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getAddressCardXpathSelector(addressId))))
                .click();
    }

    public boolean isAddressMarkedAsDefault(int addressId){
        return isElementDisplayed(wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getAddressCardXpathSelector(addressId) + defaultAddressLabelSelector))));
    }

    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(scrollableContentContainer)));
        } catch (Exception e){
            return null;
        }
    }

    public void pressAddAddressBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(addAddressBtn))
                .click();
    }
}
