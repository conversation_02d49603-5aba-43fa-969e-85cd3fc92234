package modals.customerApp.androidNative.AndroidNativeMoreScreen.AndroidNativeAddressManagementScreens;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeAddressDetailsScreen extends BaseAndroidScreen {
    public AndroidNativeAddressDetailsScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    String deleteAddressBtnSelector = "//android.view.View[@content-desc='delete_address_%s_btn']";

    @FindBy(xpath = "//android.widget.TextView[@text='You must set a new default address before deleting this one.']")
    WebElement deleteDefaultAddressErrMsg;

    public void pressOnDeleteBtn(int addressId) {
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(deleteAddressBtnSelector, addressId)))).click();
    }

    public boolean isDeleteDefaultAddressErrMsgDisplayed()
    {
        return isElementDisplayed(deleteDefaultAddressErrMsg);
    }
}

