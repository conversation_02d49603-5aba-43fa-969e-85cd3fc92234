package modals.customerApp.androidNative.AndroidNativeMoreScreen.AndroidNativeDeleteAccountScreens;

import helpers.apiClients.mobileApiClients.OrderApiClient;
import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AndroidNativeDeleteAccountOTPVerificationScreen extends BaseAndroidScreen {
    public AndroidNativeDeleteAccountOTPVerificationScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);

    }
    private static final Logger logger = LoggerFactory.getLogger(AndroidNativeDeleteAccountOTPVerificationScreen.class);

    @FindBy(xpath = "//android.view.View[@content-desc=\"deleteMyAccount_btn\"]")
    WebElement deleteAccountBtn;

    @FindBy(xpath = "//android.widget.CheckBox")
    WebElement deleteAccountCheckbox;

    @FindBy(xpath = "//android.widget.EditText")
    WebElement otpVerificationField;

    public boolean isVerifyDeletingAccountScreenDisplayed ()
    {
       return wait.until(ExpectedConditions.visibilityOf(deleteAccountCheckbox)).isDisplayed();
    }
    public void pressOnDeleteAccount ()
    {
        wait.until(ExpectedConditions.visibilityOf(deleteAccountBtn)).click();
    }

    public void pressTermsAndConditionsCheckbox ()
    {
        wait.until(ExpectedConditions.visibilityOf(deleteAccountCheckbox)).click();
    }
    public WebElement getOtpTextField() {
        try {
            return wait.until(ExpectedConditions.visibilityOf(otpVerificationField));
        } catch (TimeoutException e) {
            // Log the exception and provide a context
            logger.info("Timeout while waiting for the OTP verification field to be visible");
            throw new RuntimeException("OTP field not visible within the expected time.", e);
        } catch (NoSuchElementException e) {
            // Log if the element is not found
            logger.info("OTP verification field not found");
            throw new RuntimeException("Failed to locate the OTP field.", e);
        } catch (Exception e) {
            // Catch any other exceptions and log them
            logger.info("Unexpected error occurred while getting the OTP verification field");
            throw new RuntimeException("An unexpected error occurred.", e);
        }
    }

    public void enterOtp(String otp){
        enterValueInTextField(otpVerificationField, otp);
    }

}
