package modals.customerApp.androidNative.AndroidNativeMoreScreen.AndroidNativeDeleteAccountScreens;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeDeleteAccountScreen extends BaseAndroidScreen {
    public AndroidNativeDeleteAccountScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text=\"Delete Account\"]")
    WebElement deleteTitle;

    @FindBy(xpath = "//android.view.View[@content-desc=\"continue_btn\"]")
    WebElement continueBtn;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Tell us why you want to delete your account:\"]")
    WebElement deleteHintMsg;

    @FindBy(xpath = "//android.view.View[@content-desc=\"deleteAccount_reason_16\"]")
    WebElement deleteFirstReason;

    @FindBy(xpath = "//android.view.View[@content-desc=\"verifyMobileNumber_btn\"]")
    WebElement verifyMobileNumberBtn;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Your delete request is sent.\"]")
    WebElement accountDeletedMsg;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Close\"]")
    WebElement closeBtn;

    public boolean isDeleteAccountPageDisplayed ()
    {
       return wait.until(ExpectedConditions.visibilityOf(deleteTitle)).isDisplayed();
    }

    public void pressDeleteFirstReason ()
    {
        wait.until(ExpectedConditions.visibilityOf(deleteFirstReason)).click();
    }

    public void pressContinueBtnToDelete ()
    {
        wait.until(ExpectedConditions.visibilityOf(continueBtn)).click();
    }

    public boolean isDeleteAccountFinalScreenDisplayed ()
    {
        return wait.until(ExpectedConditions.visibilityOf(verifyMobileNumberBtn)).isDisplayed();
    }

    public void pressVerifyMobileNumber ()
    {
        wait.until(ExpectedConditions.visibilityOf(verifyMobileNumberBtn)).click();
    }

    public boolean isAccountDeletedConfirmationMsgDisplayed ()
    {
       return wait.until(ExpectedConditions.visibilityOf(accountDeletedMsg)).isDisplayed();
    }

    public void pressCloseBtn ()
    {
        wait.until(ExpectedConditions.visibilityOf(closeBtn)).click();
    }

}
