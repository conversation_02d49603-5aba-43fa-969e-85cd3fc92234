package modals.customerApp.androidNative.AndroidNativeMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeEmailAddressScreen extends BaseAndroidScreen {
    public AndroidNativeEmailAddressScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@content-desc='back_btn']")
    WebElement backBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Providing your email will let you pay for your order via card.']")
    WebElement emailHint;

    @FindBy(xpath = "//android.view.View[@content-desc='saveEmail_btn']")
    WebElement updateEmailBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='emailAddress_txtField']//android.widget.EditText")
    WebElement emailTxtFieldEdit;

    @FindBy(xpath = "//android.widget.TextView[@text='Your email was updated successfully']")
    WebElement emailUpdatedConfirmationMsg;

    public boolean isPageDisplayed() {
        return isElementDisplayed(emailTxtFieldEdit);
    }

    public void pressOnBackBtn () {
        wait.until(ExpectedConditions.visibilityOf(backBtn))
                .click();
    }

    public void enterEmailIntoEmailTxtField(String UserUpdatedMail) {
        wait.until(ExpectedConditions.visibilityOf(emailTxtFieldEdit)).clear();
        enterValueInTextField(emailTxtFieldEdit, UserUpdatedMail);
    }

    public void pressOnSubmitBtn() {
        wait.until(ExpectedConditions.visibilityOf(updateEmailBtn)).click();
    }

    public boolean isEmailUpdatedMsgDisplayed() {
        return isElementDisplayed(emailUpdatedConfirmationMsg);
    }
}
