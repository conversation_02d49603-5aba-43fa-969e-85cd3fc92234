package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativePhoneCountrySelectionDropdownScreen extends BaseAndroidScreen {
    public AndroidNativePhoneCountrySelectionDropdownScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "(//android.widget.TextView[@text='Search Your Country'])[1]")
    WebElement pageTitle;

    @FindBy(xpath = "//android.widget.EditText")
    WebElement searchCountryTextField;

    String countryPhoneCodeSelector = "//android.widget.TextView[@text='(%s)']";

    public boolean isDropdownDisplayed(){
        return isElementDisplayed(pageTitle) && isElementDisplayed(searchCountryTextField);
    }

    public void searchForCountryByName(String countryName){
        wait.until(ExpectedConditions.visibilityOf(searchCountryTextField))
                .sendKeys(countryName);
    }

    public void selectCountryByPhoneCode(String phoneCode){
        wait.until(ExpectedConditions
                        .visibilityOfElementLocated(By.xpath(String.format(countryPhoneCodeSelector, phoneCode))))
                .click();
    }

    public void searchAndSelectTheCountry(String countryName, String countryCode){
        searchForCountryByName(countryName);
        selectCountryByPhoneCode(countryCode);
    }
}
