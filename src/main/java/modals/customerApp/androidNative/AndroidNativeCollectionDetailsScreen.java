package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeCollectionDetailsScreen extends BaseAndroidScreen {
    public AndroidNativeCollectionDetailsScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@content-desc='product_incrementQtyBtn']")
    WebElement firstAddToCartBtn;

    @FindBy(xpath = "//android.view.ViewGroup/android.view.View/android.view.View/android.view.View" +
            "/android.view.View[2]/android.widget.Button")
    WebElement dismissCollectionBtn;

    @FindBy(xpath = "//android.view.ViewGroup/android.view.View/android.view.View/android.view.View" +
            "/android.view.View[2]/android.view.View[2]")
    WebElement cartBtn;

    public void pressDismissCollectionBtn(){
        wait.until(ExpectedConditions.visibilityOf(dismissCollectionBtn))
                .click();
    }

    public void pressFirstAddToCartBtn(){
        wait.until(ExpectedConditions.visibilityOf(firstAddToCartBtn))
                .click();
    }

    public void pressCartBtn(){
        wait.until(ExpectedConditions.visibilityOf(cartBtn))
                .click();
    }
}
