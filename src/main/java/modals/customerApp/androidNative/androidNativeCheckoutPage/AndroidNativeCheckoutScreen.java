package modals.customerApp.androidNative.androidNativeCheckoutPage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeCheckoutScreen extends BaseAndroidScreen {
    public AndroidNativeCheckoutScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.widget.TextView[@text='Checkout']")
    WebElement checkoutPage;

    @FindBy(xpath = "//android.view.View[@content-desc='placeOrder_btn']")
    WebElement placeOrderBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='schedule_btn']")
    WebElement scheduleBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='paymentSection_cashOnDelivery_rowContainer']")
    WebElement cashOnDeliveryPaymentOption;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='AddNewCard_rowContainer']")
    WebElement addNewCardPaymentOption;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='deliveryTime_confirmationArea_txt']")
    WebElement closestSlotDueToHighDemandMsg;

    @FindBy(xpath = "//android.view.View[@content-desc='deliveryTime_bookThisSlot_btn']")
    WebElement bookThisSlotBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='deliveryTime_pickAnotherTime_btn']")
    WebElement pickAnotherTimeBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='toggle_deliveryTime_title']")
    WebElement deliverAtTheEarliestToggle;

    @FindBy(xpath = "//android.view.View[@content-desc='toggle_deliveryTime_btn']")
    WebElement deliverAtTheEarliestToggleBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Deliver at the earliest' and not(@content-desc='toggle_deliveryTime_title')]")
    WebElement deliverAtTheEarliestPopupSheet;

    @FindBy(xpath = "//android.widget.TextView[@text='Got it']")
    WebElement gotItBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Select delivery time' or @text='Or schedule another time']")
    WebElement scheduleAnotherTimeSheetTitles;

    @FindBy(xpath = "(//android.widget.RadioButton[@enabled='true'])[1]")
    WebElement firstAvailableRadioBtn;

    @FindBy(xpath ="(//android.widget.TextView[@text='Or schedule another time']/following::android.widget.RadioButton[@enabled='true'])[1]")
    WebElement firstAvailableInScheduleAnotherTimeSheet;

    @FindBy(xpath = "//android.widget.TextView[@text='High demand - No delivery slots available']")
    WebElement busyMessage;

    @FindBy(xpath = "//android.widget.TextView[@text='Sorry, we couldn’t place your order.']")
    WebElement busyMessagePopup;

    @FindBy(xpath = "//android.widget.TextView[@text='Updated delivery time ']")
    WebElement deliveryTimeUpdatedPopup;

    @FindBy(xpath = "//android.widget.TextView[@text='Place order']")
    WebElement placeOrderDeliveryTimeUpdatedPopup;

    String scrollableContentContainerSelector = "//android.widget.ScrollView[@content-desc='checkout_container']";

    String promoCodeTxtFieldContentDescription = "promoCode_txtField";

    String promoCodeTxtFieldSelector = "//android.view.View[@content-desc='%s']/android.widget.EditText";

    @FindBy(xpath = "//android.view.View[@content-desc='promoCode_txtField']" +
            "//android.view.View[@content-desc='txtField_innerBtn']")
    WebElement promoCodeApplyBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='The coupon was applied successfully.']")
    WebElement promocodeAppliedSuccessMsg;

    @FindBy(xpath = "//android.view.View[contains(@text, 'doesn't exist as a coupon']")
    WebElement promocodeFailureMsg;

    @FindBy(xpath = "//android.view.View[@content-desc='deliveryAddress_addDeliveryNote_txtField']//android.widget.EditText")
    WebElement deliveryNoteTxtField;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='cardSelectionModal_container']")
    WebElement cardSelectionModal;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='AddNewCard_changeCard_btn']")
    WebElement changeCardBtn;
    @FindBy(xpath = "//android.widget.TextView[@text='Free shipping']")
    WebElement freeShippingBtn;
    @FindBy(xpath = "//android.widget.TextView[@text='Redeem']")
    WebElement redeemBtn;
    String tipAmountContentDescription = "tipAmount_10.0";

    String tipAmountSelector = "//android.view.View[@content-desc='%s']";

    public boolean checkoutPageIsDisplayed(){
        return isElementDisplayed(checkoutPage);
    }

    public void pressPlaceOrderBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(placeOrderBtn))
                .click();
    }

    public boolean scheduleBtnIsDisplayed(){
        return isElementDisplayed(scheduleBtn);
    }

    public void pressScheduleBtn(){
        wait.until(ExpectedConditions.visibilityOf(scheduleBtn))
                .click();
    }

    public boolean closestSlotDueToHighDemandMsgIsDisplayed(){
        return isElementDisplayed(closestSlotDueToHighDemandMsg);
    }

    public boolean bookThisSlotBtnIsDisplayed(){
        return isElementDisplayed(bookThisSlotBtn);
    }

    public void pressBookThisSlotBtn(){
        wait.until(ExpectedConditions.visibilityOf(bookThisSlotBtn))
                .click();
    }

    public boolean pickAnotherTimeBtnIsDisplayed(){
        return isElementDisplayed(pickAnotherTimeBtn);
    }

    public void pressPickAnotherTimeBtn(){
        wait.until(ExpectedConditions.visibilityOf(pickAnotherTimeBtn))
                .click();
    }

    public boolean deliverAtTheEarliestToggleIsDisplayed(){
        return isElementDisplayed(deliverAtTheEarliestToggle);
    }

    public void pressDeliverAtTheEarliestToggleBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(deliverAtTheEarliestToggleBtn))
                .click();
    }

    public boolean deliverAtTheEarliestPopupSheetIsDisplayed(){
        return isElementDisplayed(deliverAtTheEarliestPopupSheet);
    }

    public boolean isDeliverAtTheEarliestPopupSheetHidden(){
        return isElementHidden(deliverAtTheEarliestPopupSheet);
    }

    public void pressGotItBtn(){
        wait.until(ExpectedConditions.visibilityOf(gotItBtn))
                .click();
    }

    public boolean scheduleAnotherTimeSheetIsDisplayed(){
        return isElementDisplayed(scheduleAnotherTimeSheetTitles);
    }

    public void pressFirstAvailableScheduleSlot(){
        wait.until(ExpectedConditions.visibilityOf(firstAvailableRadioBtn))
                .click();
    }

    public boolean isBusyMessageDisplayed(){

        return isElementDisplayed(busyMessage);
    }

    public boolean isBusyMessagePopupDisplayed(){

        return isElementDisplayed(busyMessagePopup);
    }

    public boolean isDeliveryTimeUpdatedPopupDisplayed(){
        return isElementDisplayed(deliveryTimeUpdatedPopup);
    }

    public void pressPlaceOrderDeliveryTimeUpdatedPopup(){
        wait.until(ExpectedConditions.visibilityOf(placeOrderDeliveryTimeUpdatedPopup))
                .click();
    }

    public void pressFirstAvailableInScheduleAnotherTimeSheet(){
        wait.until(ExpectedConditions.elementToBeClickable(firstAvailableInScheduleAnotherTimeSheet))
                .click();
    }

    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public String getPromoCodeTxtFieldContentDescription(){
        return promoCodeTxtFieldContentDescription;
    }

    public void enterPromoCode(String code){
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                        By.xpath(String.format(promoCodeTxtFieldSelector, getPromoCodeTxtFieldContentDescription()))))
                .sendKeys(code);
    }

    public void pressPromoCodeApplyBtn(){
        wait.until(ExpectedConditions.visibilityOf(promoCodeApplyBtn))
                .click();
    }

    public boolean isCouponValid(){
        return isElementDisplayed(promocodeAppliedSuccessMsg);
    }

    public boolean isCouponInValid(){
        return isElementDisplayed(promocodeFailureMsg);
    }

    public boolean isCouponApplied(){
        return isElementDisplayed(promocodeAppliedSuccessMsg) || isElementDisplayed(promocodeFailureMsg);
    }

    public void enterADeliveryNote(String note){
        wait.until(ExpectedConditions.visibilityOf(deliveryNoteTxtField))
                .sendKeys(note);
    }

    public String getTipAmountContentDescription(){
        return tipAmountContentDescription;
    }

    public void pressTipAmount(){
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                        By.xpath(String.format(tipAmountSelector, getTipAmountContentDescription()))))
                .click();
    }

    public void pressCashOnDeliveryPaymentOption(){
        wait.until(ExpectedConditions.visibilityOf(cashOnDeliveryPaymentOption))
                .click();
    }

    public void pressAddNewCardPaymentOption(){
        wait.until(ExpectedConditions.visibilityOf(addNewCardPaymentOption))
                .click();
    }

    public boolean isCardSelectionModalDisplayed() {
        return isElementDisplayed(cardSelectionModal);
    }

    public void pressChangeCardBtn() {
        wait.until(ExpectedConditions.visibilityOf(changeCardBtn))
                .click();
    }
    public void pressFreeShippingOption(){
        wait.until(ExpectedConditions.visibilityOf(freeShippingBtn)).click();
    }
    public void pressRedeemBtn(){
        wait.until(ExpectedConditions.visibilityOf(redeemBtn)).click();
    }
    public boolean isGotItBottomSheetDisplayed(){

        return isElementDisplayed(gotItBtn);
    }

}
