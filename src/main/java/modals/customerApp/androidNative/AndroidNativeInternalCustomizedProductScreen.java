package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeInternalCustomizedProductScreen extends BaseAndroidScreen {
    public AndroidNativeInternalCustomizedProductScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "(//android.view.View[@content-desc=\"product_qtyController\"])[1]")
    WebElement plusOptionBtn;

    @FindBy(xpath = "//android.view.View[@content-desc=\"addToCart_btn\"]")
    WebElement addProductToCartBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Customize your order']")
    WebElement customizationTextLabel;

    @FindBy(xpath = "//android.view.View[contains(@name, 'productDetailsProductCustomizations-')]")
    WebElement radioOptionBtn;

    String scrollableContentContainer = "//android.widget.ScrollView";

    String relatedProductsContentDescription = "Related products";

    public void clickOnOptionBtn() {
        wait.until(ExpectedConditions.visibilityOf(plusOptionBtn)).click();
    }

    public void clickOnAddProductBtn() {
        wait.until(ExpectedConditions.visibilityOf(addProductToCartBtn)).click();
    }

    public WebElement getScrollableContentContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainer)));
        } catch (Exception e) {
            return null;
        }
    }

    public String getRelatedProductsContentDescription(){
        return relatedProductsContentDescription;
    }

    public boolean isCustomizationLabelDisplayed(){
        return isElementDisplayed(customizationTextLabel);
    }

}
