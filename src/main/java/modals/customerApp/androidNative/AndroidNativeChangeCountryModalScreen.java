package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class AndroidNativeChangeCountryModalScreen extends BaseAndroidScreen {
    public AndroidNativeChangeCountryModalScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy (xpath = "//android.widget.TextView[@text='Are you sure you want to change the country?']")
    WebElement modalTitle;

    public boolean isModalDisplayed() {
       return isElementDisplayed(modalTitle);
    }
}

