package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeProductScreen extends BaseAndroidScreen {
    public AndroidNativeProductScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@content-desc=\"product_actions_container\"]/android.view.View/android.widget.Button")
    WebElement addProductToCartBtn;

    @FindBy(xpath = "//android.view.View[@content-desc=\"product_favoriteBtn\"]")
    WebElement addToFavBtn;

    @FindBy (xpath = "//android.widget.TextView[@text=\"View list\"]")
    WebElement viewFavListBtn;

    @FindBy (xpath = "//android.view.View[@content-desc=\"back_btn\"]")
    WebElement backBtn;
    @FindBy(xpath = "//androidx.compose.ui.platform.ComposeView/android.view.View/android.view.View/android.view.View[1]/android.widget.Button[2]")
    WebElement cartBtn;

    @Override
    public void enterValueInTextField(WebElement txtField, String value) {
        super.enterValueInTextField(txtField, value);
    }

    public void clickFavoriteBtn() {
        wait.until(ExpectedConditions.visibilityOf(addToFavBtn)).click();
    }

    public void clickViewFavList() {
        wait.until(ExpectedConditions.visibilityOf(viewFavListBtn)).click();
    }

    public void clickBack() {
        wait.until(ExpectedConditions.visibilityOf(backBtn)).click();
    }

    public void clickAddProductToCartBtn(){
        wait.until(ExpectedConditions.visibilityOf(addProductToCartBtn)).click();
    }
    public void pressOnCartInProductDetails(){
        wait.until(ExpectedConditions.visibilityOf(cartBtn)).click();
    }

}
