package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeRegisterSuccessScreen extends BaseAndroidScreen {
    public AndroidNativeRegisterSuccessScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@content-desc='registerSuccessScreen_mainContainer']")
    WebElement pageContainer;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='registerSuccessScreen_title_text']")
    WebElement pageSubtitle;

    @FindBy(xpath = "//android.view.View[@content-desc='done_btn']")
    WebElement doneBtn;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageContainer) || isElementDisplayed(pageSubtitle);
    }

    public void pressDoneBtn(){
        wait.until(ExpectedConditions.visibilityOf(doneBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(doneBtn));
    }
}
