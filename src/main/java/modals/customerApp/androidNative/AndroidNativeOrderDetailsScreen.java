package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativeOrderDetailsScreen extends BaseAndroidScreen {
    public AndroidNativeOrderDetailsScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Order details']")
    WebElement pageTitle;

    @FindBy(xpath = "(//android.widget.Button)[1]")
    WebElement backBtn;

    String scrollableContentContainerSelector = "//android.widget.ScrollView";

    String cancelOrderBtnContentDescription = "orderDetails_cancelOrder_btn";
    String cancelOrderBtnSelector = "//android.view.View[@content-desc='%s']";

    @FindBy(xpath = "//android.widget.TextView[@content-desc='cancelOrderSheet_title']")
    WebElement cancelOrderSheetTitle;

    @FindBy(xpath = "//android.view.View[@content-desc='cancelOrderSheet_doNotCancel_btn']")
    WebElement doNotCancelBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='cancelOrderSheet_cancelAndRefund_btn']")
    WebElement cancelAndRefundBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='cancelReasonsSheet_title']")
    WebElement cancelReasonsTitle;

    @FindBy(xpath = "//android.widget.TextView[@text='I Ordered on a wrong address']")
    WebElement iOrderedOnAWrongAddressBtn;
    
    @FindBy(xpath = "//android.view.View[@content-desc='cancelReasonsSheet_reasonsList_submitBtn']")
    WebElement submitCancelReasonBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='orderDetails_title' and @text='Order canceled']")
    WebElement canceledOrderTitle;

    @FindBy(xpath = "(//android.widget.TextView[contains(@text,'Order')])[2]")
    WebElement orderStatus;
    
    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void pressBackBtn(){
        wait.until(ExpectedConditions.visibilityOf(backBtn))
                .click();
    }

    public WebElement getScrollableContentContainer(){
        try{
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public String getCancelOrderBtnContentDescription(){
        return cancelOrderBtnContentDescription;
    }

    public void pressCancelOrderBtn(){
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(cancelOrderBtnSelector, getCancelOrderBtnContentDescription()))))
                .click();
    }

    public boolean isCancelOrderBottomSheetDisplayed(){
        return isElementDisplayed(cancelOrderSheetTitle);
    }

    public void pressDoNotCancelBtn(){
        wait.until(ExpectedConditions.visibilityOf(doNotCancelBtn))
                .click();
    }

    public void pressCancelAndRefundBtn(){
        wait.until(ExpectedConditions.visibilityOf(cancelAndRefundBtn))
                .click();
    }

    public void selectIOrderedOnWrongAddressOption(){
        wait.until(ExpectedConditions.visibilityOf(iOrderedOnAWrongAddressBtn))
                .click();
    }

    public void pressSubmitCancelReasonBtn(){
        wait.until(ExpectedConditions.visibilityOf(submitCancelReasonBtn))
                .click();
    }

    public boolean isOrderCanceled(){
        return isElementDisplayed(canceledOrderTitle);
    }

    public String getOrderStatus() {
        return wait.until(ExpectedConditions.visibilityOf(orderStatus))
                .getText();
    }
}
