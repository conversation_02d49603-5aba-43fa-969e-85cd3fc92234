package modals.customerApp.androidNative;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNativePhoneNumberScreen extends BaseAndroidScreen {
    public AndroidNativePhoneNumberScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@content-desc='phoneNumberInputScreen_header']")
    WebElement pageTitle;

    @FindBy(xpath = "//android.view.View[@content-desc='phoneNumber_countryCode']")
    WebElement phoneNumberCountryCode;

    @FindBy(xpath = "//android.view.View[@content-desc='phoneNumber_txtField']//android.widget.EditText")
    WebElement phoneNumberTxtField;

    @FindBy(xpath = "//android.view.View[@content-desc='next_btn']")
    WebElement nextBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='This field is required.']")
    WebElement requiredMobileNumberErrorMsg;

    @FindBy(xpath = "//android.widget.TextView[@text='Mobile number must include numbers only.']")
    WebElement invalidFormatErrorMsg;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void pressPhoneNumberCountryCode(){
        wait.until(ExpectedConditions.visibilityOf(phoneNumberCountryCode))
                .click();
    }

    public void pressNextBtn(){
        wait.until(ExpectedConditions.visibilityOf(nextBtn)).click();
    }

    public boolean isNextBtnEnabled(){
        try {
            wait.until(ExpectedConditions.attributeToBe(nextBtn, "enabled", "true"));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public void enterPhoneNumber(String phoneNumber){
        wait.until(ExpectedConditions.visibilityOf(phoneNumberTxtField)).sendKeys(phoneNumber);
    }

    public void clearPhoneNumberTxtField(){
        wait.until(ExpectedConditions.visibilityOf(phoneNumberTxtField)).clear();
    }

    public void enterPhoneNumberAndPresNext(String phoneNumber){
        enterPhoneNumber(phoneNumber);
        isNextBtnEnabled();
        pressNextBtn();
    }

    public boolean isRequiredMobileNumberErrorMsgDisplayed(){
        return isElementDisplayed(requiredMobileNumberErrorMsg);
    }

    public boolean isInvalidFormatErrorMsgDisplayed(){
        return isElementDisplayed(invalidFormatErrorMsg);
    }

    public String getCurrentValueInTxtField(){
        return wait.until(ExpectedConditions.visibilityOf(phoneNumberTxtField)).getText();
    }
}
