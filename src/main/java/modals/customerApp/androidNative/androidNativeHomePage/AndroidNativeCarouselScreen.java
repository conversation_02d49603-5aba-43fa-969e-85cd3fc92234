package modals.customerApp.androidNative.androidNativeHomePage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import java.util.List;

public class AndroidNativeCarouselScreen extends BaseAndroidScreen {
    public AndroidNativeCarouselScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);

    }

    @FindBy(xpath = "//androidx.compose.ui.platform.ComposeView/android.view.View/android.view.View/android.view.View[1]/android.view.View")
    WebElement cartView;

    @FindBy(xpath = "//android.widget.TextView[contains(@id,'carouselTitle')]")
    List<WebElement> carouselsTitles;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"productPrice\"]")
    WebElement productPrice;

    String carouselTitleXpath = "//android.widget.TextView[@content-desc=\"carouselTitle_%s\"]";

    @FindBy(xpath = "//android.view.View[@content-desc=\"product_decrementQtyBtn\"]")
    WebElement decreaseIcon;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Product added to favorites.\"]")
    WebElement favHint;

    String viewAllBtnXpath = "//android.view.View[@content-desc=\"productCarouselViewAllBtn_%d\"]";

    @FindBy(xpath = "//android.view.View[@content-desc=\"bottomBar_cart_btn\"]")
    WebElement cartBtn;

    String productCardContentDescription = "productCard_%s_innerContainer";

    String addToCartBtnXpathSelector = "//android.view.View[@content-desc='%s']" +
            "/ancestor::android.view.View[1]" +
            "//android.view.View[@content-desc='product_incrementQtyBtn']";
    String decreaseBtnXpathSelector = "//android.view.View[@content-desc='%s']" + "/ancestor::android.view.View[1]" +
            "//android.view.View[@content-desc='product_decrementQtyBtn']";

    String addToFavoritesBtnXpathSelector = "//android.view.View[@content-desc='product_favoriteBtn']";

    String productCardXpathSelector = "//android.view.View[@content-desc='%s']";

    public void clickCartView() {
        wait.until(ExpectedConditions.visibilityOf(cartView)).click();
    }

    public boolean isUserOnCarouselPage() {
        return wait.until(ExpectedConditions.visibilityOf(cartView)).isDisplayed();
    }

    public boolean isCarouselDisplayed(int carouselIndex) {
    String dynamicXpath = String.format(carouselTitleXpath, carouselIndex);
    WebElement carousel = androidDriver.findElement(By.xpath(dynamicXpath));
    return carousel.isDisplayed();
   }

    public String getCarouselTitle(int carouselIndex) {
        String dynamicXpath = String.format(carouselTitleXpath, carouselIndex);
        WebElement carousel = wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(dynamicXpath)));
        return carousel.getText();
    }

    public void pressViewAllBtn(int carouselIndex) {
        String dynamicXpath = String.format(viewAllBtnXpath, carouselIndex);
        wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(dynamicXpath)));
        WebElement viewAllButton = androidDriver.findElement(By.xpath(dynamicXpath));
        viewAllButton.click();
    }

    public boolean isFavHintDisplayed() {
        return wait.until(ExpectedConditions.visibilityOf(favHint)).isDisplayed();
    }

    public void pressAddToCartBtnByProductId(String productId) {
        getAddToCartUiElementByProductId(productId)
                .click();
    }

    public WebElement getAddToCartUiElementByProductId(String productId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(addToCartBtnXpathSelector, getProductCardContentDescription(productId)))
        ));
    }

    public String getProductCardContentDescription(String productId) {
        return (String.format(productCardContentDescription, productId));
    }
    public void pressDecreaseBtnByProductId(String productId) {
        getDecreaseBtnUiElementByProductId(productId)
                .click();
    }

    public WebElement getDecreaseBtnUiElementByProductId(String productId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(decreaseBtnXpathSelector, getProductCardContentDescription(productId)))
        ));
    }

    public void pressAddToFavoritesBtnByProductId(String productId) {
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath((getProductCardXpathSelector(productId) + addToFavoritesBtnXpathSelector))
        )).click();
    }
    public String getProductCardXpathSelector(String productId) {
        return (String.format(productCardXpathSelector, getProductCardContentDescription(productId)));
    }
}
