package modals.customerApp.androidNative.androidNativeHomePage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

/**
 * This class represents the Rate Order popup that appears on the home screen
 * after an order is delivered. It contains all the elements and methods related
 * to the rating functionality.
 * This modal class is responsible for handling all interactions with the
 * order rating popup, including star ratings, comments, tipping options,
 * and feedback submission.
 */
public class AndroidNativeRateOrderPopup extends BaseAndroidScreen {

    @FindBy(xpath = "//android.widget.ScrollView[@content-desc=\"ratingBottomSheet_contentsContainer\"]/android.view.View[1]/android.widget.TextView\n")
    WebElement rateOrderRateYourOrderText;

    // Close button locators with multiple options to increase chances of finding it
    @FindBy(xpath = "//android.widget.ScrollView[@content-desc=\"ratingBottomSheet_contentsContainer\"]/android.view.View[1]/android.view.View/android.widget.Button")
    WebElement rateOrderRatingCloseButton;

    @FindBy(xpath = "//android.view.View[@content-desc=\"ratingBottomSheet_topProductsSection\"]")
    WebElement rateOrderRateYourOrderView;

    @FindBy(xpath = "//android.view.View[@content-desc=\"star_1\"]")
    WebElement rateOrderRateYourOrderOneStarView;

    @FindBy(xpath = "//android.view.View[@content-desc=\"star_2\"]")
    WebElement rateOrderRateYourOrderTwoStarsView;

    @FindBy(xpath = "//android.view.View[@content-desc=\"star_3\"]")
    WebElement rateOrderRateOrderRateYourOrderThreeStarsView;

    @FindBy(xpath = "//android.view.View[@content-desc=\"star_4\"]")
    WebElement rateOrderRateOrderRateYourOrderFourStarView;

    @FindBy(xpath = "//android.view.View[@content-desc=\"star_5\"]")
    WebElement rateOrderRateYourOrderFiveStarsView;

    @FindBy(id = "ratingbar_poor")
    WebElement rateOrderRatingPoorText;

    @FindBy(id = "ratingBar_txtValue")
    WebElement rateOrderRatingValueText;

    @FindBy(xpath = "//android.view.View[@content-desc=\"ratingBottomSheet_below5starsView_container\"]/android.view.View[1]")
    WebElement rateOrderPoorRatingReasonView;

    @FindBy(className = "android.widget.EditText")
    WebElement rateOrderPoorRatingReasonOtherReasonText;

    @FindBy(id = "ratingBar_excellent")
    WebElement rateOrderExcellentText;

    @FindBy(id = "ratingBottomSheet_tippingSection_title")
    WebElement rateOrderSayThanksWithATipText;

    @FindBy(id = "ratingBottomSheet_tippingSection_tooltipIcon")
    WebElement rateOrderSayThanksWithATipToolTip;

    @FindBy(id = "ratingBottomSheet_tippingSection_balanceNote_txt")
    WebElement rateOrderMakeSureYouHaveBalanceText;

    @FindBy(xpath = "//android.view.View[@content-desc=\"ratingBottomSheet_tippingSection_informationSheet\"]/android.view.View/android.widget.Button")
    WebElement rateOrderDeliveryAssociateRewardPopupOkayButton;

    @FindBy(xpath = "//android.view.View[@content-desc=\"ratingBottomSheet_leaveComment_txtField\"]/android.widget.EditText")
    WebElement rateOrderLeaveACommentTextBox;

    @FindBy(id = "ratingBottomSheet_concernedProductsSection_item_81454_selected")
    WebElement rateOrderSelectItemOneTextBox;

    @FindBy(id = "ratingBottomSheet_thankYouForRating_done_btn")
    WebElement rateOrderThankYouForRatingDoneButton;

    @FindBy(xpath = "//android.view.View[@content-desc=\"ratingBottomSheet_leaveComment_txtField\"]/android.widget.EditText")
    WebElement rateOrderQualityIssueTextBox;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'rating_star_') or contains(@content-desc, 'star_rating_')]")
    List<WebElement> ratingStars;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'ratingBottomSheet_contentsContainer') or contains(@content-desc, 'confirm_rating') or contains(@content-desc, 'submit_btn')]")
    WebElement submitRatingButton;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Thank you for your feedback') or contains(@text, 'Thanks for rating') or contains(@text, 'Thank you for your rating')]")
    WebElement ratingSuccessMessage;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Excellent') or contains(@text, 'excellent') or contains(@text, '5 stars')]")
    WebElement excellentRatingText;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Do you have a comment?') or contains(@text, 'Add a comment') or contains(@text, 'Leave a comment')]")
    WebElement commentTextField;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'tipping') or contains(@content-desc, 'gratuity') or contains(@content-desc, 'tip_section')]")
    WebElement tippingSection;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'tip_amount_5') or contains(@content-desc, 'tip_5') or contains(@content-desc, 'tip_option_5')]")
    WebElement tipAmount5;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'tip_amount_10') or contains(@content-desc, 'tip_10') or contains(@content-desc, 'tip_option_10')]")
    WebElement tipAmount10;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'tip_amount_20') or contains(@content-desc, 'tip_20') or contains(@content-desc, 'tip_option_20')]")
    WebElement tipAmount20;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'custom_tip') or contains(@content-desc, 'tip_custom') or contains(@content-desc, 'tip_option_custom')]")
    WebElement customTipOption;

    @FindBy(xpath = "//android.widget.EditText[contains(@resource-id, 'custom_tip_input') or contains(@content-desc, 'custom_tip_input')]")
    WebElement customTipInputField;

    @FindBy(xpath = "//android.widget.Button[contains(@text, 'Apply') or contains(@content-desc, 'apply_custom_tip_btn')]")
    WebElement applyCustomTipButton;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'custom_tip_selected')]")
    WebElement selectedCustomTipAmount;

    @FindBy(xpath = "//android.widget.Button[contains(@content-desc, 'remove_custom_tip') or contains(@content-desc, 'clear_custom_tip')]")
    WebElement removeCustomTipButton;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'A tip of') and contains(@text, 'was sent to your delivery associate')]")
    WebElement tipSuccessMessage;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'referral_entry_point') or contains(@content-desc, 'refer_friend_btn')]")
    WebElement referralEntryPoint;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Refer a friend') or contains(@text, 'Invite friends')]")
    WebElement referralEntryPointText;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'rating_stars_container') or contains(@content-desc, 'stars_container')]")
    WebElement ratingStarsContainer;

    // Rating reason options
    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Other') or contains(@text, 'other')]")
    WebElement otherReasonOption;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Delivery associate') or contains(@text, 'delivery associate')]")
    WebElement deliveryAssociateOption;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Packaging') or contains(@text, 'packaging')]")
    WebElement packagingOption;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Delivery Time') or contains(@text, 'delivery time')]")
    WebElement deliveryTimeOption;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'other_reason_input') or contains(@content-desc, 'otherReason_txtField')]/android.widget.EditText")
    WebElement otherReasonInputField;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Product Quality') or contains(@text, 'product quality')]")
    WebElement productQualityOption;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Select concerned items') or contains(@text, 'select items')]")
    WebElement selectConcernedItemsSection;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'concernedProductsSection_item_') or contains(@content-desc, 'product_item_')]")
    List<WebElement> concernedItemsList;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Quality Issue') or contains(@text, 'quality issue')]")
    WebElement qualityIssueLabel;

    @FindBy(xpath = "//android.view.View[contains(@content-desc, 'qualityIssue_txtField')]/android.widget.EditText")
    WebElement qualityIssueInputField;

    // Rating reason selection state elements
    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Delivery associate') and contains(@content-desc, '_selected')]")
    WebElement deliveryAssociateReasonSelected;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Packaging') and contains(@content-desc, '_selected')]")
    WebElement packagingReasonSelected;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Delivery Time') and contains(@content-desc, '_selected')]")
    WebElement deliveryTimeReasonSelected;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Other') and contains(@content-desc, '_selected')]")
    WebElement otherReasonSelected;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Product Quality') and contains(@content-desc, '_selected')]")
    WebElement productQualityReasonSelected;

    String starRatingSelector = "//android.view.View[contains(@content-desc, 'rating_star_%d')]";

    public AndroidNativeRateOrderPopup(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    public boolean isRatingPopupDisplayed() {
        return isElementDisplayed((wait.until(ExpectedConditions.visibilityOf(rateOrderRateYourOrderText))));
    }

    public void closeRatingPopup() {
        rateOrderRatingCloseButton.click();
    }

    public void rateOrderWithStars(int stars) {
        try {
            // Try to find the specific star by content description
            WebElement starElement = wait.until(ExpectedConditions.elementToBeClickable(
                    By.xpath(String.format(starRatingSelector, stars))));
            starElement.click();
        } catch (Exception e) {
            // If specific star selector fails, try a more generic approach with the list of stars
            if (ratingStars != null && !ratingStars.isEmpty() && ratingStars.size() >= stars) {
                wait.until(ExpectedConditions.elementToBeClickable(ratingStars.get(stars - 1))).click();
            }
        }
    }

    public void submitRating() {
        wait.until(ExpectedConditions.elementToBeClickable(submitRatingButton)).click();
    }

    public boolean isRatingSubmittedSuccessfully() {
        try {
            return isElementDisplayed(rateOrderThankYouForRatingDoneButton);
        } catch (Exception e) {
            return false;
        }
    }

    public void clickTheDoneButtonInThanksForRatingPopup() {
        wait.until(ExpectedConditions.elementToBeClickable(rateOrderThankYouForRatingDoneButton)).click();
    }

    public boolean isThanksForRatingPopupDisplayed() {
        try {
            return isElementDisplayed (rateOrderThankYouForRatingDoneButton);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isExcellentRatingTextDisplayed() {
        try {
            return isElementDisplayed(excellentRatingText);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isCommentTextFieldDisplayed() {
        try {
            return isElementDisplayed(commentTextField);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isTippingSectionDisplayed() {
        try {
            return isElementDisplayed(tippingSection);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isSubmitRatingButtonEnabled() {
        try {
            return submitRatingButton.isEnabled();
        } catch (Exception e) {
            return false;
        }
    }

    public boolean areRatingStarsDisplayed() {
        try {
            return isElementDisplayed(ratingStarsContainer);
        } catch (Exception e) {
            return false;
        }
    }

    public void enterCommentInRatingPopup(String comment) {
        wait.until(ExpectedConditions.visibilityOf(commentTextField)).click();
        enterValueInTextField(commentTextField, comment);
        hideKeyboardIfDisplayed();
    }

    public void selectTipAmount(int amount) {
        WebElement tipElement = switch (amount) {
            case 5 -> tipAmount5;
            case 10 -> tipAmount10;
            case 20 -> tipAmount20;
            default -> tipAmount5;
        };
        wait.until(ExpectedConditions.elementToBeClickable(tipElement)).click();
    }

    public void clickCustomTipOption() {
        wait.until(ExpectedConditions.elementToBeClickable(customTipOption)).click();
    }

    public void enterCustomTipAmount(String amount) {
        WebElement inputField = wait.until(ExpectedConditions.visibilityOf(customTipInputField));
        inputField.clear();
        inputField.sendKeys(amount);
    }

    public void clickApplyCustomTip() {
        wait.until(ExpectedConditions.elementToBeClickable(applyCustomTipButton)).click();
    }

    public boolean isCustomTipInputFieldDisplayed() {
        try {
            return isElementDisplayed(customTipInputField);
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isSelectedCustomTipAmountDisplayed() {
        return isElementDisplayed(selectedCustomTipAmount);
    }
    public boolean isTipSuccessMessageDisplayed() {
        try {
            return isElementDisplayed(tipSuccessMessage);
        } catch (Exception e) {
            return false;
        }
    }

    public String getTipSuccessMessageText() {
        try {
            return wait.until(ExpectedConditions.visibilityOf(tipSuccessMessage)).getText();
        } catch (Exception e) {
            return "";
        }
    }

    public boolean isReferralEntryPointDisplayed() {
        try {
            return isElementDisplayed(referralEntryPoint) || isElementDisplayed(referralEntryPointText);
        } catch (Exception e) {
            return false;
        }
    }

    public void selectOtherReasonOption() {
        wait.until(ExpectedConditions.elementToBeClickable(otherReasonOption)).click();
    }

    public void selectDeliveryAssociateOption() {
        wait.until(ExpectedConditions.elementToBeClickable(deliveryAssociateOption)).click();
    }

    public void selectPackagingOption() {
        wait.until(ExpectedConditions.elementToBeClickable(packagingOption)).click();
    }

    public void selectDeliveryTimeOption() {
        wait.until(ExpectedConditions.elementToBeClickable(deliveryTimeOption)).click();
    }

    public boolean isOtherReasonInputFieldDisplayed() {
        try {
            return isElementDisplayed(otherReasonInputField);
        } catch (Exception e) {
            return false;
        }
    }

    public void enterOtherReasonText(String reasonText) {
        wait.until(ExpectedConditions.visibilityOf(otherReasonInputField)).click();
        enterValueInTextField(otherReasonInputField, reasonText);
        hideKeyboardIfDisplayed();
    }

    public void selectProductQualityOption() {
        wait.until(ExpectedConditions.elementToBeClickable(productQualityOption)).click();
    }

    public boolean isSelectConcernedItemsSectionDisplayed() {
        try {
            return isElementDisplayed(selectConcernedItemsSection);
        } catch (Exception e) {
            return false;
        }
    }

    public void selectFirstConcernedItem() {
        if (concernedItemsList != null && !concernedItemsList.isEmpty()) {
            wait.until(ExpectedConditions.elementToBeClickable(concernedItemsList.getFirst())).click();
        }
    }

    public boolean isQualityIssueInputFieldDisplayed() {
        try {
            return isElementDisplayed(qualityIssueLabel) && isElementDisplayed(qualityIssueInputField);
        } catch (Exception e) {
            return false;
        }
    }

    public void enterQualityIssueText(String issueText) {
        wait.until(ExpectedConditions.visibilityOf(qualityIssueInputField)).click();
        enterValueInTextField(qualityIssueInputField, issueText);
        hideKeyboardIfDisplayed();
    }

    // Methods for checking if rating reasons are selected
    public boolean isDeliveryAssociateReasonSelected() {
        return isElementDisplayed(deliveryAssociateReasonSelected);
    }

    public boolean isPackagingReasonSelected() {
        return isElementDisplayed(packagingReasonSelected);
    }

    public boolean isDeliveryTimeReasonSelected() {
        return isElementDisplayed(deliveryTimeReasonSelected);
    }

    public boolean isOtherReasonSelected() {
        return isElementDisplayed(otherReasonSelected);
    }

    public boolean isProductQualityReasonSelected() {
        return isElementDisplayed(productQualityReasonSelected);
    }

    // Methods for toggling (selecting/deselecting) rating reasons
    public void toggleDeliveryAssociateReason() {
        selectDeliveryAssociateOption();
    }

    public void togglePackagingReason() {
        selectPackagingOption();
    }

    public void toggleDeliveryTimeReason() {
        selectDeliveryTimeOption();
    }

    public void toggleOtherReason() {
        selectOtherReasonOption();
    }

    public void toggleProductQualityReason() {
        selectProductQualityOption();
    }

    // Generic method to check if any rating reason is selected
    public boolean isAnyRatingReasonSelected() {
        return isDeliveryAssociateReasonSelected() || 
               isPackagingReasonSelected() || 
               isDeliveryTimeReasonSelected() || 
               isOtherReasonSelected() || 
               isProductQualityReasonSelected();
    }

    // Method to check if Submit Rating button is enabled (useful for validation)
    public boolean isSubmitRatingButtonDisplayed() {
        return isElementDisplayed(submitRatingButton);
    }
}
