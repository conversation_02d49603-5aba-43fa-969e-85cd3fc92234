package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidOTPVerificationScreen extends BaseAndroidScreen {
    public AndroidOTPVerificationScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Verify your mobile number']")
    WebElement pageHeader;

    @FindBy(xpath = "//android.widget.TextView[@text='Enter the 4-digit verification code sent to']")
    WebElement pageSubHeader;

    String phoneNumberXpath = "//android.widget.TextView[@text='%s']";

    @FindBy(xpath = "//android.widget.EditText")
    WebElement otpTextField;

    @FindBy(xpath = "//android.widget.TextView[@text='Welcome back to Breadfast!']")
    WebElement loginAfterDeletedAccountModalTitle;

    public boolean isPageHeaderDisplayed(){
        return isElementDisplayed(pageHeader);
    }

    public boolean isPageSubHeaderDisplayed(){
        return isElementDisplayed(pageSubHeader);
    }

    public boolean isPhoneNumberDisplayed(String mobileNumber){
        WebElement phoneNumber = wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(phoneNumberXpath, mobileNumber))));
        return isElementDisplayed(phoneNumber);
    }

    public void enterOTP(String otp){
        wait.until(ExpectedConditions.elementToBeClickable(otpTextField));
        if (!otpTextField.getText().equalsIgnoreCase(otp)){
            wait.until(ExpectedConditions.elementToBeClickable(otpTextField)).clear();
            wait.until(ExpectedConditions.elementToBeClickable(otpTextField)).sendKeys(otp);
        }
    }

    public Boolean isOTPEnteredCorrectly(String otp){
        try {
            wait.until(ExpectedConditions.textToBePresentInElement(otpTextField, otp));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public void waitTillPageIsDismissed(){
        wait.until(ExpectedConditions.invisibilityOf(pageHeader));
    }

    public boolean isGreetingAfterDeletedAccountLoginDisplayed(){
        return isElementDisplayed(loginAfterDeletedAccountModalTitle);
    }
}
