package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidCardSelectionModal extends BaseAndroidScreen {
    public AndroidCardSelectionModal(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@content-desc='cardSelectionModal_modalTitle']")
    WebElement modalTitle;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='cardSelectionModal_addNewCard_btn']")
    WebElement addNewCardBtn;

    public boolean isModalDisplayed(){
        return isElementDisplayed(modalTitle);
    }

    public void pressAddNewCardBtn(){
        wait.until(ExpectedConditions.visibilityOf(addNewCardBtn))
                .click();
    }
}
