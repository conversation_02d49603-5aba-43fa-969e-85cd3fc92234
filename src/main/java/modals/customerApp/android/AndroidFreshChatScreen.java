package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidFreshChatScreen extends BaseAndroidScreen {
    public AndroidFreshChatScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='I have a question']")
    WebElement freshChatText;

    @FindBy(xpath = "//android.widget.ImageButton[@content-desc=\"Navigate up\"]")
    WebElement backBtn;

    public boolean isPageDisplayed(){
        return isElementDisplayed(freshChatText);
    }

    public void pressBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backBtn)).click();
    }

    public String getFreshChatText(){
        return freshChatText.getText();
    }
}
