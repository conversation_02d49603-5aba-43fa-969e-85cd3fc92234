package modals.customerApp.android.androidCreateAddressScreens;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import models.Address;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidCreateAddressScreen extends BaseAndroidScreen {
    public AndroidCreateAddressScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Address Details']")
    WebElement addressDetailsTitle;

   // @FindBy(xpath = "(//android.widget.EditText)[1]")
   // WebElement addressLabelTxtField;
   String addressScreenScrollableContentContainerSelector = "//android.widget.ScrollView";

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addressDetails_addressLabel_textField']")
    WebElement addressLabelTxtField;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addressDetails_area_textField']")
    WebElement areaTxtField;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addressDetails_fullAddress_textField']")
    WebElement addressDetailsTxtField;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addressDetails_floorNumber_textField']")
    WebElement floorNumberTxtField;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addressDetails_flatNumber_textField']")
    WebElement flatNumberTxtField;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addressDetails_deliveryInstructions_textField']")
    WebElement deliveryInstructionsTxtField;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='DiffRecep_rad']")
    WebElement notYourAddressCheckBox;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='addressDetails_submitBtn']")
    WebElement saveAddressBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"DELETE ADDRESS\"]")
    WebElement deleteAddressButton;

    String scrollableContentContainerSelector = "//android.widget.ScrollView";

    String saveAddressBtnContentDescription= "addressDetails_submitBtn";

    public boolean isPageDisplayed(){
        return isElementDisplayed(addressDetailsTitle);
    }

    public void enterAddressLabel(String addressLabel){
        enterValueInTextField(addressLabelTxtField, addressLabel);
        hideKeyboardIfDisplayed();
    }

    public void enterAddressDetails(String addressDetails){
        enterValueInTextField(addressDetailsTxtField, addressDetails);
        hideKeyboardIfDisplayed();
    }

    public void enterFloorNumber(String floorNumber){
        enterValueInTextField(floorNumberTxtField, floorNumber);
        hideKeyboardIfDisplayed();
    }

    public void enterFlatNumber(String flatNumber){
        enterValueInTextField(flatNumberTxtField, flatNumber);
        hideKeyboardIfDisplayed();
    }

    public void enterDeliveryInstructions(String deliveryInstructions){
        enterValueInTextField(deliveryInstructionsTxtField, deliveryInstructions);
        hideKeyboardIfDisplayed();
    }

    public void pressSaveAddressBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(saveAddressBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(addressDetailsTitle));
    }

    public void enterAddressDetails(Address address){
        enterAddressLabel(address.getAddressLabel());
        enterAddressDetails(address.getFullAddress());
        enterFloorNumber(address.getFloorNumber());
        enterFlatNumber(address.getFlatNumber());
    }

    public WebElement getAddressScreenScrollableContentContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(addressScreenScrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }
    public void enterAddressDetailsAndPressSave(Address address){
        enterAddressDetails(address);
        pressSaveAddressBtn();
    }

    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public String getSaveAddressBtnContentDescription() {
        return saveAddressBtnContentDescription;
    }

    public void pressDeleteAddressButton(){
        wait.until(ExpectedConditions.elementToBeClickable(deleteAddressButton)).click();
    }

    public boolean isDeleteButtonDisplayed() {
        return isElementDisplayed(deleteAddressButton);
    }
    public boolean isSaveAddressBtnDisplayed() {
        return isElementDisplayed(saveAddressBtn);
    }
}
