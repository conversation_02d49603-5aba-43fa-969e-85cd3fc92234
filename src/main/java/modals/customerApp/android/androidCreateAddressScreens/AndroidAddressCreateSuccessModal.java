package modals.customerApp.android.androidCreateAddressScreens;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidAddressCreateSuccessModal extends BaseAndroidScreen {
    public AndroidAddressCreateSuccessModal(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.FrameLayout[@resource-id='android:id/content']")
    WebElement modalContainer;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='android:id/alertTitle']")
    WebElement modalSuccessTitle;

    @FindBy(xpath = "//android.widget.Button[@resource-id='android:id/button1']")
    WebElement submitBtn;

    public boolean isModalDisplayed(){
        return isElementDisplayed(modalContainer);
    }

    public void dismissModal(){
        wait.until(ExpectedConditions.elementToBeClickable(submitBtn)).click();
    }
}
