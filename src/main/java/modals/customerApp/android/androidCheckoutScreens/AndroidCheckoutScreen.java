package modals.customerApp.android.androidCheckoutScreens;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AndroidCheckoutScreen extends BaseAndroidScreen {
    public AndroidCheckoutScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='checkoutScreen_headerContainer']")
    WebElement pageHeaderContainer;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='checkoutScreen_grandTotal_value']")
    WebElement orderGrandTotal;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='checkoutScreen_deliveryFees_orderFees_valueContainer']")
    WebElement orderDeliveryFees;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='checkoutScreen_subtotal_value, Subtotal']")
    WebElement orderSubTotal;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='checkoutScreen_submitBtn']")
    WebElement placeOrderBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='PromoCodeAdd_btn']")
    WebElement applyCouponCodeBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='promocode_errorhint']")
    WebElement couponCodeErrorMsg;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='paymentSection_cashOnDelivery_rowContainer']")
    WebElement codPaymentMethodOption;

    @FindBy(xpath = "//android.widget.Switch[@content-desc='useMyBalanceToggleBtn']")
    WebElement useMyBalanceToggle;

    @FindBy(xpath="//android.widget.TextView[@content-desc='DeliveryDetails_txt']")
    WebElement deliveryInfoLabel;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Schedule\"]")
    WebElement deliveryScheduleButton;

    @FindBy(xpath = "//android.view.ViewGroup[contains(@content-desc, \"deliveryTimeSlotsModal_timeSlot_\") " +
            "and contains(@content-desc, \"_cellContainer\")]")
    List<WebElement> availableTimeSlots;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Select delivery time\"]")
    WebElement selectTomorrowDeliveryTime;

    @FindBy(xpath ="//android.widget.TextView[@content-desc='useMyBalance_currentBalanceValue']")
    WebElement currentBalanceValue ;

    @FindBy (xpath = "//android.view.ViewGroup[@content-desc='cardMiddleRow_contentContainer']")
    WebElement cardContainer;

    @FindBy (xpath = "//android.widget.TextView[@content-desc='checkoutScreen_value, Previousdueamount']")
    WebElement previousDueAmount;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='paymentSection_container']")
    WebElement paymentSectionContainer;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='deliveryInfo_instant_deliveryTime_text']")
    WebElement instantExpectedDeliveryTimeValue;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='checkoutScreen_tipAmount_value, Tipamount']")
    WebElement tipAmount;

    @FindBy(xpath = "//android.widget.Button[@content-desc='checkoutScreen_gratuitySection_customTip_undefined']")
    WebElement customTipOption;

    @FindBy(xpath = "(//android.widget.EditText)[1]")
    WebElement enterCustomTipAmountEditTxt;

    @FindBy(xpath = "(//android.widget.TextView[@text='Apply'])[1]")
    WebElement applyTipBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Maximum amount is 100 EGP.']")
    WebElement tipLimitErrorMsg;

    String scrollableContentContainerSelector = "//android.widget.ScrollView";

    String promoCodeTxtFieldContentDescription = "Promocode_cell";

    String promoCodeTxtFieldSelector = "//android.widget.EditText[@content-desc='%s']";

    String deliveryFeesContentDescription = "checkoutScreen_deliveryFees_orderFees_valueContainer";

    String deliveryFeesUiSelector = "//android.widget.TextView[@content-desc='%s']";

    String useMyBalanceContentDescription = "useMyBalanceToggleBtn";

    String cashOnDeliveryBtnContentDescription ="paymentSection_cashOnDelivery_rowContainer";

    String paymentSectionContentDescription = "paymentSection_container";

    String previousDueAmountContentDescription = "previousDueAmount";

    String tippingSectionContentDescription ="checkoutScreen_gratuitySection_container";

    String tipAmountOptionUiSelector= "//android.widget.Button[@content-desc='%s']";

    String tipAmountOptionContentDescription= "checkoutScreen_gratuitySection_tip_%s";

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageHeaderContainer);
    }

    public Double getOrderGrandTotalValue(){
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(orderGrandTotal))
                .getText().replace("EGP ", ""));
    }

    public Double getOrderSubTotalValue(){
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(orderSubTotal))
                .getText().replace("EGP ", ""));
    }

    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public String getPromoCodeTxtFieldContentDescription() {
        return promoCodeTxtFieldContentDescription;
    }

    public WebElement getPromoCodeTxtFieldUiElement(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(
                    String.format(promoCodeTxtFieldSelector, getPromoCodeTxtFieldContentDescription()))));
        } catch (Exception e){
            return null;
        }
    }
    public String getCashOnDeliveryBtnContentDescription() {
        return cashOnDeliveryBtnContentDescription;
    }

    public String getDeliveryFeesContentDescription() {
        return deliveryFeesContentDescription;
    }

    public WebElement getDeliveryFeesUiElement(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(deliveryFeesUiSelector, getDeliveryFeesContentDescription()))));
        } catch (Exception e){
            return null;
        }
    }

    public Double getDeliveryFeesValue(String deliveryFeesTxt){
        try {
            // Define a regular expression pattern to match "EGP" followed by a double value
            Pattern pattern = Pattern.compile("EGP (\\d+(\\.\\d+)?)");
            Matcher matcher = pattern.matcher(deliveryFeesTxt);

            double lastValue = 0.0; // Initialize with a default value

            // Find all matches
            while (matcher.find()) {
                // Extract and parse the matched double value
                String doubleString = matcher.group(1);
                lastValue = Double.parseDouble(doubleString);
            }
            return lastValue;
        } catch (Exception e) {
            return 0.0;
        }
    }

    public void pressPlaceOrderBtn(){
        wait.until(ExpectedConditions.visibilityOf(placeOrderBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(placeOrderBtn));
    }

    public void pressCreditCardPaymentOption() {
        wait.until(ExpectedConditions.visibilityOf(cardContainer))
                .click();
    }

    public boolean DueAmountIsDisplayed () {
        wait.until(ExpectedConditions.visibilityOf(previousDueAmount)).isDisplayed();
        return true;
    }

    public void enterCouponCode(String couponCode){
        enterValueInTextField(getPromoCodeTxtFieldUiElement(), couponCode);
        hideKeyboardIfDisplayed();
    }

    public void pressApplyCouponCodeBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(applyCouponCodeBtn))
                .click();
    }

    public boolean isCouponCodeErrorMsgDisplayed(){
        return isElementDisplayed(couponCodeErrorMsg);
    }

    public void pressCashOnDeliveryPaymentOption(){
        wait.until(ExpectedConditions.visibilityOf(codPaymentMethodOption))
                .click();
    }

    public void pressUseMyBalanceToggle(){
        wait.until(ExpectedConditions.visibilityOf(useMyBalanceToggle))
                .click();
    }

    public String getUseMyBalanceContentDescription() {
        return useMyBalanceContentDescription;
    }

    public void isCheckoutPageDisplayed()
    {
        wait.until(ExpectedConditions.visibilityOf(deliveryInfoLabel));
    }

    public void selectFirstAvailableDeliveryTimeSlot() {
        wait.until(ExpectedConditions.elementToBeClickable(availableTimeSlots.get(1)))
                .click();
    }

    public boolean isBalanceToggleEnabled() {
        try {
            return wait.until(ExpectedConditions.visibilityOf(useMyBalanceToggle))
                    .getAttribute("checked").equalsIgnoreCase("true");
        } catch (Exception e){
            return false;
        }
    }

    public boolean isBalanceDisplayed() {
        return isElementDisplayed(currentBalanceValue);
    }

    public String getPaymentSectionContentDescription(){
        return paymentSectionContentDescription;
    }

    public Double getPreviousDueAmountValue(){
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(previousDueAmount))
                .getText().replace("EGP ", ""));
    }

    public String getInstantDeliveryExpectedTime(){
        return wait.until(ExpectedConditions.visibilityOf(instantExpectedDeliveryTimeValue))
                .getText().toLowerCase();
    }

    public String getPreviousDueAmountContentDescription(){
        return previousDueAmountContentDescription;
    }

    public String getTippingSectionContentDescription(){
        return tippingSectionContentDescription;
    }

    public void pressCustomTipOption()
    {
        wait.until(ExpectedConditions.visibilityOf(customTipOption)).click();
    }

    public void enterCustomTipAmount(String amount)
    {
        wait.until(ExpectedConditions.visibilityOf(enterCustomTipAmountEditTxt)).click();
        enterCustomTipAmountEditTxt.sendKeys(amount);
    }

    public String getCustomTipTextValue()
    {
        return enterCustomTipAmountEditTxt.getText();
    }

    public boolean isTipLimitErrorMsgDisplayed()
    {
        return isElementDisplayed(tipLimitErrorMsg);
    }

    public void selectTipAmountElement(int tipValue){
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(tipAmountOptionUiSelector, getTipAmountOptionContentDescription(tipValue))))).click();
    }

    public String getTipAmountOptionContentDescription(int tipValue){
        return String.format(this.tipAmountOptionContentDescription, tipValue);
    }

    public Double getTipValue(){
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(tipAmount))
                .getText().replace("EGP ", ""));
    }

    public boolean isTipAmountDisplayed()
    {
        return isElementDisplayed(tipAmount);
    }

    public int getDisplayedSlotsCount() {
        wait.until(ExpectedConditions.visibilityOfAllElements(availableTimeSlots));
        // Check if the available time slots list is not null and has elements
        if (availableTimeSlots != null && !availableTimeSlots.isEmpty()) {
            // Return the number of available time slots
            return availableTimeSlots.size();
        } else {
            // Return 0 if no time slots are available
            return 0;
        }
    }

    public void pressSelectTomorrowDeliveryTime() {
        wait.until(ExpectedConditions.elementToBeClickable(selectTomorrowDeliveryTime))
                .click();
    }

    public void pressNowDeliveryScheduleBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(deliveryScheduleButton))
                .click();
    }
}
