package modals.customerApp.android.androidCheckoutScreens;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidCartScreen extends BaseAndroidScreen {
    public AndroidCartScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath =  "//android.widget.Button[@content-desc='cartScreen_goToCheckoutBtn']")
    WebElement goToCheckoutBtn;
    @FindBy(xpath =  "//android.widget.Button[@content-desc='cartLoyaltyBarContainer']")
    WebElement breadfastRewardBtn;
    @FindBy(xpath = "//android.widget.TextView[@content-desc='cartLoyaltyBar_earningPtsCount']")
    WebElement totalBreadfastPointsCountElement;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='cartGoToCheckout_disabledBtn']")
    WebElement disabledGoToCheckoutBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"cartGoToCheckoutBtn_deliveryFeesContainer\"]")
    WebElement deliveryFeesElement;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"cart_goToCheckoutBtn_cartSubTotal\"]")
    WebElement totalValueElement;
    @FindBy(xpath = "//android.widget.TextView[@text='Seems you haven’t added anything to your cart yet! Start shopping and add items.']")
    WebElement emptyCart;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"Delete_imgview\"]")
    WebElement deleteBtn;

    String productRowContainerSelector = "//android.view.ViewGroup[@content-desc=\"productItem_%s_container\"]";

    String productDeleteQtyBtnSelector = "//android.view.ViewGroup[@content-desc=\"Delete_imgview\"]";

    String productDecreaseQtyBtnSelector = "//android.view.ViewGroup[@content-desc=\"Delete\"]";

    String productIncreaseQtyBtnSelector = "//android.view.ViewGroup[@content-desc=\"Add_imgview\"]";

    String productPriceSelector = "//android.widget.TextView[@content-desc=\"ProductPrice_txt\"]";

    String productCurrentQtyUiElementSelector = "//android.view.ViewGroup[@content-desc=\"productQuantity_txt\"]";

    public void pressGoToCheckoutBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(goToCheckoutBtn))
                .click();
    }

    public void pressBreadfastRewardBtn() {
        wait.until(ExpectedConditions.visibilityOf(totalBreadfastPointsCountElement));
        wait.until(ExpectedConditions.elementToBeClickable(breadfastRewardBtn))
                .click();
    }
    public boolean isGoToCheckoutBtnEnabled(){
        return isElementDisplayed(goToCheckoutBtn);
    }

    public boolean isGoToCheckoutBtnDisabled(){
        return isElementDisplayed(disabledGoToCheckoutBtn);
    }

    public void pressProductDeleteBtn(int productId) {
        wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath(String.format(productRowContainerSelector, productId)
                        + productDeleteQtyBtnSelector))).click();
    }

    public Double getProductPriceValue(int productId) {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOfElementLocated(
                        By.xpath(String.format(productRowContainerSelector, productId)
                                + productPriceSelector))).getText()
                .replace("EGP ", "").replace("SAR ", ""));
    }

    public int getProductCurrentQtyInCart(int productId) {
        return  Integer.parseInt(wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(productRowContainerSelector, productId)
                        + productCurrentQtyUiElementSelector))).getText());
    }

    public void pressIncreaseQtyBtn(int productId) {
        wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath(String.format(productRowContainerSelector, productId)
                        + productIncreaseQtyBtnSelector))).click();
    }

    public void pressDecreaseQtyBtn(int productId) {
        wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath(String.format(productRowContainerSelector, productId)
                        + productDecreaseQtyBtnSelector))).click();
    }

    public Double getCurrentCartTotal() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(totalValueElement))
                .getText().replace("EGP ", "").replace("SAR ", ""));
    }

    public Double getCurrentCartDeliveryFees() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(deliveryFeesElement)).getText()
                .replace(" delivery fees", "").replace("+", ""));
    }

    public Double getCurrentTotalAndDelivery() {

        double total = getCurrentCartTotal();
        double deliveryFees = getCurrentCartDeliveryFees();
        return total + deliveryFees;
    }

    public boolean isFessDisplayed() {
        return isElementDisplayed(deliveryFeesElement);
    }

    public boolean isTotalDisplayed() {
        return isElementDisplayed(totalValueElement);
    }

    public boolean isCheckoutBtnClickable() {
        try {
            wait.until(ExpectedConditions.elementToBeClickable(goToCheckoutBtn));
            return true;
           }
        catch (Exception e) {
            return false;
        }
    }

    public void updateCartUntilTotalReached(double targetTotal) {
        // Enter the loop until the cart total is greater than or equal to the targetTotal which will be = 250
        while (getCurrentCartTotal() < targetTotal) {
            // Add items to the cart
            pressIncreaseQtyBtn(1);

            // Introduce a wait to avoid constant checking
            try {
                Thread.sleep(1000); // pause for 1 second in each check
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }
    public boolean isCartEmpty() {
        return isElementDisplayed(emptyCart);
    }
}

