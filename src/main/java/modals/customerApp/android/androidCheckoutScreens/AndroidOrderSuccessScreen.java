package modals.customerApp.android.androidCheckoutScreens;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidOrderSuccessScreen extends BaseAndroidScreen {
    public AndroidOrderSuccessScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@content-desc='checkoutSuccessScreen_deliveryDateTime_value']")
    WebElement deliveryDate;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='checkoutSuccessScreen_title']")
    WebElement orderCompletedSuccessfullyTxt;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Your') and contains(@text, 'points are on the way!')]")
    WebElement breadfastRewardBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='checkoutSuccessScreen_backBtn']")
    WebElement backBtn;

    String deliveryDateTimeContainerContentDescription = "checkoutSuccessScreen_deliveryDateTime_labelContainer";

    String scrollableContentContainerUiSelector =
            "//android.widget.ScrollView[@content-desc='checkoutSuccessScreen_scrollableContentContainer']";

    String trackYourOrderBtnUiSelector = "//android.view.ViewGroup[@content-desc='%s']";

    String trackYourOrderBtnContentDescription = "checkoutSuccessScreen_trackYourOrderBtn";

    public boolean isPageDisplayed() {
        return isElementDisplayed(orderCompletedSuccessfullyTxt);
    }

    public void pressBackBtn()  {
          wait.until(ExpectedConditions.elementToBeClickable(backBtn))
                .click();
    }

    public void pressBreadfastRewardBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(breadfastRewardBtn))
                .click();
    }

    public String getDeliveryDateValue(){
        return String.valueOf(wait.until(ExpectedConditions.visibilityOf(deliveryDate))
                .getText());
    }

    public String getDeliveryDateTimeContainerContentDescription() {
        return deliveryDateTimeContainerContentDescription;
    }

    public String getTrackYourOrderBtnContentDescription(){
        return trackYourOrderBtnContentDescription;
    }

    public WebElement getTrackYourOrderBtn(){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(trackYourOrderBtnUiSelector, getTrackYourOrderBtnContentDescription()))));
    }

    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerUiSelector)));
        } catch (Exception e){
            return null;
        }

        }

}
