package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidCountriesListScreen extends BaseAndroidScreen {
    public AndroidCountriesListScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.EditText[@text='Enter country name']")
    WebElement countrySearchTextField;

    public String countrySelector = "//android.widget.TextView[contains(@text, '%s')]";

    public void searchForCountry(String countryCode){
        wait.until(ExpectedConditions.elementToBeClickable(countrySearchTextField))
                .sendKeys(countryCode);
    }

    public void selectCountry(String countryCode){
        WebElement country = wait.until(ExpectedConditions
                .visibilityOfElementLocated(By.xpath(String.format(countrySelector, countryCode))));
        wait.until(ExpectedConditions.elementToBeClickable(country)).click();
        wait.until(ExpectedConditions.invisibilityOf(countrySearchTextField));
    }

    public boolean isCountriesListScreenDisplayed(){
        return isElementDisplayed(countrySearchTextField);
    }

}
