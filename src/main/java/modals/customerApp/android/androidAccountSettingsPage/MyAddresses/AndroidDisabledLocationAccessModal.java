package modals.customerApp.android.androidAccountSettingsPage.MyAddresses;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidDisabledLocationAccessModal extends BaseAndroidScreen {
    public AndroidDisabledLocationAccessModal(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.Button[@resource-id=\"android:id/button2\"]")
    WebElement locationProceedButton;

    public void dismissDisabledLocationAccessModal() {
        if (isLocationProceedBtnDisplayed()) {
            wait.until(ExpectedConditions.visibilityOf(locationProceedButton))
                    .click();
            try {
                wait.until(ExpectedConditions.invisibilityOf(locationProceedButton));
            } catch (Exception e) {
                //catch exception if exists
            }
        }
    }
    public boolean isLocationProceedBtnDisplayed() {
        return isElementDisplayed(locationProceedButton);
    }}
