package modals.customerApp.android.androidAccountSettingsPage.MyAddresses;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class AndroidMyAddressesScreen extends BaseAndroidScreen {
    public AndroidMyAddressesScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"ADD NEW ADDRESS\"]")
    WebElement addNewAddressButton;
    @FindBy(xpath = "//android.widget.TextView[@text=\"My Addresses\"]")
    WebElement myAddressesHeader;
    String addressRowSelector = " //android.widget.ScrollView[@content-desc='scrollableContentContainer']/android.view.ViewGroup/android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup[%s]";
    @FindBy(xpath = "//android.widget.ScrollView[@content-desc='scrollableContentContainer']/android.view.ViewGroup/android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup[\"1\"]")
    List<WebElement> addressesList;

    public boolean isNewAddressBtnDisplayed() {
        return isElementDisplayed(addNewAddressButton);
    }

    public void pressAddNewAddressButton() {

        wait.until(ExpectedConditions.elementToBeClickable(addNewAddressButton)).click();
    }

    public boolean isMyAddressesHeaderDisplayed() {
        return isElementDisplayed(myAddressesHeader);
    }

    public void clickAddressRecord(int index) {
        wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath((String.format(addressRowSelector, index))))).click();
    }

    public int getAddressesCount() {
        return wait.until(ExpectedConditions.visibilityOfAllElements(addressesList)).size();
    }
}
