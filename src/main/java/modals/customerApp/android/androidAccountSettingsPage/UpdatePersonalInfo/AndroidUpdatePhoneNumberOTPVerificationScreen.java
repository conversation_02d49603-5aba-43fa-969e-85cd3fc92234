package modals.customerApp.android.androidAccountSettingsPage.UpdatePersonalInfo;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidUpdatePhoneNumberOTPVerificationScreen extends BaseAndroidScreen {

    public AndroidUpdatePhoneNumberOTPVerificationScreen (AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.widget.EditText[@index='2']")
    WebElement otpField;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"Update Phone Number\"]/android.widget.TextView")
    WebElement updatePhoneNumber;

    public WebElement getOtpTxtField(){
        return otpField;
    }

    public void pressUpdatePhoneNumber(){
        wait.until(ExpectedConditions.elementToBeClickable(updatePhoneNumber)).click();
    }
}
