package modals.customerApp.android.androidAccountSettingsPage.UpdatePersonalInfo;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidUpdateAccountSettingScreen extends BaseAndroidScreen {
    public AndroidUpdateAccountSettingScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "(//android.widget.EditText)[1]")
    WebElement firstNameTxtField;

    @FindBy(xpath = "(//android.widget.EditText)[2]")
    WebElement lastNameTxtField;

    @FindBy(xpath = "(//android.widget.EditText)[3]")
    WebElement phoneNumberTxtField;

    @FindBy(xpath = "//android.view.ViewGroup[@index='5']")
    WebElement saveChangesBtn;

    public void enterFirstName(String updatedFirstName) {
        wait.until(ExpectedConditions.elementToBeClickable(firstNameTxtField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(firstNameTxtField)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(firstNameTxtField)).sendKeys(updatedFirstName);
        wait.until(ExpectedConditions.textToBePresentInElement(firstNameTxtField, updatedFirstName));
        hideKeyboardIfDisplayed();
    }

    public void enterLastName(String updatedLastName){
        wait.until(ExpectedConditions.elementToBeClickable(lastNameTxtField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(lastNameTxtField)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(lastNameTxtField)).sendKeys(updatedLastName);
        wait.until(ExpectedConditions.textToBePresentInElement(lastNameTxtField, updatedLastName));
        hideKeyboardIfDisplayed();
    }

    public void enterPhoneNumber(String updatedPhoneNumber){
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberTxtField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberTxtField)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberTxtField)).sendKeys(updatedPhoneNumber);
        wait.until(ExpectedConditions.textToBePresentInElement(phoneNumberTxtField, updatedPhoneNumber));
        hideKeyboardIfDisplayed();
    }

    public void pressSaveChangesBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(saveChangesBtn))
                .click();
    }

}
