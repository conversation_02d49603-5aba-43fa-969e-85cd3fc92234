package modals.customerApp.android.androidAccountSettingsPage.androidDeleteAccount;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidDeleteRequestScreen extends BaseAndroidScreen {
    public AndroidDeleteRequestScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Your delete request is sent.']")
    WebElement headerText;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"Close\"]")
    WebElement closeBtn;

    public void clickOnCloseBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(closeBtn))
                .click();
    }

    public boolean isDeleteConfirmationHeaderDisplayed(){
        return isElementDisplayed(headerText);
    }

}
