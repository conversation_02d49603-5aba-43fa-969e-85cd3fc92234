package modals.customerApp.android.androidAccountSettingsPage.androidDeleteAccount;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidDeleteAccountScreen extends BaseAndroidScreen {
    public AndroidDeleteAccountScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@text='Delete Account']")
    WebElement deleteAccountPageTitle;

    @FindBy(xpath = "//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup[6]")
    WebElement firstReasonDisplayedInTheList;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"Continue\"]")
    WebElement continueBtn;

    String deleteAccountReasonsScrollableContentContainerSelector = "//android.widget.ScrollView";

    String continueBtnContentDescription = "Continue";

    public boolean isPageDisplayed(){
        return isElementDisplayed(deleteAccountPageTitle);
    }

    public void selectFirstReasonDisplayedInTheReasonsList(){
        wait.until(ExpectedConditions.elementToBeClickable(firstReasonDisplayedInTheList)).click();

    }
    public void pressContinueBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(continueBtn))
                .click();
    }

    public String getContinueBtnContentDescription(){
        return continueBtnContentDescription;
    }

    public WebElement getPageScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(deleteAccountReasonsScrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }
}
