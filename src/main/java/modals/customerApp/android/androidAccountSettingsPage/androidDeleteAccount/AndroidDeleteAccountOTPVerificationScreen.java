package modals.customerApp.android.androidAccountSettingsPage.androidDeleteAccount;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidDeleteAccountOTPVerificationScreen extends BaseAndroidScreen {
    public AndroidDeleteAccountOTPVerificationScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.widget.EditText")
    WebElement otpVerificationField;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='DiffRecep_rad']")
    WebElement acceptTermsAndConditionsCheckBos;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='Delete my account']")
    WebElement deleteAccountBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Resend code']")
    WebElement resendCodeBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='android:id/message']")
    WebElement errorMsgModalText;

    public void pressResendCodeBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(resendCodeBtn)).click();
    }

    public WebElement getOtpTextField(){
        try {
            return wait.until(ExpectedConditions.visibilityOf(otpVerificationField));
        } catch (Exception e){
            return null;
        }
    }

    public void enterOtp(String otp){
        enterValueInTextField(otpVerificationField, otp);
    }

    public void pressTermsAndConditionsCheckbox(){
        wait.until(ExpectedConditions.elementToBeClickable(acceptTermsAndConditionsCheckBos)).click();
    }

    public void pressDeleteAccountBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(deleteAccountBtn)).click();
    }

    public boolean isErrorMsgTextDisplayed(){
        return isElementDisplayed(errorMsgModalText);
    }
}
