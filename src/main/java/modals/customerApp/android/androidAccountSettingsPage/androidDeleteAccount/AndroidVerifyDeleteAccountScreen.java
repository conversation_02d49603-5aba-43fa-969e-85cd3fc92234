package modals.customerApp.android.androidAccountSettingsPage.androidDeleteAccount;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidVerifyDeleteAccountScreen extends BaseAndroidScreen {
    public AndroidVerifyDeleteAccountScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"Verify mobile number\"]")
    WebElement  verifyMobileNumberBtn;

    public void pressVerifyMobileNumberBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(verifyMobileNumberBtn)).click();
    }
}
