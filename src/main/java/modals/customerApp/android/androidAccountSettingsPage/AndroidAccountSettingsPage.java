package modals.customerApp.android.androidAccountSettingsPage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidAccountSettingsPage extends BaseAndroidScreen {
    public AndroidAccountSettingsPage(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Delete Account']")
    WebElement deleteAccountBtn;

     @FindBy(xpath = "//android.view.ViewGroup[@content-desc='SavedCards_txtview']")
     WebElement savedCardBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"UpdatePerInfo_txtview\"]")
    WebElement updatePerInfoBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"Mngaddress_txtview\"]")
    WebElement manageAddressBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"UpdateEmail_txtview\"]")
    WebElement emailAddressBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"SavedCards_txtview\"]")
    WebElement savedCardsBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"ChangeLang_txtview\"]")
    WebElement changeLangBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"Notification_txtview\"]")
    WebElement notificationBtn;

    public void pressDeleteAccountBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(deleteAccountBtn)).click();
    }

    public void pressUpdatePerInfoBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(updatePerInfoBtn)).click();
    }

    public void pressManageAddressBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(manageAddressBtn)).click();
    }

    public void pressEmailAddressBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(emailAddressBtn)).click();
    }

    public void pressSavedCardsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(savedCardsBtn)).click();
    }

    public void pressChangeLangBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(changeLangBtn)).click();
    }

    public void pressNotificationBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(notificationBtn)).click();
    }

    public void pressSavedCardBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(savedCardBtn)).click();
    }
}
