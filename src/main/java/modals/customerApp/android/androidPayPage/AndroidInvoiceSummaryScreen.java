package modals.customerApp.android.androidPayPage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidInvoiceSummaryScreen extends BaseAndroidScreen {
    public AndroidInvoiceSummaryScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Pay Bill']")
    WebElement payBillBtn;
    String payBillBtnContentDescription = "continueBtn";

    String scrollableContentContainerSelector = "//android.widget.ScrollView";

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"userInfo_value\"]")
    WebElement paymentServicePhoneNumber;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"taxAndFees_title\"]")
    WebElement taxAndFeesTitle;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"taxAndFees_value\"]")
    WebElement taxAndFessValue;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"billAmount_title\"]")
    WebElement billAmountTitle;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"billAmount_value\"]")
    WebElement billAmountValue;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"totalAmount_title\"]")
    WebElement totalTitle;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"totalAmount_value\"]")
    WebElement totalValue;

    @FindBy(xpath = "(//android.widget.TextView[@content-desc=\"undefined_title\"])[1]")
    WebElement balanceTitle;

    @FindBy(xpath = "(//android.widget.TextView[@content-desc=\"undefined_value\"])[1]")
    WebElement balanceUsedValue;

    @FindBy(xpath = "(//android.widget.TextView[@content-desc=\"undefined_title\"])[2]")
    WebElement grandTotal;

    @FindBy(xpath = "(//android.widget.TextView[@content-desc=\"undefined_value\"])[2]")
    WebElement grandTotalValue;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"useMyBalanceTitle\"]")
    WebElement useMyBalance;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"useMyBalance_currentBalanceValue\"]")
    WebElement userCurrentBalance;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"useMyBalance_infoToolTip\"]")
    WebElement balanceTooltip;

    @FindBy(xpath = "//android.widget.Switch[@content-desc=\"useMyBalanceToggleBtn\"]")
    WebElement useBalanceToggle;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"continueBtn\"]")
    WebElement continueBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"cardMiddleRow_contentContainer\"]")
    WebElement cardContainer;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"backBtn\"]")
    WebElement backBtn;

    public void pressPayBillBtn() {
        wait.until(ExpectedConditions.visibilityOf(payBillBtn)).click();
    }

    public String getPayBtnContentDescription() {
        return payBillBtnContentDescription;
    }

    public boolean IsTotalDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(totalTitle));
        return true;
    }

    public boolean IsTaxAndFeesRowDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(taxAndFeesTitle));
        return true;
    }

    public Double getBillAmount() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(billAmountValue))
                .getText().replace("EGP ", ""));
    }

    // This is the used balance when user enables use my balance toggle
    public Double getBalanceUsedValue() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(balanceUsedValue))
                .getText().replace("EGP ", ""));
    }

    public Double getTotalValue() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(totalValue))
                .getText().replace("EGP ", ""));
    }

    public boolean isGrandTotalDisplayed() {
        try {
            wait.until(ExpectedConditions.visibilityOf(grandTotal));
            return true; // Grand total is displayed
        } catch (TimeoutException e) {
            return false; // Grand total is not displayed
        }
    }

    public Double getGrandTotalValue() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(grandTotalValue))
                .getText().replace("EGP ", ""));
    }

    // This is the current balance of the user whether he used it or not
    public Double getCurrentBalanceValue() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(userCurrentBalance))
                .getText().replace("EGP ", ""));
    }

    public boolean checkBalanceSection() {
        return isElementDisplayed(balanceTitle);
    }

    public boolean isCurrentBalanceSectionDisplayed() {
        return isElementDisplayed(userCurrentBalance);
    }

    public WebElement getScrollableContentContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e) {
            return null;
        }
    }

    public boolean isBalanceToggleON() {
        try {
            return  wait.until(ExpectedConditions.visibilityOf(useBalanceToggle)).getAttribute("checked")
                    .equalsIgnoreCase("true");
        } catch (Exception e){
            return false;
        }
    }

    public void pressBalanceToggle() {
        wait.until(ExpectedConditions.visibilityOf(useBalanceToggle))
                .click();
    }
}
