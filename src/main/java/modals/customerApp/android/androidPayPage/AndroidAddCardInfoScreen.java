
package modals.customerApp.android.androidPayPage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidAddCardInfoScreen  extends BaseAndroidScreen {
    public AndroidAddCardInfoScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addCardForm_cardNumber_inputField']")
    WebElement cardNumberTxtField;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addCardForm_expDate_inputField']")
    WebElement expiryDateTxtField;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addCardForm_cvc_inputField']")
    WebElement cvvTxtField;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addCardForm_cardHolderName_inputField']")
    WebElement cardHolderNameTxtField;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addCardForm_email_inputField']")
    WebElement emailTxtField;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='addCardForm_cardLabel_inputField']")
    WebElement cardLabelTxtField;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='addCardForm_submitBtn']")
    WebElement addCardBtn;

    public boolean isPageDisplayed(){
        return isElementDisplayed(cardNumberTxtField);
    }

    public void enterCardNumber(String testCreditCard) {
        enterValueInTextField(cardNumberTxtField, testCreditCard);
    }

    public void enterExpiryDate(String testExpiryDate) {
        enterValueInTextField(expiryDateTxtField, testExpiryDate);
    }

    public void enterCvv(String testCVC) {
        enterValueInTextField(cvvTxtField, testCVC);
    }

    public void enterEmailIfPresent(String email) {
        try {
            enterValueInTextField(emailTxtField, email);
        } catch (Exception e){
            // Do nothing
        }
    }

    public void enterCardHolderName(String cardHolderName) {
        enterValueInTextField(cardHolderNameTxtField, cardHolderName);
    }

    public void enterCardLabel(String cardLabel) {
        enterValueInTextField(cardLabelTxtField, cardLabel);
    }

    public void pressAddCardBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(addCardBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(addCardBtn));
    }

    public void fillCardInfoForm(String cardNumber, String cardExpiryDate, String cardCvv, String email
            , String cardHolderName){
        enterCardNumber(cardNumber);
        enterExpiryDate(cardExpiryDate);
        enterCvv(cardCvv);
        enterCardHolderName(cardHolderName);
        enterEmailIfPresent(email);
    }
}
