package modals.customerApp.android.androidPayPage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidPayScreen extends BaseAndroidScreen
{
    public AndroidPayScreen (AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Payment Services']")
    WebElement paymentServicesHeader;

    @FindBy(xpath = "//android.widget.TextView[@text='Mobile']")
    WebElement mobileCategory;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='savedCardsBtn_icon']")
    WebElement savedCardIcon;

    String categoryContentDescription = "paymentCategory_%s_btn";

    String categoryUiSelector = "//android.view.ViewGroup[@content-desc='%s']";

    public boolean isPageDisplayed()
    {
        return isElementDisplayed(paymentServicesHeader);
    }

    public void selectMobilePaymentCategory()
    {
        wait.until(ExpectedConditions.elementToBeClickable(mobileCategory)).click();
    }
    public void pressSavedCardIcon(){
        wait.until(ExpectedConditions.elementToBeClickable(savedCardIcon)).click();

    }

    public String getCategoryContentDescription(int categoryId){
        return String.format(categoryContentDescription, categoryId);
    }

    public String getCategoryUiSelector(int categoryId){
        return String.format(categoryUiSelector, getCategoryContentDescription(categoryId));
    }
    public WebElement getCategoryUiElement(int categoryID){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getCategoryUiSelector(categoryID))));
    }
    public void pressCategory(int categoryId){
        WebElement sc = getCategoryUiElement(categoryId);
        if (sc != null){
            wait.until(ExpectedConditions.visibilityOf(sc)).click();
        }
    }
}

