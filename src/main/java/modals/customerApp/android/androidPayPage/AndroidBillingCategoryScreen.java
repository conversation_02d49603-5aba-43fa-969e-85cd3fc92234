package modals.customerApp.android.androidPayPage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidBillingCategoryScreen extends BaseAndroidScreen {
    public AndroidBillingCategoryScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    String providerContentDescription = "provider_%s";

    String providerUiSelector = "//android.view.ViewGroup[@content-desc='%s']";

    String pageTitleFullXpath = "//android.widget.TextView[@content-desc='serviceCategory_%s_title']";

    String providerFullXpath = "//android.widget.TextView[@content-desc='provider_%s_title']";

    public boolean isPageDisplayed(String categoryId)
    {
        WebElement pageTitle = androidDriver.findElement(By.xpath(String.format(pageTitleFullXpath, categoryId)));
        wait.until(ExpectedConditions.visibilityOf(pageTitle));
        return isElementDisplayed(pageTitle);
    }

    public boolean isProviderDisplayed(String providerId)
    {
        WebElement provider = androidDriver.findElement(By.xpath(String.format(providerFullXpath, providerId)));
        return isElementDisplayed(provider);
    }

    public void selectProvider(String providerId)
    {
        WebElement provider = androidDriver.findElement(By.xpath(String.format(providerFullXpath, providerId)));
        if(isProviderDisplayed(providerId)) {
            wait.until(ExpectedConditions.elementToBeClickable(provider)).click();
        }
    }

    public String getProviderContentDescription(int providerId){
        return String.format(providerContentDescription, providerId);
    }

    public String getProviderUiSelector(int providerId){
        return String.format(providerUiSelector, getProviderContentDescription(providerId));
    }
    public WebElement getProviderUiElement(int providerId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getProviderUiSelector(providerId))));
    }
    public void pressProvider(int providerId){
        WebElement sc = getProviderUiElement(providerId);
        if (sc != null){
            wait.until(ExpectedConditions.visibilityOf(sc)).click();
        }
    }
}
