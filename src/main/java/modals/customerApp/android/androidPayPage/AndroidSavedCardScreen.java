package modals.customerApp.android.androidPayPage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidSavedCardScreen extends BaseAndroidScreen {
    public AndroidSavedCardScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "(//android.widget.TextView[@text='Saved cards'])[2]")
    WebElement savedCardHeader;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='savedCards_addCard_btn']")
    WebElement addCardButton;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='savedCards_addPaymentMethodBtn']")
    WebElement plusButton;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='savedCards_backBtn']")
    WebElement backButton;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='removeBtn']")
    WebElement removeCardButton;

    String savedCardRowSelector = "//android.view.ViewGroup[@content-desc='%s']";

    String savedCardRowContentDescription = "savedCards_card_%s_rowContainer";

    String savedCardActionsBtnUiSelector = "//android.view.ViewGroup[@content-desc='%s']";

    String savedCardActionsBtnContentDescription = "card_%s_actionsBtn";

    String savedCardLabelUiSelector = "//android.widget.TextView[@content-desc='%s']";

    String savedCardLabelContentDescription = "card_%s_label";

    String confirmSavedCardDeletionBtn = "//android.view.ViewGroup[@content-desc='card_%s_confirmBtn']";

    String cardLabelSaveChangesBtn = "//android.view.ViewGroup[@content-desc='editLabelBottomSheet_card_%s_submitBtn']";

    @FindBy(xpath = "//android.widget.TextView[@content-desc='editBtnText']")
    WebElement editCardLabelIcon;

    @FindBy(xpath = "//android.widget.EditText[@content-desc='editLabelBottomSheet_txtField']")
    WebElement editCardLabeltxtField;

    public boolean isSavedCardPageDisplayed()
    {
        return isElementDisplayed(savedCardHeader);
    }

    public void pressAddCardButton()
    {
        wait.until(ExpectedConditions.elementToBeClickable(addCardButton)).click();
    }
    public boolean isPressAddCardButtonDisplayed()
    {
        return isElementDisplayed(addCardButton);
    }
    public void pressPlusBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(plusButton))
                .click();
    }

    public void pressBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backButton))
            .click();
    }

    public void pressRemoveCardButton(){
        wait.until(ExpectedConditions.elementToBeClickable(removeCardButton))
                .click();
    }
    public String getSavedCardRowContentDescription(String cardLast4){
        return String.format(savedCardRowContentDescription, cardLast4);
    }

    public String getSavedCardRowContainerUiSelector(String cardLast4){
        return String.format(savedCardRowSelector, getSavedCardRowContentDescription(cardLast4));
    }

    public WebElement getSavedCardRowUiElement(String cardLast4){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getSavedCardRowContainerUiSelector(cardLast4))));
    }

    public boolean isCardRowDisplayed(String cardLast4){
        return isElementDisplayed(getSavedCardRowUiElement(cardLast4));
    }

    public String getSavedCardActionsBtnContentDescription(String cardLast4){
        return String.format(savedCardActionsBtnContentDescription, cardLast4);
    }

    public String getSavedCardActionsBtnUiSelector(String cardLast4){
        return String.format(savedCardActionsBtnUiSelector, getSavedCardActionsBtnContentDescription(cardLast4));
    }

    public WebElement getSavedCardActionsBtnUiElement(String cardLast4){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getSavedCardActionsBtnUiSelector(cardLast4))));
    }

    public String getSavedCardLabelContentDescription(String cardLast4){
        return String.format(savedCardLabelContentDescription, cardLast4);
    }

    public String getSavedCardUiSelector(String cardLast4){
        return String.format(savedCardLabelUiSelector, getSavedCardLabelContentDescription(cardLast4));
    }

    public WebElement getSavedCardLabelUiElement(String cardLast4){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(getSavedCardUiSelector(cardLast4))));
    }

    public String getSavedCardCurrentLabelTxt(String cardLast4){
        return getSavedCardLabelUiElement(cardLast4).getText();
    }
    public boolean isEditLabelCardBtnDisplayed() {return isElementDisplayed(editCardLabelIcon);}
    public void pressEditLabelCardButton(){
        wait.until(ExpectedConditions.elementToBeClickable(editCardLabelIcon))
                .click();
    }
    public void enterCardLabelForSavedCard(String cardLabel) {
        enterValueInTextField(editCardLabeltxtField, cardLabel);
        hideKeyboardIfDisplayed();
    }
    public String getEditedLabelSavedCardContentDescription(String cardLast4){
        return String.format(cardLabelSaveChangesBtn, cardLast4);
    }
    public void clickSavedCardSubmitBtn(String cardLast4){
        wait.until(ExpectedConditions.elementToBeClickable(By.xpath(getEditedLabelSavedCardContentDescription(cardLast4)))).click();
    }
    public String getDeleteCardContentDescription(String cardLast4) {
        return String.format(confirmSavedCardDeletionBtn, cardLast4);
    }
    public boolean isConfirmDeleteCardBtnDisplayed(String cardLast4) {
        return isElementDisplayed(wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(getDeleteCardContentDescription(cardLast4)))));
    }
    public void confirmDeleteSavedCard(String cardLast4) {
        wait.until(ExpectedConditions.elementToBeClickable(By.xpath(getDeleteCardContentDescription(cardLast4)))).click();
    }
}
