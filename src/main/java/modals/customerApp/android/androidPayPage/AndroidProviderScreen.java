package modals.customerApp.android.androidPayPage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidProviderScreen extends BaseAndroidScreen {
    public AndroidProviderScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    String serviceTypeContentDescription = "provider_%s_serviceType_%s";

    String serviceTypeUiSelector = "//android.view.ViewGroup[@content-desc='%s']";

    String mobileNumberInputFieldContentDescription = "service_%s_inputParam_%s_textField";

    String mobileNumberInputFieldUiSelector = "//android.widget.EditText[@content-desc='%s']";

    String nextBtnContentDescription = "provider_%s_service_%s_continueBtn";

    String nextBtnUiSelector = "//android.view.ViewGroup[@content-desc='%s']";

    String pageTitleFullXpath = "//android.widget.TextView[@content-desc='provider_%s_title']";

    String serviceListTitleFullXpath =
            "//android.widget.TextView[@content-desc='provider_serviceTypesList_%s_headerTitle']";

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='provider_serviceTypesList_closeBtn']")
    WebElement servicesListCloseBtn;

    String serviceFullXpath = "//android.view.ViewGroup[@content-desc='provider_%s_serviceType_%s']";

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='selectServiceType_dropDownContainer']")
    WebElement serviceTypeDropDownContainer;

    public boolean isPageDisplayed(String providerId) {
        WebElement pageTitle = androidDriver.findElement(By.xpath(String.format(pageTitleFullXpath, providerId)));
        return isElementDisplayed(pageTitle);
    }

    public void openServiceList(){
        wait.until(ExpectedConditions.elementToBeClickable(serviceTypeDropDownContainer))
                .click();
    }

    public boolean isServicesListDropDownDisplayed(){
        return isElementDisplayed(servicesListCloseBtn);
    }

    public String getServiceTypeContentDescription(int providerId, int serviceTypeId){
        return String.format(serviceTypeContentDescription, providerId, serviceTypeId);
    }

    public String getServiceTypeUiSelector( int providerId, int serviceTypeId){
        return String.format(serviceTypeUiSelector, getServiceTypeContentDescription(providerId,serviceTypeId));
    }

    public WebElement getServiceTypeUiElement(int providerId, int serviceTypeId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getServiceTypeUiSelector(providerId,serviceTypeId))));
    }

    public void selectServiceType(int providerId, int serviceTypeId){
        while(isServicesListDropDownDisplayed()){
            try {
                wait.until(ExpectedConditions.visibilityOf(getServiceTypeUiElement(providerId,serviceTypeId))).click();
            } catch (Exception e){
                // do nothing
            }
        }
    }

    public String getMobileNumberInputFieldContentDescription(int serviceId, int inputParamId){
        return String.format(mobileNumberInputFieldContentDescription, serviceId, inputParamId);
    }

    public String getMobileNumberInputFieldUiSelector( int serviceId, int inputParamId){
        return String.format(mobileNumberInputFieldUiSelector,
                getMobileNumberInputFieldContentDescription(serviceId, inputParamId));
    }

    public WebElement getMobileNumberInputFieldUiElement(int serviceId, int inputParamId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getMobileNumberInputFieldUiSelector(serviceId,inputParamId))));
    }

    public void enterMobileNumber(int serviceId, int inputParamId, String mobileNumber){
        wait.until(ExpectedConditions.visibilityOf(getMobileNumberInputFieldUiElement(serviceId,inputParamId))).click();
        wait.until(ExpectedConditions.visibilityOf(getMobileNumberInputFieldUiElement(serviceId,inputParamId)))
                .sendKeys(mobileNumber);
        hideKeyboardIfDisplayed();
    }

    public String getNextBtnContentDescription(int providerId, int serviceTypeId){
        return String.format(nextBtnContentDescription, providerId, serviceTypeId);
    }

    public String getNextBtnUiSelector( int providerId, int serviceTypeId){
        return String.format(nextBtnUiSelector, getNextBtnContentDescription(providerId,serviceTypeId));
    }

    public WebElement getNextBtnUiElement(int providerId, int serviceTypeId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getNextBtnUiSelector(providerId,serviceTypeId))));
    }

    public void pressNextBtn (int providerId, int serviceTypeId){
        wait.until(ExpectedConditions.elementToBeClickable(getNextBtnUiElement(providerId,serviceTypeId))).click();
    }
}
