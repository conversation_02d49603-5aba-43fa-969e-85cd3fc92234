package modals.customerApp.android.androidPayPage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidPaymentSuccessScreen extends BaseAndroidScreen
{
    public AndroidPaymentSuccessScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@content-desc='paymentSuccessModal_modalTitle']")
    WebElement pageTitle;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='serviceTitle_value']")
    WebElement serviceName;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='transactionDateTime_value']")
    WebElement paymentDateTime;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='amountPaid_value']")
    WebElement paymentAmount;

    @FindBy(xpath = "//android.widget.TextView[@text='Done']")
    WebElement doneBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='transactionId_value']")
    WebElement transactionId;

    String payBillBtnContentDescription ="continueBtn";

    String scrollableContentContainerSelector = "//android.widget.ScrollView";

    @FindBy (xpath = "//android.widget.TextView[@content-desc=\"serviceTitle_title\"]")
    WebElement serviceTitle;

    @FindBy (xpath = "//android.widget.TextView[@content-desc=\"serviceTitle_value\"]")
    WebElement serviceValue;

    @FindBy (xpath = "//android.widget.TextView[@content-desc=\"transactionId_title\"]")
    WebElement trxIDTitle;

    @FindBy (xpath = "//android.widget.TextView[@content-desc=\"transactionId_value\"]")
    WebElement trxID;

    @FindBy (xpath = "//android.widget.TextView[@content-desc=\"amountPaid_title\"]")
    WebElement totalPaidTitle;

    @FindBy (xpath = "//android.widget.TextView[@content-desc=\"amountPaid_value\"]")
    WebElement totalPaidValue;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public String getTransactionDateTime(){
        return wait.until(ExpectedConditions.visibilityOf(paymentDateTime))
                .getText();
    }

    public void pressDoneBtn()
    {
        wait.until(ExpectedConditions.visibilityOf(doneBtn)).click();
    }

    public String getTransactionId(){
        return wait.until(ExpectedConditions.visibilityOf(transactionId))
                .getText();
    }

    public String getDoneContentDescription() {
        return payBillBtnContentDescription;
    }

    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public String getPaymentCurrency(){
        return wait.until(ExpectedConditions.visibilityOf(paymentAmount)).getText().split(" ")[0];
    }

    public String getPaymentAmount(){
        return wait.until(ExpectedConditions.visibilityOf(paymentAmount)).getText().split(" ")[1];
    }

    public void getPaidService ()
    {
        wait.until(ExpectedConditions.visibilityOf(serviceValue)).getText();
    }

    public boolean IsTrxIdDisplayed()
    {
        wait.until(ExpectedConditions.visibilityOf(trxIDTitle));
        return true;
    }

    public void getTrxIdValue ()
    {
        wait.until(ExpectedConditions.visibilityOf(trxID)).getText();
    }

    public Double getTotalPaidValue() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(totalPaidValue))
                .getText().replace("EGP ", ""));
    }

    public boolean IsDoneBtnDisplayed ()
    {
        wait.until(ExpectedConditions.visibilityOf(doneBtn));
        return true;
    }
}
