package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import models.TestData;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidCreateAccountScreen extends BaseAndroidScreen {
    public AndroidCreateAccountScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Let’s finish up your account')]")
    WebElement topHeader;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Add your basic information to start shopping!')]")
    WebElement subHeader;

    @FindBy(xpath = "//android.view.ViewGroup[@index='2']//android.widget.EditText[@index='0']")
    WebElement firstNameField;

    @FindBy(xpath = "//android.view.ViewGroup[@index='3']//android.widget.EditText")
    WebElement lastNameField;

    @FindBy(xpath = "//android.view.ViewGroup[@index='4']//android.widget.EditText")
    WebElement emailField;

    @FindBy(xpath = "//android.view.ViewGroup[@index='5']//android.widget.EditText")
    WebElement referralCodeField;

    @FindBy(xpath = "//android.widget.TextView[@text='Create Account']")
    WebElement submitBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='This invitation code is invalid.']")
    WebElement invalidInvitationCodeError;

    @FindBy(xpath="//android.view.ViewGroup[@index='5']//android.view.ViewGroup[@index='2']")
    WebElement validInvitationCodeCheckMark;

    @FindBy(xpath = "(//android.widget.TextView[@text='This invitation code is invalid.'])[2]")
    WebElement invalidInvitationCodeToast;

    public boolean isTopHeaderDisplayed(){
        return isElementDisplayed(topHeader);
    }

    public boolean isSubHeaderDisplayed(){
        return isElementDisplayed(subHeader);
    }

    // A method to press on an element that should remove the focus from the text field
    public void unFocusFromInputField(){
        wait.until(ExpectedConditions.elementToBeClickable(topHeader))
                .click();
    }

    public void enterFirstName(String firstName){
        wait.until(ExpectedConditions.visibilityOf(firstNameField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(firstNameField)).sendKeys(firstName);
        wait.until(ExpectedConditions.textToBePresentInElement(firstNameField, firstName));
        unFocusFromInputField();
    }

    public void enterLastName(String lastName){
        wait.until(ExpectedConditions.visibilityOf(lastNameField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(lastNameField)).sendKeys(lastName);
        wait.until(ExpectedConditions.textToBePresentInElement(lastNameField, lastName));
        unFocusFromInputField();
    }

    public void enterEmail(String email){
        wait.until(ExpectedConditions.visibilityOf(emailField)).click();
        wait.until(ExpectedConditions.visibilityOf(emailField)).sendKeys(email);
        wait.until(ExpectedConditions.textToBePresentInElement(emailField, email));
        unFocusFromInputField();
    }

    public void enterReferralCode(String referralCode){
        wait.until(ExpectedConditions.visibilityOf(referralCodeField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(referralCodeField)).sendKeys(referralCode);
        wait.until(ExpectedConditions.textToBePresentInElement(referralCodeField, referralCode));
        unFocusFromInputField();
    }

    public void pressSubmitBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(submitBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(submitBtn));
    }

    public void fillInAccountInformationForm(TestData testData){
        enterFirstName(testData.getRandomTestUser().getFirstName());
        enterLastName(testData.getRandomTestUser().getLastName());
        enterEmail(testData.getRandomTestUser().getEmailAddress());

        hideKeyboardIfDisplayed();
    }

    public void fillInAccountInformationFormWithAdminReferralCode(TestData testData , String referralCode){
        enterFirstName(testData.getRandomTestUser().getFirstName());
        enterLastName(testData.getRandomTestUser().getLastName());
        enterEmail(testData.getRandomTestUser().getEmailAddress());
        enterReferralCode(referralCode);

        unFocusFromInputField();
        hideKeyboardIfDisplayed();
    }

    public boolean isInvalidInvitationCodeErrorDisplayed(){
        return isElementDisplayed(invalidInvitationCodeError);
    }

    public boolean isValidInvitationCodeCheckMarkDisplayed(){
        return isElementDisplayed(validInvitationCodeCheckMark);
    }

    public boolean isInvalidInvitationCodeToastDisplayed(){
        return isElementDisplayed(invalidInvitationCodeToast);
    }

    public void clearReferralCodeField(){
        wait.until(ExpectedConditions.visibilityOf(referralCodeField)).click();
        wait.until(ExpectedConditions.visibilityOf(referralCodeField)).clear();
    }
}
