package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidLandingScreen extends BaseAndroidScreen {
    public AndroidLandingScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Explore Breadfast']")
    WebElement exploreBtn;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Log in or sign up')]")
    WebElement authHyperLink;

    public void pressExploreBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(exploreBtn))
                .click();
    }

    public void pressAuthHyperLink(){
        wait.until(ExpectedConditions.elementToBeClickable(authHyperLink))
                .click();
    }

    public Boolean isPageDisplayed() {
        return isElementDisplayed(exploreBtn);
    }
}
