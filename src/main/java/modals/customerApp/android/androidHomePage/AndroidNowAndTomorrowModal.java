package modals.customerApp.android.androidHomePage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidNowAndTomorrowModal extends BaseAndroidScreen {
    public AndroidNowAndTomorrowModal(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.widget.TextView[@text='Tomorrow']")
    WebElement tomorrowBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Now']")
    WebElement nowBtn;

    public boolean isModalDisplayed(){
        return isElementDisplayed(tomorrowBtn) && isElementDisplayed(nowBtn);
    }

    public void changeServeType(String serveType){
        if (isModalDisplayed()){
            WebElement targetBtn = null;
            switch (serveType.toLowerCase()){
                case "now" -> targetBtn = nowBtn;
                case "tomorrow", "later" -> targetBtn = tomorrowBtn;
            }

            wait.until(ExpectedConditions.elementToBeClickable(targetBtn)).click();
            wait.until(ExpectedConditions.invisibilityOf(targetBtn));
        }
    }

}
