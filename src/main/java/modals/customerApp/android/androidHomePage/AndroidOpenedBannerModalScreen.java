package modals.customerApp.android.androidHomePage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidOpenedBannerModalScreen extends BaseAndroidScreen {
    public AndroidOpenedBannerModalScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "/hierarchy/android.widget.FrameLayout")
    WebElement copiedBannerPopup;
    @FindBy(id = "android:id/button1")
    WebElement okButtonId;
    @FindBy(xpath = "//android.widget.TextView[@text='Close']")
    WebElement closeButton;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=categoryDetails_header_backBtn]/android.view.ViewGroup")
    WebElement backArrowForBanner;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=payHeaderContainer]")
    WebElement redirectToPayScreenBannerHeader;

    public void clickOkButtonOfRedirectingToCouponBanner() {
        wait.until(ExpectedConditions.elementToBeClickable(okButtonId))
                .click();
    }

    public boolean isClickOkButtonOfRedirectingToCouponBannerPopupDisplayed() {
        return isElementDisplayed(copiedBannerPopup);
    }

    public void clickBackButtonOfRedirectingToCategorySubCategoryBanner() {
        wait.until(ExpectedConditions.elementToBeClickable(backArrowForBanner))
                .click();
    }

    public void clickCloseButtonOfInformativeBanner() {
        wait.until(ExpectedConditions.elementToBeClickable(closeButton))
                .click();
    }

    public boolean isCloseButtonOfInformativeBannerDisplayed() {
        return isElementDisplayed(closeButton);
    }

    //Check which banner type is opened :
    public void getOpenedBannerAction() {

        if (isElementDisplayed(copiedBannerPopup)) {
            clickOkButtonOfRedirectingToCouponBanner();
        } else if (isElementDisplayed(closeButton)) {
            clickCloseButtonOfInformativeBanner();
        } else if (isElementDisplayed(backArrowForBanner)) {
            clickBackButtonOfRedirectingToCategorySubCategoryBanner();

        } else {
            // Handle case where no element is displayed
            throw new IllegalStateException("No banner action needed");
        }

    }
}
