package modals.customerApp.android.androidHomePage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class AndroidReferralScreen extends BaseAndroidScreen {
    public AndroidReferralScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@text='Refer and Earn']")
    WebElement pageTitle;

    @FindBy(xpath = "//android.widget.TextView[@text='Unlock your Referral Program']")
    WebElement newUserRefText;

    @FindBy(xpath = "//android.view.ViewGroup[@index='3' and @package='com.breadfast' " +
            "and not(@text) and not(@clickable) and @enabled='true']")
   WebElement newUserWidget;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Complete\n2 orders')]")
    WebElement progressBarFirstMilestone;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Spend EGP 200+') and contains(@text, 'on Breadfast')]")
    WebElement progressBarSecondMilestone;

    public boolean isRefScreenDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public String getReferralText(){
        return pageTitle.getText();
    }

    public boolean isRefNewUserTextDisplayed(){
        return isElementDisplayed(newUserRefText);
    }

    public String getRefNewUserTextText(){
        return newUserRefText.getText();
    }

    public boolean isRefNewUserWidgetDisplayed(){
        return isElementDisplayed(newUserWidget);
    }

    public boolean isProgressFirstMilestoneDisplayed(){
        return isElementDisplayed(progressBarFirstMilestone);
    }

    public boolean isProgressSecondMilestoneDisplayed(){
        return isElementDisplayed(progressBarSecondMilestone);
    }
}
