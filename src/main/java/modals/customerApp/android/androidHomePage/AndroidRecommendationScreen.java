package modals.customerApp.android.androidHomePage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidRecommendationScreen extends BaseAndroidScreen {
    public AndroidRecommendationScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"recommendations_page_container\"]/android.view.ViewGroup/android.view.ViewGroup[1]/android.view.ViewGroup")
    WebElement backBtn;

    @FindBy(xpath = "//android.widget.TextView[@index='1']")
    WebElement recommendationTxt;

    @FindBy(xpath = "//android.view.ViewGroup[@index='2']")
    WebElement cartIcon;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Top Picks For You \"]")
    WebElement recommendationTitle;

    public boolean isRecommendationDisplayed() {
        return isElementDisplayed(recommendationTxt);
    }

    public boolean isCartIconDisplayed() {
        return isElementDisplayed(cartIcon);
    }

    public void pressBackBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(backBtn)).click();
    }

    public String getRecommendationText() {
        return recommendationTxt.getText();
    }

    public boolean isRecommendationTitleDisplayed() {
        return isElementDisplayed(recommendationTitle);
    }

    public String getRecommendationTitle() {
        return recommendationTitle.getText();
    }
}
