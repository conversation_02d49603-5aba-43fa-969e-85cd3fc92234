package modals.customerApp.android.androidHomePage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidBusyModalScreen extends BaseAndroidScreen {
    public AndroidBusyModalScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text=\"We’re busy\"]")
    WebElement weAreBusyTitle;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Switch to Tomorrow\"]")
    WebElement switchToTomorrowOption;

    @FindBy(xpath = "//android.widget.FrameLayout[@resource-id=\"android:id/content\"]/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup/android.view.ViewGroup")
    WebElement closeIcon;

    public boolean isBusyModalDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(weAreBusyTitle));
        return isElementDisplayed(weAreBusyTitle);
    }

    public void pressSwitchToTomorrowOption() {
        wait.until(ExpectedConditions.elementToBeClickable(switchToTomorrowOption))
                .click();
    }

    public void pressCloseIcon() {
        wait.until(ExpectedConditions.elementToBeClickable(closeIcon))
                .click();
    }
}
