package modals.customerApp.android.androidHomePage;

import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidHomeScreen extends BaseAndroidScreen {
    public AndroidHomeScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Delivering to']")
    WebElement pageHeader;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='Address_label']")
    WebElement addressLabel;
    @FindBy(xpath = "//android.widget.TextView[@text=\"التوصيل إلى\"]")
    WebElement pageHeaderAr;

    @FindBy(xpath = "//android.widget.TextView[@text='Home']")
    WebElement homeTabBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Pay']")
    WebElement payTabBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='More']")
    WebElement moreTabBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Search']")
    WebElement searchTabBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='freshDesk_imgview']")
    WebElement freshChatBtn;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"freshDesk_imgview\"]/android.view.ViewGroup")
    WebElement freshChatBtnWhilePlacedOrder;

    @FindBy(xpath = "//android.widget.Button[@index='1']")
    WebElement viewAllRecBtn;

    @FindBy(xpath = "//android.widget.HorizontalScrollView[@index='2']")
    WebElement recommendationWidget;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='referral_imgview']")
    WebElement referralBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='change_txtView']")
    WebElement nowTomorrowTopBannerChangeBtn;

    @FindBy(xpath = "//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup/android.widget.HorizontalScrollView[1][@index='1']")
            WebElement homeScreenFirstBanner;

    String categorySelector = "//android.view.ViewGroup[@content-desc='%s']";

    String categoryContentDescription = "categoryItem-%s";

    @FindBy(xpath = "//android.widget.ScrollView")
    WebElement homeScreenScrollableContentContainer;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='Address_label']")
    WebElement changeAddressBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='Vote_btn']")
    WebElement voteForThisLocationBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='ChangeLocation_view']")
    WebElement changeLocationBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='product_card_homeRecommendations_0']")
    String recommendedProductCard;

    String homeScreenScrollableContentContainerSelector = "//android.widget.ScrollView";

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"home_recommendations_sectionTitle\"]")
    WebElement recommendationTitle;
    @FindBy(xpath = "//android.widget.TextView[@text=\"\uF3D8\"]")
    WebElement chevronDownBtn;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Tomorrow\"]")
    WebElement tomorrowDeliveryTimePromise;

    @FindBy(xpath = "//android.widget.TextView[@text=\"60 minutes\"]")
    WebElement instantDeliveryTimePromise;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Order placed successfully \"]")
    WebElement miniTrackerOrderPlacedTxt;

    String addressRowSelector = "//android.widget.ScrollView[@content-desc='addressesList']/android.view.ViewGroup/android.view.ViewGroup[%s]";

    String recommendationContentDescription = "home_recommendations_sectionTitle";

    @AndroidFindBy(uiAutomator = "new UiScrollable(new UiSelector().scrollable(true).instance(0)).scrollIntoView(new UiSelector().textContains(\""+"Delivery in"+"\").instance(0))")
    private WebElement scrollingByDeliverInText;

    public boolean isReferralBtnDisplayed() {
        return isElementDisplayed(referralBtn);
    }
    public boolean isHomeScreenFirstBannerDisplayed() { return isElementDisplayed(homeScreenFirstBanner);}
    public boolean pressHomeScreenFirstBanner() {return isElementDisplayed(homeScreenFirstBanner);}

    public void clickFirstBanner(){
        wait.until(ExpectedConditions.visibilityOf(homeScreenFirstBanner)).click();
    }

    public void pressReferralBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(referralBtn))
                .click();
    }

    public void pressRecViewAllBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(viewAllRecBtn))
                .click();
    }

    public boolean isRecProductsDisplayed() {
        return isElementDisplayed(recommendationWidget);
    }

    public boolean isFreshChatBtnDisplayed() {
        return isElementDisplayed(freshChatBtn);
    }

    public boolean isPageHeaderArDisplayed() {
        return isElementDisplayed(pageHeaderAr);
    }

    public void pressFreshChatBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(freshChatBtn))
                .click();
    }
    public void pressFreshChatBtnWhilePlacedOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(freshChatBtnWhilePlacedOrder))
                .click();
    }

    public boolean isHomePageDisplayed() {
        return isElementDisplayed(homeTabBtn)
                && (isElementDisplayed(addressLabel)
                || isAnyUnknownLocationViewElementsDisplayed()
                || isElementDisplayed(nowTomorrowTopBannerChangeBtn));
    }

    public void pressHomeTabBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(homeTabBtn))
                .click();
    }
    public void pressDownArrowBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(chevronDownBtn))
                .click();
    }
    public void pressPayTabBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(payTabBtn))
                .click();
    }

    public void pressMoreTabBtn() {

        wait.until(ExpectedConditions.elementToBeClickable(moreTabBtn))
                .click();
    }

    public void pressSearchTabBtn() {
        while (isElementDisplayed(addressLabel)) {
            wait.until(ExpectedConditions.elementToBeClickable(searchTabBtn))
                    .click();
        }
    }

    public void pressNowTomorrowTopBanner() {
        wait.until(ExpectedConditions.elementToBeClickable(nowTomorrowTopBannerChangeBtn))
                .click();
    }

    public WebElement getHomeScreenScrollableContentContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(homeScreenScrollableContentContainerSelector)));
        } catch (Exception e) {
            return null;
        }
    }

    public String getCategoryContentDescription(int categoryId) {
        return String.format(categoryContentDescription, categoryId);
    }

    public String getCategoryUiSelector(int categoryId) {
        return String.format(categorySelector, getCategoryContentDescription(categoryId));
    }

    public WebElement getCategoryUiElement(int categoryId) {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(getCategoryUiSelector(categoryId))));
        } catch (Exception e) {
            return null;
        }
    }

    public void pressCategory(int categoryId) {
        getCategoryUiElement(categoryId).click();
        wait.until(ExpectedConditions.invisibilityOfElementLocated(By.xpath(getCategoryUiSelector(categoryId))));
    }

    public boolean isCategoryDisplayed(int categoryId) {
        if (getCategoryUiElement(categoryId) == null)
            return false;
        return isElementDisplayed(getCategoryUiElement(categoryId));
    }
    public void pressChangeAddressBtn() {
        wait.until(ExpectedConditions.visibilityOf(changeAddressBtn)).click();
    }

    public boolean isAnyUnknownLocationViewElementsDisplayed() {
        return isElementDisplayed(voteForThisLocationBtn) || isElementDisplayed(changeLocationBtn);
    }

    public void takeActionIfAnyUnknownLocationViewElementsDisplayed(int index) {
        try {
            if (isAnyUnknownLocationViewElementsDisplayed()) {
                {
                    wait.until(ExpectedConditions.visibilityOf(changeLocationBtn)).click();
                    wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath((String.format(addressRowSelector, index))))).click();
                }
                wait.until(ExpectedConditions.invisibilityOf(changeLocationBtn));
            }
        }
        catch(Exception e){
                //Do Nothing
        }
        }
    public String getRecommendationContentDescription() {
        return recommendationContentDescription;
    }

    public boolean isRecommendationDisplay() {
        return isElementDisplayed(recommendationTitle);
    }

    public void pullToRefresh() {
        isElementDisplayed(scrollingByDeliverInText);
    }

    public boolean isTomorrowDeliveryTimeDisplayed() {
        return isElementDisplayed(tomorrowDeliveryTimePromise);
    }

    public boolean isInstantDeliveryTimeDisplayed() {
        return isElementDisplayed(instantDeliveryTimePromise);
    }

    public boolean isMiniTrackerDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(miniTrackerOrderPlacedTxt));
        return isElementDisplayed(miniTrackerOrderPlacedTxt);
    }

}
