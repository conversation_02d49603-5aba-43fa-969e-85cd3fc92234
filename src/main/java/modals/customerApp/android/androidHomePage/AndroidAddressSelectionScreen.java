package modals.customerApp.android.androidHomePage;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidAddressSelectionScreen extends BaseAndroidScreen {
    public AndroidAddressSelectionScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.ScrollView")
    WebElement contentScrollViewContainer;

    @FindBy(xpath = "//android.widget.ScrollView[@content-desc='addressesList']")
    WebElement addressesList;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='add_new_address_container']")
    WebElement addNewAddressBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Select delivery address']")
    WebElement addressSelectionDropdownHeader;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"Close_imgview\"]/android.view.ViewGroup")
    WebElement closeIcon;

    String addNewAddressBtnContentDescription = "add_new_address_container";

    String contentScrollViewContainerSelector = "//android.widget.ScrollView";

    // ------In case Now & Tomorrow Options are available in the Fp --------
    @FindBy(xpath = "//android.widget.TextView[@text='Select delivery options']")
    WebElement selectDeliveryOptionsHeader;

    @FindBy(xpath = "//android.view.ViewGroup[@resource-id=\"selectNowTouchable\"]/android.view.View")
    WebElement nowOption;

    @FindBy(xpath = "//android.view.ViewGroup[@resource-id=\"selectTomorrowTouchable\"]/android.view.View")
    WebElement tomorrowOption;

    @FindBy(xpath = "//android.widget.TextView[@text=\"“Tomorrow” is the only available option.\"]")
    WebElement tomorrowIsOnlyAvailableOptionTxt;

    public WebElement getContentScrollViewContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(contentScrollViewContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public String getAddNewAddressBtnContentDescription() {
        return addNewAddressBtnContentDescription;
    }

    public void pressAddNewAddressBtn(){
        wait.until(ExpectedConditions.visibilityOf(addNewAddressBtn)).click();
    }

    public boolean isDropDownDisplayed(){
        return (isElementDisplayed(addressSelectionDropdownHeader) && isElementDisplayed(contentScrollViewContainer));
    }

    public boolean isAddNewAddressBtnDisplayed(){
        return isElementDisplayed(addNewAddressBtn);
    }

   // ------------------In case Now & Tomorrow Options are available in the Fp -----------------

    public boolean isSelectDeliveryOptionsHeaderIsDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(selectDeliveryOptionsHeader));
        return isElementDisplayed(selectDeliveryOptionsHeader);
    }

    public void pressNowOptionBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(nowOption))
                .click();
    }

    public void pressTomorrowOptionBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(tomorrowOption))
                .click();
    }

    public void pressCloseIcon() {
        wait.until(ExpectedConditions.elementToBeClickable(closeIcon))
                .click();
    }

    public boolean isTomorrowOnlyAvailableOptionTxtDisplayed() {
        wait.until(ExpectedConditions.elementToBeClickable(tomorrowIsOnlyAvailableOptionTxt))
                .click();
        return isElementDisplayed(tomorrowIsOnlyAvailableOptionTxt);
    }

}
