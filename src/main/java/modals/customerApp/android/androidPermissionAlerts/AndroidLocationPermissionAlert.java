package modals.customerApp.android.androidPermissionAlerts;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidLocationPermissionAlert extends BaseAndroidScreen {
    public AndroidLocationPermissionAlert(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(id = "com.android.permissioncontroller:id/grant_dialog")
    WebElement locationPermissionAlert;

    @FindBy(id = "com.android.permissioncontroller:id/permission_location_accuracy_radio_fine")
    WebElement preciseLocationBtn;

    @FindBy(id = "com.android.permissioncontroller:id/permission_location_accuracy_radio_coarse")
    WebElement approximateLocationBtn;

    @FindBy(id = "com.android.permissioncontroller:id/permission_allow_foreground_only_button")
    WebElement allowWhileUsingBtn;

    @FindBy(id = "com.android.permissioncontroller:id/permission_allow_one_time_button")
    WebElement allowOnceBtn;

    @FindBy(id = "com.android.permissioncontroller:id/permission_deny_button")
    WebElement dontAllowBtn;

    public boolean isLocationPermissionAlertDisplayed(){
        return isElementDisplayed(locationPermissionAlert);
    }

    public void pressPreciseLocationBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(preciseLocationBtn)).click();
        wait.until(ExpectedConditions.attributeToBe(preciseLocationBtn, "checked", "true"));
    }

    public void pressApproximateLocationBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(approximateLocationBtn)).click();
        wait.until(ExpectedConditions.attributeToBe(approximateLocationBtn, "checked", "true"));
    }

    public void chooseLocationPrecisionAccess(String option){
        switch (option){
            case "approximate" -> pressApproximateLocationBtn();
            default -> pressPreciseLocationBtn();
        }
    }

    public void pressAllowWhileUsingBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(allowWhileUsingBtn))
                .click();
    }

    public void pressAllowOnceBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(allowOnceBtn))
                .click();
    }

    public void pressDontAllowBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(dontAllowBtn))
                .click();
    }

    public void takeActionIfAlertDisplayed(String action){
        if (isLocationPermissionAlertDisplayed()){
            switch (action){
                case "once" -> pressAllowOnceBtn();
                case "whileUsing" -> pressAllowWhileUsingBtn();
                default -> pressDontAllowBtn();
            }
            wait.until(ExpectedConditions.invisibilityOf(locationPermissionAlert));
        }
    }
}
