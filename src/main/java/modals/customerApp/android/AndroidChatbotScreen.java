package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidChatbotScreen extends BaseAndroidScreen {
    public AndroidChatbotScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    //-----------------------------------Enter message on chat----------------------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='Back']")
    WebElement backBtn;

    @FindBy(xpath = "//android.widget.AutoCompleteTextView[@resource-id='com.breadfast:id/freshchat_conv_detail_reply_text']")
    WebElement textField;

    @FindBy(xpath = "//android.widget.ImageView[@content-desc='Send message']")
    WebElement sendTextInTextField;

    @FindBy(xpath = "//android.widget.Button[@resource-id='com.breadfast:id/freshchat_new_messages_count_btn']")
    WebElement newMessagesBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Chat with us']")
    WebElement chatWithUsText;
    //-----------------------------------ChatbotIHaveAnIssueFlow----------------------------------//
    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'I have an issue with')]")
    WebElement iHaveAnIssueWithOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I have an issue with a processing order']")
    WebElement iHaveAnIssueWithProcessingOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'late']")
    WebElement orderIsLateBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I’d like to edit my order']")
    WebElement editMyOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I’d like to cancel my order']")
    WebElement cancelMyOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Change payment method']")
    WebElement changePaymentMethodBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Something else']")
    WebElement orderSomeThingElseBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Thank you, A customer support agent will be with you shortly']")
    WebElement assignCustomerToAnAgentMsg;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'assigned Delivery')]")
    WebElement assignToDaMsg;

    @FindBy(xpath = "//android.widget.TextView[@text='Phone number']")
    WebElement phoneNumberBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Payment method']")
    WebElement paymentMethodBtn;

    @FindBy(xpath = "//android.widget.TextView[contains(@text,'Ok')]")
    WebElement okBtn;

    @FindBy(xpath = "//android.widget.TextView[contains (@text,'I want to speak to an agent')]")
    WebElement noIWantToSpeakToAnAgentBtn;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'cancel')]")
    WebElement cancelMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Ok, thank you. Order will get canceled shortly.']")
    WebElement yourOrderWillBeCancelledMsg;

    @FindBy(xpath = "//android.widget.TextView[@text='Add a product']")
    WebElement addAProductBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Remove a product']")
    WebElement removeAProductBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Change address']")
    WebElement changeAddressBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I Ordered on a wrong address']")
    WebElement orderWrongAddressBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Wrong Payment Method']")
    WebElement wrongPaymentMethodBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Don't need the order any more']")
    WebElement doNotNeedOrderAnyMoreBtn;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Order will get canceled shortly')]")
    WebElement orderCanceledMsg;

    @FindBy(xpath = "//android.widget.TextView[@text='Yes, cancel my order']")
    WebElement yesCancelMyOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='No, keep my order']")
    WebElement noKeepMyOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Your current order status') and contains(@text, 'as promised')]")
    WebElement yourCurrentOrderStatusChatbotMsg;

    //-------------------------------------------------------------------------------------------//
    @FindBy(xpath = "/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.FrameLayout[2]/android.widget.RelativeLayout/android.widget.RelativeLayout/android.widget.LinearLayout/android.widget.RelativeLayout/androidx.recyclerview.widget.RecyclerView/android.widget.LinearLayout/android.view.ViewGroup/android.widget.TextView[4]")
    WebElement someThingElseBtn;

    //-----------------------------------NonOfTHeAboveSubFlow----------------------------------//

    @FindBy(xpath = "//android.widget.TextView[@text='Please send an <NAME_EMAIL> or visit our website https://www.breadfast.com/careers/']")
    WebElement emailCareer;
    @FindBy(xpath = "//android.widget.TextView[@text='Please send an <NAME_EMAIL>']")
    WebElement emailPartners;
    @FindBy(xpath = "//android.widget.TextView[@text='Something else']")
    WebElement someThingElseBtnNoneOfTheAbove;
    @FindBy(xpath = "//android.widget.TextView[@text='Please type here what you’d like us to help with and an agent will get back to you shortly.']")
    WebElement cxAgentInSomethingElse;
    @FindBy(xpath="//android.widget.TextView[@text='None of these products']")
    WebElement noneOfTheseProducts;
    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Order #')]")
    WebElement chooseRandomOrder;
    @FindBy(xpath = "//android.widget.TextView[@text='I have a suggestion or feedback']")
    WebElement iHaveASuggestionBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I’d like to return a product']")
    WebElement iWouldLikeToReturnAProductBtn;
    @FindBy(xpath = "//android.widget.TextView[@text='Ok, thank you']")
    WebElement OkThankYouBtn;
    @FindBy(xpath = "//android.widget.TextView[@text='Ok, a customer support agent will be with you shortly']")
    WebElement okCustomerSupportAgentWillBeWithYou;

    @FindBy(xpath = "//android.widget.TextView[@text='I received an extra item']")
    WebElement iReceivedAnExtraItemBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Return an item']")
    WebElement returnAnItemBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Work at Breadfast']")
    WebElement iWouldLikeToWorkAtBreadFastBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Sell products on Breadfast']")
    WebElement iWouldLikeToSellAProductBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='None of the above']")
    WebElement nonOfTheAboveBtn;

    @FindBy(xpath="//android.widget.TextView[@text='Ok, we’ll review your request and send a delivery agent shortly to pick up the product(s).']")
    WebElement itDidNotMeetExpectationsAfterNonOfTheAbove;

    @FindBy(xpath = "//android.widget.TextView[@text='Please select the product you are referring to:']")
    WebElement pleaseSelectTheProductYouAreReferringTo;
    @FindBy(xpath ="//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='None of the above']")
    WebElement noneOftheAboveMainFlow;
    //-----------------------------------IHaveSuggestionSubFlow-----------------------------------//
    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Nice, we love to hear suggestions from our customers! :)\"]")
    WebElement weLoveToHearASuggestionFromCustomerMsg;

    @FindBy(xpath = "//android.widget.TextView[@text='I have a suggestion to add a product on Breadfast']")
    WebElement iHaveSuggestionToAddProductAtBreadFastBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I have a general suggestion']")
    WebElement iHaveAGeneralSuggestionBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I’d like to share a positive feedback']")
    WebElement iWouldLikeToShareAPositiveFeedbackBtn;

    //-------------------------------------------IWouldLikeToReturnAProductSubFlow-------------------------//
    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Which product are you referring to?']")
    WebElement whichProductYouAreReferringToMsg;
    @FindBy(xpath = "//android.widget.RelativeLayout[@resource-id='com.breadfast:id/freshchat_drop_down_input_frame']")
    WebElement selectAllThatApplyDropdown;

    @FindBy(xpath = "///android.widget.TextView[@resource-id='com.breadfast:id/freshchat_bottomsheet_unselect_button']")
    WebElement selectAllAndDeselectAllBtn;

    @FindBy(xpath = "(//android.widget.CheckBox[@resource-id='com.breadfast:id/freshchat_selection_checkbox'])[1]")
    WebElement productSelectionCheckBox;

    @FindBy(xpath = "(//android.widget.CheckBox[@resource-id='com.breadfast:id/freshchat_selection_checkbox'])[2]")
    WebElement nonOfThisProductsCheckBox;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='.breadfast:id/drop_down_list_item_text' and @text='Afia Corn Oill (X1)']")
    WebElement productNameSelector;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_bottomsheet_send_button']")
    WebElement sendBtn;

    @FindBy(xpath = "//android.widget.ImageView[@resource-id='com.breadfast:id/freshchat_bottomsheet_close']")
    WebElement closeBtn;

    @FindBy(xpath ="//android.widget.TextView[@text='It didn’t meet my expectations']")
    WebElement chooseReasonToReturnProductDidnotMeetMyExpectations;

    @FindBy(xpath ="//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Please clarify further what was the issue with the product:\"]")
    WebElement clarifyWhatDoesnotMeetExpectations ;

    @FindBy(xpath ="//android.widget.TextView[@text='Quality issue']")
    WebElement chooseReasonToReturnProductQualityIssue;
    @FindBy(xpath ="//android.widget.TextView[@text='I ordered it by mistake']")
    WebElement chooseReasonToReturnProductOrderByMistake;

    @FindBy(xpath ="//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Ok, we’ll create a pickup order shortly.']")
    WebElement isPickUpOrderMessageDisplayed;

    //----------------------------------IHaveSuggestionToAddProductAtBreadFastSubFlow----------------------//
    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Please type the product/s you’d like to add on Breadfast and click send. \n" +
            "\n" +
            "We will forward your message and feedback to our commercial team. \n" +
            "\n" +
            "Thank you for helping us serve you better. :)']")
    WebElement pleaseTypeTheProductYouWouldLikeToAddMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Please, give us more details & an agent will be with you shortly.']")
    WebElement pleaseGiveUsMoreDetailsMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Thank you so much for your suggestions. Is there anything else you’d like to share with us?']")
    WebElement thankYouSoMuchForYourSuggestionMsg;

    //----------------------------------IHaveAProblem/Complaint----------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='I have a problem/complaint']")
    WebElement iHaveAProblemComplaintBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='My delivered order']")
    WebElement myDeliveredOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Making an order']")
    WebElement makingAnOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Balance/Charge issue']")
    WebElement balanceChargeIssueBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Applying a coupon code']")
    WebElement applyingACouponCodeBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Bills & donations payment']")
    WebElement billsDonationsPaymentBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='The Delivery Associate']")
    WebElement theDeliveryAssociateBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you, an agent will get assigned to you shortly\"]")
    WebElement thankYouAgentWillGetAssignedToYouYouShortly;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"The issue is:\"]")
    WebElement theIssueIsMsg;

    //----------------------------------IHaveAProblem/Complaint--> My delivered order-->----------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='Poor quality or damaged items']")
    WebElement poorQualityOrDamagedItemsBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Missing item(s)']")
    WebElement missingItemBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Incorrect items or order']")
    WebElement incorrectItemsOrOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Whole order\"]")
    WebElement wholeOrderBtn;

    @FindBy(xpath = "//android.widget.TextView[@text=\"One or more items\"]")
    WebElement oneOrMoreItemsBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"How many items were wrong?\"]")
    WebElement howWrongItemsWereWrongMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you for the clarification, please enter the order number on the bags you received and an agent will be with you shortly\"]")
    WebElement pleaseEnterTheOrderNumberOnTheBagsYouReceivedMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Please type what products did you receive instead what you should’ve received.\"]")
    WebElement pleaseEnterProductYouReceivedInsteadWhatShouldBeReceivedMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you for the clarification, an agent will be with you shortly\"]")
    WebElement thankYouForClarificationAnAgentWillBeWithYouShortly;

    @FindBy(xpath = "(//android.widget.CheckBox[@resource-id='com.breadfast:id/freshchat_selection_checkbox'])[1]")
    WebElement firstProductInTheOrderProductsList;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Refund the money\"]")
    WebElement refundMoneyBtn;

    @FindBy(xpath = " //android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Which order did you face a problem with?\"]")
    WebElement whichOrderDidYouFaceAProblemWithMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Would you like us to refund the amount on wallet or Credit Card?\"]")
    WebElement wouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsg;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Credit Card\"]")
    WebElement creditCardBtn;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Wallet\"]")
    WebElement walletBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"An agent will get assigned to you now to review the case & take the needed action.\"]")
    WebElement anAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"To help with our investigation, can you share a picture and, if available, the production date?\"]")
    WebElement canYouShareImgOrProductionDateMsg;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Deliver missing items\"]")
    WebElement deliverMissingItemsBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you, a customer support agent will be with you shortly\"]")
    WebElement customerSupportWillBeWithYouShortly;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"A customer support agent will get assigned to you now to review the case & take the needed action.\"]")
    WebElement customerSupportAgentWillGetAssignedToYouNow;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Do you want us to deliver the missing item(s) or refund the money?\"]")
    WebElement doYouWantToDeliverMissingItemOrRefundMsg;

    String orderNumberUiSelector= "//android.widget.TextView[contains(@text, '%s')]";
    String orderNumberContentDescription= "Order #%s";

    //----------------------------------IHaveAProblem/Complaint--> Balance/Charge issue-->----------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='I got charged more than once']")
    WebElement iGotChargedMoreThanOnceBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Inaccurate balance value']")
    WebElement inaccurateBalanceValueBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I paid more or less than what the DA reported']")
    WebElement iPaidMoreOrLessThanWhatTheDAReportedBtn;
    //----------------------------------IHaveAProblem/Complaint --> Service bills & Donations payments----------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='I can’t pay my bill']")
    WebElement iCantPayMyBillBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Bill paid, but the service doesn't work']")
    WebElement billPaidButTheServiceDoesntWorkBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Thank you, an agent will get assigned to you shortly']")
    WebElement ServiceBillsDonationsPaymentsMsg;

    //----------------------------------IHaveAProblem/Complaint --> The Delivery Associate----------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='Inappropriate behavior']")
    WebElement inappropriateBehaviorBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='He reported collecting a wrong amount']")
    WebElement heReportedCollectingAWrongAmountBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='How much did the agent collect?']")
    WebElement howMuchDidTheAgentCollectMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Thank you, we’ll review the case & adjust the collection shortly.']")
    WebElement weWillReviewTheCaseAndAdjustTheCollectionShortlyMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you, we'll address this shortly. Goodbye for now.\"]")
    WebElement thankYouWeWillAddressThisShortlyGoodByeMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Please share what went wrong in details & we’ll review it shortly.\n" +
            "\n" +
            "We’ll get back in touch with you in case further details are needed. \n" +
            "\n" +
            "Please rest assured that your feedback will be taken very seriously to prevent this issue from happening in the future.\"]]")
    WebElement pleaseShareWhatWentWrongMsg;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'agent will get assigned to you')]")
    WebElement chatBotMessageToAssignCXAgentInProblemComplaintMenu;

    //-----------------------------------I have a question flow-----------------------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='I have a question']")
    WebElement iHaveAQuestionBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='My current balance']")
    WebElement myBalanceValueBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Coffee stores']")
    WebElement coffeeStoresBtn;

    @FindBy(xpath="(//android.widget.LinearLayout[@resource-id='com.breadfast:id/freshchat_message_fragment_wrapper'])[5]")
    WebElement coffeStoresLinks;

    @FindBy(xpath = "//android.widget.TextView[@text='Rewards program']")
    WebElement rewardsProgramBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='None of the above']")
    WebElement noneOfTheAboveFromQuestionsBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Back']")
    WebElement backFromQuestionsBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I have another question']")
    WebElement iHaveAnotherQuestionBtn;

    @FindBy(xpath="//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='We will review your question and get back to you shortly.']")
    WebElement reviewQuestionMessage;
    //-------------------------------I have a question - Balance flow------------------------------//
    @FindBy(xpath ="(//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text'])[4]")
    WebElement checkYourBalanceStepsMsg;
    //-----------------------------------I have a question - Coffee Stores-----------------------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='Kindly check our Menu and stores locations links;\n" +
            "\n" +
            "Breadfast Coffee Menu:\n" +
            "https://rb.gy/eagmt4\n" +
            "\n" +
            "Breadfast Coffee Stores:\n" +
            "https://qrco.de/bcpl1A]']")
    WebElement coffeeStoresMsg;
    //----------------------------I have a question - Rewards Program----------------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='Rewards Program FAQs']")
    WebElement rewardsProgramMsg;

    // Points Inquiry
    @FindBy(xpath = "//android.widget.TextView[@text='What is breadfast points?']")
    WebElement whatIsBreadfastPointsBtn;

    @FindBy(xpath = "//android.widget.TextView[contains@text='You receive 1 point for every EGP 1 spent']")
    WebElement programFunctionalityMsg;

    // How to redeem points
    @FindBy(xpath = "//android.widget.TextView[@text='How to redeem my points?']")
    WebElement howToRedeemPointsBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='Hcom.breadfast:id/freshchat_conv_detail_reply_text']")
    WebElement redeemStepsMsg;

    // Points expiration
    @FindBy(xpath = "//android.widget.TextView[@text='When will my points expire?']")
    WebElement whenWillMyPointsExpireBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Points will expire six months from the date of earning.")
    WebElement pointsExpirationMsg;

    // Pending points
    @FindBy(xpath = "//android.widget.TextView[@text='I have pending points")
    WebElement iHavePendingPointsBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Points earned on each order remain pending for" +
            " 48 hours before becoming available for redemption, ensuring the order is completed.")
    WebElement pendingPointsMsg;

    @FindBy(xpath = "//android.widget.TextView[@text='I have another question")
    WebElement iHaveAnotherQuestionForPendingPointsBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Please type your question in one message and we’ll get back to you as soon as possible']")
    WebElement typeAMessageMsg;

    //----------------------I have a question - my question is not here ----------------------//
    @FindBy(xpath = "//android.widget.TextView[@text=\"My question is not here\"]")
    WebElement myQuestionIsNotHereBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Please type your question in one message and we’ll get back to you as soon as possible\"]")
    WebElement askAQuestionMsg;
    //----------------------------I have a question - none of the above----------------------------//
    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Other questions:\"]")
    WebElement fAQsMsg;

    // Cashback
    @FindBy(xpath = "//android.widget.TextView[@text=\"What does cashback mean?\"]")
    WebElement whatDoesCashbackMeanBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"A cashback coupon is a promotional voucher or discount that enables you to receive a percentage of your purchase amount back as balance in your Breadfast wallet. When you apply a cashback coupon to an order, you'll receive a portion of the money spent returned to your Breadfast balance within 24 hours at most. This balance can then be used in future orders.\"]")
    WebElement cashbackFunctionalityMsg;

    // Delivery Areas
    @FindBy(xpath = "//android.widget.TextView[@text='Where do you deliver?'")
    WebElement whatDoYouDeliverBtn;

    @FindBy(xpath = "//android.widget.TextView[contains@text='In Cairo, our delivery network covers Maadi, New Cairo, Madinaty, Al Rehab, Downtown, El Shorouk City, Mokattam, Heliopolis, Nasr City, Sixth of October City, Sheikh Zayed City, Ahram Gardens, Mostakbal City, Manial, Garden City," +
            " Mohandessin, Zamalek, Dokki, Obour, and Shoubra.'")
    WebElement whatDoYouDeliverMsg;
    // DA Tipping
    @FindBy(xpath = "//android.widget.TextView[@text=\"How can I tip the delivery agent?\"]")
    WebElement howCanITipTheDeliveryAgentBtn;

    @FindBy(xpath = "//android.widget.TextView[contains@text='You can tip your delivery associate either during the order rating process or while checking out.']")
    WebElement howCanITipTheDeliveryAgentMsg;

    // Applying more than 1 coupon
    @FindBy(xpath = "//android.widget.TextView[@text='Can I apply more than one coupon code?']")
    WebElement canIApplyMoreThanOneCouponCodeBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='You can apply only one coupon per order.']")
    WebElement canIApplyMoreThanOneCouponCodeMsg;
    //----------------------I have a question flow - Thank you + I have another question----------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='Thank you, that helped']")
    WebElement thankYouFromQuestionsBtn;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Thanks for the help\"]")
    WebElement thanksForHelpFromQuestionsBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='I have another question']")
    WebElement iHaveAnotherQuestionMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"We will review your question and get back to you shortly.\"]")
    WebElement reviewQuestionMsg;
    //-----------------I have a question flow - Glad we could assist + Thank you for chatting----------------//
    @FindBy(xpath = "//android.widget.TextView[@text='Glad we could assist.']")
    WebElement gladWeCouldAssistMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Thank you for chatting with us today!']")
    WebElement thankYouForChattingWithUsMsg;

    //-----------------------------------ChatbotMainScreen----------------------------------------//
    @FindBy(xpath = "//android.widget.TextView[@text='No, that’s it']")
    WebElement noThatISItBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Yes, please']")
    WebElement yesPleaseBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you so much, G! \n" +
            "\n" +
            "Keep them coming :) \n" +
            "\n" +
            "We’ll be waiting for more feedback and suggestions from you.\"]")
    WebElement thanksMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you so much for your time! Your suggestion will be reviewed shortly and will be sent to the relevant department.\"]")
    WebElement thanksMsgAfterTheSecondProductSuggestion;

    @FindBy(xpath = "//android.widget.TextView[@resource-id='com.breadfast:id/freshchat_text' and @text='Your suggestions are highly appreciated. Please type your message and click send.']")
    WebElement generalSuggestionMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Aren’t we just the luckiest! \n" +
            "This means the world to us, thank you in advance for taking the time to share a positive feedback with us.\n" +
            "\n" +
            "Please type your msg & click send, we can’t wait to read it \uD83E\uDD73\"]")
    WebElement positiveFeedbackMsg;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"An agent will get assigned to you now\"]")
    WebElement receivedAnExtraItemMsg;

    public boolean isPageDisplayed(){
        return isElementDisplayed(chatWithUsText);
    }

    //---------------------------------------click on non of the above--------------------------------------//
    public void pressSomethingElse() {
        wait.until(ExpectedConditions.elementToBeClickable(someThingElseBtn))
                .click();
    }
    //-----------------------------------NonOfTHeAboveSubFlowMethods----------------------------------//
    public void pressIHaveASuggestion() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveASuggestionBtn))
                .click();
    }
    public void pressOnARandomOrder(){
        wait.until(ExpectedConditions.elementToBeClickable(chooseRandomOrder)).click();
    }
    public void pressNoneOfTheseProducts(){
        wait.until(ExpectedConditions.elementToBeClickable(noneOfTheseProducts))
                .click();
    }
    public boolean isCareerEmailDisplayed(){
        return isElementDisplayed(emailCareer);
    }

    public boolean isPartnersEmailDisplayed(){return isElementDisplayed(emailPartners);}

    public boolean isCxInSomethingElseDisplayed(){return isElementDisplayed(cxAgentInSomethingElse);}

    public void pressSomethingElseBtnNoneOfTheAbove(){
        wait.until(ExpectedConditions.elementToBeClickable(someThingElseBtnNoneOfTheAbove))
                .click();
    }
    public boolean isTheItDidNotMeetMyExpectationsPickUpMessageDisplayedInNoneOfTheAboveFlow(){
        return isElementDisplayed(itDidNotMeetExpectationsAfterNonOfTheAbove);
    }
    public boolean isOkCustomerSupportAgentWillBeWithYouShortlyDisplayed(){
        return isElementDisplayed(okCustomerSupportAgentWillBeWithYou);
    }
    public void pressOkThankYouBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(OkThankYouBtn))
                .click();
    }
    public void pressReturnAnItemBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(returnAnItemBtn))
                .click();
    }
    public boolean isPleaseReturnTheProductYouAreReferringToMsgDisplayed(){
        return isElementDisplayed(pleaseSelectTheProductYouAreReferringTo);
    }
    public void pressIWouldLikeToReturnAProduct() {
        wait.until(ExpectedConditions.elementToBeClickable(iWouldLikeToReturnAProductBtn))
                .click();
    }

    public void pressIReceivedAnExtraItem() {
        wait.until(ExpectedConditions.elementToBeClickable(iReceivedAnExtraItemBtn))
                .click();
    }

    public void pressIWouldLikeToWorkAtBreadFast() {
        wait.until(ExpectedConditions.elementToBeClickable(iWouldLikeToWorkAtBreadFastBtn))
                .click();
    }

    public void pressIWouldLikeToSellAProduct() {
        wait.until(ExpectedConditions.elementToBeClickable(iWouldLikeToSellAProductBtn))
                .click();
    }

    public void pressNonOfTheAbove() {
        wait.until(ExpectedConditions.elementToBeClickable(nonOfTheAboveBtn))
                .click();
    }

    public void pressBackBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(backBtn))
                .click();
    }
    //-----------------------------------IHaveSuggestionSubFlowMethods-----------------------------------//

    public void pressIHaveSuggestionToAddProductAtBreadFastBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveSuggestionToAddProductAtBreadFastBtn))
                .click();
    }

    public void pressIHaveAGeneralSuggestionBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveAGeneralSuggestionBtn))
                .click();
    }

    public void pressIWouldLikeToShareAPositiveFeedbackBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iWouldLikeToShareAPositiveFeedbackBtn))
                .click();
    }

    public boolean isWeLoveToHearASuggestionFromCustomerMsgDisplayed(){
        return isElementDisplayed(weLoveToHearASuggestionFromCustomerMsg);
    }

    //-------------------------------------------IWouldLikeToReturnAProductSubFlowMethods-------------------------//
    public boolean isWhichProductYouAreReferringToMsgDisplayed(){
        return isElementDisplayed(whichProductYouAreReferringToMsg);
    }

    public void pressSelectAllThatApplyDropdown() {
        wait.until(ExpectedConditions.elementToBeClickable(selectAllThatApplyDropdown))
                .click();
    }

    public void pressSelectAllAndDeselectAllBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(selectAllAndDeselectAllBtn))
                .click();
    }

    public void pressProductSelectionCheckBox() {
        wait.until(ExpectedConditions.elementToBeClickable(productSelectionCheckBox))
                .click();
    }

    public void pressNonOfThisProductsCheckBox() {
        wait.until(ExpectedConditions.elementToBeClickable(nonOfThisProductsCheckBox))
                .click();
    }

    public void pressProductNameSelector() {
        wait.until(ExpectedConditions.elementToBeClickable(productNameSelector))
                .click();
    }

    public void presSSendBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(sendBtn))
                .click();
    }

    public void pressCloseBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(closeBtn))
                .click();
    }

    public boolean isReturnBecauseOfQualityDisplayed(){
        return isElementDisplayed(chooseReasonToReturnProductQualityIssue);
    }

    public boolean isReturnBecauseOfOrderedByMistakeDisplayed(){
        return isElementDisplayed(chooseReasonToReturnProductOrderByMistake);
    }

    public boolean isReturnBecauseOfItDidnotMeetExpectations(){
        return isElementDisplayed(chooseReasonToReturnProductDidnotMeetMyExpectations);
    }

    public boolean isMessageToWhyProductDoesnotMeetExpectationsDisplayed(){
        return isElementDisplayed(clarifyWhatDoesnotMeetExpectations);
    }

    public void ClickReturnBecauseOfQualityDisplayed(){
        wait.until(ExpectedConditions.elementToBeClickable(chooseReasonToReturnProductQualityIssue))
                .click();
    }

    public void clickReturnBecauseOfOrderedByMistakeDisplayed(){
        wait.until(ExpectedConditions.elementToBeClickable(chooseReasonToReturnProductOrderByMistake))
                .click();
    }

    public void clickReturnBecauseOfItDidnotMeetExpectations(){
        wait.until(ExpectedConditions.elementToBeClickable(chooseReasonToReturnProductDidnotMeetMyExpectations))
                .click();
    }

    public boolean isPickupMessageDisplayedAfterEnteringMessageDoesnotMeetExpectations(){
        return isElementDisplayed(isPickUpOrderMessageDisplayed);
    }

    //----------------------------------IHaveSuggestionToAddProductAtBreadFastSubFlow----------------------//
    public boolean isPleaseTypeTheProductYouWouldLikeToAddMsgDisplayed(){
        return isElementDisplayed(pleaseTypeTheProductYouWouldLikeToAddMsg);
    }

    public boolean iThankYouSoMuchForYourSuggestionMsgDisplayed(){
        return isElementDisplayed(thankYouSoMuchForYourSuggestionMsg);
    }

    public void pressNOThatIsIt() {
        wait.until(ExpectedConditions.elementToBeClickable(noThatISItBtn))
                .click();
    }

    public void pressYesPlease() {
        wait.until(ExpectedConditions.elementToBeClickable(yesPleaseBtn))
                .click();
    }

    public boolean isThanksMsgAfterSuggestionDisplayed(){
        return isElementDisplayed(thanksMsg);
    }

    public boolean isThanksMsgAfterSecondSuggestionDisplayed(){
        return isElementDisplayed(thanksMsgAfterTheSecondProductSuggestion);
    }
    //----------------------------------IHaveAProblem/Complaint----------------------//
    public void pressIHaveAProblemComplaintBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveAProblemComplaintBtn))
                .click();
    }

    public void pressMyDeliveredOrderBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(myDeliveredOrderBtn))
                .click();
    }

    public void pressMakingAnOrderBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(makingAnOrderBtn))
                .click();
    }

    public void pressBalanceChargeIssueBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(balanceChargeIssueBtn))
                .click();
    }

    public void pressApplyingACouponCodeBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(applyingACouponCodeBtn))
                .click();
    }

    public void pressBillsDonationsPaymentBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(billsDonationsPaymentBtn))
                .click();
    }

    public void pressTheDeliveryAssociateBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(theDeliveryAssociateBtn))
                .click();
    }

    public boolean isThankYouAgentWillGetAssignedToYouYouShortlyMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(thankYouAgentWillGetAssignedToYouYouShortly));
        return isElementDisplayed(thankYouAgentWillGetAssignedToYouYouShortly);
    }

    public boolean isTheIssueIsMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(theIssueIsMsg));
        return isElementDisplayed(theIssueIsMsg);
    }
    //---------------------------------IHaveAProblem/Complaint--> My delivered order-->----------------------//
    public void pressPoorQualityOrDamagedItemsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(poorQualityOrDamagedItemsBtn))
                .click();
    }

    public void pressMissingItemBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(missingItemBtn))
                .click();
    }

    public void pressIncorrectItemsOrOrderBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(incorrectItemsOrOrderBtn))
                .click();
    }

    public void pressWholeOrderBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(wholeOrderBtn))
                .click();
    }

    public void pressOneOrMoreItemsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(oneOrMoreItemsBtn))
                .click();
    }

    public boolean isHowWrongItemsWereWrongMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(howWrongItemsWereWrongMsg));
        return isElementDisplayed(howWrongItemsWereWrongMsg);
    }

    public boolean isPleaseEnterTheOrderNumberOnTheBagsYouReceivedMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(pleaseEnterTheOrderNumberOnTheBagsYouReceivedMsg));
        return isElementDisplayed(pleaseEnterTheOrderNumberOnTheBagsYouReceivedMsg);
    }

    public boolean isPleaseEnterProductYouReceivedInsteadWhatShouldBeReceivedMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(pleaseEnterProductYouReceivedInsteadWhatShouldBeReceivedMsg));
        return isElementDisplayed(pleaseEnterProductYouReceivedInsteadWhatShouldBeReceivedMsg);
    }

    public boolean isThankYouForClarificationAnAgentWillBeWithYouShortlyMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(thankYouForClarificationAnAgentWillBeWithYouShortly));
        return isElementDisplayed(thankYouForClarificationAnAgentWillBeWithYouShortly);
    }

    public void pressFirstProductInTheOrderProductsList() {
        wait.until(ExpectedConditions.elementToBeClickable(firstProductInTheOrderProductsList))
                .click();
    }

    public void pressRefundMoneyBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(refundMoneyBtn))
                .click();
    }

    public void pressCreditCardBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(creditCardBtn))
                .click();
    }

    public void pressWalletBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(walletBtn))
                .click();
    }

    public void selectOrderNumberElement(String orderNumber){
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(orderNumberUiSelector, getOrderNumberContentDescription(orderNumber))))).click();
    }

    public String getOrderNumberContentDescription(String orderNumber){
        return String.format(this.orderNumberContentDescription, orderNumber);
    }

    public boolean isWhichOrderDidYouFaceAProblemWithMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(whichOrderDidYouFaceAProblemWithMsg));
        return isElementDisplayed(whichOrderDidYouFaceAProblemWithMsg);
    }

    public boolean isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(anAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsg));
        return isElementDisplayed(anAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsg);
    }

    public boolean isWouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(wouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsg));
        return isElementDisplayed(wouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsg);
    }

    public boolean isCanYouShareImgOrProductionDateMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(canYouShareImgOrProductionDateMsg));
        return isElementDisplayed(canYouShareImgOrProductionDateMsg);
    }
    public boolean isOrderProductDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(firstProductInTheOrderProductsList));
        return isElementDisplayed(firstProductInTheOrderProductsList);
    }

    public void pressDeliverMissingItemsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(deliverMissingItemsBtn))
                .click();
    }

    public boolean isCustomerSupportWillBeWithYouShortlyMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(customerSupportWillBeWithYouShortly));
        return isElementDisplayed(customerSupportWillBeWithYouShortly);
    }

    public boolean isDoYouWantToDeliverMissingItemOrRefundMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(doYouWantToDeliverMissingItemOrRefundMsg));
        return isElementDisplayed(doYouWantToDeliverMissingItemOrRefundMsg);
    }

    public boolean areOptionsForMyDeliveredOrderIssueDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(poorQualityOrDamagedItemsBtn));
        wait.until(ExpectedConditions.visibilityOf(missingItemBtn));
        wait.until(ExpectedConditions.visibilityOf(incorrectItemsOrOrderBtn));
        wait.until(ExpectedConditions.visibilityOf(someThingElseBtn));
        wait.until(ExpectedConditions.visibilityOf(backBtn));

        return isElementDisplayed(poorQualityOrDamagedItemsBtn)
                && isElementDisplayed(missingItemBtn)
                && isElementDisplayed(incorrectItemsOrOrderBtn)
                && isElementDisplayed(someThingElseBtn)
                && isElementDisplayed(backBtn);
    }

    public boolean isCustomerSupportAgentWillGetAssignedToYouNowMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(customerSupportAgentWillGetAssignedToYouNow));
        return isElementDisplayed(customerSupportAgentWillGetAssignedToYouNow);
    }

    //----------------------------------IHaveAProblem/Complaint-->  Balance/Charge issue-->----------------------//

    public void pressIGotChargedMoreThanOnceBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iGotChargedMoreThanOnceBtn))
                .click();
    }

    public void pressInaccurateBalanceValueBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(inaccurateBalanceValueBtn))
                .click();
    }

    public void pressIPaidMoreOrLessThanWhatTheDAReportedBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iPaidMoreOrLessThanWhatTheDAReportedBtn))
                .click();
    }
    //----------------------------------IHaveAProblem/Complaint --> Service bills & Donations payments----------------------//
    public void pressICantPayMyBillBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iCantPayMyBillBtn))
                .click();
    }

    public void pressBillPaidButTheServiceDoesntWorkBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(billPaidButTheServiceDoesntWorkBtn))
                .click();
    }

    public boolean isServiceBillsDonationsPaymentsMsgDisplayed() {
        return isElementDisplayed(ServiceBillsDonationsPaymentsMsg);
    }

    //----------------------------------IHaveAProblem/Complaint --> The Delivery Associate----------------------//
    public void pressInappropriateBehaviorBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(inappropriateBehaviorBtn))
                .click();
    }

    public void pressHeReportedCollectingAWrongAmountBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(heReportedCollectingAWrongAmountBtn))
                .click();
    }
    //-----------------------------------I have a question flow Methods-----------------------------------//
    public void pressIHaveAQuestionBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveAQuestionBtn))
                .click();
    }

    public void pressMyBalanceValueBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(myBalanceValueBtn))
                .click();
    }

    public void pressCoffeeStoresBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(coffeeStoresBtn))
                .click();
    }

    public void pressRewardsProgramBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(rewardsProgramBtn))
                .click();
    }

    public void pressNoneOfTheAboveFromQuestionsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(noneOfTheAboveFromQuestionsBtn))
                .click();
    }

    public void pressBackFromQuestionsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(backFromQuestionsBtn))
                .click();
    }
    //----------------------------I have a question - Balance & Coffee flows Methods---------------------------//
    public boolean isCheckYourBalanceStepsMsgDisplayed(){
        return isElementDisplayed(checkYourBalanceStepsMsg);
    }

    public boolean isCoffeeStoresMsgMsgDisplayed(){
        return isElementDisplayed(coffeeStoresMsg);
    }

    public boolean isCoffeeStoreMenuLinksDisplayed(){
        return isElementDisplayed(coffeStoresLinks);
    }
    //----------------------------I have a question - Rewards Program Methods----------------------------//
    public void pressRewardsProgramMsg() {
        wait.until(ExpectedConditions.elementToBeClickable(rewardsProgramMsg))
                .click();
    }

    public void pressWhatIsBreadfastPointsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(whatIsBreadfastPointsBtn))
                .click();
    }

    public void pressHowToRedeemPointsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(howToRedeemPointsBtn))
                .click();
    }

    public void pressWhenWillMyPointsExpireBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(whenWillMyPointsExpireBtn))
                .click();
    }

    public void pressIHavePendingPointsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iHavePendingPointsBtn))
                .click();
    }

    public boolean isProgramFunctionalityMsgDisplayed() {
        return isElementDisplayed(programFunctionalityMsg);
    }

    public boolean isRedeemStepsMsgDisplayed() {
        return isElementDisplayed(redeemStepsMsg);
    }

    public boolean isPointsExpirationMsgDisplayed() {
        return isElementDisplayed(pointsExpirationMsg);
    }

    public boolean isPendingPointsMsgDisplayed() {
        return isElementDisplayed(pendingPointsMsg);
    }

    public boolean isTypeAMessageMsgDisplayed() {
        return isElementDisplayed(typeAMessageMsg);
    }
    //----------------------I have a question - my question is not here Methods ----------------------//
    public void pressMyQuestionIsNotHereBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(myQuestionIsNotHereBtn))
                .click();
    }

    public boolean isAskAQuestionMsgDisplayed() {
        return isElementDisplayed(askAQuestionMsg);
    }
    //----------------------------I have a question - none of the above Methods----------------------------//

    public void pressWhatDoesCashbackMeanBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(whatDoesCashbackMeanBtn))
                .click();
    }

    public void pressWhatDoYouDeliverBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(whatDoYouDeliverBtn))
                .click();
    }

    public void pressHowCanITipTheDeliveryAgentBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(howCanITipTheDeliveryAgentBtn))
                .click();
    }

    public void pressCanIApplyMoreThanOneCouponCodeBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(canIApplyMoreThanOneCouponCodeBtn))
                .click();
    }

    public boolean isCashbackFunctionalityMsgDisplayed() {
        return isElementDisplayed(cashbackFunctionalityMsg);
    }

    public boolean isFAQsMsgDisplayed() {
        return isElementDisplayed(fAQsMsg);
    }

    public boolean isReviewQuestionMsgMsgDisplayed() {
        return isElementDisplayed(reviewQuestionMsg);
    }

    public boolean isWhatDoYouDeliverMsgDisplayed() {
        return isElementDisplayed(whatDoYouDeliverMsg);
    }

    public String isHowCanITipTheDeliveryAgentMsgDisplayed() {
        return howCanITipTheDeliveryAgentMsg.getText();
    }

    public boolean isCanIApplyMoreThanOneCouponCodeMsgDisplayed() {
        return isElementDisplayed(canIApplyMoreThanOneCouponCodeMsg);
    }

    //-------------------I have a question flow - Thank you + I have another question Methods-------------------//
    public void pressThankYouThatHelpedBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(thankYouFromQuestionsBtn))
                .click();
    }

    public boolean isThankYouFromQuestionsMsgDisplayed() {
        return isElementDisplayed(thankYouFromQuestionsBtn);
    }

    public boolean isIHaveAnotherQuestionMsgDisplayed() {
        return isElementDisplayed(iHaveAnotherQuestionMsg);
    }

    public void pressIHaveAnotherQuestionMsg(){ wait.until(ExpectedConditions.elementToBeClickable(iHaveAnotherQuestionMsg))
            .click();
    }

    //--------------I have a question flow - Glad we could assist + Thank you for chatting Methods-------------//

    public boolean isGladWeCouldAssistMsgDisplayed() {
        return isElementDisplayed(gladWeCouldAssistMsg);
    }

    public boolean isThankYouForChattingWithUsMsgDisplayed() {
        return isElementDisplayed(thankYouForChattingWithUsMsg);
    }

    public void pressThanksForHelpFromQuestionsBtn(){ wait.until(ExpectedConditions.elementToBeClickable(thanksForHelpFromQuestionsBtn))
            .click();
    }

    public void pressIHaveAnotherQuestionBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveAnotherQuestionBtn))
                .click();
    }

    public boolean isReviwingIhaveAnotherQuestionMessageDisplayed(){
        return isElementDisplayed(reviewQuestionMessage);
    }

    //---------------------------------------clickOnIHaveAnIssueMethods--------------------------------------//
    public void pressIHaveAnIssue() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveAnIssueWithOrderBtn))
                .click();
    }

    public void pressIHaveAnIssueWithProcessingOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveAnIssueWithProcessingOrderBtn))
                .click();
    }

    public void pressOrderIsLate() {
        wait.until(ExpectedConditions.elementToBeClickable(orderIsLateBtn))
                .click();
    }

    public boolean isAssignedToDaMsgIsDisplayed(){
        return isElementDisplayed(assignToDaMsg);
    }

    public void pressPhoneNumber() {
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberBtn))
                .click();
    }

    public void pressPaymentMethod() {
        wait.until(ExpectedConditions.elementToBeClickable(paymentMethodBtn))
                .click();
    }

    public void PressChangePaymentMethodBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(changePaymentMethodBtn))
                .click();
    }

    public void PressOrderSomeThingElseBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(orderSomeThingElseBtn))
                .click();
    }

    public void pressEditMyOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(editMyOrderBtn))
                .click();
    }

    public void pressCancelMyOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(cancelMyOrderBtn))
                .click();
    }

    public void pressOk() {
        wait.until(ExpectedConditions.elementToBeClickable(okBtn))
                .click();
    }

    public void pressNoIWantToSpeakToAnAgent() {
        wait.until(ExpectedConditions.elementToBeClickable(noIWantToSpeakToAnAgentBtn))
                .click();
    }

    public boolean isCancelMsgIsDisplayed(){
        return isElementDisplayed(cancelMsg);
    }

    public boolean isAssignCustomerToAnAgentMsgDisplayed(){return isElementDisplayed(assignCustomerToAnAgentMsg);}

    public boolean isIOrderOnAWrongAddressBtnDisplayed(){return isElementDisplayed(orderWrongAddressBtn);}

    public boolean isWrongPaymentMethodBtnDisplayed(){return isElementDisplayed(wrongPaymentMethodBtn);}

    public boolean isDoNotNeedOrderAnYMoreBtnDisplayed(){return isElementDisplayed(doNotNeedOrderAnyMoreBtn);}

    public boolean isNonOfTheAboveBtnDisplayed(){return isElementDisplayed(nonOfTheAboveBtn);}

    public boolean isYourOrderWillBeCancelledMsgDisplayed(){return isElementDisplayed(yourOrderWillBeCancelledMsg);}

    public void pressAddAProduct() {
        wait.until(ExpectedConditions.elementToBeClickable(addAProductBtn))
                .click();
    }

    public void pressRemoveAProduct() {
        wait.until(ExpectedConditions.elementToBeClickable(removeAProductBtn))
                .click();
    }

    public void pressChangeAddress() {
        wait.until(ExpectedConditions.elementToBeClickable(changeAddressBtn))
                .click();
    }

    public void pressOrderWrongAddress() {
        wait.until(ExpectedConditions.elementToBeClickable(orderWrongAddressBtn))
                .click();
    }

    public void PressWrongPaymentMethod() {
        wait.until(ExpectedConditions.elementToBeClickable(wrongPaymentMethodBtn))
                .click();
    }

    public void pressDoNotNeedOrderAnyMore() {
        wait.until(ExpectedConditions.elementToBeClickable(doNotNeedOrderAnyMoreBtn))
                .click();
    }

    public boolean isOrderCanceledMsgIsDisplayed(){
        return isElementDisplayed(orderCanceledMsg);
    }

    public void pressYesCancelMyOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(yesCancelMyOrderBtn))
                .click();
    }

    public void pressNoKeepMyOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(noKeepMyOrderBtn))
                .click();
    }

    //-----------------------------------Enter message on chat----------------------------------//
    public void enterTextIntoChatbotTxtField(String freeText) {
        wait.until(ExpectedConditions.elementToBeClickable(textField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(textField)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(textField)).sendKeys(freeText);
        wait.until(ExpectedConditions.textToBePresentInElement(textField, freeText));
        hideKeyboardIfDisplayed();
    }

    public void pressSendMessageBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(sendTextInTextField))
                .click();
    }

    public boolean isChatBotRedirectedUserToTheCxAgent(){
        return isElementDisplayed(chatBotMessageToAssignCXAgentInProblemComplaintMenu);
    }

    public String getChatBotRedirectedUserToTheCxAgentText(){
        wait.until(ExpectedConditions.visibilityOf(chatBotMessageToAssignCXAgentInProblemComplaintMenu));
        return chatBotMessageToAssignCXAgentInProblemComplaintMenu.getText();
    }

    public boolean isChatBotRedirectedUserToHaveProblemOrComplaintMenu(){
        wait.until(ExpectedConditions.visibilityOf(makingAnOrderBtn));
        wait.until(ExpectedConditions.visibilityOf(balanceChargeIssueBtn));
        wait.until(ExpectedConditions.visibilityOf(applyingACouponCodeBtn));
        wait.until(ExpectedConditions.visibilityOf(billsDonationsPaymentBtn));
        wait.until(ExpectedConditions.visibilityOf(theDeliveryAssociateBtn));
        wait.until(ExpectedConditions.visibilityOf(backBtn));

        return isElementDisplayed(makingAnOrderBtn)
                && isElementDisplayed(balanceChargeIssueBtn)
                && isElementDisplayed(applyingACouponCodeBtn)
                && isElementDisplayed(billsDonationsPaymentBtn)
                && isElementDisplayed(theDeliveryAssociateBtn)
                && isElementDisplayed(backBtn);
    }

    public boolean isNewMessageButtonDisplayed() {
        return isElementDisplayed(newMessagesBtn);
    }

    public void takeActionIfNewMessageButtonDisplayed() {
        try {
            if (isNewMessageButtonDisplayed()) {
                {
                    wait.until(ExpectedConditions.visibilityOf(newMessagesBtn)).click();
                }
                wait.until(ExpectedConditions.invisibilityOf(newMessagesBtn));
            }
        }
        catch(Exception e){
            //Do Nothing
            return;
        }
    }

    public boolean isHowMuchDidTheAgentCollectMsgMsgDisplayed(){
        return isElementDisplayed(howMuchDidTheAgentCollectMsg);
    }

    public boolean isPleaseShareWhatWentWrongMsgDisplayed(){
        return isElementDisplayed(pleaseShareWhatWentWrongMsg);
    }

    public boolean isWeWillReviewTheCaseAndAdjustTheCollectionShortlyMsgMsgDisplayed(){
        return isElementDisplayed(weWillReviewTheCaseAndAdjustTheCollectionShortlyMsg);
    }

    public boolean isPleaseGiveUsMoreDetailsMsgDisplayed(){
        return isElementDisplayed(pleaseGiveUsMoreDetailsMsg);
    }

    public boolean isYourCurrentOrderStatusDisplayed(){
        return isElementDisplayed(yourCurrentOrderStatusChatbotMsg);
    }

    public boolean isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(thankYouWeWillAddressThisShortlyGoodByeMsg));
        return isElementDisplayed(thankYouWeWillAddressThisShortlyGoodByeMsg);
    }

    public String getThankYouWeWillAddressThisShortlyGoodByeText(){
        wait.until(ExpectedConditions.visibilityOf(thankYouWeWillAddressThisShortlyGoodByeMsg));
        return thankYouWeWillAddressThisShortlyGoodByeMsg.getText();
    }

    public boolean isAddAProductBtnDisplayed(){
        return isElementDisplayed(addAProductBtn);
    }

    public boolean isRemoveAProductBtnDisplayed(){
        return isElementDisplayed(removeAProductBtn);
    }

    public boolean isChangeAddressBtnDisplayed(){
        return isElementDisplayed(changeAddressBtn);
    }

    public boolean isNonOfAboveBtnDisplayed(){
        return isElementDisplayed(nonOfTheAboveBtn);
    }

    //-----------------------I have general suggestion flow-----------------//
    public boolean isGeneralSuggestionMsgDisplayed() {
        return isElementDisplayed(generalSuggestionMsg);
    }
    //------- ---------------I want to share positive feedbackFlow---------//
    public boolean isPositiveFeedbackMsgDisplayed() {
        return isElementDisplayed(positiveFeedbackMsg);
    }
    //-------------------------I recived an extra item flow--------------//
    public boolean isReceivedAnExtraItemMsgDisplayed() {
            return isElementDisplayed(receivedAnExtraItemMsg);  }
}
