package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidSetAddressScreen extends BaseAndroidScreen {
    public AndroidSetAddressScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='locationScreen_locateMeBtn']")
    WebElement goToCurrentLocationBtn;

    @FindBy(name = "//android.widget.TextView[@content-desc='locationScreen_title']")
    WebElement pageHeader;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='locationScreen_confirmLocationBtn']")
    WebElement confirmLocationBtn;

    @FindBy(xpath = "//android.view.View[@content-desc='Google Map']/android.view.View")
    WebElement currentLocationSignalIcon;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'United States')]")
    WebElement unitedStatesDefaultAddressElement;

    @FindBy(xpath = "//android.widget.EditText")
    WebElement enterAddressTextField;

    @FindBy(xpath = "//android.widget.ScrollView/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup")
    WebElement searchResultsFirstElement;

    public boolean isPageHeaderDisplayed(){
        return isElementDisplayed(pageHeader);
    }

    public boolean isConfirmLocationBtnDisplayed(){
        return isElementDisplayed(confirmLocationBtn);
    }

    public void pressGoToCurrentLocationBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(goToCurrentLocationBtn))
                .click();
        wait.until(ExpectedConditions.visibilityOf(currentLocationSignalIcon));
    }

    public void pressConfirmLocationBtn(){
        wait.until(ExpectedConditions.visibilityOf(confirmLocationBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(confirmLocationBtn));
    }

    public void goToCurrentLocationAndConfirmIfDisplayed(){
        try {
            if (isConfirmLocationBtnDisplayed()){
                pressGoToCurrentLocationBtn();
                pressConfirmLocationBtn();
            }
        } catch (Exception e){
            //Do Nothing
            return;
        }
    }

    public void enterValueInTextFieldIfLocationIsSetToUS(String textValue){
        try {
            //Show the text field
            wait.until(ExpectedConditions.visibilityOf(unitedStatesDefaultAddressElement)).click();

            //Click and clear text field
            wait.until(ExpectedConditions.visibilityOf(enterAddressTextField)).click();
            wait.until(ExpectedConditions.visibilityOf(enterAddressTextField)).clear();

            //Enter text in text field
            wait.until(ExpectedConditions.visibilityOf(enterAddressTextField)).sendKeys(textValue);
            wait.until(ExpectedConditions.textToBePresentInElement(enterAddressTextField, textValue));
        } catch (Exception e){
            // Do nothing
        }
    }

    public void selectFirstAddressDisplayedInSearchResults(){
        wait.until(ExpectedConditions.visibilityOf(searchResultsFirstElement)).click();
    }

    public boolean isCurrentLocationSetToUnitedStates(){
        return isElementDisplayed(unitedStatesDefaultAddressElement) && isElementDisplayed(goToCurrentLocationBtn);
    }
}
