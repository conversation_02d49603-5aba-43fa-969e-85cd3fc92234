package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class AndroidCategoryScreen extends BaseAndroidScreen {
    public AndroidCategoryScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@content-desc='categoryTitle']")
    WebElement categoryTitle;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='cartImage']")
    WebElement cartIcon;

    @FindBy(xpath = "(//android.widget.Button[@content-desc='addToCart_button'])")
    List<WebElement> addToCartIcons;
    @FindBy(xpath = "//android.widget.HorizontalScrollView")
    WebElement subCategoriesScrollableContentContainer;

    @FindBy(xpath = "//android.widget.ScrollView")
    WebElement productsListScrollableContentContainer;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='categoryDetails_header_backBtn']")
    WebElement backBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='cartBtn_topNav']")
    WebElement headerCartBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='productsList_subCategory_title']")
    WebElement subCategoryProductsTitle;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"android:id/message\"]")
    WebElement notifyMePopUp;

    @FindBy(xpath = "//android.widget.Button[@resource-id=\"android:id/button2\"]")
    WebElement registerLoginBtnInNotifyMePopUp;

    @FindBy(xpath = "//android.widget.Button[@resource-id=\"android:id/button1\"]")
    WebElement noThanksBtnInNotifyMePopUp;

    String subCategorySelector = "//android.view.ViewGroup[@content-desc='%s']";

    String subCategoryContentDescription = "subcategory_%s";

    String productSelector = "//android.widget.Button[@content-desc='%s']";

    String productContentDescription = "product_card_categoryProductsList_%s";

    String subCategoriesScrollableContentContainerSelector = "//android.widget.HorizontalScrollView";

    String productsListScrollableContentContainerSelector = "//android.widget.ScrollView";

    String productAddToCartBtnContentDescription = "addToCart_button";

    String productNotifyMeBtnContentDescription = "notifyMeBtn_%s";

    String productNotifyMeBtnSelector = "//android.widget.Button[@content-desc='%s']";

    String notifyMePopUpDescription = "To be able to receive a notification when %s is back, " +
            "you have to be registered/logged-in first.";

    public boolean isPageDisplayed(){
        return (isElementDisplayed(categoryTitle)
                && (isElementDisplayed(subCategoriesScrollableContentContainer)
                || isElementDisplayed(subCategoryProductsTitle)));
    }

    public WebElement getSubCategoryUiElement(int subCategoryId){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(subCategorySelector, getSubCategoryContentDescription(subCategoryId)))));
        } catch (Exception e){
            return null;
        }
    }

    public boolean isSubCategoryDisplayed(int subCategoryId){
        WebElement sc = getSubCategoryUiElement(subCategoryId);
        if (sc == null){
            return false;
        } else {
            return isElementDisplayed(sc);
        }
    }

    public String getSubCategoryContentDescription(int subCategoryId){
        return String.format(this.subCategoryContentDescription, subCategoryId);
    }

    public void pressSubCategory(int subCategoryId){
        WebElement sc = getSubCategoryUiElement(subCategoryId);
        if (sc != null){
            wait.until(ExpectedConditions.visibilityOf(sc)).click();
        }
    }

    public WebElement getProductUiElement(String productId){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(productSelector, getProductContentDescription(productId)))));
        } catch (Exception e){
            return null;
        }
    }

    public boolean isProductDisplayed(String productId){
        return isElementDisplayed(getProductUiElement(productId));
    }

    public String getProductContentDescription(String productId){
        return String.format(productContentDescription, productId);
    }

    public void pressProduct(String productId){
        wait.until(ExpectedConditions.visibilityOf(getProductUiElement(productId)))
                .click();
    }

    public WebElement getSubCategoriesScrollableContentContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(subCategoriesScrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public WebElement getProductsListScrollableContentContainer() {
        if (productsListScrollableContentContainer.isDisplayed())
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(productsListScrollableContentContainerSelector)));
        else
            return null;
    }

    public void pressBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(categoryTitle));
    }

    public void pressCartBtn() {
        wait.until(ExpectedConditions.visibilityOf(headerCartBtn))
                .click();
    }

    public WebElement getSubCategoryProductsTitle() {
        try {
            return wait.until(ExpectedConditions.visibilityOf(subCategoryProductsTitle));
        } catch (Exception e){
            return null;
        }
    }

    public void pressAddToCartIconByIndex(int itemOrder) {
        wait.until(ExpectedConditions.visibilityOf(addToCartIcons.get(itemOrder-1))).click();
    }

    public void pressCartIcon() {
        wait.until(ExpectedConditions.visibilityOf(cartIcon)).click();
    }

    public String getProductAddToCartBtnContentDescription(){
        return productAddToCartBtnContentDescription;
    }

    public String getProductNotifyMeBtnContentDescription(String productObjectId){
        return String.format(productNotifyMeBtnContentDescription, productObjectId);
    }

    public WebElement getProductNotifyMeBtnUiElement(String productObjectId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(productNotifyMeBtnSelector,
                        getProductNotifyMeBtnContentDescription(productObjectId)))));
    }

    public void pressProductNotifyMeBtn(String productObjectId){
        wait.until(ExpectedConditions.visibilityOf(getProductNotifyMeBtnUiElement(productObjectId)))
                .click();
    }

    public boolean isProductNotifyMePopUpDisplayed(){
        return isElementDisplayed(notifyMePopUp);
    }

    public void pressRegisterLoginBtnInNotifyMe(){
        wait.until(ExpectedConditions.visibilityOf(registerLoginBtnInNotifyMePopUp)).click();
    }

    public void pressNoThanksBtnInNotifyMe(){
        wait.until(ExpectedConditions.visibilityOf(noThanksBtnInNotifyMePopUp)).click();
    }

    public String getNotifyMePopUpDescription(){
        return wait.until(ExpectedConditions.visibilityOf(notifyMePopUp)).getText();
    }

    public boolean isProductNotifyMePopUpDescriptionDisplayedCorrectly(String productName){
        return getNotifyMePopUpDescription().equals(String.format(notifyMePopUpDescription,productName));
    }
}
