package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class AndroidOrderDetailsScreen extends BaseAndroidScreen {
    public AndroidOrderDetailsScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Your') and contains(@text, 'points are on the way!')]")
    WebElement breadfastRewardBtn;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='Cancel_btn']")
    WebElement cancelBtn;
    @FindBy(xpath = "//android.widget.Button[@content-desc='ordersHistory_order__reorderBtn']")
    WebElement reOrderBtn;
    @FindBy(xpath = "//android.widget.TextView[@content-desc='Orderstatus_txtview']")
    WebElement orderStatusText;
    @FindBy(xpath = "//android.widget.TextView[@text='Submit']")
    WebElement submitCancellationReasonBtn;
    @FindBy(xpath = "//android.widget.TextView[@text='Cancelled']")
    WebElement cancelledText;
    @FindBy(xpath = "//android.widget.TextView[@content-desc='Delivered_hint']")
    WebElement deliveredText;
    @FindBy(xpath = "//android.widget.TextView[@text='Help us understand Why you cancelled?']")
    WebElement cancellationReasonsText;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Order Details\"]")
    WebElement orderDetailsTxt;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Order placed successfully \"]")
    WebElement orderPlacedText;

    @FindBy(xpath = "//android.widget.FrameLayout[@resource-id=\"android:id/content\"]/android.widget.FrameLayout/android.view.ViewGroup[2]/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[3]/android.view.ViewGroup/android.view.ViewGroup/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[1]/android.view.ViewGroup")
    WebElement backBtn;

    String cancellationReasonsSelector = "//android.widget.FrameLayout[@resource-id=\"android:id/content\"]/android.widget.FrameLayout/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[2]/android.view.ViewGroup/android.view.ViewGroup/android.view.ViewGroup[%s]";

    String scrollableContentContainerSelector = "//android.widget.ScrollView";

    String cancelButtonContentDescription = "Cancel_btn";

    public void pressBreadfastRewardBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(breadfastRewardBtn))
                .click();
    }
    public void pressCancelBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(cancelBtn))
                .click();
    }
    public void pressConfirmCancelBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(cancelBtn))
                .click();
    }
    public void pressSubmitCancellationReasonBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(submitCancellationReasonBtn))
                .click();
    }
    public void pressReOrderBtn() {
        if (isCancelledTextDisplayed()||isOrderDelivered()) {
            wait.until(ExpectedConditions.elementToBeClickable(reOrderBtn))
                    .click();
        }
    }
    public Boolean isCancelledTextDisplayed(){
        return isElementDisplayed(cancelledText);
    }
    public Boolean isReOrderBtnDisplayed(){
        return isElementDisplayed(reOrderBtn);
    }
    public Boolean isOrderDelivered(){
        return isElementDisplayed(deliveredText);
    }
    public Boolean isOderStatusDisplayed(){

        return isElementDisplayed(orderStatusText);
    }
    public Boolean isCancellationReasonsBottomSheetDisplayed(){

        return isElementDisplayed(cancellationReasonsText);
    }
    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }
    public String getCancelButtonContentDescription(){
        return cancelButtonContentDescription;
    }

    public void selectCancellationReason(int index) {
        wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath((String.format(cancellationReasonsSelector, index))))).click();
    }

    public Boolean isOrderDetailsPageDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(orderDetailsTxt));
        return isElementDisplayed(orderDetailsTxt);
    }

    public Boolean isOrderPlacedSuccessfullyDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(orderPlacedText));
        return isElementDisplayed(orderPlacedText);
    }

    public void pressBackBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(backBtn))
                .click();
    }
}
