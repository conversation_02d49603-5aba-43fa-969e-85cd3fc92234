package modals.customerApp.android.androidMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidHelpScreen extends BaseAndroidScreen
{
    public AndroidHelpScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='Back_btn']")
    WebElement backBtn;

    @FindBy(xpath = "//android.view.View[@text='Need Help?']")
    WebElement pageTitle;

    public void pressFreeCreditBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backBtn)).click();
    }

    public Boolean isPageDisplayed()
    {
        return isElementDisplayed(pageTitle);
    }
}
