package modals.customerApp.android.androidMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidAccountSettingsScreen extends BaseAndroidScreen
{
    public AndroidAccountSettingsScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text=\"Update Personal Information\"]")
    WebElement updatePersonalInformationTab;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Manage Addresses\"]")
    WebElement manageAddressesTab;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Email Address\"]")
    WebElement emailAddressesTab;
    @FindBy(xpath = "//android.widget.TextView[@text=\"Saved cards\"]")
    WebElement savedCardsTab;
    @FindBy(xpath = "//android.widget.TextView[@text=\"Change Language\"]")
    WebElement changeLanguageTab;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Notification & SMS\"]")
    WebElement notificationsSmsTab;
    @FindBy(xpath = "//android.widget.TextView[@text=\"Delete Account\"]")
    WebElement deleteAccountTab;

    @FindBy(xpath = "//android.widget.ScrollView[@content-desc=\"scrollableContentContainer\"]" +
            "/android.view.ViewGroup/android.view.ViewGroup[1]/android.view.ViewGroup/android.view.ViewGroup")
    WebElement accountSettingsBackBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Account Settings']")
    WebElement pageTitle;

    public boolean isAccountSettingsRelatedButtonsDisplayed(){
        return (isElementDisplayed(updatePersonalInformationTab)
                && isElementDisplayed(manageAddressesTab)
                && isElementDisplayed(emailAddressesTab)
                && isElementDisplayed(savedCardsTab)
                && isElementDisplayed(changeLanguageTab)
                && isElementDisplayed(notificationsSmsTab)
                && isElementDisplayed(deleteAccountTab));
    }
    public void pressAccountSettingsBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(accountSettingsBackBtn)).click();
    }

    public Boolean isPageDisplayed()
    {
        return isElementDisplayed(pageTitle);
    }

    public void pressManageAddressesTab(){
        wait.until(ExpectedConditions.elementToBeClickable(manageAddressesTab)).click();
    }
}

