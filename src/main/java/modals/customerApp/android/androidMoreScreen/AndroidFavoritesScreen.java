package modals.customerApp.android.androidMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidFavoritesScreen extends BaseAndroidScreen {
    public AndroidFavoritesScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"favoritesScreen_container\"]")
    WebElement pageContainer;
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"favoritesScreen_container\"]" +
            "/android.view.ViewGroup/android.view.ViewGroup[1]")
    WebElement favoritesBackIcon;

    @FindBy(xpath="//android.widget.TextView[@text=\"No products added yet\"]")
    WebElement noProductsAddedYetTxt;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageContainer);
    }

    public void pressBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(favoritesBackIcon))
                .click();
    }

    public boolean isFavListEmpty() {
        return isElementDisplayed(noProductsAddedYetTxt);
    }
}
