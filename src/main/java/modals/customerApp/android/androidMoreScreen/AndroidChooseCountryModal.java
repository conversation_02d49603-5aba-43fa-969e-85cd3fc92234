package modals.customerApp.android.androidMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidChooseCountryModal extends BaseAndroidScreen
{
    public AndroidChooseCountryModal(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"changeCountryModal_title\"]")
    WebElement pageTitle;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='country_egypt']")
    WebElement egypt;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='country_ksa']")
    WebElement ksa;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='saveCountryBtn']")
    WebElement saveChangesBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='changeCountry_confirmBtn']")
    WebElement yesChangeCountryBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='changeCountry_keepCurrentBtn']")
    WebElement keepCurrentBtn;

    public boolean isPageDisplayed()
    {
        return isElementDisplayed(pageTitle);
    }

    public void selectCountry(String countryCode)
    {
        WebElement targetCountryElement = egypt;
        switch (countryCode.toLowerCase())
        {
            case "ksa":
                targetCountryElement = ksa;
                break;
            default:
                break;
        }
        wait.until(ExpectedConditions.elementToBeClickable(targetCountryElement)).click();
        wait.until(ExpectedConditions.elementToBeClickable(saveChangesBtn)).click();
        wait.until(ExpectedConditions.elementToBeClickable(yesChangeCountryBtn)).click();
    }
}
