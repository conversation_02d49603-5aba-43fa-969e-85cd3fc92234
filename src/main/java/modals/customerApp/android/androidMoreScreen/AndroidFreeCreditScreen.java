package modals.customerApp.android.androidMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidFreeCreditScreen extends BaseAndroidScreen
{
    public AndroidFreeCreditScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_freeCreditsBtn']")
    WebElement freeCreditBtn;
    @FindBy(xpath = "//android.view.View[@text=\"Refer and Earn\"]")
    WebElement freeCreditTitle;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"Back_btn\"]")
    WebElement freeCreditBackIcon;

    @FindBy(xpath = "//android.view.View[@text='Refer and Earn']")
    WebElement pageTitle;

    public void pressFreeCreditBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(freeCreditBtn)).click();
    }
    public String getFreeCreditTitle(){
        return (wait.until(ExpectedConditions.visibilityOf(freeCreditTitle)).getText());
    }

    public void pressFreeCreditBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(freeCreditBackIcon)).click();
    }

    public Boolean isPageDisplayed()
    {
        return isElementDisplayed(pageTitle);
    }
}
