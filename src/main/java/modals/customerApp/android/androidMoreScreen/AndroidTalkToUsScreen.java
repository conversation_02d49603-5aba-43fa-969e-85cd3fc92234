package modals.customerApp.android.androidMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidTalkToUsScreen extends BaseAndroidScreen {
    public AndroidTalkToUsScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.ImageButton[@content-desc='Navigate up']")
    WebElement talkToUsBackIcon;

    @FindBy(xpath = "//android.widget.FrameLayout[@resource-id='com.breadfast:id/action_bar_container']")
    WebElement pageHeaderContainer;

    public void pressBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(talkToUsBackIcon))
                .click();
    }

    public Boolean isPageDisplayed(){
        return isElementDisplayed(pageHeaderContainer);
    }
}
