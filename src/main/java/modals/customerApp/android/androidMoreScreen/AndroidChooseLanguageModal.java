package modals.customerApp.android.androidMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidChooseLanguageModal extends BaseAndroidScreen
{
    public AndroidChooseLanguageModal(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);

    }

    @FindBy(xpath = "//android.widget.TextView[@text='Choose Language']")
    WebElement pageTitle;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"languageBtn_English_radioBtn\"]")
    WebElement english;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"languageBtn_Arabic_radioBtn\"]")
    WebElement arabic;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='Update Language']")
    WebElement updateLanguageBtn;

    @FindBy(xpath = "//android.widget.Button[@resource-id=\"android:id/button2\"]")
    WebElement changeBtn;

    @FindBy(xpath = "//android.widget.Button[@resource-id=\"android:id/button1\"]")
    WebElement cancelBtn;

    public boolean isPageDisplayed()
    {
        return isElementDisplayed(pageTitle);
    }

    public void selectLanguage(String language)
    {
        WebElement targetlanguageElement = english;
        switch (language.toLowerCase())
        {
            case "ar":
                targetlanguageElement = arabic;
                break;
            default:
                break;
        }
        wait.until(ExpectedConditions.elementToBeClickable(targetlanguageElement)).click();
        wait.until(ExpectedConditions.elementToBeClickable(updateLanguageBtn)).click();
        wait.until(ExpectedConditions.elementToBeClickable(changeBtn)).click();
    }
}
