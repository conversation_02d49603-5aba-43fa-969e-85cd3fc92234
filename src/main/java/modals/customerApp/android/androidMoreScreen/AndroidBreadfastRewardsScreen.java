package modals.customerApp.android.androidMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidBreadfastRewardsScreen extends BaseAndroidScreen
{
    public AndroidBreadfastRewardsScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.View[@text=\"Welcome to Breadfast Rewards\"]")
    WebElement breadfastRewardsTitle;

    @FindBy(xpath = "//android.widget.ImageView")
    WebElement xIcon;

    @FindBy(xpath = "//android.view.View[@text='Points']")
    WebElement pointsTitle;

    @FindBy(xpath = "//android.view.View[@resource-id=\"wdgtIframe\"]/android.view.View/android.view.View" +
            "/android.view.View/android.view.View[1]")
    WebElement pageHeaderContainer;

    public String getPointsTitle(){
        return  (wait.until(ExpectedConditions.visibilityOf(pointsTitle)).getText());
    }

    public String getBreadfastRewardsTitle(){
        return (wait.until(ExpectedConditions.visibilityOf(breadfastRewardsTitle)).getText());
    }

    public void pressCloseBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(xIcon))
                .click();
    }

    public Boolean isPageDisplayed(){
        return isElementDisplayed(pageHeaderContainer);
    }
    public Boolean isPageTitleDisplayed(){
        return isElementDisplayed(breadfastRewardsTitle);
    }
}
