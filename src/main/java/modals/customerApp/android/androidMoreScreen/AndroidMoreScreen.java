package modals.customerApp.android.androidMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidMoreScreen extends BaseAndroidScreen
{
    public AndroidMoreScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    String fullNameXpath = "//android.widget.TextView[@text='%s']";

    @FindBy(xpath ="//android.widget.TextView[@text='More']")
    WebElement moreTabButton;

    @FindBy(xpath = "//android.widget.TextView[contains(@text, 'Find Balance and Payment Services')]")
    WebElement payBtnCoachMarks;

    @FindBy(xpath = "//android.widget.TextView[@text='Create New Account']")
    WebElement createAccountBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Login To Breadfast']")
    WebElement loginBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_activityHistoryBtn']")
    WebElement activityHistoryBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_favoritesBtn']")
    WebElement favoritesBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_rewardsBtn']")
    WebElement breadfastRewardsBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_freeCreditsBtn']")
    WebElement freeCreditBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_accountSettingsBtn']")
    WebElement accountSettingsBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_needHelpBtn']")
    WebElement helpBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_languageBtn']")
    WebElement languageBtn;

    @FindBy(xpath = "(//android.widget.TextView[@content-desc='listItem_subTitle'])[2]")
    WebElement currentLanguage;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_changeCountryBtn']")
    WebElement countryBtn;

    @FindBy(xpath = "(//android.widget.TextView[@content-desc='listItem_subTitle'])[3]")
    WebElement currentCountry;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_chatBtn']")
    WebElement talkToUsBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='userMenu_logoutBtn']")
    WebElement logoutBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='userMenu_versionNumberContainer']")
    WebElement versionNumber;

    @FindBy(xpath = "//android.widget.ScrollView[@content-desc='userMenu_scrollViewContainer']")
    WebElement scrollableContentContainer;

    @FindBy(xpath = "//android.widget.Button[@content-desc=\"activityHistory_orders_tab\"]")
    WebElement activityHistoryOrdersTab;

    @FindBy(xpath = "//android.widget.TextView[@text=\"Bills\"]")
    WebElement activityHistoryBillsTab;

    String logoutBtnContentDescription = "userMenu_logoutBtn";

    String scrollableContentContainerSelector =
            "//android.widget.ScrollView[@content-desc='userMenu_scrollViewContainer']";

    public void pressMoreTabBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(moreTabButton)).click();
    }

    public void dismissCoachMarksIfDisplayed()
    {
        if (isCoachMarksDisplayed())
        {
            wait.until(ExpectedConditions.visibilityOf(payBtnCoachMarks)).click();
        }
    }

    public boolean isCoachMarksDisplayed()
    {
        return  isElementDisplayed(payBtnCoachMarks);
    }

    public boolean isPageDisplayed()
    {
        return (isElementDisplayed(loginBtn) || isElementDisplayed(activityHistoryBtn));
    }

    public boolean isPageTabsDisplayed()
    {
        return (isElementDisplayed(activityHistoryBtn)
                && isElementDisplayed(favoritesBtn)
                && isElementDisplayed(breadfastRewardsBtn)
                && isElementDisplayed(freeCreditBtn)
                && isElementDisplayed(accountSettingsBtn)
                && isElementDisplayed(helpBtn)
                && isElementDisplayed(languageBtn)
                && isElementDisplayed(countryBtn)
                && isElementDisplayed(talkToUsBtn)
                && isElementDisplayed(logoutBtn));
    }

    public boolean isFullNameDisplayed(String firstName, String lastName) {
        WebElement fullName = wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(fullNameXpath, (firstName + " " + lastName)))));
        return isElementDisplayed(fullName);
    }

    public void pressCreateAccountBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(createAccountBtn)).click();
    }

    public void pressLoginBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(loginBtn))
                .click();
    }

    public void pressActivityHistoryBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(activityHistoryBtn))
                .click();
    }

    public void pressFavoritesBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(favoritesBtn))
                .click();
    }

    public void pressHelpBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(helpBtn))
                .click();
    }

    public void pressRewardsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(breadfastRewardsBtn))
                .click();
    }

    public void pressLanguageBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(languageBtn)).click();
    }

    public void pressCountryBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(countryBtn)).click();
    }

    public void pressTalkToUsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(talkToUsBtn))
                .click();
    }

    public void pressLogoutBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(logoutBtn)).click();
    }

    public String getVersionNumber() {
        return wait.until(ExpectedConditions.visibilityOf(versionNumber)).getText()
                .replace("Version ", "");
    }

    public WebElement getScrollableContentContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public void pressAccountSettingsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(accountSettingsBtn))
                .click();
    }

    public String getLogoutBtnContentDescription() {
        return logoutBtnContentDescription;
    }

    public boolean isPageDismissed(){
        try {
            wait.until(ExpectedConditions.invisibilityOf(activityHistoryBtn));
            wait.until(ExpectedConditions.invisibilityOf(loginBtn));
            return true;
        } catch (Exception e){
            return false;
        }
    }
}
