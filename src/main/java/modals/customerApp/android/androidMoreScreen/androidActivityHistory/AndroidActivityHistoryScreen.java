package modals.customerApp.android.androidMoreScreen.androidActivityHistory;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class AndroidActivityHistoryScreen extends BaseAndroidScreen
{
    public AndroidActivityHistoryScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='activityHistory_headerContainer']")
    WebElement pageHeaderContainer;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"activityHistory_headerContainer\"]" +
            "/android.view.ViewGroup/android.view.ViewGroup")
    WebElement activityHistoryBackIcon;

    @FindBy(xpath = "//android.widget.Button[@content-desc='activityHistory_orders_tab']")
    WebElement ordersTab;

    @FindBy(xpath = "//android.widget.Button[@content-desc='activityHistory_bills_tab']")
    WebElement billsTab;

    @FindBy(xpath = "//android.view.ViewGroup[contains(@content-desc,'ordersHistory_order_') and contains(@content-desc,'_cardContainer')]")
    List<WebElement> orderHistoryCards;

    public boolean areOrdersAndBillsTabsDisplayed(){
        return (isElementDisplayed(ordersTab) && isElementDisplayed(billsTab)) ;
    }

    public void pressActivityHistoryBackButton(){
        wait.until(ExpectedConditions.elementToBeClickable(activityHistoryBackIcon)).click();
    }

    public Boolean isPageDisplayed() {
        return isElementDisplayed(pageHeaderContainer);
    }

    public void selectOrdersTab() {
        wait.until(ExpectedConditions.elementToBeClickable(ordersTab))
                .click();
    }

    public void selectBillsTab() {
        wait.until(ExpectedConditions.elementToBeClickable(billsTab))
                .click();
    }

    public int getOrderHistoryCardCount() {
        return wait.until(ExpectedConditions.visibilityOfAllElements(orderHistoryCards))
                .size();
    }
    public void selectOrdersCard(int orderIndex) {
        wait.until(ExpectedConditions.visibilityOfAllElements(orderHistoryCards)).get(orderIndex).click();

    }
}
