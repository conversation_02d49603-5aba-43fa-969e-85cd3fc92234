package modals.customerApp.android.androidMoreScreen.androidActivityHistory;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class AndroidBillsTabScreen extends BaseAndroidScreen {

    public AndroidBillsTabScreen(AndroidDriver androidDriver)
    {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    String billCardContentDescription = "billsHistory_billCard_bill_%s_contentContainer";
    String billCardUiSelector = "//android.view.ViewGroup[@content-desc='%s']";

    @FindBy(xpath = "//android.view.ViewGroup[contains(@content-desc,'billsHistory_billCard_bill') and contains(@content-desc,'contentContainer')]")
    List<WebElement> billsHistoryCards;

    public int getBillsCardCount() {
        if (billsHistoryCards.isEmpty()) {
            return 0;
        } else {
            wait.until(ExpectedConditions.visibilityOfAllElements(billsHistoryCards));
            return billsHistoryCards.isEmpty() ? 0 : billsHistoryCards.size();
        }
    }

    public String getBillCardContentDescription(String transactionId){
        return String.format(billCardContentDescription,transactionId);
    }

    public String getBillCardUiSelector(String transactionId){
        return String.format(billCardUiSelector, getBillCardContentDescription(transactionId));
    }

    public WebElement getBillCardUiElement(String transactionId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getBillCardUiSelector(transactionId))));
    }

    public boolean isBillCardDisplayed(String transactionId){
        wait.until(ExpectedConditions.visibilityOf(getBillCardUiElement(transactionId)));
            return isElementDisplayed(getBillCardUiElement(transactionId));
        }
    }
