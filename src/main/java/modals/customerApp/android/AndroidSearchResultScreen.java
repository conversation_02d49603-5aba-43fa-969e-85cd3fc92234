package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class AndroidSearchResultScreen extends BaseAndroidScreen {
    public AndroidSearchResultScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text=\"We can’t find any items matching your search results.\"]")
    WebElement noResultFoundLabel;

    @FindBy(xpath= "//android.widget.TextView[contains(@content-desc,'product_card_searchResults')]")
    List<WebElement> productResultsItems;

    @FindBy(xpath="(//android.widget.Button[@content-desc=\"favoriteIcon\"])")
    List<WebElement> favoriteIcons;

    @FindBy(xpath="//android.widget.TextView[@content-desc=\"searchResults_productsList_title\"]")
    WebElement productsTitle;
    
    @FindBy(xpath= "//android.widget.TextView[@text=\"View list\"]")
    WebElement viewListBtn;

    String categorySelector = "//android.view.ViewGroup[@content-desc='%s']";

    String categoryContentDescription = "searchResults_categoriesResults_category_%s_contentContainer";

    String subCategorySelector="//android.view.ViewGroup[@content-desc='%s']";

    String subCategoryContentDescription="searchResults_subCategoriesResults_subCategory_%s_contentContainer";

    String productSelector="//android.widget.Button[@content-desc='%s']";

    String productContentDescription="product_card_searchResults_%s";

    public boolean ifNoResultFoundLabelReturned() {
        return isElementDisplayed(noResultFoundLabel);
    }

    public void pressFavIconOfItem(int itemNumber) {
        wait.until(ExpectedConditions.visibilityOf(productsTitle));
        wait.until(ExpectedConditions.visibilityOf(favoriteIcons.get(itemNumber-1))).click();
    }

    public WebElement getCategoryUiElement(int CategoryId){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(categorySelector, getCategoryContentDescription(CategoryId)))));
        } catch (Exception e){
            return null;
        }
    }

    public boolean isCategoryDisplayedInSearchResults(int CategoryId){
        return isElementDisplayed(getCategoryUiElement(CategoryId));
    }

    public String getCategoryContentDescription(int CategoryId){
        return String.format(this.categoryContentDescription, CategoryId);
    }

    public WebElement getSubCategoryUiElement(int SubCategoryId){
        try {
           return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(subCategorySelector, getSubCategoryContentDescription(SubCategoryId)))));

        } catch (Exception e){
            return null;
        }
    }

    public boolean isSubCategoryDisplayedInSearchResults(int SubCategoryId){
        return isElementDisplayed(getSubCategoryUiElement(SubCategoryId));
    }

    public String getSubCategoryContentDescription(int subCategoryId){
        return String.format(this.subCategoryContentDescription, subCategoryId);
    }

    public WebElement getProductUiElement(int productId){
        try {
         return  wait.until(ExpectedConditions.visibilityOfElementLocated(
                 By.xpath(String.format(productSelector, getProductContentDescription(productId)))));
        } catch (Exception e){
         return null;
        }
    }

    public boolean isProductDisplayedInSearchResults(int ProductId){
            return isElementDisplayed(getProductUiElement(ProductId));

    }

    public String getProductContentDescription(int ProductId){
        return String.format(this.productContentDescription, ProductId);
    }

    public boolean checkProductResultsMatchKeyword(String keyword) {
        String[] words = keyword.split(" ");
        boolean exist = false;

        for (WebElement searchResult : productResultsItems) {
             for (String word : words) {
                 if (searchResult.getText().toLowerCase().contains(word)) {
                    exist = true;
                    break;
                 }
             }
            if(!exist) {

                return false;
            }
        }
        return true;
    }

    public void pressViewListBtn() {
        wait.until(ExpectedConditions.visibilityOf(viewListBtn))
                .click();
    }
}
