package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidCountriesSelectionScreen extends BaseAndroidScreen {
    public AndroidCountriesSelectionScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='countries-container']")
    WebElement countriesContainer;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='country-list-item-egypt']")
    WebElement egyptSelectionBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='country-list-item-ksa']")
    WebElement ksaSelectionBtn;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='continue-button']")
    WebElement continueBtn;

    @FindBy(xpath = "//android.widget.TextView[@content-desc='footnote-text']")
    WebElement footerNote;

    public boolean isPageDisplayed(){
        return isElementDisplayed(countriesContainer);
    }

    public void chooseCountryAndSubmit(String country){
        switch (country.toLowerCase()){
            case "egypt", "eg" -> {
                pressEgyptCountryOption();
                break;
            }
            case "ksa" -> {
                pressKsaCountryOption();
                break;
            }
            default -> {
                try {
                    pressEgyptCountryOption();
                } catch (Exception e){
                    //Return on failure
                    return;
                }
            }
        }
        pressContinueBtn();
    }

    public void pressEgyptCountryOption(){
        wait.until(ExpectedConditions.elementToBeClickable(egyptSelectionBtn)).click();
    }

    public void pressKsaCountryOption(){
        wait.until(ExpectedConditions.elementToBeClickable(ksaSelectionBtn)).click();
    }

    public void pressContinueBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(continueBtn)).click();
    }
}
