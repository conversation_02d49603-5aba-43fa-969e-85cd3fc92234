package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidRegisterSuccessScreen extends BaseAndroidScreen {
    public AndroidRegisterSuccessScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Your account has been successfully created.']")
    WebElement confirmationMessage;

    @FindBy(xpath = "//android.widget.TextView[@text='Done']")
    WebElement proceedBtn;

    public boolean isConfirmationMessageDisplayed(){
        return isElementDisplayed(confirmationMessage);
    }

    public void pressProceedBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(proceedBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(confirmationMessage));
    }
}
