package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.android.nativekey.AndroidKey;
import io.appium.java_client.android.nativekey.KeyEvent;
import modals.BaseAndroidScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidSearchScreen extends BaseAndroidScreen {
    public AndroidSearchScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.EditText[@content-desc='searchTextBox_textField']" )
    WebElement searchTxtField;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc='searchTextBox_textFieldContainer']")
    WebElement searchBoxContainer;

    public boolean isPageDisplayed(){
        return isElementDisplayed(searchBoxContainer);
    }

    public void enterKeywordForSearch(String keyword){
        wait.until(ExpectedConditions.visibilityOf(searchBoxContainer)).click();
        wait.until(ExpectedConditions.visibilityOf(searchTxtField)).sendKeys(keyword);
        wait.until(ExpectedConditions.textToBePresentInElement(searchTxtField, keyword));
        androidDriver.pressKey(new KeyEvent(AndroidKey.ENTER));
    }
}
