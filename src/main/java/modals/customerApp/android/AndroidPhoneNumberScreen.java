package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidPhoneNumberScreen extends BaseAndroidScreen {
    public AndroidPhoneNumberScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }

    @FindBy(xpath = "//android.widget.TextView[@text='Log in or sign up']")
    WebElement pageHeader;

    String countryCodeDropdownBtnXpath = "//android.widget.TextView[@text='%s']";

    @FindBy(xpath = "//android.widget.ScrollView//android.widget.EditText")
    WebElement phoneNumberTextField;

    @FindBy(xpath = "//android.widget.TextView[@text='Next']")
    WebElement submitBtn;

    @FindBy(xpath = "//android.widget.TextView[@text='Please use a valid number format']")
    WebElement invalidPhoneNumberError;

    @FindBy(xpath = "//android.widget.TextView[@text='Mobile number is required']")
    WebElement missingPhoneNumberError;

    public boolean isPageHeaderDisplayed(){
        try {
            wait.until(ExpectedConditions.visibilityOf(pageHeader));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public void pressCountryCodeBtn(String defaultCountryCode){
        wait.until(ExpectedConditions.elementToBeClickable(By.xpath(String.format(countryCodeDropdownBtnXpath,
                defaultCountryCode)))).click();
    }

    public void enterPhoneNumber(String phoneNumber){
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberTextField))
                .click();
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberTextField))
                .clear();
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberTextField))
                .sendKeys(phoneNumber);
        wait.until(ExpectedConditions.textToBePresentInElement(phoneNumberTextField, phoneNumber));
    }

    public String getPhoneNumberFieldValue(){
        return  wait.until(ExpectedConditions.elementToBeClickable(phoneNumberTextField))
                .getText();
    }

    public void pressNextBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(submitBtn))
                .click();
    }

    public void unFocusFromMobileNumberField(){
        wait.until(ExpectedConditions.elementToBeClickable(pageHeader)).click();
    }

    public boolean isPhoneErrorValidationMsgDisplayed(){
        return isElementDisplayed(invalidPhoneNumberError);
    }

    public boolean isMissingPhoneNumberErrorValidationMsgDisplayed(){
        return isElementDisplayed(missingPhoneNumberError);
    }
}
