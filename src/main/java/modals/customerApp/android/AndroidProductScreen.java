package modals.customerApp.android;

import io.appium.java_client.android.AndroidDriver;
import modals.BaseAndroidScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class AndroidProductScreen extends BaseAndroidScreen {
    public AndroidProductScreen(AndroidDriver androidDriver) {
        super(androidDriver);
        PageFactory.initElements(androidDriver, this);
    }
    @FindBy(xpath = "//android.widget.Button[@content-desc=\"favoriteIcon\"]")
    WebElement addToFavBtn;

    @FindBy(xpath = "//android.widget.Button[@content-desc=\"addToCart_button\"]")
    WebElement addToCart;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"categoryDetails_header_backBtn\"]")
    WebElement backBtn;

    @FindBy(xpath = "//android.widget.TextView[@resource-id=\"android:id/message\"]")
    WebElement notifyMePopUp;

    @FindBy(xpath = "//android.widget.Button[@resource-id=\"android:id/button2\"]")
    WebElement registerLoginBtnInNotifyMePopUp;

    @FindBy(xpath = "//android.widget.Button[@resource-id=\"android:id/button1\"]")
    WebElement noThanksBtnInNotifyMePopUp;

    @FindBy(xpath = "//android.widget.TextView[@content-desc=\"similarProducts_sectionTitle\"]")
    WebElement relatedProductsHeader;

    @FindBy(xpath = "//android.view.ViewGroup[@content-desc=\"goToCartButton\"]")
    WebElement cartIcon;

    String productSelector = "//android.widget.TextView[@content-desc='%s']";

    String productContentDescription = "productName_%s";

    String productNotifyMeBtnSelector = "//android.view.ViewGroup[@content-desc='%s']";

    String productNotifyMeBtnContentDescription = "product_%s_notifyMeBtn";

    String notifyMePopUpDescription = "To be able to receive a notification when %s is back, " +
            "you have to be registered/logged-in first.";

    public void clickFavoriteBtn(){
        wait.until(ExpectedConditions.visibilityOf(addToFavBtn)).click();
    }

    public void clickBackBtn(){
        wait.until(ExpectedConditions.visibilityOf(backBtn)).click();
    }

    public void clickAddToCart(){
        wait.until(ExpectedConditions.visibilityOf(addToCart)).click();
    }

    public boolean isProductPageDisplayed(int productId){
        return (isElementDisplayed(getProductUiElement(productId))
                || isElementDisplayed(relatedProductsHeader));
    }

    public String getProductContentDescription(int productId){
        return String.format(productContentDescription, productId);
    }

    public WebElement getProductUiElement(int productId){
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(productSelector, getProductContentDescription(productId)))));
    }

    public String getProductNotifyMeBtnContentDescription(int productId){
        return String.format(productNotifyMeBtnContentDescription, productId);
    }

    public WebElement getProductNotifyMeBtnUiElement(int productId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(productNotifyMeBtnSelector,
                        getProductNotifyMeBtnContentDescription(productId)))));
    }

    public void pressProductNotifyMeBtn(int productId){
        wait.until(ExpectedConditions.visibilityOf(getProductNotifyMeBtnUiElement(productId)))
                .click();
    }

    public boolean isProductNotifyMePopUpDisplayed(){
        return isElementDisplayed(notifyMePopUp);
    }

    public void pressRegisterLoginBtnInNotifyMe(){
        wait.until(ExpectedConditions.visibilityOf(registerLoginBtnInNotifyMePopUp)).click();
    }

    public void pressNoThanksBtnInNotifyMe(){
        wait.until(ExpectedConditions.visibilityOf(noThanksBtnInNotifyMePopUp)).click();
    }

    public String getNotifyMePopUpDescription(){
        return wait.until(ExpectedConditions.visibilityOf(notifyMePopUp)).getText();
    }

    public boolean isProductNotifyMePopUpDescriptionDisplayedCorrectly(String productName){
        return getNotifyMePopUpDescription().equals(String.format(notifyMePopUpDescription,productName));
    }

    public void clickOnCartIcon(){
        wait.until(ExpectedConditions.visibilityOf(cartIcon)).click();
    }
}
