package modals.customerApp.ios;

import helpers.factories.WebVideoRecorderFactory;
import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosChatbotScreen extends BaseIosScreen {
    public IosChatbotScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    //-----------------------------------ChatbotMainScreen----------------------------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Type a message\"]")
    WebElement chatbotTextField;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='Chat with us']")
    WebElement chatWithUsText;

    @FindBy(xpath = "//XCUIElementTypeButton[@name=\"freshchat send icon\"]")
    WebElement sendTextInTextField;
    //-----------------I have a question flow - Glad we could assist + Thank you for chatting----------------//
    @FindBy(xpath = "//XCUIElementTypeButton[[@text='Thank you for chatting with us today!]")
    WebElement thankYouForChattingWithUsMsg;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"userMenu_chatBtn\"]")

    WebElement chatBotBtn;
    //-----------------I have a question flow - Glad we could assist + Thank you for chatting----------------/

    //-----------------------------------ChatbotIHaveAnIssueFlow----------------------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'I have an issue with')]")
    WebElement iHaveAnIssueWithOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'late']")
    WebElement orderIsLateBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='I’d like to edit my order']")
    WebElement editMyOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='I’d like to cancel my order']")
    WebElement cancelMyOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Change payment method\"]")
    WebElement changePaymentMethodBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'assigned Delivery')]")
    WebElement assignToDaMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='Phone number']")
    WebElement phoneNumberBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='Payment method']")
    WebElement paymentMethodBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='OK']")
    WebElement okBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='No, I want to speak to an agent']")
    WebElement noIWantToSpeakToAnAgentBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'cancel')]")
    WebElement cancelMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='Add a product']")
    WebElement addAProductBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='Remove a product']")
    WebElement removeAProductBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='Change address']")
    WebElement changeAddressBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='I Ordered on a wrong address']")
    WebElement orderWrongAddressBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='Wrong Payment Method']")
    WebElement wrongPaymentMethodBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='Don't need the order any more']")
    WebElement dontNeedOrderAnyMoreBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'Order will get canceled shortly')]")
    WebElement orderCanceledMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='Yes, cancel my order']")
    WebElement yesCancelMyOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='No, keep my order']")
    WebElement noKeepMyOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Don't need the order any more\"]")
    WebElement doNotNeedOrderAnyMoreBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"Ok, a customer support agent will be with you shortly\"]")
    WebElement lastMsg;
    //----------------------------------IHaveAProblem/Complaint----------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='I have a problem/complaint']")
    WebElement IHaveAProblemComplaintBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='My delivered order']")
    WebElement MyDeliveredOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Making an order']")
    WebElement MakingAnOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Balance/Charge issue']")
    WebElement BalanceChargeIssueBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Applying a coupon code']")
    WebElement ApplyingACouponCodeBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Bills & donations payment']")
    WebElement BillsDonationsPaymentBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='The Delivery Associate']")
    WebElement TheDeliveryAssociateBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Back']")
    WebElement BackBtn;
    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'new message']")
    WebElement newMessagesBtn;
    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'Please share what went wrong in details & we’ll review it shortly.the future.']")
    WebElement pleaseShareWhatWentWrongMsg;
    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'Thank you, we’ll review the case & adjust the collection shortly.']")
    WebElement weWillReviewTheCaseAndAdjustTheCollectionShortlyMsg;
    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'Thank you, we'll address this shortly. Goodbye for now.']")
    WebElement thankYouWeWillAddressThisShortlyGoodByeMsg;
    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'How much did the agent collect?']")
    WebElement howMuchDidTheAgentCollectMsg;
    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'agent will get assigned to you')]")
    WebElement chatBotMessageToAssignCXAgentInProblemComplaintMenu;
    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@text, 'Please, give us more details & an agent will be with you shortly.']")
    WebElement pleaseGiveUsMoreDetailsMsg;
    @FindBy(xpath = "/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.FrameLayout[2]/android.widget.RelativeLayout/android.widget.RelativeLayout/android.widget.LinearLayout/android.widget.RelativeLayout/androidx.recyclerview.widget.RecyclerView/android.widget.LinearLayout/android.view.ViewGroup/android.widget.TextView[4]")
    WebElement someThingElseBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"The issue is:\"]")
    WebElement theIssueIsMsg;

    //----------------------------------IHaveAProblem/Complaint--> My delivered order-->----------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Poor quality or damaged items']")
    WebElement PoorQualityOrDamagedItemsBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Missing item(s)']")
    WebElement MissingItemBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Incorrect items or order']")
    WebElement IncorrectItemsOrOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Something else']")
    WebElement SomethingElseBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Select all that apply']")
    WebElement selectAllThatApplyDropdown;

    @FindBy(xpath = "//XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Which order did you face a problem with?\"]")
    WebElement whichOrderDidYouFaceAProblemWithMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='None of these products']")
    WebElement nonOfThisProductsCheckBox;

    String orderNumberUiSelector = "//XCUIElementTypeStaticText[contains(@text, '%s')]";
    String orderNumberContentDescription = "Order #%s";

    @FindBy(xpath = "//XCUIElementTypeApplication[@name=\"Breadfast\"]/XCUIElementTypeWindow[1]/XCUIElementTypeOther[3]/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeTable/XCUIElementTypeCell[1]")
    WebElement firstProductInTheOrderProductsList;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Deliver missing items\"]")
    WebElement deliverMissingItemsBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Refund the money\"]")
    WebElement refundMoneyBtn;

    @FindBy(xpath = "/XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"An agent will get assigned to you now to review the case & take the needed action.\"]")
    WebElement anAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsg;

    @FindBy(xpath = "/XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Would you like us to refund the amount on wallet or Credit Card?\"]")
    WebElement wouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text=\"Credit Card\"]")
    WebElement creditCardBtn;
    @FindBy(xpath = "//XCUIElementTypeStaticText[@text=\"Wallet\"]")
    WebElement walletBtn;
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Poor quality or damaged items']")
    WebElement poorQualityOrDamagedItemsBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Missing item(s)']")
    WebElement missingItemBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Incorrect items or order']")
    WebElement incorrectItemsOrOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"To help with our investigation, can you share a picture and, if available, the production date?\"]")
    WebElement canYouShareImgOrProductionDateMsg;

    @FindBy(xpath = "//XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you, a customer support agent will be with you shortly\"]")
    WebElement customerSupportWillBeWithYouShortly;

    @FindBy(xpath = "//XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Do you want us to deliver the missing item(s) or refund the money?\"]")
    WebElement doYouWantToDeliverMissingItemOrRefundMsg;

    @FindBy(xpath = "//XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you, an agent will get assigned to you shortly\"]")
    WebElement thankYouAgentWillGetAssignedToYouYouShortly;

    @FindBy(xpath = "//XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you for the clarification, an agent will be with you shortly\"]")
    WebElement thankYouForClarificationAnAgentWillBeWithYouShortly;

    @FindBy(xpath = "//XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"How many items were wrong?\"]")
    WebElement howWrongItemsWereWrongMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text=\"Whole order\"]")
    WebElement wholeOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@text=\"One or more items\"]")
    WebElement oneOrMoreItemsBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Please type what products did you receive instead what you should’ve received.\"]")
    WebElement pleaseEnterProductYouReceivedInsteadWhatShouldBeReceivedMsg;

    @FindBy(xpath = "//XCUIElementTypeTextView[@resource-id=\"com.breadfast:id/freshchat_text\" and @text=\"Thank you for the clarification, please enter the order number on the bags you received and an agent will be with you shortly\"]")
    WebElement pleaseEnterTheOrderNumberOnTheBagsYouReceivedMsg;

    //---------------------------------NonOfAboveOption---------------------------------------------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@text='None of the above']")
    WebElement nonOfTheAboveBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I have a suggestion or feedback\"]")
    WebElement IHaveASuggestionOrFeedback;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"Nice, we love to hear suggestions from our customers! :)\"]")
    WebElement LoveToHearSuggestionsMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I have a suggestion to add a product on Breadfast\"]")
    WebElement SuggestionToAddProductOnBreadfast;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"FreshchatMessageView\"]/XCUIElementTypeOther/XCUIElementTypeTable/XCUIElementTypeCell[39]/XCUIElementTypeTextView")
    WebElement SuggestionThankYouMsg ;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I have a general suggestion\"]")
    WebElement GeneralSuggestion ;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"Your suggestions are highly appreciated. Please type your message and click send.\"]")
    WebElement GeneralSuggestionMsg ;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I’d like to share a positive feedback\"]")
    WebElement SharePositiveFeedback ;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"FreshchatMessageView\"]/XCUIElementTypeOther/XCUIElementTypeTable/XCUIElementTypeCell[18]/XCUIElementTypeTextView")
    WebElement SharePositiveFeedbackMsg ;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I received an extra item\"]")
    WebElement IReceivedExtraItem ;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"Please enter the name of the product you received. An agent will be assigned to you shortly. \"]")
    WebElement IReceivedExtraItemMsg ;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"An agent will get assigned to you now\"]")
    WebElement IAnagentWillGetAssignedToYouNowMsg ;

    //----------------------------------IHaveAProblem/Complaint--> Balance/Charge issue-->----------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='I got charged more than once']")
    WebElement IGotChargedMoreThanOnceBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Inaccurate balance value\"]")
    WebElement InaccurateBalanceValueBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I paid more or less than what the DA reported\"]")
    WebElement IPaidMoreOrLessThanWhatTheDAReportedBtn;

    //----------------------------------IHaveAProblem/Complaint --> Service bills & Donations payments----------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I can’t pay my bill\"]")
    WebElement ICantPayMyBillBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Bill paid, but the service doesn't work\"]")
    WebElement BillPaidButTheServiceDoesntWorkBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"Thank you, an agent will get assigned to you shortly\"]")
    WebElement ServiceBillsDonationsPaymentsMsg;
    //----------------------------------IHaveAProblem/Complaint --> The Delivery Associate----------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Inappropriate behavior\"]")
    WebElement InappropriateBehaviorBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"He reported collecting a wrong amount\"]")
    WebElement HeReportedCollectingAWrongAmountBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"None of the above\"]")
    WebElement NoneOfTheAboveBtn;
    //----------------------------------I have an issue with a processing order----------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I have an issue with a processing order\"]")
    WebElement iHaveAnIssueWithProcessingOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I’d like to cancel my order\"]")
    WebElement idliketocancelmyorderBtn;

    //----------------------------------I Have a question----------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I have a question\"]")
    WebElement iHaveAQuestionBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"My current balance\"]")
    WebElement myCurrentBalanceBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Coffee stores\"]")
    WebElement coffeeStoresBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Rewards program\"]")
    WebElement rewardProgramBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"1 New Message\"]")
    WebElement newMsgBtn;

    //------------------I Have a question - Thanks for help & I have another Question----------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Thanks for the help\"]")
    WebElement thanksForTheHelpMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I have another question\"]")
    WebElement iHaveAnotherQuestionBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"We will review your question and get back to you shortly.\"]")
    WebElement cxAgentWillReviewYourQuestionMsg;

    //-------------I Have a question - Glad we could assist + Thank you for chatting with us--------------//
    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"Glad we could assist.\"]")
    WebElement gladWeCouldAssistMsg;

    //--------------------------I Have a question - My current balance----------------------//
    @FindBy(xpath = "//XCUIElementTypeOther[contains@name, 'To check your balance:")
    WebElement balanceStepsMsg;

    //----------------------------------I Have a question - Coffee Stores----------------------//
    @FindBy(xpath = "//XCUIElementTypeOther[contains@name, 'Breadfast Coffee Stores Locations")
    WebElement coffeeStoresLocationMsg;

    //----------------------------------I Have a question - Breadfast reward----------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"What are breadfast points?\"]")
    WebElement whatAreBreadfastPointsBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"Breadfast points are a reward system for customers who frequently purchase or engage in specific activities like earning badges. " +
            "These points can be collected and redeemed later for free delivery or products.\"]")
    WebElement breadfastPointsDescriptionMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"How to redeem my points?\"]")
    WebElement howToRedeemMyPointsBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"Note: If the coupon is for a free product, it will be automatically added to your order when applied.\"]")
    WebElement howToRedeemMyPointsMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"When will my points expire?\"]")
    WebElement whenWillMyPointsExpireBtn;

    @FindBy(xpath = "XCUIElementTypeTextView[contains@name=\"Points are valid for 6 months after you earn them.\"]")
    WebElement whenWillMyPointsExpireMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"I have pending points\"]")
    WebElement iHavePendingPointsBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"Points earned on each order will remain pending for 48 hours before becoming available for redemption. " +
            "This ensures that the order is completed before the points can be used.\"]")
    WebElement iHavePendingPointsMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"My question is not here\"]")
    WebElement myQuestionIsNotHereBtn;
    //----------------------------------I Have a question - Something Else----------------------//
    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"What does cashback mean?\"]")
    WebElement whatDoesCashBackMeanBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"A cashback coupon is a promotional voucher or discount that enables you to receive a percentage of your purchase amount back as balance in your Breadfast wallet. " +
            "When you apply a cashback coupon to an order, you'll receive a portion of the money spent returned to your Breadfast balance within 24 hours at most. " +
            "This balance can then be used in future orders.\"]")
    WebElement whatDoesCashBackMeanMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Where do you deliver?\"]")
    WebElement whereDoYouDeliverBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[contains@name=\"We currently offer delivery services to various major neighborhoods in Cairo, Alexandria, " +
            "and the North Coast, with plans for further expansion in the near future.\"]")
    WebElement whereDoYouDeliverMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"How can I tip the delivery agent?\"]")
    WebElement howCanITipTheDeliveryAgentBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[contains@name=\"You can tip your delivery associate " +
            "either during the order rating process or while checking out.\"]")
    WebElement howCanITipTheDeliveryAgentMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Can I apply more than one coupon code?\"]")
    WebElement canIApplyMoreThanOneCouponBtn;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name=\"You can apply only one coupon per order.\"]")
    WebElement canIApplyMoreThanOneCouponMsg;

    //-----------------------------------ChatbotMainScreen----------------------------------------//
    public boolean isPageDisplayed() {
        return isElementDisplayed(chatWithUsText);
    }

    public void pressSendMessageBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(sendTextInTextField))
                .click();
    }

    public void pressChatBotFromMore() {
        wait.until(ExpectedConditions.elementToBeClickable(chatBotBtn))
                .click();
    }

    public void enterTextIntoChatbotTxtField(String message) {
        wait.until(ExpectedConditions.elementToBeClickable(chatbotTextField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(chatbotTextField)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(chatbotTextField)).sendKeys(message);
        dismissKeyboardIfDisplayed(iosDriver);

    }
    //---------------------------------------clickOnIHaveAnIssueMethods--------------------------------------//
    public void pressIHaveAnIssue() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveAnIssueWithOrderBtn))
                .click();
    }

    public void pressOrderIsLate() {
        wait.until(ExpectedConditions.elementToBeClickable(orderIsLateBtn))
                .click();
    }

    public boolean isAssignedToDaMsgIsDisplayed() {
        return isElementDisplayed(assignToDaMsg);
    }

    public void pressPhoneNumber() {
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberBtn))
                .click();
    }

    public void pressPaymentMethod() {
        wait.until(ExpectedConditions.elementToBeClickable(paymentMethodBtn))
                .click();
    }

    public void PressChangePaymentMethodBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(changePaymentMethodBtn))
                .click();
    }

    public void pressEditMyOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(editMyOrderBtn))
                .click();
    }

    public void pressCancelMyOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(cancelMyOrderBtn))
                .click();
    }

    public void pressOk() {
        wait.until(ExpectedConditions.elementToBeClickable(okBtn))
                .click();
    }

    public void pressNoIWantToSpeakToAnAgent() {
        wait.until(ExpectedConditions.elementToBeClickable(noIWantToSpeakToAnAgentBtn))
                .click();
    }

    public boolean isCancelMsgIsDisplayed() {
        return isElementDisplayed(cancelMsg);
    }

    public void pressAddAProduct() {
        wait.until(ExpectedConditions.elementToBeClickable(addAProductBtn))
                .click();
    }

    public void pressRemoveAProduct() {
        wait.until(ExpectedConditions.elementToBeClickable(removeAProductBtn))
                .click();
    }

    public void pressChangeAddress() {
        wait.until(ExpectedConditions.elementToBeClickable(changeAddressBtn))
                .click();
    }

    public void pressOrderWrongAddress() {
        wait.until(ExpectedConditions.elementToBeClickable(orderWrongAddressBtn))
                .click();
    }

    public void PressWrongPaymentMethod() {
        wait.until(ExpectedConditions.elementToBeClickable(wrongPaymentMethodBtn))
                .click();
    }

    public void pressDontNeedOrderAnyMore() {
        wait.until(ExpectedConditions.elementToBeClickable(dontNeedOrderAnyMoreBtn))
                .click();
    }

    public boolean isOrderCanceledMsgIsDisplayed() {
        return isElementDisplayed(orderCanceledMsg);
    }

    public void pressYesCancelMyOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(yesCancelMyOrderBtn))
                .click();
    }

    public void pressNoKeepMyOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(noKeepMyOrderBtn))
                .click();
    }

    public boolean isAddAProductBtnDisplayed() {
        return isElementDisplayed(addAProductBtn);
    }

    public boolean isRemoveAProductBtnDisplayed() {
        return isElementDisplayed(removeAProductBtn);
    }

    public boolean isChangeAddressBtnDisplayed() {
        return isElementDisplayed(changeAddressBtn);
    }

    //---------------------------------NonOfAboveOption--------------------------------//
    public void pressNonOfTheAbove() {
        wait.until(ExpectedConditions.elementToBeClickable(nonOfTheAboveBtn))
                .click();
    }

    public boolean isNonOfAboveBtnDisplayed() {
        return isElementDisplayed(nonOfTheAboveBtn);
    }
    public void pressIHaveASuggestionOrFeedback() {
        wait.until(ExpectedConditions.elementToBeClickable(IHaveASuggestionOrFeedback))
                .click();
    }
    public boolean LoveToHearSuggestionsMsgDisplayed(){
        return isElementDisplayed(LoveToHearSuggestionsMsg);
    }

    public void pressSuggestionToAddProductOnBreadfast() {
        wait.until(ExpectedConditions.elementToBeClickable(SuggestionToAddProductOnBreadfast))
                .click();
    }
    public boolean SuggestionThankYouMsgDisplayed(){
        return isElementDisplayed(SuggestionThankYouMsg);
    }
    public void pressGeneralSuggestion() {
        wait.until(ExpectedConditions.elementToBeClickable(GeneralSuggestion))
                .click();
    }
    public boolean GeneralSuggestionMsgDisplayed(){
        return isElementDisplayed(GeneralSuggestionMsg);
    }
    public void pressSharePositiveFeedback() {
        wait.until(ExpectedConditions.elementToBeClickable(SharePositiveFeedback))
                .click();
    }
    public boolean SharePositiveFeedbackMsgDisplayed(){
        return isElementDisplayed(SharePositiveFeedbackMsg);
    }
    public void pressIReceivedExtraItem() {
        wait.until(ExpectedConditions.elementToBeClickable(IReceivedExtraItem))
                .click();
    }
    public boolean IReceivedExtraItemMsgDisplayed(){
        return isElementDisplayed(IReceivedExtraItemMsg);
    }
    public boolean IAnagentWillGetAssignedToYouNowMsgDisplayed(){
        return isElementDisplayed(IAnagentWillGetAssignedToYouNowMsg);
    }

    //----------------------------------IHaveAProblem/Complaint----------------------//
    public void pressIHaveAProblemComplaintBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(IHaveAProblemComplaintBtn))
                .click();
    }

    public void pressMyDeliveredOrderBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(MyDeliveredOrderBtn))
                .click();
    }

    public void pressMakingAnOrderBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(MakingAnOrderBtn))
                .click();
    }

    public void pressBalanceChargeIssueBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(BalanceChargeIssueBtn))
                .click();
    }

    public void pressApplyingACouponCodeBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(ApplyingACouponCodeBtn))
                .click();
    }

    public void pressBillsDonationsPaymentBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(BillsDonationsPaymentBtn))
                .click();
    }

    public void pressTheDeliveryAssociateBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(TheDeliveryAssociateBtn))
                .click();
    }

    public void pressBackBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(BackBtn))
                .click();
    }

    public boolean isTheIssueIsMsgDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(theIssueIsMsg));
        return isElementDisplayed(theIssueIsMsg);
    }
    public void takeActionIfNewMessageButtonDisplayed() {
        try {
            if (isNewMessageButtonDisplayed()) {
                {
                    wait.until(ExpectedConditions.visibilityOf(newMessagesBtn)).click();
                }
                wait.until(ExpectedConditions.invisibilityOf(newMessagesBtn));
            }
        }
        catch(Exception e){
            //Do Nothing
            return;
        }
    }
    public boolean isChatBotRedirectedUserToHaveProblemOrComplaintMenu() {
        wait.until(ExpectedConditions.visibilityOf(MakingAnOrderBtn));
        wait.until(ExpectedConditions.visibilityOf(BalanceChargeIssueBtn));
        wait.until(ExpectedConditions.visibilityOf(ApplyingACouponCodeBtn));
        wait.until(ExpectedConditions.visibilityOf(BillsDonationsPaymentBtn));
        wait.until(ExpectedConditions.visibilityOf(TheDeliveryAssociateBtn));
        wait.until(ExpectedConditions.visibilityOf(BackBtn));

        return isElementDisplayed(MakingAnOrderBtn)
                && isElementDisplayed(BalanceChargeIssueBtn)
                && isElementDisplayed(ApplyingACouponCodeBtn)
                && isElementDisplayed(BillsDonationsPaymentBtn)
                && isElementDisplayed(TheDeliveryAssociateBtn)
                && isElementDisplayed(BackBtn);
    }

    public boolean isPleaseShareWhatWentWrongMsgDisplayed(){
        return isElementDisplayed(pleaseShareWhatWentWrongMsg);
    }

    public boolean isWeWillReviewTheCaseAndAdjustTheCollectionShortlyMsgMsgDisplayed(){
        return isElementDisplayed(weWillReviewTheCaseAndAdjustTheCollectionShortlyMsg);
    }
    public boolean isThankYouWeWillAddressThisShortlyGoodByeMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(thankYouWeWillAddressThisShortlyGoodByeMsg));
        return isElementDisplayed(thankYouWeWillAddressThisShortlyGoodByeMsg);
    }
    public String getThankYouWeWillAddressThisShortlyGoodByeText(){
        wait.until(ExpectedConditions.visibilityOf(thankYouWeWillAddressThisShortlyGoodByeMsg));
        return thankYouWeWillAddressThisShortlyGoodByeMsg.getText();
    }
    public boolean isHowMuchDidTheAgentCollectMsgMsgDisplayed(){
        return isElementDisplayed(howMuchDidTheAgentCollectMsg);
    }
    public boolean isChatBotRedirectedUserToTheCxAgent(){
        return isElementDisplayed(chatBotMessageToAssignCXAgentInProblemComplaintMenu);
    }
    public String getChatBotRedirectedUserToTheCxAgentText(){
        wait.until(ExpectedConditions.visibilityOf(chatBotMessageToAssignCXAgentInProblemComplaintMenu));
        return chatBotMessageToAssignCXAgentInProblemComplaintMenu.getText();
    }
    public void pressSomethingElse() {
        wait.until(ExpectedConditions.elementToBeClickable(someThingElseBtn))
                .click();
    }
    public boolean isPleaseGiveUsMoreDetailsMsgDisplayed(){
        return isElementDisplayed(pleaseGiveUsMoreDetailsMsg);
    }
    //----------------------------------IHaveAProblem/Complaint--> My delivered order-->----------------------//
    public void pressPoorQualityOrDamagedItemsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(PoorQualityOrDamagedItemsBtn))
                .click();
    }

    public void pressMissingItemBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(MissingItemBtn))
                .click();
    }

    public void pressSelectAllThatApplyDropdown() {
        wait.until(ExpectedConditions.elementToBeClickable(selectAllThatApplyDropdown))
                .click();
    }

    public void pressNonOfThisProductsCheckBox() {
        wait.until(ExpectedConditions.elementToBeClickable(nonOfThisProductsCheckBox))
                .click();
    }

    public void pressIncorrectItemsOrOrderBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(IncorrectItemsOrOrderBtn))
                .click();
    }

    public boolean isWhichOrderDidYouFaceAProblemWithMsgDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(whichOrderDidYouFaceAProblemWithMsg));
        return isElementDisplayed(whichOrderDidYouFaceAProblemWithMsg);
    }

    public void selectOrderNumberElement(String orderNumber) {
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(orderNumberUiSelector, getOrderNumberContentDescription(orderNumber))))).click();
    }

    public String getOrderNumberContentDescription(String orderNumber) {
        return String.format(this.orderNumberContentDescription, orderNumber);
    }

    public void pressFirstProductInTheOrderProductsList() {
        wait.until(ExpectedConditions.elementToBeClickable(firstProductInTheOrderProductsList))
                .click();
    }

    public void pressRefundMoneyBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(refundMoneyBtn))
                .click();
    }

    public void pressSomethingElseBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(SomethingElseBtn))
                .click();
    }

    public void pressCreditCardBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(creditCardBtn))
                .click();
    }

    public void pressWalletBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(walletBtn))
                .click();
    }

    public boolean isNewMessageButtonDisplayed() {
        return isElementDisplayed(newMessagesBtn);
    }

    public boolean areOptionsForMyDeliveredOrderIssueDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(poorQualityOrDamagedItemsBtn));
        wait.until(ExpectedConditions.visibilityOf(missingItemBtn));
        wait.until(ExpectedConditions.visibilityOf(incorrectItemsOrOrderBtn));
        wait.until(ExpectedConditions.visibilityOf(someThingElseBtn));
        wait.until(ExpectedConditions.visibilityOf(BackBtn));

        return isElementDisplayed(poorQualityOrDamagedItemsBtn)
                && isElementDisplayed(missingItemBtn)
                && isElementDisplayed(incorrectItemsOrOrderBtn)
                && isElementDisplayed(someThingElseBtn)
                && isElementDisplayed(BackBtn);
    }

    public boolean isOrderProductDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(firstProductInTheOrderProductsList));
        return isElementDisplayed(firstProductInTheOrderProductsList);
    }

    public void pressDeliverMissingItemsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(deliverMissingItemsBtn))
                .click();
    }
    public boolean isHowWrongItemsWereWrongMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(howWrongItemsWereWrongMsg));
        return isElementDisplayed(howWrongItemsWereWrongMsg);
    }
    public void pressWholeOrderBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(wholeOrderBtn))
                .click();
    }
    public void pressOneOrMoreItemsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(oneOrMoreItemsBtn))
                .click();
    }
    public boolean isPleaseEnterProductYouReceivedInsteadWhatShouldBeReceivedMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(pleaseEnterProductYouReceivedInsteadWhatShouldBeReceivedMsg));
        return isElementDisplayed(pleaseEnterProductYouReceivedInsteadWhatShouldBeReceivedMsg);
    }
    public boolean isPleaseEnterTheOrderNumberOnTheBagsYouReceivedMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(pleaseEnterTheOrderNumberOnTheBagsYouReceivedMsg));
        return isElementDisplayed(pleaseEnterTheOrderNumberOnTheBagsYouReceivedMsg);
    }
    //----------------------------------IHaveAProblem/Complaint--> Balance/Charge issue-->----------------------//
    public void pressIGotChargedMoreThanOnceBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(IGotChargedMoreThanOnceBtn))
                .click();
    }

    public void pressInaccurateBalanceValueBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(InaccurateBalanceValueBtn))
                .click();
    }

    public void pressIPaidMoreOrLessThanWhatTheDAReportedBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(IPaidMoreOrLessThanWhatTheDAReportedBtn))
                .click();
    }

    //----------------------------------IHaveAProblem/Complaint --> Service bills & Donations payments----------------------//
    public void pressICantPayMyBillBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(ICantPayMyBillBtn))
                .click();
    }

    public void pressBillPaidButTheServiceDoesntWorkBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(BillPaidButTheServiceDoesntWorkBtn))
                .click();
    }

    public boolean isServiceBillsDonationsPaymentsMsgDisplayed() {
        return isElementDisplayed(ServiceBillsDonationsPaymentsMsg);
    }

    //----------------------------------IHaveAProblem/Complaint --> The Delivery Associate----------------------//
    public void pressInappropriateBehaviorBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(InappropriateBehaviorBtn))
                .click();
    }

    public void pressHeReportedCollectingAWrongAmountBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(HeReportedCollectingAWrongAmountBtn))
                .click();
    }

    public void pressNoneOfTheAboveBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(NoneOfTheAboveBtn))
                .click();
    }
    //--------------I have a question flow - Glad we could assist + Thank you for chatting Methods-------------//

    public boolean isThankYouForChattingWithUsMsgDisplayed() {
        return isElementDisplayed(thankYouForChattingWithUsMsg);
    }

    //----------------------------------I have an issue with a processing order----------------------//
    public void pressIHaveAnIssueWithProcessingOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveAnIssueWithProcessingOrderBtn))
                .click();
    }

    public void PressdoNotNeedOrderAnyMoreBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(doNotNeedOrderAnyMoreBtn))
                .click();
    }

    public void pressIWouldLikeToCancelMyOrderBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(idliketocancelmyorderBtn))
                .click();
    }
    public boolean isIOrderOnAWrongAddressBtnDisplayed(){
        return isElementDisplayed(orderWrongAddressBtn);}

    public boolean isWrongPaymentMethodBtnDisplayed(){
        return isElementDisplayed(wrongPaymentMethodBtn);}

    public boolean isDoNotNeedOrderAnYMoreBtnDisplayed(){
        return isElementDisplayed(doNotNeedOrderAnyMoreBtn);}

    public boolean isNonOfTheAboveBtnDisplayed(){
        return isElementDisplayed(nonOfTheAboveBtn);}

    public boolean islastMsgDisplayed(){
        return isElementDisplayed(lastMsg);}

    public boolean isGladWeCouldAssistMsgDisplayed() {
        return isElementDisplayed(gladWeCouldAssistMsg);
    }

    //------------------I Have a question - Thanks for help & I have another Question----------------------//

    public boolean isThanksForTheHelpMsgDisplayed() {
        return isElementDisplayed(thanksForTheHelpMsg);
    }

    public void pressIHaveAnotherQuestionBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveAnotherQuestionBtn))
                .click();
    }

    public boolean isCxAgentWillReviewYouQuestionMsgDisplayed() {
        return isElementDisplayed(cxAgentWillReviewYourQuestionMsg);
    }
    //--------------------------I Have a question - My current balance----------------------//

    public void pressIHaveAQuestionBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iHaveAQuestionBtn)).click();
    }

    public void pressBalanceStepsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(myCurrentBalanceBtn))
                .click();
    }

    public boolean balanceStepsMsgDisplayed() {
        return isElementDisplayed(balanceStepsMsg);
    }

    //----------------------------------I Have a question - Coffee Stores----------------------//

    public void pressCoffeeStoresLocationBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(coffeeStoresBtn))
                .click();
    }

    public boolean coffeeStoresLocationMsgDisplayed() {
        return isElementDisplayed(coffeeStoresLocationMsg);
    }

    //----------------------------------I Have a question - Breadfast reward----------------------//

    public void pressBreadfastRewardBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(rewardProgramBtn))
                .click();
    }

    public void pressWhatAreBreadfastPointsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(whatAreBreadfastPointsBtn))
                .click();
    }

    public boolean isBreadfastPointsMsgDisplayed() {
        return isElementDisplayed(breadfastPointsDescriptionMsg);
    }

    //howToRedeemMyPointsBtn
    public void pressHowToRedeemMyPointsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(howToRedeemMyPointsBtn))
                .click();
    }

    public boolean isHowToRedeemMyPointsMsgDisplayed() {
        return isElementDisplayed(howToRedeemMyPointsMsg);
    }

    public void pressWhenWillMyPointsExpireBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(whenWillMyPointsExpireBtn))
                .click();
    }

    public boolean isWhenWillMyPointsExpireMsgDisplayed() {
        return isElementDisplayed(whenWillMyPointsExpireMsg);
    }

    public void pressIHavePendingPointsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(iHavePendingPointsBtn))
                .click();
    }

    public boolean isIHavePendingPointsMsgDisplayed() {
        return isElementDisplayed(iHavePendingPointsMsg);
    }

    public void pressMyQuestionIsNotHereBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(myQuestionIsNotHereBtn))
                .click();
    }
    //----------------------------------I Have a question - Something Else----------------------//

    public void pressSomeThingElseBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(someThingElseBtn))
                .click();
    }

    public void pressWhatDoesCashBackMeanBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(whatDoesCashBackMeanBtn))
                .click();
    }

    public boolean isWhatDoesCashBackMeanMsgDisplayed() {
        return isElementDisplayed(whatDoesCashBackMeanMsg);
    }

    public void pressWhereDoYouDeliverBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(whereDoYouDeliverBtn))
                .click();
    }

    public boolean isWhereDoYouDeliverMsgDisplayed() {
        return isElementDisplayed(whereDoYouDeliverMsg);
    }

    public void pressHowCanITipTheDeliveryAgentBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(howCanITipTheDeliveryAgentBtn))
                .click();
    }

    public boolean isHowCanITipTheDeliveryAgentMsgDisplayed() {
        return isElementDisplayed(howCanITipTheDeliveryAgentMsg);
    }

    public void pressCanIApplyMoreThanOneCouponBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(canIApplyMoreThanOneCouponBtn))
                .click();
    }

    public boolean isCanIApplyMoreThanOneCouponMsgDisplayed() {
        return isElementDisplayed(canIApplyMoreThanOneCouponMsg);
    }
    //----------------------------- General Messages -------------------------------------------------------

    public boolean isCanYouShareImgOrProductionDateMsgDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(canYouShareImgOrProductionDateMsg));
        return isElementDisplayed(canYouShareImgOrProductionDateMsg);
    }

    public boolean isCustomerSupportWillBeWithYouShortlyMsgDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(customerSupportWillBeWithYouShortly));
        return isElementDisplayed(customerSupportWillBeWithYouShortly);
    }
    public boolean isAnAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsgDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(anAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsg));
        return isElementDisplayed(anAgentWillAssignedToYouToReviewCaseAndTakeNeededActionMsg);
    }

    public boolean isWouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsgDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(wouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsg));
        return isElementDisplayed(wouldYouLikeToRefundTheAmountOnWalletOrCreditCardMsg);
    }

    public boolean isDoYouWantToDeliverMissingItemOrRefundMsgDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(doYouWantToDeliverMissingItemOrRefundMsg));
        return isElementDisplayed(doYouWantToDeliverMissingItemOrRefundMsg);
    }

    public boolean isThankYouAgentWillGetAssignedToYouYouShortlyMsgDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(thankYouAgentWillGetAssignedToYouYouShortly));
        return isElementDisplayed(thankYouAgentWillGetAssignedToYouYouShortly);
    }
    public boolean isThankYouForClarificationAnAgentWillBeWithYouShortlyMsgDisplayed(){
        wait.until(ExpectedConditions.visibilityOf(thankYouForClarificationAnAgentWillBeWithYouShortly));
        return isElementDisplayed(thankYouForClarificationAnAgentWillBeWithYouShortly);
    }
        public boolean isNewMsgBtnDisplayed () {
            return isElementDisplayed(newMsgBtn);
        }
        public void pressNewMsgBtnIfDisplayed () {
            try {
                if (isNewMsgBtnDisplayed()) {
                    {
                        wait.until(ExpectedConditions.visibilityOf(newMsgBtn)).click();
                    }
                    wait.until(ExpectedConditions.invisibilityOf(newMsgBtn));
                }
            } catch (Exception e) {
                return;
            }
        }
    }
