package modals.customerApp.ios.iosAlerts;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.jetbrains.annotations.Nullable;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosLocationPermissionAlert extends BaseIosScreen {
    public IosLocationPermissionAlert(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeAlert[@name='Allow “Breadfast” to use your location?']")
    WebElement locationPermissionAlert;

    @FindBy(xpath = "//XCUIElementTypeButton[contains(@name, 'Precise:')]")
    WebElement preciseLocationBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Allow Once']")
    WebElement allowOnceBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Allow While Using App']")
    WebElement allowWhileUsingBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Don’t Allow']")
    WebElement dontAllowBtn;

    public boolean isLocationPermissionAlertDisplayed(){
        return isElementDisplayed(locationPermissionAlert);
    }

    public void switchPreciseLocationBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(preciseLocationBtn));
    }

    public void pressAllowOnceBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(allowOnceBtn))
                .click();
    }

    public void pressAllowWhileUsingBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(allowWhileUsingBtn))
                .click();
    }

    public void pressDontAllowBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(dontAllowBtn))
                .click();
    }

    public void takeActionIfAlertIsDisplayed(String action){
        if (isLocationPermissionAlertDisplayed()){
            switch (action) {
                case "once" -> pressAllowOnceBtn();
                case "whileUsing" -> pressAllowWhileUsingBtn();
                default -> pressDontAllowBtn();
            }
            wait.until(ExpectedConditions.invisibilityOf(locationPermissionAlert));
        }
    }

}
