package modals.customerApp.ios.iosAlerts;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNotificationsPermissionsAlert extends BaseIosScreen {
    public IosNotificationsPermissionsAlert(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeAlert[@name='“Breadfast” Would Like to Send You Notifications']")
    WebElement notificationsPermissionAlert;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Don’t Allow']")
    WebElement dontAllowBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Allow']")
    WebElement allowBtn;

    public boolean isNotificationsPermissionsAlertDisplayed(){
        return isElementDisplayed(notificationsPermissionAlert);
    }

    public void pressAllowBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(allowBtn))
                .click();
    }

    public void pressDontAllowBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(dontAllowBtn))
                .click();
    }

    private void acceptNotificationsPermission(){
        pressAllowBtn();
    }

    private void rejectNotificationsPermission(){
        pressDontAllowBtn();
    }

    public void takeActionIfAlertIsDisplayed(String action){
        if (isNotificationsPermissionsAlertDisplayed()){
            switch (action) {
                case "accept" -> acceptNotificationsPermission();
                default -> rejectNotificationsPermission();
            }
            wait.until(ExpectedConditions.invisibilityOf(notificationsPermissionAlert));
        }
    }
}
