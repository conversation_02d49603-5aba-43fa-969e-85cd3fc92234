package modals.customerApp.ios.iosAlerts;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosTrackingPermissionAlert extends BaseIosScreen {
    public IosTrackingPermissionAlert(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeAlert[@name='Allow “Breadfast” to track your activity " +
            "across other companies’ apps and websites?']")
    WebElement trackingPermissionAlert;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Ask App Not to Track']")
    WebElement dontTrackBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Allow']")
    WebElement allowBtn;

    public boolean isTrackingPermissionsAlertDisplayed(){
        return isElementDisplayed(trackingPermissionAlert);
    }

    public void pressAllowBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(allowBtn))
                .click();
    }

    public void pressDontTrackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(dontTrackBtn))
                .click();
    }

    private void acceptNotificationsPermission(){
        pressAllowBtn();
    }

    private void rejectNotificationsPermission(){
        pressDontTrackBtn();
    }

    public void takeActionIfAlertIsDisplayed(String action){
        if (isTrackingPermissionsAlertDisplayed()){
            switch (action) {
                case "accept" -> acceptNotificationsPermission();
                default -> rejectNotificationsPermission();
            }
            wait.until(ExpectedConditions.invisibilityOf(trackingPermissionAlert));
        }
    }

}
