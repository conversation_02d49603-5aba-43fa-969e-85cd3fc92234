package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosCountriesListScreen extends BaseIosScreen {
    public IosCountriesListScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='text-input-country-filter']")
    WebElement countrySearchTextField;

    public String countrySelector = "//XCUIElementTypeOther[@name='country-selector-%s']";

    public void searchForCountry(String countryCode){
        wait.until(ExpectedConditions.elementToBeClickable(countrySearchTextField))
                .sendKeys(countryCode);
    }

    public void selectCountry(String countryCode){
        WebElement country = wait.until(ExpectedConditions
                .visibilityOfElementLocated(By.xpath(String.format(countrySelector, countryCode))));
        wait.until(ExpectedConditions.elementToBeClickable(country)).click();
        wait.until(ExpectedConditions.invisibilityOf(countrySearchTextField));
    }

    public boolean isCountriesListScreenDisplayed(){
        return isElementDisplayed(countrySearchTextField);
    }
}
