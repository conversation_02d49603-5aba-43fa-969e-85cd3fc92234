package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import models.Address;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.springframework.web.bind.support.WebExchangeBindException;

public class IosSetAddressScreen extends BaseIosScreen {
    public IosSetAddressScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(name = "//XCUIElementTypeStaticText[@name='Select Delivery Location']")
    WebElement pageHeader;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='CONFIRM LOCATION']")
    WebElement confirmLocationBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='OK']")
    WebElement okBtn;

    String saveAddressBtnContentDescription= "SAVE ADDRESS";

    public boolean isPageHeaderDisplayed(){
        return isElementDisplayed(pageHeader);
    }

    public boolean isConfirmLocationBtnDisplayed(){
        return isElementDisplayed(confirmLocationBtn);
    }

    public void pressConfirmLocationBtn(){
        wait.until(ExpectedConditions.visibilityOf(confirmLocationBtn))
                .click();
        // Wait until confirm Location btn is dismissed or retry
        try {
            wait.until(ExpectedConditions.invisibilityOf(confirmLocationBtn));
        } catch (Exception e){
            pressConfirmLocationBtn();
        }
    }

    public void confirmLocationIfDisplayed(){
        try {
            if (isConfirmLocationBtnDisplayed()){
                pressConfirmLocationBtn();
            }
        } catch (Exception e){
            //Do Nothing
        }
    }
}
