package modals.customerApp.ios.iosCheckoutScreen;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosCartScreen extends BaseIosScreen {
    public IosCartScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeButton[@name=\"cartScreen_goToCheckoutBtn\"]")
    WebElement goToCheckoutBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"cartGoToCheckoutBtn_deliveryFeesContainer\"]")
    WebElement deliveryFeesElement;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"cart_goToCheckoutBtn_cartSubTotal\"]")
    WebElement cartSubTotal;
    @FindBy(xpath =
            "//XCUIElementTypeStaticText[@name=\"Seems you haven’t added anything to your cart yet! Start shopping and add items.\"]")
    WebElement emptyCart;
    String productRowContainerSelector = "//XCUIElementTypeOther[@name=\"productItem_%s_container\"]";

    String productDeleteQtyBtnSelector = "//XCUIElementTypeOther[@name=\"Delete_imgview\"]";

    String productDecreaseQtyBtnSelector = "//XCUIElementTypeOther[@name=\"Delete\"]";

    String productIncreaseQtyBtnSelector = "//XCUIElementTypeOther[@name=\"Add_imgview\"]";

    String productPriceSelector = "//XCUIElementTypeStaticText[@name=\"ProductPrice_txt\"]";

    String productCurrentQtyUiElementSelector = "//XCUIElementTypeOther[@name=\"productQuantity_txt\"]";

    public boolean isProductRowDisplayed(int productId){
        try {
            wait.until(ExpectedConditions.elementToBeClickable(By.xpath(productRowContainerSelector)));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public void pressProductDeleteBtn(int productId){
        wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath(String.format(productRowContainerSelector, productId)
                        + productDeleteQtyBtnSelector))).click();
    }

    public void pressProductDecreaseQtyBtn(int productId){
        wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath(String.format(productRowContainerSelector, productId)
                        + productDecreaseQtyBtnSelector))).click();
    }

    public void pressProductIncreaseQtyBtn(int productId){
        wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath(String.format(productRowContainerSelector, productId)
                        + productIncreaseQtyBtnSelector))).click();
    }

    public void pressGoToCheckoutBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(goToCheckoutBtn))
                .click();
    }

    public Double getProductPriceValue(int productId) {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(productRowContainerSelector, productId)
                        + productPriceSelector))).getText()
                .replace("EGP ", "").replace("SAR ", ""));
    }

    public int getProductCurrentQtyInCart(int productId) {
        return  Integer.parseInt(wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(productRowContainerSelector, productId)
                        + productCurrentQtyUiElementSelector))).getText());
    }

    public Double getCurrentCartTotal() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(cartSubTotal))
                .getText().replace("EGP ", "").replace("SAR ", ""));
    }

    public Double getCurrentCartDeliveryFees() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(deliveryFeesElement)).getText()
                .replace(" delivery fees", "").replace("+", ""));
    }

    public Double getCurrentTotalAndDelivery() {
        double total = getCurrentCartTotal();
        double deliveryFees = getCurrentCartDeliveryFees();
        return total + deliveryFees;
    }

    public boolean isFessDisplayed() {
        return isElementDisplayed(deliveryFeesElement);
    }

    public boolean isTotalDisplayed() {
        return isElementDisplayed(cartSubTotal);
    }

    public boolean isCheckoutBtnClickable() {
        try {
            wait.until(ExpectedConditions.elementToBeClickable(goToCheckoutBtn));
            return true;
        } catch (Exception e) {
            return false;
        }
    }
        public void updateCartUntilTotalReached(double targetTotal) {
        // Enter the loop until the cart total is greater than or equal to the targetTotal which will be = 250
        while (getCurrentCartTotal() < targetTotal) {
            // Add items to the cart
            pressProductIncreaseQtyBtn(1);

            // Introduce a wait to avoid constant checking
            try {
                Thread.sleep(1000); // pause for 1 second in each check
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }
    public boolean isCartEmpty() {
        return isElementDisplayed(emptyCart);
    }
}
