package modals.customerApp.ios.iosCheckoutScreen;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class IosCheckoutScreen extends BaseIosScreen {

    public IosCheckoutScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Checkout\"]")
    WebElement pageHeaderContainer;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='checkoutScreen_grandTotal_value']")
    WebElement orderGrandTotal;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='checkoutScreen_deliveryFees_orderFees_valueContainer']")
    WebElement orderDeliveryFees;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='checkoutScreen_subtotal_value']")
    WebElement orderSubTotal;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='checkoutScreen_submitBtn']")
    WebElement placeOrderBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"paymentSection_cashOnDelivery_rowContainer\"]")
    WebElement codPaymentMethodOption;

    @FindBy(xpath = "//XCUIElementTypeSwitch[@name='useMyBalanceToggleBtn']")
    WebElement useMyBalanceToggle;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='useMyBalance_currentBalanceValue']")
    WebElement currentBalanceValue;

    @FindBy(xpath = "(//XCUIElementTypeStaticText[@name='checkoutScreen_value'])[2]")
    WebElement previousDueAmount;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='paymentSection_container']")
    WebElement paymentSectionContainer;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='checkoutScreen_feesBreakdown_container']")
    WebElement orderFeesBreakDownContainer;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='PromoCodeAdd_btn']")
    WebElement applyCouponCodeBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='checkoutScreen_promoCodeSection_title']")
    WebElement promoCodeSection;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='cardSelectionModal_modalTitle']")
    WebElement modalTitle;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='cardSelectionModal_addNewCard_btnContents']")
    WebElement addNewCardBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='cardMiddleRow_contentContainer']")
    WebElement creditCardPaymentOption;

    @FindBy(xpath = "//XCUIElementTypeOther[contains@name=\"Delivery time Schedule Instant]")
    WebElement instantDeliveryBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='selectDeliveryDate_dropDown']")
    WebElement deliveryDateDropdown;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='selectDeliveryDate_dropDown_text']")
    WebElement deliveryDayText;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='selectDeliveryTime_dropDown']")
    WebElement deliveryTimeDropdown;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='selectDeliveryTime_dropDown_text']")
    WebElement deliveryTimeText;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='modalContainer']")
    WebElement deliveryDateTimeModalContainer;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='modalTitle']")
    WebElement deliveryDateTimeModalTitle;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='modalCloseBtn']")
    WebElement deliveryDateTimeModalCloseBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='deliveryDayPicker_day_container']")
    List<WebElement> availableDeliveryDays;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='timeslot_view']")
    List<WebElement> availableDeliveryTimes;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='checkoutScreen_promoCodeSection_errorMsg']")
    WebElement couponCodeErrorMsg;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"Select delivery time\"]")
    WebElement selectTomorrowDeliveryTimeBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Schedule\"]")
    WebElement selectNowDeliveryTimeBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[contains(@name, 'deliveryTimeSlotsModal_timeSlot_') " +
            "and contains(@name, '_cellContainer')]")
    List<WebElement> availableTimeSlots;

    String orderFeesBreakDownNameSelector = "checkoutScreen_feesBreakdown_container";

    String scrollableContentContainerSelector = "//XCUIElementTypeScrollView";

    String useMyBalanceNameSelector = "useMyBalanceToggleBtn";

    String cashOnDeliveryBtnNameSelector = "paymentSection_cashOnDelivery_rowContainer";

    String paymentSectionNameSelector = "paymentSection_container";

    String previousDueAmountNameSelector = "checkoutScreen_title";
    String promoCodeTxtFieldContentDescription = "Promocode_cell";

    String promoCodeTxtFieldSelector = "//XCUIElementTypeOther[@content-desc='%s']";

    public boolean isPageDisplayed() {
        return isElementDisplayed(pageHeaderContainer);
    }

    public Double getOrderGrandTotalValue() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(orderGrandTotal))
                .getText().replace("EGP ", ""));
    }

    public Double getOrderSubTotalValue() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(orderSubTotal))
                .getText().replace("EGP ", ""));
    }

    public WebElement getScrollableContentContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e) {
            return null;
        }
    }

    public String getCashOnDeliveryBtnNameSelector() {
        return cashOnDeliveryBtnNameSelector;
    }

    public String getDeliveryFeesTxtValue() {
        return wait.until(ExpectedConditions.visibilityOf(orderDeliveryFees)).getText();
    }

    public Double getDeliveryFeesAmount() {
        try {
            // Define a regular expression pattern to match "EGP" followed by a double value
            Pattern pattern = Pattern.compile("EGP (\\d+(\\.\\d+)?)");
            Matcher matcher = pattern.matcher(getDeliveryFeesTxtValue());

            double lastValue = 0.0; // Initialize with a default value

            // Find all matches
            while (matcher.find()) {
                // Extract and parse the matched double value
                String doubleString = matcher.group(1);
                lastValue = Double.parseDouble(doubleString);
            }
            return lastValue;
        } catch (Exception e) {
            return 0.0;
        }
    }

    public void pressPlaceOrderBtn() {
        wait.until(ExpectedConditions.visibilityOf(placeOrderBtn)).click();
        try {
            wait.until(ExpectedConditions.invisibilityOf(placeOrderBtn));
        } catch (Exception e) {
            // Do nothing
        }
    }

    public boolean isPreviousDueAmountIsDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(previousDueAmount)).isDisplayed();
        return true;
    }

    public void pressCashOnDeliveryPaymentOption() {
        wait.until(ExpectedConditions.visibilityOf(codPaymentMethodOption))
                .click();
    }

    public void pressCreditCardPaymentOption() {
        wait.until(ExpectedConditions.visibilityOf(creditCardPaymentOption))
                .click();
    }

    public void pressUseMyBalanceToggle() {
        wait.until(ExpectedConditions.visibilityOf(useMyBalanceToggle))
                .click();
    }

    public String getUseMyBalanceNameSelector() {
        return useMyBalanceNameSelector;
    }

    public boolean isBalanceToggleEnabled() {
        try {
            return wait.until(ExpectedConditions.visibilityOf(useMyBalanceToggle))
                    .getAttribute("value").equalsIgnoreCase("1");
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isBalanceDisplayed() {
        return isElementDisplayed(currentBalanceValue);
    }

    public String getPaymentSectionNameSelector() {
        return paymentSectionNameSelector;
    }

    public Double getPreviousDueAmountValue() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(previousDueAmount))
                .getText().replace("EGP ", ""));
    }

    public String getPreviousDueAmountNameSelector() {
        return previousDueAmountNameSelector;
    }

    public boolean isModalDisplayed() {
        return isElementDisplayed(modalTitle);
    }

    public void pressAddNewCardBtn() {
        wait.until(ExpectedConditions.visibilityOf(addNewCardBtn))
                .click();
    }

    public String getOrderFeesBreakDownNameSelector() {
        return orderFeesBreakDownNameSelector;
    }

    public boolean isOrderFeesBreakdownContainerDisplayed() {
        return isElementDisplayed(orderFeesBreakDownContainer);
    }

    public void openDeliveryDaysDropdown() {
        wait.until(ExpectedConditions.visibilityOf(deliveryDateDropdown))
                .click();
    }

    public void openDeliveryTimeDropdown() {
        wait.until(ExpectedConditions.visibilityOf(deliveryTimeDropdown))
                .click();
    }

    public boolean isDeliveryDaysDropdownDisplayed() {
        return isElementDisplayed(deliveryDateTimeModalContainer)
                && isElementDisplayed(deliveryDateTimeModalTitle)
                && isElementDisplayed(availableDeliveryDays.get(0));
    }

    public boolean isDeliveryTimesDropdownDisplayed() {
        return isElementDisplayed(deliveryDateTimeModalContainer)
                && isElementDisplayed(deliveryDateTimeModalTitle)
                && isElementDisplayed(availableDeliveryTimes.get(0));
    }

    public void dismissDeliveryDateTimeModal() {
        wait.until(ExpectedConditions.visibilityOf(deliveryDateTimeModalCloseBtn))
                .click();
    }

    public void selectFirstAvailableDeliveryDay() {
        wait.until(ExpectedConditions.visibilityOf(availableDeliveryDays.get(0))).click();
        wait.until(ExpectedConditions.invisibilityOf(deliveryDateTimeModalContainer));
    }

    public void selectFirstAvailableDeliveryTime() {
        wait.until(ExpectedConditions.visibilityOf(availableDeliveryTimes.get(0))).click();
        wait.until(ExpectedConditions.invisibilityOf(deliveryDateTimeModalContainer));
    }

    public String getSelectedDayText() {
        return wait.until(ExpectedConditions.visibilityOf(deliveryDayText)).getText();
    }

    public String getSelectedTimeText() {
        return wait.until(ExpectedConditions.visibilityOf(deliveryTimeText)).getText();
    }

    public String getInstantDeliveryExpectedTime() {
        return wait.until(ExpectedConditions.visibilityOf(instantDeliveryBtn))
                .getText()
                .replace("Instant ", "")
                .toLowerCase();
    }

    public String getPromoCodeTxtFieldContentDescription() {
        return promoCodeTxtFieldContentDescription;
    }

    public WebElement getPromoCodeTxtFieldUiElement() {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(
                String.format(promoCodeTxtFieldSelector, getPromoCodeTxtFieldContentDescription()))));
    }

    public void enterCouponCode(String couponCode) {
        wait.until(ExpectedConditions.visibilityOf(applyCouponCodeBtn));
        enterValueInTextField(getPromoCodeTxtFieldUiElement(), couponCode);
        dismissKeyboardIfDisplayed(iosDriver);

    }

    public void pressApplyCouponCodeBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(applyCouponCodeBtn))
                .click();
    }

    public boolean isCouponCodeErrorMsgDisplayed() {
        return isElementDisplayed(couponCodeErrorMsg);
    }

    public String getCouponCodeMessage() {
        return wait.until(ExpectedConditions.visibilityOf(couponCodeErrorMsg))
                .getAttribute("value");
    }

    public void pressSelectDeliveryTimeInTomorrow() {
        wait.until(ExpectedConditions.elementToBeClickable(selectTomorrowDeliveryTimeBtn))
                .click();
    }
    public void pressSelectDeliveryTimeInNow() {
        wait.until(ExpectedConditions.elementToBeClickable(selectNowDeliveryTimeBtn))
                .click();
    }
    public void pressFirstAvailableTimeSlot() {
        // Wait for the first available time slot to be clickable and click it
         wait.until(ExpectedConditions.elementToBeClickable(availableTimeSlots.get(0)))
        .click();
    }
        public int getDisplayedSlotsCount() {
            wait.until(ExpectedConditions.visibilityOfAllElements(availableTimeSlots));
        // Check if the available time slots list is not null and has elements
        if (availableTimeSlots != null && !availableTimeSlots.isEmpty()) {
            // Return the number of available time slots
            return availableTimeSlots.size();
        } else {
            // Return 0 if no time slots are available
            return 0;
        }
    }
    public void pressFirstAvailableTimeSlotInNow() {
        // Wait for the first available time slot to be clickable and click it
        wait.until(ExpectedConditions.elementToBeClickable(availableTimeSlots.get(1)))
                .click();
    }
}
