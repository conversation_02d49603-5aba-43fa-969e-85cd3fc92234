package modals.customerApp.ios.iosCheckoutScreen;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosOrderSuccessScreen extends BaseIosScreen {
    public IosOrderSuccessScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='checkoutSuccessScreen_deliveryDateTime_value']")
    WebElement deliveryDate;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Order placed successfully \"]")
    WebElement orderCompletedSuccessfullyTxt;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name=\"Order Details help_txtview\"])[2]/XCUIElementTypeOther[2]")
    WebElement backBtn;
    @FindBy(id= "checkoutSuccessScreen_trackYourOrderBtn")
            WebElement trackYouOrderBtn;

    String deliveryDateTimeContainerNameSelector = "checkoutSuccessScreen_deliveryDateTime_labelContainer";

    String scrollableContentContainerUiSelector =
            "//XCUIElementTypeOther[@name='checkoutSuccessScreen_scrollableContentContainer']/XCUIElementTypeScrollView";

    String trackYourOrderBtnUiSelector = "//XCUIElementTypeOther[@name='%s']";

    String trackYourOrderBtnNameSelector = "checkoutSuccessScreen_trackYourOrderBtn";

    public boolean isPageDisplayed() {
        return isElementDisplayed(orderCompletedSuccessfullyTxt);
    }

    public void pressBackBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(backBtn))
                .click();
    }
    public void pressTrackYourOrderBtn (){
        wait.until(ExpectedConditions.elementToBeClickable(trackYouOrderBtn))
                .click();
    }

    public String getDeliveryDateValue(){
        return String.valueOf(wait.until(ExpectedConditions.visibilityOf(deliveryDate))
                .getText());
    }

    public String getDeliveryDateTimeContainerNameSelector() {
        return deliveryDateTimeContainerNameSelector;
    }

    public String getTrackYourOrderBtnNameSelector(){
        return trackYourOrderBtnNameSelector;
    }

    public WebElement getTrackYourOrderBtn(){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(trackYourOrderBtnUiSelector, getTrackYourOrderBtnNameSelector()))));
    }

    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerUiSelector)));
        } catch (Exception e){
            return null;
        }
    }
}
