package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class IosCategoryScreen extends BaseIosScreen {

    public IosCategoryScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "(//XCUIElementTypeScrollView)")
    List<WebElement> subCategoriesScrollableContentContainer;

    String subCategorySelector = "//XCUIElementTypeOther[@name='%s']";

    String subCategoryNameSelector = "subcategory_%s";

    @FindBy(xpath = "//XCUIElementTypeScrollView")
    List<WebElement> productsListScrollableContentContainer;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='categoryTitle']")
    WebElement categoryTitle;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='cartBtn_topNav']")
    WebElement cartTopBarBtn;

    @FindBy(id = "categoryDetails_header_backBtn")
    WebElement categoryBackBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='productsList_subCategory_title'")
    WebElement subCategoryProductsTitle;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"cartBtn_topNav\"]")
    WebElement cartIcon;
  
    @FindBy(xpath = "(//XCUIElementTypeButton[@name=\"addToCart_button\"])[1]")
    WebElement addToCartBtn;

    String productCardSelector = "//XCUIElementTypeButton[@name='%s']";

    String productsListScrollableContentContainerSelector = "//XCUIElementTypeScrollView";

    String productCardNameSelector = "product_card_categoryProductsList_%s";

    String addToCartBtnSelector =
            "//XCUIElementTypeButton[@name='%s']//XCUIElementTypeButton[@name='addToCart_button']";

    String favoriteBtnSelector = "//XCUIElementTypeButton[@name='%s']//XCUIElementTypeButton[@name='favoriteIcon']";

    String productPriceLabelSelector =
            "//XCUIElementTypeButton[@name='%s']//XCUIElementTypeOther[@name='productPrice']";

    String removeFromCartBtnSelector =
            "//XCUIElementTypeButton[@name='%s']//XCUIElementTypeButton[@name='removeFromCart_button']";

    String currentQtyTextSelector =
            "//XCUIElementTypeButton[@name='%s']//XCUIElementTypeOther[@name='currentQty_Container']";

    String productTagNameSelector = "//XCUIElementTypeStaticText[@name='%s_tagName']";

    @FindBy(xpath = "(//XCUIElementTypeButton[@name='addToCart_button'])")
    List<WebElement> addToCartIcons;

    String addToCartBtnNameSelector = "addToCart_button";

    public boolean isPageDisplayed(){
        return isElementDisplayed(categoryTitle);
    }

    public String getSubCategoryNameSelector(int subCategoryId){
        return String.format(subCategoryNameSelector, subCategoryId);
    }

    public WebElement getSubCategoryUiElement(int subCategoryId){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(subCategorySelector, getSubCategoryNameSelector(subCategoryId)))));
        } catch (Exception e){
            return null;
        }
    }

    public boolean isSubCategoryDisplayed(int subCategoryId){
        try {
            return isElementDisplayed(getSubCategoryUiElement(subCategoryId));
        } catch (Exception e){
            return false;
        }
    }

    public void pressSubCategory(int subCategoryId){
        if (isSubCategoryDisplayed(subCategoryId))
            getSubCategoryUiElement(subCategoryId).click();
    }

    public String getProductCardNameSelector(String productObjectId){
        return String.format(productCardNameSelector, productObjectId);
    }

    public WebElement getProductCardUiElement(String productObjectId){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(productCardSelector, getProductCardNameSelector(productObjectId)))));
        } catch (Exception e){
            return null;
        }
    }

    public void pressAddToCartIconByIndex(int itemOrder) {
        wait.until(ExpectedConditions.visibilityOf(addToCartIcons.get(itemOrder-1))).click();
    }

    public boolean isProductCardDisplayed(String productObjectId){
        try {
            return isElementDisplayed(getProductCardUiElement(productObjectId));
        } catch (Exception e){
            return false;
        }
    }

    public void pressProductCard(String productObjectId){
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(productCardSelector, getProductCardNameSelector(productObjectId))))).click();
    }

    public void pressAddToCartBtn(String productObjectId){
        wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath(String.format(addToCartBtnSelector, getProductCardNameSelector(productObjectId))))).click();
    }

    public void pressRemoveFromCartBtn(String productObjectId){
        wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath(String.format(removeFromCartBtnSelector,
                        getProductCardNameSelector(productObjectId))))).click();
    }

    public void pressFavoriteBtn(String productObjectId){
        wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(favoriteBtnSelector, getProductCardNameSelector(productObjectId))))).click();
    }

    public String getProductPrice(String productObjectId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(productPriceLabelSelector, getProductCardNameSelector(productObjectId)))))
                .getAttribute("label");
    }

    public String getProductCartQty(String productObjectId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                        By.xpath(String.format(currentQtyTextSelector, getProductCardNameSelector(productObjectId)))))
                .getAttribute("label");
    }

    public String getProductTagName(String productObjectId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                        By.xpath(String.format(productTagNameSelector, getProductCardNameSelector(productObjectId)))))
                .getText();
    }

    public void pressBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(categoryBackBtn))
                .click();
    }

    public void pressCartBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(cartTopBarBtn))
                .click();
    }

    public void pressCategoryTitle(){
        wait.until(ExpectedConditions.visibilityOf(categoryTitle))
                .click();
    }
    public void pressCartIcon(){
        wait.until(ExpectedConditions.visibilityOf(cartIcon))
                .click();
    }

    public WebElement getScrollableContentContainer(String contentType){
        switch (contentType.toLowerCase()){
            case "subcategory":
                if (subCategoriesScrollableContentContainer.size() > 1){
                    return subCategoriesScrollableContentContainer.get(1);
                } else {
                    return null;
                }
            case "product":
                return productsListScrollableContentContainer.get(0);
            default:
                return null;
        }
    }

    public WebElement getSubCategoryProductsTitle(){
        try {
            return wait.until(ExpectedConditions.visibilityOf(subCategoryProductsTitle));
        } catch (Exception e){
            return null;
        }
    }

    public String getAddToCartBtnNameSelector(){
        return addToCartBtnNameSelector;
    }
}

