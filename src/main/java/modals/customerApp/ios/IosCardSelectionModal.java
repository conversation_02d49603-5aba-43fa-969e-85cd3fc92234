package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class IosCardSelectionModal extends BaseIosScreen {
    public IosCardSelectionModal(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='cardSelectionModal_modalTitle']")
    WebElement modalTitle;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='cardSelectionModal_addNewCard_btnContents']")
    WebElement addNewCardOption;

    String modalScrollViewContainerUiSelector = "//XCUIElementTypeOther[@name='cardSelectionModal_cardSelection" +
            "ScrollView_container']/XCUIElementTypeScrollView";

    String cardRowNameSelector = "cardSelectionModal_card_%s_container";

    String cardRowUiSelector = "//XCUIElementTypeOther[@name='%s']";

    @FindBy(xpath = "//XCUIElementTypeOther[contains(@name, \"cardSelectionModal_card_\") " +
            "and contains(@name, \"_container\")]\n")
    List<WebElement> cardsList;

    public boolean isModalDisplayed(){
        return isElementDisplayed(modalTitle);
    }

    public WebElement getCardsListScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(modalScrollViewContainerUiSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public void pressAddNewCardOption(){
        wait.until(ExpectedConditions.visibilityOf(addNewCardOption))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(modalTitle));
    }

    public String getCardRowContainerNameSelector(String cardLast4){
        return String.format(cardRowNameSelector, cardLast4);
    }

    public WebElement getCardPaymentOptionByLast4(String cardLast4){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(cardRowUiSelector, getCardRowContainerNameSelector(cardLast4)))));
    }

    public void selectFirstAvailableCard(){
        wait.until(ExpectedConditions.visibilityOf(cardsList.get(0)))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(modalTitle));
    }
}
