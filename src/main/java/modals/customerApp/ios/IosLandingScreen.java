package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosLandingScreen extends BaseIosScreen {
    public IosLandingScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Log in or sign up']")
    WebElement authHyperLink;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Explore Breadfast']")
    WebElement exploreBtn;

    public void pressAuthHyperLink(){
        wait.until(ExpectedConditions.elementToBeClickable(authHyperLink))
                .click();
    }

    public void pressExploreBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(exploreBtn))
                .click();
    }

    public Boolean isPageDisplayed(){
        return isElementDisplayed(exploreBtn);
    }
}
