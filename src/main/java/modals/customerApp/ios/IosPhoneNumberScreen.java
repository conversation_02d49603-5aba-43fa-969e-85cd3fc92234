package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import javax.sound.midi.SysexMessage;

public class IosPhoneNumberScreen extends BaseIosScreen {
    public IosPhoneNumberScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Log in or sign up']")
    WebElement pageHeader;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='+20']")
    WebElement countryCodeDropdownBtn;

    String countryCodeDropdownBtnXpath = "//XCUIElementTypeOther[@name='%s']";

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Mobile number'])[1]")
    WebElement phoneNumberTextField;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Please use a valid number format']")
    WebElement invalidPhoneNumberError;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Next']")
    WebElement submitBtn;

    public boolean isPageHeaderDisplayed(){
        return isElementDisplayed(pageHeader);
    }

    public void pressCountryCodeDropDownBtn(String defaultCountryCode){
        wait.until(ExpectedConditions.elementToBeClickable(By.xpath(
                String.format(countryCodeDropdownBtnXpath, defaultCountryCode)))).click();

    }

    public void enterPhoneNumber(String phoneNumber){
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberTextField))
                .sendKeys(phoneNumber);
    }

    public void pressNextBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(submitBtn))
                .click();
    }

    public void unFocusFromMobileNumberField(){
        wait.until(ExpectedConditions.elementToBeClickable(pageHeader))
                .click();
    }

    public boolean isPhoneErrorValidationMsgDisplayed(){
        return isElementDisplayed(invalidPhoneNumberError);
    }
}
