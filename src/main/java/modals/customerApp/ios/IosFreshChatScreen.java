package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosFreshChatScreen extends BaseIosScreen {
    public IosFreshChatScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "(//XCUIElementTypeStaticText[@name='I have a question'])[2]")
    WebElement pageTitle;

    @FindBy(xpath = "///XCUIElementTypeButton[@name='Close']")
    WebElement closeBtn;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void pressCloseBtn(){
        wait.until(ExpectedConditions.visibilityOf(closeBtn)).click();
    }
}
