package modals.customerApp.ios;
import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
public class IosSearchScreen extends BaseIosScreen {
    public IosSearchScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }
    @FindBy( id = "searchTextBox_textField")
    WebElement searchTxtField;
    @FindBy(id = "searchTextBox_textFieldContainer")
    WebElement searchBoxContainer;

    public boolean isPageDisplayed(){return isElementDisplayed(searchTxtField);}
    public void enterKeywordForSearch(String keyword){
        wait.until(ExpectedConditions.visibilityOf(searchBoxContainer)).click();
        wait.until(ExpectedConditions.visibilityOf(searchTxtField)).sendKeys(keyword);
        wait.until(ExpectedConditions.textToBePresentInElement(searchTxtField, keyword));
        searchTxtField.sendKeys(Keys.ENTER);
    }
}
