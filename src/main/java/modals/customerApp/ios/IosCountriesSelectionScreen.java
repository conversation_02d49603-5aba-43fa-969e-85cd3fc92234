package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosCountriesSelectionScreen extends BaseIosScreen {
    public IosCountriesSelectionScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(id = "countries-container")
    WebElement countriesContainer;

    @FindBy(id = "country-list-item-egypt")
    WebElement egyptSelectionBtn;

    @FindBy(id = "country-list-item-ksa")
    WebElement ksaSelectionBtn;

    @FindBy(id = "continue-button")
    WebElement continueBtn;

    @FindBy(id = "footnote-text")
    WebElement footerNote;

    public boolean isPageDisplayed(){
        return isElementDisplayed(countriesContainer);
    }

    public void pressEgyptCountryOption(){
        wait.until(ExpectedConditions.elementToBeClickable(egyptSelectionBtn))
                .click();
    }

    public void pressKsaCountryOption(){
        wait.until(ExpectedConditions.elementToBeClickable(ksaSelectionBtn))
                .click();
    }

    public void pressContinueBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(continueBtn))
                .click();
    }

    public void chooseCountryAndSubmit(String country){
        switch (country.toLowerCase()){
            case "egypt", "eg" -> {
                pressEgyptCountryOption();
                break;
            }
            case "ksa" -> {
                pressKsaCountryOption();
                break;
            }
            default -> {
                try {
                    pressEgyptCountryOption();
                } catch (Exception e){
                    //Return on failure
                    return;
                }
            }
        }
        pressContinueBtn();
    }
}
