package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosOrderDetailsScreen extends BaseIosScreen {
    public IosOrderDetailsScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }
    @FindBy(id= "ordersHistory_order__reorderBtn")
    WebElement reorderBtn;
    public Boolean isReorderBtnDisplayed(){
        return isElementDisplayed(reorderBtn);
    }
    public void pressReorderBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(reorderBtn))
                .click();
    }
}

