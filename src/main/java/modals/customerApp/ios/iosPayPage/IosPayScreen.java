package modals.customerApp.ios.iosPayPage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosPayScreen extends BaseIosScreen {
    public IosPayScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    String categoryFullXpath = "//XCUIElementTypeStaticText[@name='paymentCategory_%s_title']";

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='paymentServicesSection_title']")
    WebElement paymentServicesHeader;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='savedCardsBtn_container']")
    WebElement savedCardsBtn;

    String categoryContentDescription = "paymentCategory_%s_btn";

    String categoryUiSelector = "//XCUIElementTypeOther[@name='%s']";

    public boolean isPayPageDisplayed() {
        return isElementDisplayed(paymentServicesHeader);
    }

    public void selectCategory(String categoryId) {
        WebElement category = iosDriver.findElement(By.xpath(String.format(categoryFullXpath, categoryId)));
        wait.until(ExpectedConditions.elementToBeClickable(category)).click();
    }

    public String getCategoryContentDescription(int categoryId) {
        return String.format(categoryContentDescription, categoryId);
    }

    public String getCategoryUiSelector(int categoryId) {
        return String.format(categoryUiSelector, getCategoryContentDescription(categoryId));
    }

    public WebElement getCategoryUiElement(int categoryID) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getCategoryUiSelector(categoryID))));
    }

    public void pressCategory(int categoryId) {
        WebElement sc = getCategoryUiElement(categoryId);
        if (sc != null) {
            wait.until(ExpectedConditions.visibilityOf(sc)).click();
        }
    }

    public void pressSavedCardsBtn(){
        wait.until(ExpectedConditions.visibilityOf(savedCardsBtn))
                .click();
    }
}
