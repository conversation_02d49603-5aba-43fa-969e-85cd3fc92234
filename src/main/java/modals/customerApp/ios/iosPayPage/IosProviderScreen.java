package modals.customerApp.ios.iosPayPage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosProviderScreen extends BaseIosScreen
{
    public IosProviderScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    String pageTitleFullXpath = "//XCUIElementTypeStaticText[@name='provider_%s_title']";

    String serviceTypeContentDescription = "provider_%s_serviceType_%s";

    String mobileNumberInputFieldContentDescription = "service_%s_inputParam_%s_textField";

    String mobileNumberInputFieldUiSelector = "//XCUIElementTypeTextField[@name='%s']";

    String nextBtnUiSelector = "//XCUIElementTypeOther[@name='%s']";

    String serviceTypeUiSelector = "//XCUIElementTypeOther[@name='%s']";

    String nextBtnNameSelector = "provider_%s_service_%s_continueBtn";

    @FindBy(xpath = "//XCUIElementTypeOther[@name='selectServiceType_dropDownContainer']")
    WebElement serviceTypeDropDownContainer;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='provider_serviceTypesList_closeBtn']")
    WebElement serviceTypesListcloseBtn;

    @FindBy (xpath = "//XCUIElementTypeOther[@name='headerContainer']")
    WebElement headerContainer;

    public void openServiceList(){
        wait.until(ExpectedConditions.elementToBeClickable(serviceTypeDropDownContainer))
                .click();
    }
    public boolean isServicesListDropDownDisplayed(){
        return isElementDisplayed(serviceTypesListcloseBtn);
    }

    public String getServiceTypeContentDescription(int providerId, int serviceTypeId){
        return String.format(serviceTypeContentDescription, providerId, serviceTypeId);
    }

    public String getServiceTypeUiSelector( int providerId, int serviceTypeId){
        return String.format(serviceTypeUiSelector, getServiceTypeContentDescription(providerId,serviceTypeId));
    }
    public WebElement getServiceTypeUiElement(int providerId, int serviceTypeId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getServiceTypeUiSelector(providerId,serviceTypeId))));
    }

    public void selectServiceType(int providerId, int serviceTypeId){
        while(isServicesListDropDownDisplayed()){
            try {
                wait.until(ExpectedConditions.visibilityOf(getServiceTypeUiElement(providerId,serviceTypeId))).click();
            } catch (Exception e){
                // do nothing
            }
        }
    }

    public String getMobileNumberInputFieldContentDescription(int serviceId, int inputParamId){
        return String.format(mobileNumberInputFieldContentDescription, serviceId, inputParamId);
    }

    public String getMobileNumberInputFieldUiSelector( int serviceId, int inputParamId){
        return String.format(mobileNumberInputFieldUiSelector,
                getMobileNumberInputFieldContentDescription(serviceId, inputParamId));
    }

    public WebElement getMobileNumberInputFieldUiElement(int serviceId, int inputParamId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getMobileNumberInputFieldUiSelector(serviceId,inputParamId))));
    }

    public void enterMobileNumber(int serviceId, int inputParamId, String mobileNumber){
        wait.until(ExpectedConditions.visibilityOf(getMobileNumberInputFieldUiElement(serviceId,inputParamId))).click();
        wait.until(ExpectedConditions.visibilityOf(getMobileNumberInputFieldUiElement(serviceId,inputParamId)))
                .sendKeys(mobileNumber);
        dismissKeyboardIfDisplayed(iosDriver);
    }

    public String getNextBtnNameSelector(int providerId, int serviceTypeId){
        return String.format(nextBtnNameSelector, providerId, serviceTypeId);
    }

    public String getNextBtnUiSelector( int providerId, int serviceTypeId){
        return String.format(nextBtnUiSelector, getNextBtnNameSelector(providerId,serviceTypeId));
    }

    public WebElement getNextBtnUiElement(int providerId, int serviceTypeId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getNextBtnUiSelector(providerId,serviceTypeId))));
    }

    public void pressNextBtn (int providerId, int serviceTypeId){
        wait.until(ExpectedConditions.visibilityOf(getNextBtnUiElement(providerId,serviceTypeId)))
                .click();
    }

    public WebElement getPageTitleUiElementByProviderId(String providerId)
    {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(pageTitleFullXpath, providerId))));
    }

    public boolean isPageDisplayed(String providerId) {
        return isElementDisplayed(getPageTitleUiElementByProviderId(providerId));
    }

    public void pressPageTitleToDismissKeyboard(String providerId){
        wait.until(ExpectedConditions.visibilityOf(getPageTitleUiElementByProviderId(providerId)))
                .click();
        while (iosDriver.isKeyboardShown()){
            pressPageTitleToDismissKeyboard(providerId);
        }
    }
}
