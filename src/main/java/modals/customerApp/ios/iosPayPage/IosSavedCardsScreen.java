package modals.customerApp.ios.iosPayPage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosSavedCardsScreen extends BaseIosScreen {
    public IosSavedCardsScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeOther[@name='savedCards_addPaymentMethodBtn']")
    WebElement addPaymentMethodFromHeader;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='savedCards_backBtn']")
    WebElement backBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='savedCards_addCard_btn']")
    WebElement addPaymentMethodFromEmptyListView;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='savedCards_noCardsTitle']")
    WebElement emptyListTitle;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='actionsSheetContainer']")
    WebElement actionsSheetContainer;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='modalTitle']")
    WebElement actionsSheetTitle;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='editBtn']")
    WebElement editCardBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='removeBtn']")
    WebElement removeCardBtn;

    String scrollableContentContainerUiSelector = "(//XCUIElementTypeScrollView)[2]";

    String savedCardRowUiSelector = "//XCUIElementTypeOther[@name='%s']";

    String savedCardRowNameSelector = "savedCards_card_%s_rowContainer";

    String savedCardActionsBtnUiSelector = "//XCUIElementTypeOther[@name='%s']";

    String savedCardActionsBtnNameSelector = "card_%s_actionsBtn";

    String savedCardLabelUiSelector = "//XCUIElementTypeStaticText[@name='%s']";

    String savedCardLabelNameSelector = "card_%s_label";

    public boolean isPageDisplayed(){
        return isElementDisplayed(addPaymentMethodFromHeader);
    }

    public boolean isListEmpty(){
        return isElementDisplayed(emptyListTitle) && isElementDisplayed(addPaymentMethodFromEmptyListView);
    }

    public void pressAddCardBtnFromHeader(){
        wait.until(ExpectedConditions.visibilityOf(addPaymentMethodFromHeader))
                .click();
    }

    public void pressBackBtn(){
        wait.until(ExpectedConditions.visibilityOf(backBtn))
                .click();
    }

    public void pressAddCardBtnFromTheEmptyListView(){
        wait.until(ExpectedConditions.visibilityOf(addPaymentMethodFromEmptyListView))
                .click();
    }

    public WebElement getCardsListScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerUiSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public String getSavedCardRowNameSelector(String cardLast4){
        return String.format(savedCardRowNameSelector, cardLast4);
    }

    public String getSavedCardRowUiSelector(String cardLast4){
        return String.format(savedCardRowUiSelector, getSavedCardRowNameSelector(cardLast4));
    }

    public WebElement getSavedCardRowUiElement(String cardLast4){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getSavedCardRowUiSelector(cardLast4))));
    }

    public boolean isCardDisplayed(String cardLast4){
        return isElementDisplayed(getSavedCardRowUiElement(cardLast4));
    }

    public String getSavedCardActionsBtnNameSelector(String cardLast4){
        return String.format(savedCardActionsBtnNameSelector, cardLast4);
    }

    public String getSavedCardActionsBtnUiSelector(String cardLast4){
        return String.format(savedCardActionsBtnUiSelector, getSavedCardActionsBtnNameSelector(cardLast4));
    }

    public WebElement getSavedCardActionsBtnUiElement(String cardLast4){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getSavedCardActionsBtnUiSelector(cardLast4))));
    }

    public void pressSavedCardActionsBtn(String cardLast4){
        wait.until(ExpectedConditions.visibilityOf(getSavedCardActionsBtnUiElement(cardLast4)))
                .click();
    }

    public String getSavedCardLabelNameSelector(String cardLast4){
        return String.format(savedCardLabelNameSelector, cardLast4);
    }

    public String getSavedCardUiSelector(String cardLast4){
        return String.format(savedCardLabelUiSelector, getSavedCardLabelNameSelector(cardLast4));
    }

    public WebElement getSavedCardLabelUiElement(String cardLast4){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(getSavedCardUiSelector(cardLast4))));
    }

    public String getSavedCardCurrentLabelTxt(String cardLast4){
        return getSavedCardLabelUiElement(cardLast4).getText();
    }

    public boolean isActionsSheetDisplayed(){
        return isElementDisplayed(actionsSheetContainer)
                && isElementDisplayed(actionsSheetTitle);
    }

    public void pressEditCardBtn(){
        wait.until(ExpectedConditions.visibilityOf(editCardBtn))
                .click();
    }

    public void pressRemoveCardBtn(){
        wait.until(ExpectedConditions.visibilityOf(removeCardBtn))
                .click();
    }
}
