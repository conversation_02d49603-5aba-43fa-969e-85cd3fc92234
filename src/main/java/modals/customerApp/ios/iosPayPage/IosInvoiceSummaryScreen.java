package modals.customerApp.ios.iosPayPage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import models.ValidationResults;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosInvoiceSummaryScreen extends BaseIosScreen {
    public IosInvoiceSummaryScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    String pageTitleFullXpath = "//XCUIElementTypeStaticText[@name='provider_%s_invoiceTitle']";

    @FindBy(xpath = "//XCUIElementTypeOther[@name='continueBtn']")
    WebElement payBillBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='userInfo_value']")
    WebElement serviceNumber;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='billAmount_value']")
    WebElement billAmount;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='taxAndFees_value']")
    WebElement taxAndFeesAmount;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='taxAndFees_title']")
    WebElement taxAndFeesTitle;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='totalAmount_value']")
    WebElement totalAmount;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='totalAmount_title']")
    WebElement totalTitle;

    @FindBy(xpath = "(//XCUIElementTypeStaticText[@name='undefined_value'])[2]")
    WebElement grandTotal;

   @FindBy (xpath = "(//XCUIElementTypeStaticText[@name='undefined_title'])[1]")
   WebElement balanceTitle;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='useMyBalance_currentBalanceValue']")
    WebElement currentUserBalance;

    @FindBy (xpath = "(//XCUIElementTypeStaticText[@name='undefined_value'])[1]")
    WebElement usedBalanceValue;

    @FindBy(xpath = "//XCUIElementTypeSwitch[@name='useMyBalanceToggleBtn']")
    WebElement useMyBalanceToggleBtn;

    public WebElement getPageTitleUiElementByProviderId(String providerId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(pageTitleFullXpath, providerId))));
    }

    public boolean isTotalDisplayed (){
       return isElementDisplayed(totalTitle);
    }

    public boolean isTaxAndFeesDisplayed(){
        return isElementDisplayed(taxAndFeesTitle);
    }

    public boolean isPageDisplayed(String providerId) {
        return isElementDisplayed(getPageTitleUiElementByProviderId(providerId));
    }

    public void pressPayBillBtn() {
        wait.until(ExpectedConditions.visibilityOf(payBillBtn))
                .click();
    }

    public String getCurrentBalance() {
        wait.until(ExpectedConditions.visibilityOf(currentUserBalance));
        return getTextDisplayed(currentUserBalance);
    }

    public double getGrandTotalAmount() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(grandTotal))
                .getText().replace("EGP ", ""));
    }

    public double getTotalValue() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(totalAmount))
                .getText().replace("EGP ", ""));
    }

    public boolean isBalanceSectionDisplayed() {
        return isElementDisplayed(balanceTitle);
    }

    public boolean isUserCurrentBalanceDisplayed() {
        return isElementDisplayed(currentUserBalance);
    }

    public boolean isGrandTotalDisplayed() {
        return isElementDisplayed(grandTotal);
    }

    public Double getUsedBalanceValue() {
        return Double.parseDouble(wait.until(ExpectedConditions.visibilityOf(usedBalanceValue))
                .getText().replace("EGP ", ""));
    }

    public boolean isBalanceToggleON() {
        try {
            return wait.until(ExpectedConditions.visibilityOf(useMyBalanceToggleBtn)).getAttribute("value")
                    .equalsIgnoreCase("1");
        } catch (Exception e){
            return false;
        }
    }

    public void pressBalanceToggle() {
        wait.until(ExpectedConditions.visibilityOf(useMyBalanceToggleBtn))
                .click();
    }

    public ValidationResults validateBillDetails(String actualServiceNumber, String actualBillAmount,
                                                 String actualTaxAndFeesAmount, String actualTotalAmount,
                                                 ValidationResults validationResults) {
        validationResults.setResult(true);
        if(!actualServiceNumber.equalsIgnoreCase(getTextDisplayed(serviceNumber)))
        {
            validationResults.setResult(false);
            validationResults.addALogToValidationResults("Service number is " +
                    getTextDisplayed(serviceNumber) + "while the expected is " + actualServiceNumber);
        }
        if(!actualBillAmount.equalsIgnoreCase(getTextDisplayed(billAmount)))
        {
            validationResults.setResult(false);
            validationResults.addALogToValidationResults("Bill amount is " +
                    getTextDisplayed(billAmount) + "while the expected is " + actualBillAmount);
        }
        if(!actualTaxAndFeesAmount.equalsIgnoreCase(getTextDisplayed(taxAndFeesAmount)))
        {
            validationResults.setResult(false);
            validationResults.addALogToValidationResults("Tax and Fees amount is " +
                    getTextDisplayed(taxAndFeesAmount) + "while the expected is " + actualTaxAndFeesAmount);
        }
        if(!actualTotalAmount.equalsIgnoreCase(getTextDisplayed(totalAmount)))
        {
            validationResults.setResult(false);
            validationResults.addALogToValidationResults("Total amount is " +
                    getTextDisplayed(totalAmount) + "while the expected is " + actualTotalAmount);
        }
        return validationResults;
    }
}
