package modals.customerApp.ios.iosPayPage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosBillingCategoryScreen extends BaseIosScreen
{
    public IosBillingCategoryScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    String pageTitleFullXpath = "//XCUIElementTypeStaticText[@name='serviceCategory_%s_title']";
    String providerFullXpath = "//XCUIElementTypeStaticText[@name='provider_%s_title']";

    String providerUiSelector = "//XCUIElementTypeOther[@name='%s']";

    String providerContentDescription = "provider_%s";

    public WebElement getPageTitleSelectorByCategoryId(String categoryId)
    {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(pageTitleFullXpath, categoryId))));
    }

    public boolean isPageDisplayed(String categoryId)
    {
        return isElementDisplayed(getPageTitleSelectorByCategoryId(categoryId));
    }

    public boolean isProviderDisplayed(String providerId)
    {
        WebElement provider = iosDriver.findElement(By.xpath(String.format(providerFullXpath, providerId)));
        return isElementDisplayed(provider);
    }

    public void selectProvider(String providerId)
    {
        WebElement provider = iosDriver.findElement(By.xpath(String.format(providerFullXpath, providerId)));
        if(isProviderDisplayed(providerId))
        {
            wait.until(ExpectedConditions.elementToBeClickable(provider)).click();
        }
    }

    public String getProviderContentDescription(int providerId){
        return String.format(providerContentDescription, providerId);
    }

    public String getProviderUiSelector(int providerId){
        return String.format(providerUiSelector, getProviderContentDescription(providerId));
    }
    public WebElement getProviderUiElement(int providerId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getProviderUiSelector(providerId))));
    }
    public void pressProvider(int providerId){
        WebElement sc = getProviderUiElement(providerId);
        if (sc != null){
            wait.until(ExpectedConditions.visibilityOf(sc)).click();
        }
    }
}
