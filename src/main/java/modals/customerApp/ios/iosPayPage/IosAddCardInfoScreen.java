package modals.customerApp.ios.iosPayPage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosAddCardInfoScreen extends BaseIosScreen {
    public IosAddCardInfoScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='addCardForm_cardNumber_inputField']")
    WebElement cardNumberTxtField;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='addCardForm_expDate_inputField']")
    WebElement expiryDateTxtField;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='addCardForm_cvc_inputField']")
    WebElement cvvTxtField;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='addCardForm_cardHolderName_inputField']")
    WebElement cardHolderNameTxtField;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='addCardForm_email_inputField']")
    WebElement emailTxtField;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='addCardForm_cardLabel_inputField']")
    WebElement cardLabelTxtField;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='addCardForm_submitBtn']")
    WebElement addCardBtn;

    public boolean isPageDisplayed(){
        return isElementDisplayed(cardNumberTxtField);
    }

    public void enterCardNumber(String testCreditCard) {
        enterValueInTextField(cardNumberTxtField, testCreditCard);
        dismissKeyboardIfDisplayed(iosDriver);
    }

    public void enterExpiryDate(String testExpiryDate) {
        enterValueInTextField(expiryDateTxtField, testExpiryDate);
        dismissKeyboardIfDisplayed(iosDriver);
    }

    public void enterCvv(String testCVC) {
        enterValueInTextField(cvvTxtField, testCVC);
        dismissKeyboardIfDisplayed(iosDriver);
    }

    public void enterEmailIfPresent(String email) {
        try {
            enterValueInTextField(emailTxtField, email);
            dismissKeyboardIfDisplayed(iosDriver);
        } catch (Exception e){
            // Do nothing
        }
    }

    public void enterCardHolderName(String cardHolderName) {
        enterValueInTextField(cardHolderNameTxtField, cardHolderName);
        dismissKeyboardIfDisplayed(iosDriver);
    }

    public void enterCardLabel(String cardLabel) {
        enterValueInTextField(cardLabelTxtField, cardLabel);
        dismissKeyboardIfDisplayed(iosDriver);
    }

    public void pressAddCardBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(addCardBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(addCardBtn));
    }

    public void fillCardInfoForm(String cardNumber, String cardExpiryDate, String cardCvv, String email
            , String cardHolderName){
        enterCardNumber(cardNumber);
        enterExpiryDate(cardExpiryDate);
        enterCvv(cardCvv);
        enterCardHolderName(cardHolderName);
        enterEmailIfPresent(email);
    }
}
