package modals.customerApp.ios.iosPayPage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosInvoiceSuccessScreen extends BaseIosScreen
{
    public IosInvoiceSuccessScreen (IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy (xpath = "//XCUIElementTypeStaticText[@name='paymentSuccessModal_modalTitle']")
    WebElement pageTitle;

    @FindBy (xpath = "//XCUIElementTypeOther[@name='paymentSuccess_continueBtn']")
    WebElement doneBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='transactionId_value']")
    WebElement transactionId;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='transactionDateTime_value']")
    WebElement paymentDateTime;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='amountPaid_value']")
    WebElement totalPaidAmount;

    public boolean isPageDisplayed() {
        return isElementDisplayed(pageTitle);
    }

    public String getTextOfTotalPaidAmount() {
        wait.until(ExpectedConditions.visibilityOf(totalPaidAmount));
        return getTextDisplayed(totalPaidAmount);
    }

    public boolean isDoneBtnDisplayed () {
        return isElementDisplayed(doneBtn);
    }

    public String getTransactionId(){
        return wait.until(ExpectedConditions.visibilityOf(transactionId))
                .getText();
    }

    public String getTransactionDateTime(){
        return wait.until(ExpectedConditions.visibilityOf(paymentDateTime))
                .getText();
    }

    public String getTotalPaidAmount(){
        return wait.until(ExpectedConditions.visibilityOf(totalPaidAmount))
                .getText().split(" ")[1];
    }

    public String getPaymentCurrency(){
        return wait.until(ExpectedConditions.visibilityOf(totalPaidAmount))
                .getText().split(" ")[0];
    }

    public void pressDoneBtn(){
        wait.until(ExpectedConditions.visibilityOf(doneBtn))
                .click();
    }
}
