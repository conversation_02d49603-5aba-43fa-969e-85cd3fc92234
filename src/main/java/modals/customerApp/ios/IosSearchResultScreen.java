package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class IosSearchResultScreen extends BaseIosScreen {
    public IosSearchResultScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"searchResults_noResults_subText\"]")
    WebElement noResultsFoundLabel;
    @FindBy(id = "searchResults_productsList_contentContainer")
    List<WebElement> productResultsItems;
    @FindBy(xpath="(//XCUIElementTypeButton[@name=\"favoriteIcon\"])")
    List<WebElement> favoriteIcons;
    @FindBy(id="searchResults_productsList_title")
    WebElement productsTitle;
    @FindBy(id= "product_addedToFavorites_Toaster")
    WebElement viewListBtn;
    String productContentDescription= "//XCUIElementTypeButton[@name=\"product_card_searchResults_%s\"]";
    String productSelector = "//XCUIElementTypeButton[@name=\"product_card_searchResults_%s\"]";

    public boolean ifNoResultFoundLabelReturned() {
        wait.until(ExpectedConditions.visibilityOf(noResultsFoundLabel));
        return isElementDisplayed(noResultsFoundLabel);
    }

    public boolean checkProductResultsMatchKeyword(String keyword) {
        String[] words = keyword.split(" ");
        boolean exist = false;
        for (WebElement searchResult : productResultsItems) {
            for (String word : words) {
                if (searchResult.getText().toLowerCase().contains(word)) {
                    exist = true;
                    break;
                }
            }
            if (!exist) {
                return false;
            }
        }
        return true;
    }
    public void pressFavIconOfItem(int itemNumber) {
        wait.until(ExpectedConditions.visibilityOf(productsTitle));
        wait.until(ExpectedConditions.visibilityOf(favoriteIcons.get(itemNumber-1))).click();
    }
    public void pressViewListBtn() {
        wait.until(ExpectedConditions.visibilityOf(viewListBtn))
                .click();
    }
    public WebElement getProductUiElement(int productId){
        try {
            return  wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(productSelector, getProductContentDescription(productId)))));
        } catch (Exception e){
            return null;
        }
    }
    public String getProductSelector(int ProductId){
        return String.format(this.productSelector, ProductId);
    }
    public boolean isProductDisplayedInSearchResults(int ProductId){
        wait.until(ExpectedConditions.visibilityOf(getProductUiElement(ProductId)));
        return isElementDisplayed(getProductUiElement(ProductId));
    }
    public String getProductContentDescription(int ProductId){
        wait.until(ExpectedConditions.visibilityOf(getProductUiElement(ProductId)));
        return String.format(this.productContentDescription, ProductId);
    }

}

