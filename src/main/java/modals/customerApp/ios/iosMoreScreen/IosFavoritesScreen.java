package modals.customerApp.ios.iosMoreScreen;
import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosFavoritesScreen extends BaseIosScreen
{
    public IosFavoritesScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Favorites'])[2]/XCUIElementTypeOther[2]")
    WebElement backBtn;
    @FindBy(xpath = "//XCUIElementTypeButton[@name=\"product_card_favoritesScreen_%s\"]")
    WebElement firstFavoriteProduct;
    @FindBy(xpath = "(//XCUIElementTypeOther[@name=\"Favorites\"])[2]/XCUIElementTypeOther[2]")
    WebElement favoriteBackBtn;
    @FindBy(id="favoritesScreen_emptyList_title")
    WebElement noProductsAddedYetTxt;
    String productCardSelector = "//XCUIElementTypeButton[@name=\"product_card_favoritesScreen_%s\"]";
    String productCardNameSelector = "//XCUIElementTypeStaticText[@name=\"product_card_favoritesScreen_%s_tagName\"]";
    public String getProductCardNameSelector(String productObjectId){
        return String.format(productCardNameSelector, productObjectId);
    }
    public void pressCloseBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(backBtn)).click();
    }

    public void pressFavoriteBackBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(favoriteBackBtn)).click();
    }
    public boolean isFavListEmpty() {
        return isElementDisplayed(noProductsAddedYetTxt);
    }
}
