package modals.customerApp.ios.iosMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosChooseLanguageModal extends BaseIosScreen
{
    public IosChooseLanguageModal(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Choose Language']")
    WebElement pageTitle;

    @FindBy(id = "languageBtn_English_text")
    WebElement english;

    @FindBy(id = "languageBtn_Arabic_text")
    WebElement arabic;

    @FindBy(id = "changeLanguageModal_updateBtn")
    WebElement updateLanguageBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Change']")
    WebElement changeBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Cancel']")
    WebElement cancelBtn;

    public boolean isPageDisplayed()
    {
        return isElementDisplayed(pageTitle);
    }

    public void selectLanguage(String language)
    {
        WebElement targetLanguageElement = english;
        switch (language.toLowerCase())
        {
            case "ar":
                targetLanguageElement = arabic;
                break;
            default:
                break;
        }
        wait.until(ExpectedConditions.elementToBeClickable(targetLanguageElement)).click();
        wait.until(ExpectedConditions.elementToBeClickable(updateLanguageBtn)).click();
        wait.until(ExpectedConditions.elementToBeClickable(changeBtn)).click();
    }
}
