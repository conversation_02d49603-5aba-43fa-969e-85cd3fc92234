package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosManageAddressesScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosDisabledLocationModal extends BaseIosScreen {
    public IosDisabledLocationModal(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeAlert[@name='Access location disabled']")
    WebElement disabledLocationModalContainer;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Proceed anyway']")
    WebElement proceedAnywayBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Go to settings']")
    WebElement goToSettingsBtn;

    public boolean isPageDisplayed(){
        return isElementDisplayed(disabledLocationModalContainer);
    }

    public void pressProceedAnywayBtn(){
        wait.until(ExpectedConditions.visibilityOf(proceedAnywayBtn))
                .click();
    }

    public void pressGoToSettingsBtn(){
        wait.until(ExpectedConditions.visibilityOf(goToSettingsBtn))
                .click();
    }

    public void dismissModalIfDisplayed(){
        try {
            if (isPageDisplayed())
                pressProceedAnywayBtn();
        } catch (Exception e){
            // Do nothing
        }
    }
}
