package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosCreateAddressScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosAddressCreateSuccessModal extends BaseIosScreen {
    public IosAddressCreateSuccessModal(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeAlert")
    WebElement modalContainer;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='OK']")
    WebElement okBtn;

    public boolean isModalDisplayed(){
        return isElementDisplayed(modalContainer);
    }

    public void dismissModal(){
        wait.until(ExpectedConditions.visibilityOf(okBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(modalContainer));
    }
}
