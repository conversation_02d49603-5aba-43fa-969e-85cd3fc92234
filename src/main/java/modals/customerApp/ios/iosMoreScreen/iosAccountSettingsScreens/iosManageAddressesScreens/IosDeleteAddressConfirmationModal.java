package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosManageAddressesScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosDeleteAddressConfirmationModal extends BaseIosScreen {
    public IosDeleteAddressConfirmationModal(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeAlert[@name='Delete address']")
    WebElement modalContainer;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='Yes']")
    WebElement yesBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='No']")
    WebElement noBtn;

    public boolean isModalDisplayed(){
        return isElementDisplayed(modalContainer);
    }

    public void pressYesBtnAndDismissModal(){
        wait.until(ExpectedConditions.visibilityOf(yesBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(modalContainer));
    }

    public void pressNoBtnAndDismissModal(){
        wait.until(ExpectedConditions.visibilityOf(noBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(modalContainer));
    }
}
