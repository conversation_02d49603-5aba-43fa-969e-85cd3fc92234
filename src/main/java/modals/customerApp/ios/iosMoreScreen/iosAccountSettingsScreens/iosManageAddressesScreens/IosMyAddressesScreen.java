package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosManageAddressesScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class IosMyAddressesScreen extends BaseIosScreen {
    public IosMyAddressesScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Back_imgview'])[2]")
    WebElement backBtn;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='ADD NEW ADDRESS'])[1]")
    WebElement addNewAddressBtn;

    @FindBy(xpath = "//XCUIElementTypeScrollView/XCUIElementTypeOther/XCUIElementTypeOther" +
            "/XCUIElementTypeOther[not(position()=1)]")
    List<WebElement> addressesList;

    String scrollableContentContainerUiSelector = "//XCUIElementTypeScrollView";

    public void pressBackBtn(){
        wait.until(ExpectedConditions.visibilityOf(backBtn))
                .click();
    }

    public void pressAddNewAddressBtn(){
        wait.until(ExpectedConditions.visibilityOf(addNewAddressBtn))
                .click();
    }

    public int getCurrentlyDisplayedAddressesCount(){
        return addressesList.isEmpty() ? 0 : addressesList.size();
    }

    public void pressFirstAvailableAddress(){
        wait.until(ExpectedConditions.visibilityOf(getAddressCardByIndex(0)))
                .click();
    }

    public String getFirstAvailableAddressDisplayedTxt(){
        return wait.until(ExpectedConditions.visibilityOf(getAddressCardByIndex(0)))
                .getText();
    }

    public WebElement getAddressCardByIndex(int index){
        try {
            return wait.until(ExpectedConditions.visibilityOf(addressesList.get(index-1)));
        } catch (Exception e){
            return null;
        }
    }

    public void pressAddressByIndex(int index){
        wait.until(ExpectedConditions.visibilityOf(getAddressCardByIndex(index-1)))
                .click();
    }
}
