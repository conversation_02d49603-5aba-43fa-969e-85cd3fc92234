package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosDeleteAccountScreen;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosDeleteAccountOTPVerificationScreen extends BaseIosScreen {
    public IosDeleteAccountOTPVerificationScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }
    @FindBy(xpath = "//XCUIElementTypeTextField")
    WebElement deleteAccountOTPVerificationTextField;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='DiffRecep_rad']")
    WebElement termsAndConditionsCheckbox;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Delete my account']")
    WebElement deleteAccountBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Resend code']")
    WebElement resendCodeBtn;

    @FindBy(xpath = "//XCUIElementTypeAlert")
    WebElement errorAlert;

    public WebElement getOtpTextField(){
        wait.until(ExpectedConditions.elementToBeClickable(deleteAccountOTPVerificationTextField)).click();
        return (deleteAccountOTPVerificationTextField);
    }

    public void resendCodeBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(resendCodeBtn))
                .click();
    }

    public void enterOtp(String otp){
        wait.until(ExpectedConditions.elementToBeClickable(deleteAccountOTPVerificationTextField))
                .sendKeys(otp);
    }

    public void checkTermsAndConditionsCheckbox(){
        wait.until(ExpectedConditions.elementToBeClickable(termsAndConditionsCheckbox))
                .click();
    }

    public void pressDeleteAccountBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(deleteAccountBtn))
                .click();
    }

    public boolean isErrorAlertDisplayed(){
        return isElementDisplayed(errorAlert);
    }
}
