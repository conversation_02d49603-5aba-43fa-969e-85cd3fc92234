package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosManageAddressesScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosDeleteAddressErrorModal extends BaseIosScreen {
    public IosDeleteAddressErrorModal(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeAlert[@name='Error']")
    WebElement modalContainer;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='OK']")
    WebElement okButton;

    public boolean isModalDisplayed(){
        return isElementDisplayed(modalContainer);
    }

    public void dismissModal(){
        wait.until(ExpectedConditions.visibilityOf(okButton))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(modalContainer));
    }
}
