package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosAccountSettingsScreen extends BaseIosScreen
{
    public IosAccountSettingsScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Account Settings']")
    WebElement pageTitle;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Account Settings'])[2]/XCUIElementTypeOther[2]")
    WebElement backBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Delete Account']")
    WebElement deleteAccountList;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='UpdatePerInfo_txtview']")
    WebElement updatePerInfoBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='SavedCards_txtview']")
    WebElement savedCardsBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='ChangeLang_txtview']")
    WebElement changeLanguageBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Mngaddress_txtview']")
    WebElement manageAddressesBtn;

    public Boolean isPageDisplayed()
    {
        return isElementDisplayed(pageTitle);
    }

    public void pressBackBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(backBtn)).click();
    }

    public void pressDeleteAccount(){
        wait.until(ExpectedConditions.elementToBeClickable(deleteAccountList))
                .click();
    }

    public void pressUpdatePersonalInfoBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(updatePerInfoBtn)).click();
    }

    public void pressSavedCardsBtn(){
        wait.until(ExpectedConditions.visibilityOf(savedCardsBtn))
                .click();
    }

    public void pressChangeLanguageBtn(){
        wait.until(ExpectedConditions.visibilityOf(changeLanguageBtn))
                .click();
    }

    public void pressManageAddressesBtn(){
        wait.until(ExpectedConditions.visibilityOf(manageAddressesBtn))
                .click();
    }
}
