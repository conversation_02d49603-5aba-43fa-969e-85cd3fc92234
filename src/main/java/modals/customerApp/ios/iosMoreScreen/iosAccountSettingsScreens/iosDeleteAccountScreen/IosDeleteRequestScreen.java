package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosDeleteAccountScreen;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosDeleteRequestScreen extends BaseIosScreen {
    public IosDeleteRequestScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Your delete request is sent.']")
    WebElement pageHeader;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Close']")
    WebElement closeBtn;

    public void pressCloseBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(closeBtn))
                .click();
    }

    public boolean isPageHeaderDisplayed(){
        return isElementDisplayed(pageHeader);
    }
}
