package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosDeleteAccountScreen;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosDeleteAccountScreen extends BaseIosScreen {
    public IosDeleteAccountScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Delete Account']")
    WebElement pageTitle;

    @FindBy(xpath = "((//XCUIElementTypeScrollView//XCUIElementTypeOther)//XCUIElementTypeOther[2]" +
            "//XCUIElementTypeOther)[15]")
    WebElement firstReasonInList;

    @FindBy(xpath = "//XCUIElementTypeScrollView")
    WebElement scrollableContentContainer;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Continue']")
    WebElement continueBtn;

    String continueBtnContentDescription = "Continue";

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void selectFirstReasonInList(){
        wait.until(ExpectedConditions.elementToBeClickable(firstReasonInList))
                .click();
    }

    public WebElement getScrollableContentContainer() {
        return scrollableContentContainer;
    }
    public void pressContinueBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(continueBtn))
                .click();
    }

    public String getContinueBtnContentDescription(){
        return continueBtnContentDescription;
    }
}
