package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosCreateAddressScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import models.Address;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosCreateAddressScreen extends BaseIosScreen {
    public IosCreateAddressScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Address Details']")
    WebElement pageTitle;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='addressDetails_mapViewContainer']")
    WebElement miniMapView;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='addressDetails_addressLabel_title']")
    WebElement addressLabelTxtFieldTitle;

    @FindBy(xpath = "//XCUIElementTypeTextField[@name='addressDetails_addressLabel_textField']")
    WebElement addressLabelTxtField;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='addressDetails_fullAddress_title']")
    WebElement addressDetailsTxtFieldTitle;

    @FindBy(xpath = "//XCUIElementTypeTextView[@name='addressDetails_fullAddress_textField']")
    WebElement addressDetailsTxtField;

    @FindBy (xpath = "//XCUIElementTypeStaticText[@name='addressDetails_floorNumber_title']")
    WebElement floorNumberTxtFieldTitle;

    @FindBy (xpath = "//XCUIElementTypeTextField[@name='addressDetails_floorNumber_textField']")
    WebElement floorNumberTxtField;

    @FindBy (xpath = "//XCUIElementTypeStaticText[@name='addressDetails_flatNumber_title']")
    WebElement flatNumberTxtFieldTitle;

    @FindBy (xpath = "//XCUIElementTypeTextField[@name='addressDetails_flatNumber_textField']")
    WebElement flatNumberTxtField;

    @FindBy (xpath = "//XCUIElementTypeOther[@name='addressDetails_submitBtn']")
    WebElement saveAddressBtn ;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='DELETE ADDRESS']")
    WebElement deleteAddressBtn;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='SET DEFAULT ADDRESS'])[1]")
    WebElement setAsDefaultAddressBtn;

    String scrollableContentContainerSelector = "//XCUIElementTypeScrollView";

    String saveAddressBtnNameSelector = "addressDetails_submitBtn";

    String deleteAddressBtnNameSelector = "DELETE ADDRESS";

    String setAsDefaultAddressBtnNameSelector = "SET DEFAULT ADDRESS";

    public boolean isPageDisplayed(){
        return isElementDisplayed(miniMapView) || isElementDisplayed(pageTitle);
    }

    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(scrollableContentContainerSelector)));
        } catch (Exception e){
            return null;
        }
    }

    public String getSaveAddressBtnNameSelector()
    {
        return saveAddressBtnNameSelector;
    }

    public void enterAddressLabel(String addressLabel, String mode){
        if (mode.equalsIgnoreCase("update")){
            wait.until(ExpectedConditions.visibilityOf(addressLabelTxtField)).clear();
        }
        enterValueInTextField(addressLabelTxtField, addressLabel);
        dismissKeyboardIfDisplayed(iosDriver);
    }

    public void enterAddressDetails(String addressDetails){
        wait.until(ExpectedConditions.visibilityOf(addressDetailsTxtField)).click();
        wait.until(ExpectedConditions.visibilityOf(addressDetailsTxtField)).sendKeys(addressDetails);
        try{
            wait.until(ExpectedConditions.textToBePresentInElement(addressDetailsTxtField, addressDetails));
        } catch (Exception e){
            // Do nothing
        }
        wait.until(ExpectedConditions.visibilityOf(addressDetailsTxtFieldTitle)).click();
    }

    public void enterFloorNumber(String floorNumber){
        enterValueInTextField(floorNumberTxtField, floorNumber);
        dismissKeyboardIfDisplayed(iosDriver);
    }

    public void enterFlatNumber(String flatNumber){
        enterValueInTextField(flatNumberTxtField, flatNumber);
        dismissKeyboardIfDisplayed(iosDriver);
    }

    public void enterAddressDetails(Address address){
        enterAddressLabel(address.getAddressLabel(), "create");
        enterAddressDetails(address.getFullAddress());
        enterFloorNumber(address.getFloorNumber());
        enterFlatNumber(address.getFlatNumber());
    }

    public void pressSaveAddressBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(saveAddressBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(saveAddressBtn));
    }

    public String getDeleteAddressBtnNameSelector(){
        return deleteAddressBtnNameSelector;
    }

    public void pressDeleteAddressBtn(){
        wait.until(ExpectedConditions.visibilityOf(deleteAddressBtn))
                .click();
    }

    public String getSetAsDefaultAddressBtnNameSelector(){
        return setAsDefaultAddressBtnNameSelector;
    }

    public void pressSetAsDefaultAddressBtn(){
        wait.until(ExpectedConditions.visibilityOf(setAsDefaultAddressBtn))
                .click();
    }

    public void updateAddressLabel(Address address){
        enterAddressLabel(address.getAddressLabel(), "update");
    }
}
