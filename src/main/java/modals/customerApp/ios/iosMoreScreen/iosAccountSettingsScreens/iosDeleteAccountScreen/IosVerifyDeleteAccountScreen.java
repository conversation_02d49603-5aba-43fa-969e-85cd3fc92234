package modals.customerApp.ios.iosMoreScreen.iosAccountSettingsScreens.iosDeleteAccountScreen;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosVerifyDeleteAccountScreen extends BaseIosScreen {
    public IosVerifyDeleteAccountScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"Verify mobile number\"]")
    WebElement  verifyMobileNumberBtn;

    public void pressVerifyMobileNumberBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(verifyMobileNumberBtn))
                .click();
    }
}
