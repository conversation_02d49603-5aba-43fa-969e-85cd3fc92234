package modals.customerApp.ios.iosMoreScreen;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosHelpScreen extends BaseIosScreen
{
    public IosHelpScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Need Help?'])[2]")
    WebElement pageTitle;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Back_btn'])[3]")
    WebElement backBtn;

    public Boolean isPageDisplayed()
    {
        return isElementDisplayed(pageTitle);
    }

    public void pressCloseBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(backBtn)).click();
    }
}
