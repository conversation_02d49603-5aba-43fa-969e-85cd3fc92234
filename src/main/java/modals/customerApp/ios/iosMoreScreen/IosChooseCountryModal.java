package modals.customerApp.ios.iosMoreScreen;

import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosChooseCountryModal extends BaseIosScreen
{
    public IosChooseCountryModal(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(id = "changeCountryModal_title")
    WebElement pageTitle;

    @FindBy(id = "country_egypt")
    WebElement egypt;

    @FindBy(id = "country_ksa")
    WebElement ksa;

    @FindBy(id = "saveCountryBtn")
    WebElement saveChangesBtn;

    @FindBy(id = "changeCountry_confirmBtn")
    WebElement yesChangeCountryBtn;

    @FindBy(id = "changeCountry_keepCurrentBtn")
    WebElement noKeepCurrentBtn;

    public boolean isPageDisplayed()
    {
        return isElementDisplayed(pageTitle);
    }

    public void selectCountry(String countryCode)
    {
        WebElement targetCountryElement = egypt;
        switch (countryCode.toLowerCase())
        {
            case "ksa":
                targetCountryElement = ksa;
                break;
            default:
                break;
        }
        wait.until(ExpectedConditions.elementToBeClickable(targetCountryElement)).click();
        wait.until(ExpectedConditions.elementToBeClickable(saveChangesBtn)).click();
        wait.until(ExpectedConditions.elementToBeClickable(yesChangeCountryBtn)).click();
    }
}
