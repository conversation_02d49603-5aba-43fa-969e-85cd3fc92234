package modals.customerApp.ios.iosMoreScreen.iosActivityHistoryScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class IosBillsTabScreen extends BaseIosScreen {
    public IosBillsTabScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    String billCardNameSelector = "billsHistory_billCard_bill_%s_contentContainer";

    String billCardUiSelector = "//XCUIElementTypeOther[@name='%s']";

    @FindBy(xpath = "//XCUIElementTypeOther[contains(@name,'billsHistory_billCard_bill') " +
            "and contains(@name,'contentContainer')]")
    List<WebElement> billsHistoryCards;

    String scrollableContentContainer = "//XCUIElementTypeScrollView";

    public int getBillsCardCount() {
        if (billsHistoryCards.isEmpty()) {
            return 0;
        } else {
            wait.until(ExpectedConditions.visibilityOfAllElements(billsHistoryCards));
            return billsHistoryCards.isEmpty() ? 0 : billsHistoryCards.size();
        }
    }
    public String getBillCardNameSelector(String transactionId){
        return String.format(billCardNameSelector,transactionId);
    }

    public String getBillCardUiSelector(String transactionId){
        return String.format(billCardUiSelector, getBillCardNameSelector(transactionId));
    }

    public WebElement getBillCardUiElement(String transactionId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(getBillCardUiSelector(transactionId))));
    }

    public boolean isBillCardDisplayed(String transactionId){
        wait.until(ExpectedConditions.visibilityOf(getBillCardUiElement(transactionId)));
        return isElementDisplayed(getBillCardUiElement(transactionId));
    }

    public void pressBillCard(String transactionId){
        wait.until(ExpectedConditions.visibilityOf(getBillCardUiElement(transactionId)))
                .click();
    }

    public WebElement getScrollableContentContainer(){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(scrollableContentContainer)));
        } catch (Exception e){
            return null;
        }
    }
}
