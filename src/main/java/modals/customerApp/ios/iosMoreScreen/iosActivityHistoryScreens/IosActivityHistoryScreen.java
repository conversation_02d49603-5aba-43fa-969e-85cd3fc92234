package modals.customerApp.ios.iosMoreScreen.iosActivityHistoryScreens;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class IosActivityHistoryScreen extends BaseIosScreen
{
    public IosActivityHistoryScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Activity History']")
    WebElement pageTitle;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='activityHistory_orders_tab']")
    WebElement ordersTab;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='activityHistory_bills_tab']")
    WebElement billsTab;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Activity History'])[1]/XCUIElementTypeOther[2]")
    WebElement backBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[contains(@name,'ordersHistory_order_') and contains(@name,'_cardContainer')]")
    List<WebElement> ordersList;

    String orderCardNameSelector = "ordersHistory_order_%s_cardContainer";

    String orderCardUiSelector = "//XCUIElementTypeOther[@name='%s']";

    public Boolean isPageDisplayed()
    {
        return isElementDisplayed(ordersTab) && isElementDisplayed(billsTab);
    }

    public void selectOrdersTab()
    {
        wait.until(ExpectedConditions.elementToBeClickable(ordersTab)).click();
    }

    public void selectBillsTab()
    {
        wait.until(ExpectedConditions.elementToBeClickable(billsTab)).click();
    }

    public void pressBackBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(backBtn)).click();
    }

    public String getOrderCardNameSelector(String orderId){
        return String.format(orderCardNameSelector, orderId);
    }

    public WebElement getOrderCardUiElement(String orderId){
        return wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(orderCardUiSelector, getOrderCardNameSelector(orderId)))));
    }

    public void pressOrderCard(String orderId){
        getOrderCardUiElement(orderId).click();
    }

    public int getDisplayedOrdersCount(){
        return ordersList.isEmpty() ? 0 : ordersList.size();
    }

    public boolean isOrderCardDisplayed(String orderId){
        return isElementDisplayed(getOrderCardUiElement(orderId));
    }
}
