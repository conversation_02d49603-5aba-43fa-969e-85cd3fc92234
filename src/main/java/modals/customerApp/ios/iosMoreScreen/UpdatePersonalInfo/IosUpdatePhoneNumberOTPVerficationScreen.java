package modals.customerApp.ios.iosMoreScreen.UpdatePersonalInfo;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosUpdatePhoneNumberOTPVerficationScreen extends BaseIosScreen {
    public IosUpdatePhoneNumberOTPVerficationScreen (IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeTextField")
    WebElement otpTxtField;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Update Phone Number'])[4]")
    WebElement submitOtpBtn;

    public WebElement getOtpTxtField(){
        return otpTxtField;
    }

    public void pressUpdatePhoneNumberBtn(){
        wait.until(ExpectedConditions.visibilityOf(submitOtpBtn))
                .click();
    }
}
