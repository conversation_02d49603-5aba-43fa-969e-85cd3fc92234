package modals.customerApp.ios.iosMoreScreen.UpdatePersonalInfo;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosUpdateAccountSettingScreen extends BaseIosScreen {
    public IosUpdateAccountSettingScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "(//XCUIElementTypeTextField)[1]")
    WebElement firstNameTxtField;

    @FindBy(xpath = "(//XCUIElementTypeTextField)[2]")
    WebElement lastNameTxtField;

    @FindBy(xpath = "(//XCUIElementTypeTextField)[3]")
    WebElement phoneNumberTxtField;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Save Changes']")
    WebElement saveChangesBtn;

    public void enterFirstName(String firstName) {
        wait.until(ExpectedConditions.visibilityOf(firstNameTxtField)).click();
        wait.until(ExpectedConditions.visibilityOf(firstNameTxtField)).clear();
        enterValueInTextField(firstNameTxtField, firstName);
    }

    public void enterLastName(String lastName){
        wait.until(ExpectedConditions.elementToBeClickable(lastNameTxtField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(lastNameTxtField)).clear();
        enterValueInTextField(lastNameTxtField, lastName);
    }

    public void enterPhoneNumber(String phoneNumber){
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberTxtField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(phoneNumberTxtField)).clear();
        enterValueInTextField(phoneNumberTxtField, phoneNumber);
    }

    public void pressSaveChangesBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(saveChangesBtn)).click();
    }

}
