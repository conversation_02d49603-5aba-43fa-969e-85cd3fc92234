package modals.customerApp.ios.iosMoreScreen;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import models.TestData;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosMoreScreen extends BaseIosScreen
{
    public IosMoreScreen(IOSDriver iosDriver)
    {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    String fullNameXpath = "//XCUIElementTypeStaticText[@name='%s']";

    @FindBy(xpath = "//XCUIElementTypeOther" +
            "[contains(@name, 'Find Balance and Payment Services in the all-new “Breadfast Pay” screen.')]")
    WebElement payBtnCoachMarks;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Create New Account']")
    WebElement createAccountBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Login To Breadfast']")
    WebElement loginBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='userMenu_activityHistoryBtn']")
    WebElement activityHistoryBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='userMenu_favoritesBtn']")
    WebElement favoritesBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='userMenu_rewardsBtn']")
    WebElement breadfastRewardsBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='userMenu_freeCreditsBtn']")
    WebElement freeCreditBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='userMenu_accountSettingsBtn']")
    WebElement accountSettingsBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='userMenu_needHelpBtn']")
    WebElement helpBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='userMenu_languageBtn']")
    WebElement languageBtn;

    @FindBy(xpath = "(//XCUIElementTypeStaticText[@name='listItem_subTitle'])[2]")
    WebElement currentLanguage;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='userMenu_changeCountryBtn']")
    WebElement countryBtn;

    @FindBy(xpath = "(//XCUIElementTypeStaticText[@name='listItem_subTitle'])[3]")
    WebElement currentCountry;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='userMenu_chatBtn']")
    WebElement talkToUsBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='userMenu_logoutBtn']")
    WebElement logoutBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='userMenu_versionNumberContainer']")
    WebElement versionNumber;

    @FindBy(xpath = "//XCUIElementTypeScrollView")
    WebElement scrollableContentContainer;
    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"Home\"]")
            WebElement homeNavBar;

    String logoutBtnContentDescription = "userMenu_logoutBtn";

    public void dismissCoachMarksIfDisplayed()
    {
        if (isCoachMarksDisplayed())
        {
            wait.until(ExpectedConditions.visibilityOf(payBtnCoachMarks)).click();
        }
    }

    public boolean isCoachMarksDisplayed()
    {
        return  isElementDisplayed(payBtnCoachMarks);
    }

    public boolean isPageDisplayed()
    {
        return (isElementDisplayed(helpBtn) && isElementDisplayed(languageBtn) && isElementDisplayed(countryBtn));
    }

    public boolean isFullNameDisplayed(String firstName, String lastName)
    {
        WebElement fullName = iosDriver.findElement(By.xpath(String.format(fullNameXpath, (firstName + " " + lastName))));
        return isElementDisplayed(fullName);
    }

    public void pressCreateAccountBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(createAccountBtn))
                .click();
    }

    public void pressLoginBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(loginBtn))
                .click();
    }

    public void pressActivityHistoryBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(activityHistoryBtn)).click();
    }

    public void pressFavoritesBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(favoritesBtn)).click();
    }

    public void pressBreadfastRewardsBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(breadfastRewardsBtn)).click();
    }

    public void pressFreeCreditBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(freeCreditBtn)).click();
    }

    public void pressAccountSettingsBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(accountSettingsBtn)).click();
    }

    public void pressHelpBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(helpBtn)).click();
    }

    public void pressLanguageBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(languageBtn)).click();
    }

    public void pressCountryBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(countryBtn)).click();
    }

    public void pressTalkToUsBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(talkToUsBtn)).click();
    }

    public void pressLogoutBtn()
    {
        wait.until(ExpectedConditions.elementToBeClickable(logoutBtn))
                .click();
    }
    public void pressHomeNavBarIcon()
    {
        wait.until(ExpectedConditions.elementToBeClickable(homeNavBar))
                .click();
    }

    public String getVersionNumber()
    {
        return getTextDisplayed(versionNumber);
    }

    public String getCurrentLanguage()
    {
        return getTextDisplayed(currentLanguage);
    }

    public String getCurrentCountry()
    {
        return getTextDisplayed(currentCountry);
    }

    public WebElement getScrollableContentContainer() {
        try {
            return wait.until(ExpectedConditions.visibilityOf(scrollableContentContainer));
        } catch (Exception e){
            return null;
        }
    }

    public String getLogoutBtnContentDescription() {
        return logoutBtnContentDescription;
    }
}
