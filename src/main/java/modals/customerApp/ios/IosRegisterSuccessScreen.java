package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosRegisterSuccessScreen extends BaseIosScreen {
    public IosRegisterSuccessScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Your account has been successfully created.']")
    WebElement confirmationMessage;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='done_btn'])[2]")
    WebElement proceedBtn;

    public boolean isConfirmationMessageDisplayed(){
        return isElementDisplayed(confirmationMessage);
    }

    public void pressProceedBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(proceedBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(confirmationMessage));
    }
}
