package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import models.TestData;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosCreateAccountScreen extends BaseIosScreen {
    public IosCreateAccountScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Let’s finish up your account']")
    WebElement topHeader;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Add your basic information to start shopping!']")
    WebElement subHeader;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='First name'])[3]/XCUIElementTypeOther[2]/XCUIElementTypeTextField")
    WebElement firstNameField;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='First name'])[2]/XCUIElementTypeOther[2]" +
            "/XCUIElementTypeOther/XCUIElementTypeOther")
    WebElement firstNameSuccessMark;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Last name'])[3]/XCUIElementTypeOther[2]/XCUIElementTypeTextField")
    WebElement lastNameField;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Last name'])[2]/XCUIElementTypeOther[2]" +
            "/XCUIElementTypeOther/XCUIElementTypeOther")
    WebElement lastNameSuccessMark;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Email (Optional)'])[3]/XCUIElementTypeOther[2]" +
            "/XCUIElementTypeTextField")
    WebElement emailField;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Email (Optional)'])[2]/XCUIElementTypeOther[2]" +
            "/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther")
    WebElement emailSuccessMark;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Create Account']")
    WebElement submitBtn;

    public boolean isTopHeaderDisplayed(){
        return isElementDisplayed(topHeader);
    }

    public boolean isSubHeaderDisplayed(){
        return isElementDisplayed(subHeader);
    }

    // A method to press on an element that should remove the focus from the text field
    public void unFocusFromInputField(){
        wait.until(ExpectedConditions.elementToBeClickable(topHeader))
                .click();
    }

    public void enterFirstName(String firstName){
        wait.until(ExpectedConditions.elementToBeClickable(firstNameField)).sendKeys(firstName);
        wait.until(ExpectedConditions.textToBePresentInElement(firstNameField, firstName));
    }

    public void enterLastName(String lastName){
        wait.until(ExpectedConditions.elementToBeClickable(lastNameField))
                .sendKeys(lastName);
        wait.until(ExpectedConditions.textToBePresentInElement(lastNameField, lastName));
    }

    public void enterEmail(String email){
        wait.until(ExpectedConditions.elementToBeClickable(emailField)).sendKeys(email);
        wait.until(ExpectedConditions.textToBePresentInElement(emailField, email));
    }

    public void pressSubmitBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(submitBtn))
                .click();
    }

    public void fillInAccountInformationForm(TestData testData){
        enterFirstName(testData.getRandomTestUser().getFirstName());
        enterLastName(testData.getRandomTestUser().getLastName());
        enterEmail(testData.getRandomTestUser().getEmailAddress());
    }
}
