package modals.customerApp.ios;
import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosProductDetailsScreen extends BaseIosScreen {
    public IosProductDetailsScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }
    @FindBy(xpath = "(//XCUIElementTypeButton[@name=\"favoriteIcon\"])[1]")
    WebElement favoriteIcon;
    @FindBy(xpath = "(//XCUIElementTypeOther[@name=\"Add to cart\"])[2]")
    WebElement addToCartBtn;
    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"ProductHeader_Container\"]/XCUIElementTypeOther[1]")
    WebElement backBtn;
    @FindBy(xpath = "(//XCUIElementTypeOther[@name=\"goToCartButton\"])[2]")
    WebElement goToCartBtn;

    public void pressFavoriteIcon() {
        wait.until(ExpectedConditions.elementToBeClickable(favoriteIcon))
                .click();
    }
    public void pressBackBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(backBtn))
                .click();
    }
    public void pressAddToCartBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(addToCartBtn))
                .click();
    }
    public void pressGoToCartBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(goToCartBtn))
                .click();
    }
}
