package modals.customerApp.ios;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosOTPVerificationScreen extends BaseIosScreen {
    public IosOTPVerificationScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Verify your mobile number']")
    WebElement pageHeader;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='Enter the 4-digit verification code sent to']")
    WebElement pageSubHeader;

    String phoneNumberXpath = "//XCUIElementTypeStaticText[@name='%s']";

    @FindBy(xpath = "//XCUIElementTypeTextField[@type='XCUIElementTypeTextField']")
    WebElement otpTextField;

    @FindBy(xpath = "//XCUIElementTypeStaticText[contains(@name, \"Wrong verification code\")]")
    WebElement wrongOtpErrorMsg;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name=\"Welcome back to Breadfast!\"]")
    WebElement loginAfterDeletedAccountModalTitle;

    public boolean isPageHeaderDisplayed(){
        return isElementDisplayed(pageHeader);
    }

    public boolean isPageSubHeaderDisplayed(){
        return isElementDisplayed(pageSubHeader);
    }

    public boolean isPhoneNumberDisplayed(String mobileNumber){
        try {
            WebElement phoneNumber = wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(phoneNumberXpath, mobileNumber))));
            return isElementDisplayed(phoneNumber);
        } catch (Exception e){
            return false;
        }
    }

    public void enterOTP(String otp){
        wait.until(ExpectedConditions.elementToBeClickable(otpTextField));
        if (!otpTextField.getText().equalsIgnoreCase(otp)) {
            wait.until(ExpectedConditions.elementToBeClickable(otpTextField)).clear();
            wait.until(ExpectedConditions.elementToBeClickable(otpTextField))
                    .sendKeys(otp);
        }
    }

    public Boolean isOTPEnteredCorrectly(String otp){
        try {
            wait.until(ExpectedConditions.textToBePresentInElement(otpTextField, otp));
            return true;
        } catch (Exception e){
            return false;
        }
    }

    public boolean isErrorMsgDisplayed(){
        return isElementDisplayed(wrongOtpErrorMsg);
    }

    public boolean isGreetingAfterDeletedAccountLoginDisplayed(){
        return isElementDisplayed(loginAfterDeletedAccountModalTitle);
    }
}
