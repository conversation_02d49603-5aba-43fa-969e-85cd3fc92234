package modals.customerApp.ios.iosHomePage;
import io.appium.java_client.ios.IOSDriver;
import io.appium.java_client.pagefactory.AndroidFindBy;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosHomeScreen extends BaseIosScreen {
    public IosHomeScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Home']")
    WebElement homeTabBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Pay']")
    WebElement payTabBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='More']")
    WebElement moreTabBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Cart']")
    WebElement cartTabBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='referral_imgview']")
    WebElement referralBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='freshDesk_imgview']")
    WebElement freshDeskBtn;

    @FindBy(xpath = "//XCUIElementTypeButton[@name='home_recommendations_viewAllBtn']")
    WebElement viewAllRecommendationsBtn;

    @FindBy(xpath = "//XCUIElementTypeStaticText[@name='home_recommendations_sectionTitle']")
    WebElement recommendationsSectionTitle;

    @FindBy(xpath = "//XCUIElementTypeScrollView")
    WebElement scrollableContentContainer;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='addressChangeTouchable']")
    WebElement addressChangeBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='changeStoreTouchable']")
    WebElement nowAndTomorrowHeaderBar;

    @FindBy(id = "Search")
    WebElement searchBtn;

    String categorySelector = "//XCUIElementTypeOther[@name='%s']";

    String categoryNameSelector = "categoryItem-%s";

    @FindBy(id = "CloseRatingBottom_btn")
    WebElement closeRatingBottom;

    @AndroidFindBy(uiAutomator = "new UiScrollable(new UiSelector().scrollable(true).instance(0)).scrollIntoView(new UiSelector().textContains(\""+"Delivery in"+"\").instance(0))")
    private WebElement scrollingByDeliverInText;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"\uF3D8\"]")
    WebElement chevronDownBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"wqeqwewqeqw The Spotlight collection_imgview collection_imgview Vertical scroll bar, 1 page\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[1]")
    WebElement homeScreenFirstBanner;
    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"addressChangeTouchable\"]")
    WebElement AddressesDropDownList;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"Tomorrow\"]")
    WebElement tomorrowTxt;

    @FindBy(xpath="//XCUIElementTypeOther[@name=\"Delivery in 60 minutes\"]")
    WebElement nowTxt;

    @FindBy(xpath="//XCUIElementTypeStaticText[@name=\"We’re busy\"]")
    WebElement busyModal;

    @FindBy(xpath="//XCUIElementTypeOther[@name=\"\uE14C\"]")
    WebElement closeBusyModalIcon;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"Switch to Tomorrow\"]")
    WebElement switchToTomorrowBtn;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name=\"We’re temporarily busy\"])[2]")
    WebElement weAreBusyTxt;

    @FindBy(id= "Order placed successfully ")
    WebElement miniTrackerMsg;

    public void pressDownArrowBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(chevronDownBtn))
                .click();
    }

    public boolean isHomePageDisplayed(){
        return isElementDisplayed(homeTabBtn) || isElementDisplayed(nowAndTomorrowHeaderBar);
    }

    public void pressHomeTabBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(homeTabBtn))
                .click();
    }

    public void pressPayTabBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(payTabBtn))
                .click();
    }

    public void pressMoreTabBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(moreTabBtn))
                .click();
    }

    public void pressCartTabBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(cartTabBtn))
                .click();
    }

    public boolean isReferralBtnDisplayed(){
        return isElementDisplayed(referralBtn);
    }

    public void pressReferralBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(referralBtn))
                .click();
    }

    public boolean isFreshDeskBtnDisplayed(){
        return isElementDisplayed(freshDeskBtn);
    }

    public void pressFreshDeskBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(freshDeskBtn))
                .click();
    }
    public boolean isHomeScreenFirstBannerDisplayed() { return isElementDisplayed(homeScreenFirstBanner);}

    public boolean isRecommendationsSectionDisplayed(){
        return isElementDisplayed(recommendationsSectionTitle);
    }

    public boolean isViewAllRecommendationsBtnDisplayed(){
        return isElementDisplayed(viewAllRecommendationsBtn);
    }

    public void pressViewAllRecommendationsBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(viewAllRecommendationsBtn)).click();
    }

    public WebElement getRecommendationsSectionTitle() {
        return recommendationsSectionTitle;
    }

    public WebElement getScrollableContentContainer() {
        return scrollableContentContainer;
    }

    public void pressChangeAddressBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(addressChangeBtn))
                .click();
    }
    public void pressChangeNowAndTomorrowBar() {
        wait.until(ExpectedConditions.elementToBeClickable(nowAndTomorrowHeaderBar))
                .click();
    }

    public void clickFirstBanner(){
        wait.until(ExpectedConditions.visibilityOf(homeScreenFirstBanner)).click();
    }

    public String getCategoryNameSelector(int categoryId){
        return String.format(categoryNameSelector, categoryId);
    }

    public String getCategoryUiSelector(int categoryId){
        return String.format(categorySelector, getCategoryNameSelector(categoryId));
    }
    public void pullToRefresh() {
        isElementDisplayed(scrollingByDeliverInText);
    }

    public WebElement getCategoryUiElement(int categoryId){
        try {
            return wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(getCategoryUiSelector(categoryId))));
        } catch (Exception e){
            return null;
        }
    }

    public void pressCategory(int categoryId){
        getCategoryUiElement(categoryId).click();
        wait.until(ExpectedConditions.invisibilityOfElementLocated(By.xpath(getCategoryUiSelector(categoryId))));
    }

    public boolean isCategoryDisplayed(int categoryId){
        try {
            return isElementDisplayed(getCategoryUiElement(categoryId));
        } catch (Exception e){
            return false;
        }
    }

    public Boolean isRatingBottomDisplayed ()
    {
       return isElementDisplayed(closeRatingBottom);
    }

    public void dismissRatingBottomIfDisplayed()
    {
        if(isRatingBottomDisplayed())
        {
            wait.until(ExpectedConditions.elementToBeClickable(closeRatingBottom)).click();
        }
    }
    public void pressOnSearchBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(searchBtn)).click();
    }
    public void pressAddressesListBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(AddressesDropDownList))
                .click();
    }
    public boolean isTomorrowTxtDisplayed() {
        return isElementDisplayed(tomorrowTxt);
    }

    public boolean isNowTxtDisplayed() {
        return isElementDisplayed(nowTxt);
    }
    public boolean isBusyModalDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(busyModal));
        return isElementDisplayed(busyModal);
    }
    public void pressCloseBusyModalIcon(){
        wait.until(ExpectedConditions.elementToBeClickable(closeBusyModalIcon))
                .click();
    }
    public void pressSwitchToTomorrowInBusyModal(){
        wait.until(ExpectedConditions.elementToBeClickable(switchToTomorrowBtn))
                .click();
    }
    public boolean isWeAreBusyTxtDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(weAreBusyTxt));
        return isElementDisplayed(weAreBusyTxt);
    }
    public boolean isMiniTrackerDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(miniTrackerMsg));
        return isElementDisplayed(miniTrackerMsg);
    }
}
