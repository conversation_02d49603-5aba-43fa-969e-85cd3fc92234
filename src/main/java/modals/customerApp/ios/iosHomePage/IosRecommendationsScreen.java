package modals.customerApp.ios.iosHomePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class IosRecommendationsScreen extends BaseIosScreen {
    public IosRecommendationsScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(id = "recommendations_page_container")
    WebElement recommendationsPageContainer;

    public boolean isPageDisplayed(){
        return isElementDisplayed(recommendationsPageContainer);
    }
}
