package modals.customerApp.ios.iosHomePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosNowAndTomorrowModal extends BaseIosScreen {

    public IosNowAndTomorrowModal(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }
    @FindBy (xpath = "//XCUIElementTypeOther[@name='selectTomorrowTouchable']")
    WebElement tomorrowBtn;

    @FindBy (xpath = "//XCUIElementTypeOther[@name='selectNowTouchable']")
    WebElement nowBtn;

    public boolean isModalDisplayed(){
        return isElementDisplayed(tomorrowBtn) || isElementDisplayed(nowBtn);
    }

    public void changeServeType(String serveType){
        if (isModalDisplayed()){
            WebElement targetBtn = null;
            switch (serveType.toLowerCase()){
                case "now" -> targetBtn = nowBtn;
                case "tomorrow" -> targetBtn = tomorrowBtn;
            }

            wait.until(ExpectedConditions.elementToBeClickable(targetBtn)).click();
            wait.until(ExpectedConditions.invisibilityOf(targetBtn));
        }
    }

}
