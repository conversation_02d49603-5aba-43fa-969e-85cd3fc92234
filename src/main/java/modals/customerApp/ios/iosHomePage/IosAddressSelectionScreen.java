package modals.customerApp.ios.iosHomePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosAddressSelectionScreen extends BaseIosScreen {
    public IosAddressSelectionScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Select delivery address']")
    WebElement addressSelectionDropdownHeader;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Close_imgview']")
    WebElement addressSelectionDropdownCloseBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='addressesList']")
    WebElement addressesListContainer;

    @FindBy(xpath = "//XCUIElementTypeScrollView")
    WebElement addressesListScrollableContainer;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='add_new_address_container']")
    WebElement addNewAddressBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Select delivery options']")
    WebElement addressOptionsDropdownHeader;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"selectTomorrowTouchable\"]")
    WebElement tomorrowOptionBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"Close_imgview\"]")
    WebElement addressOptionsCloseBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"60 minutes\"]")
     WebElement instantSlotTxt;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"selectNowTouchable\"]")
    WebElement nowModeBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"“Tomorrow” is the only available option.\"]")
    WebElement nowOnlyIsVisibleTxt;

    String addNewAddressBtnNameSelector = "add_new_address_container";

    public boolean isDropDownDisplayed(){
        return isElementDisplayed(addressSelectionDropdownHeader) && isElementDisplayed(addressesListContainer);
    }

    public void closeAddressSelectionDropDown(){
        wait.until(ExpectedConditions.elementToBeClickable(addressSelectionDropdownCloseBtn)).click();
        wait.until(ExpectedConditions.invisibilityOf(addressSelectionDropdownHeader));
    }

    public boolean isAddNewAddressBtnDisplayed(){
        return isElementDisplayed(addNewAddressBtn);
    }

    public void pressAddNewAddressBtn(){
        wait.until(ExpectedConditions.visibilityOf(addNewAddressBtn)).click();
    }

    public WebElement getAddressesListScrollableContainer() {
        return addressesListScrollableContainer;
    }

    public WebElement getAddNewAddressBtn() {
        return addNewAddressBtn;
    }

    public String getAddNewAddressBtnNameSelector() {
        return addNewAddressBtnNameSelector;
    }

    public boolean isModalDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(addressOptionsDropdownHeader));
        return isElementDisplayed(addressOptionsDropdownHeader);
    }
    public void pressTomorrowOptionBtn() {
        wait.until(ExpectedConditions.visibilityOf(tomorrowOptionBtn)).click();
    }
    public void pressAddressOptionsCloseBtn() {
        wait.until(ExpectedConditions.visibilityOf(addressOptionsCloseBtn)).click();
    }
    public boolean isInstantSlotDisplayed() {
        return isElementDisplayed(instantSlotTxt);
    }
    public void pressNowModeBtn() {
        wait.until(ExpectedConditions.visibilityOf(nowModeBtn)).click();
    }

    public boolean isTomorrowOnlyIsAvailableOptionTxtDisplayed() {
        wait.until(ExpectedConditions.visibilityOf(nowOnlyIsVisibleTxt));
        return isElementDisplayed(nowOnlyIsVisibleTxt);
    }
    public boolean isTomorrowOptionBtnClickable() {
        if (tomorrowOptionBtn.isDisplayed() && tomorrowOptionBtn.isEnabled()) {
            return true;
        }
        return false;        // Return true if the button is clickable, otherwise return false

    }
}
