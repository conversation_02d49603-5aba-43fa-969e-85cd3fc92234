package modals.customerApp.ios.iosHomePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosReferralScreen extends BaseIosScreen {
    public IosReferralScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Re<PERSON> and Earn']")
    WebElement pageTitle;

    @FindBy(xpath = "(//XCUIElementTypeOther[@name='Back_btn'])[1]")
    WebElement backBtn;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"Complete\n2 orders 2 Orders left\"]")
    WebElement progressBarFirstMilestone;

    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"Spend EGP 200+\non Breadfast EGP 200 left\"]")
    WebElement progressBarSecondMilestone;

    @FindBy(xpath = "//XCUIElementTypeOther[@name='Share']")
    WebElement shareBtn;

    String referralCodeTextFieldSelector = "//XCUIElementTypeOther[@name='%s']";

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }

    public void pressBackBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(backBtn))
                .click();
    }

    public boolean isProgressBarsDisplayed(){
        return isElementDisplayed(progressBarFirstMilestone) && isElementDisplayed(progressBarSecondMilestone);
    }

    public boolean isShareBtnDisplayed(){
        return isElementDisplayed(shareBtn);
    }

    public boolean isReferralCodeDisplayedCorrectly(String referralCode){
        return isElementDisplayed(wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(referralCodeTextFieldSelector, referralCode)))));
    }
}
