package modals.customerApp.ios.iosHomePage;

import io.appium.java_client.ios.IOSDriver;
import modals.BaseIosScreen;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class IosOpenedBannerModalScreen extends BaseIosScreen {
    public IosOpenedBannerModalScreen(IOSDriver iosDriver) {
        super(iosDriver);
        PageFactory.initElements(iosDriver, this);
    }

    @FindBy(xpath = "//XCUIElementTypeAlert[@name=\"Coupon copied to clipboard!\"]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeScrollView[1]/XCUIElementTypeOther[1]")
    WebElement copiedBannerPopup;
    @FindBy(id = "//XCUIElementTypeButton[@name=\"OK\"]")
    WebElement okButtonId;
    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"closeNonSuppliersBtn\"]")
    WebElement closeButton;
    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"categoryDetails_header_backBtn\"]")
    WebElement backArrowForBanner;
    @FindBy(xpath = "//XCUIElementTypeOther[@name=\"payHeaderContainer\"]")
    WebElement redirectToPayScreenBannerHeader;

    public void clickOkButtonOfRedirectingToCouponBanner() {
        wait.until(ExpectedConditions.elementToBeClickable(okButtonId))
                .click();
    }

    public boolean isClickOkButtonOfRedirectingToCouponBannerPopupDisplayed() {
        return isElementDisplayed(copiedBannerPopup);
    }

    public void clickBackButtonOfRedirectingToCategorySubCategoryBanner() {
        wait.until(ExpectedConditions.elementToBeClickable(backArrowForBanner))
                .click();
    }

    public void clickCloseButtonOfInformativeBanner() {
        wait.until(ExpectedConditions.elementToBeClickable(closeButton))
                .click();
    }

    public boolean isCloseButtonOfInformativeBannerDisplayed() {
        return isElementDisplayed(closeButton);
    }

    //Check which banner type is opened :
    public void getOpenedBannerAction() {

        if (isElementDisplayed(copiedBannerPopup)) {
            clickOkButtonOfRedirectingToCouponBanner();
        } else if (isElementDisplayed(closeButton)) {
            clickCloseButtonOfInformativeBanner();
        } else if (isElementDisplayed(backArrowForBanner)) {
            clickBackButtonOfRedirectingToCategorySubCategoryBanner();

        } else {
            // Handle case where no of these cases happened
            throw new IllegalStateException("No banner action needed");
        }

    }
}
