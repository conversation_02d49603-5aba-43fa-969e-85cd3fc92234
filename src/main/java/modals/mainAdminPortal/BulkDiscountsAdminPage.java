package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class BulkDiscountsAdminPage extends BaseWebPage {
    public BulkDiscountsAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "dashboard/bulk-discounts";

    @FindBy(xpath = "//span[@class='ant-page-header-heading-title' and @title='Bulk Discounts']")
    WebElement pageTitle;

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle);
    }
}
