package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class SignatureAdminPage extends BaseWebPage {
    public SignatureAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "signature/";

    @FindBy(xpath = "//button[contains(@class, 'btn-breadfast') and text()='Generate URL']")
    WebElement generateUrlBtn;

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(generateUrlBtn);
    }
}
