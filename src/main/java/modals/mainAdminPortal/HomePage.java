package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.time.Duration;

public class HomePage extends BaseWebPage {
    public HomePage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    @FindBy(id = "headerDdl")
    WebElement moreBtn;

    @FindBy(xpath = "//a[@class='dropdown-item' and contains(@href, 'logout')]")
    WebElement logoutBtn;

    public boolean isMoreBtnDisplayed(){
        int maxRetries = 15;
        for (int attempt = 0; attempt < maxRetries; attempt++){
            try {
                webDriver.navigate().refresh();
                wait.until(ExpectedConditions.visibilityOf(moreBtn));
                return true;
            } catch (Exception e){
                try {
                    Thread.sleep(Duration.ofSeconds(2).toMillis());
                } catch (Exception ie){
                    // do nothing
                }
            }
        }
        return false;
    }

    public void clickMoreBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(moreBtn))
                .click();
        wait.until(ExpectedConditions.visibilityOf(logoutBtn));
    }

    public void clickLogoutBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(logoutBtn))
                .click();
    }

    public void logout(){
        wait.until(ExpectedConditions.elementToBeClickable(logoutBtn))
                .click();
    }
}
