package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class RecommendationsAdminPage extends BaseWebPage {
    public RecommendationsAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "recommendations/";

    @FindBy(xpath = "//div[@class='v-btn__content' and text()='Add New']")
    WebElement addNewRecommendationBtn;

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(addNewRecommendationBtn);
    }
}
