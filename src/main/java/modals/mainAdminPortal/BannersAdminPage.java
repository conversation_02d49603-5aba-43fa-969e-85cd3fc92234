package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class BannersAdminPage extends BaseWebPage {
    public BannersAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "dashboard/banners";

    @FindBy(xpath = "//span[@class='ant-page-header-heading-title' and text()='Banners']")
    WebElement pageTitle;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-primary' and span[text()='Add Banner']]")
    WebElement addBannerBtn;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-primary' and span[text()='Sort Banners']]")
    WebElement sortBannersBtn;

    @FindBy(xpath = "//ul[@class='ant-pagination ant-table-pagination ant-table-pagination-right']")
    WebElement bannersTablePaginationHeader;

    @FindBy(xpath = "//tr[@class='ant-table-row ant-table-row-level-0']")
    List<WebElement> bannersList;

    String editBtnXpath = "./td[last()]/div/div/a/button/span[@aria-label='edit']";

    String deleteBtnXpath = "./td[last()]/div/div[last()]/button/span[@aria-label='delete']";

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isBannersListDisplayed(){
        return isElementDisplayed(bannersTablePaginationHeader);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle) && isBannersListDisplayed();
    }

    public boolean isAddBannerBtnDisplayed(){
        return isElementDisplayed(addBannerBtn);
    }

    public boolean isSortBannersBtnDisplayed(){
        return isElementDisplayed(sortBannersBtn);
    }

    public boolean isEditBtnDisplayed(){
        if (isBannersListDisplayed()){
            try{
                wait.until(ExpectedConditions.visibilityOf(bannersList.get(0).findElement(By.xpath(editBtnXpath))));
                return true;
            } catch (Exception e){
                return false;
            }
        } else {
            return false;
        }
    }

    public boolean isDeleteBtnDisplayed(){
        if (isBannersListDisplayed()){
            try{
                wait.until(ExpectedConditions.visibilityOf(bannersList.get(0).findElement(By.xpath(deleteBtnXpath))));
                return true;
            } catch (Exception e){
                return false;
            }
        } else {
            return false;
        }
    }
}
