package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.*;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class DevlieryCapacityManagementAdminPage extends BaseWebPage {

    public DevlieryCapacityManagementAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String path = "dashboard/dcm";
    @FindBy(xpath = "//input[@type='search' and @class='ant-select-selection-search-input']")
    WebElement fpDropDown;

    @FindBy(xpath = "//input[@id='DCM_selectFP_selector' and @type='search' and @autocomplete='off' and @class='ant-select-selection-search-input']")
    WebElement fpDropdownSearch;
    @FindBy(xpath = "//div[@class='ant-select-item ant-select-item-option']/div[@class='ant-select-item-option-content']")
    WebElement fpDropDownList;
    String fpSelectionFromDropdown = "//div[@class='ant-select-item-option-content' and text()='%s']";
    @FindBy(id = "DCM_editSettings_btn")
    WebElement editSettingsBtn;
    @FindBy(id = "editCapacity_saveChanges_btn")
    WebElement saveSettingsBtn;
    @FindBy(id = "editCapacity_24H_tgl")
    WebElement edit24hrtoggle;
    @FindBy(id = "editCapacity_extendedHours_tgl")
    WebElement editExtendedhrsToggle;
    @FindBy(id = "editCapacity_close_btn")
    WebElement closeSettingsBtn;
    @FindBy(xpath = "//div[@class='ant-picker ant-picker-large']//input[@placeholder='From']")
    WebElement workinghrsFromTextbox;
    @FindBy(xpath = "//div[@class='ant-picker ant-picker-large']//input[@placeholder='To']")
    WebElement workinghrsToTextbox;
    @FindBy(xpath = "//div[p[text()='Working Hours']]/h3")
    WebElement workinghrsTimeSet;
    @FindBy(xpath = "//div[p[text()='Extended Hours']]/h3")
    WebElement extendedhrsSet;
    @FindBy(xpath = "//div[@class='rc-virtual-list-holder-inner']//div[@class='ant-select-item-option-content']")
    public List<WebElement> listItems;
    @FindBy(xpath = "//div[@class=\"ant-select-selector\"]/span[@class=\"ant-select-selection-item\"]")
    WebElement selectedFp;
    @FindBy(id = "rc-tabs-0-tab-2")
    WebElement daTab;

    @FindBy(id = "rc-tabs-0-tab-1")
    WebElement capacityTab;

    @FindBy(id = "DCM_capacityPerTimeslotSave_btn")
    WebElement saveCapacityBtn;

    @FindBy(className = "ant-spin")
    WebElement spinner;
    String capacityTextBox = "DCM_capacityPerTimeslot_inputText";

    String capacityRow = "//tr[contains(td[@class='ant-table-cell'], '%s')]";
    String daRow = "//tr[contains(td, '%s')]";

    String assignDaList = "//div[@id='dcm-list']//div[contains(@class, 'ant-table-body')]//tr[contains(., '%s')]//input[@type='checkbox']";

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-default' and span[text()='Unassign']]")
    WebElement unassignBtn;

    @FindBy(id = "DCM_assignDA_btn")
    WebElement assignModalBtn;

    @FindBy(id = "assignNewDA_assign_btn")
    WebElement confirmAssignBtn;

    @FindBy(xpath = "//div[contains(@class, 'ant-modal-content')]//input[@data-testid='searchUserInput']")
    WebElement searchDaBox;

    public void goToPage() {
        visitASubPage(path);
    }

    public void choosefp () {

        wait.until(ExpectedConditions.elementToBeClickable(fpDropDown))
                .click();
    }

    public List<String> getFpNames () {

        List<String> fpNamesInDropDown = new ArrayList<>();
        wait.until(ExpectedConditions.elementToBeClickable(fpDropDownList));
        for (WebElement listItem : listItems) {
            fpNamesInDropDown.add(listItem.getText());
        }
        return fpNamesInDropDown;
    }

    public void chooseFpByName(String fpName) {

        fpDropdownSearch.click();
        fpDropdownSearch.sendKeys(fpName);
        WebElement targetfp = wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath(String.format(fpSelectionFromDropdown, fpName))));
        targetfp.click();
        wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn));
    }

    public void setCapacityValues(String timeslot, String value) {
        wait.until(ExpectedConditions.invisibilityOf(spinner));

        WebElement targetSlot = wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(capacityRow, timeslot))));

        WebElement targetCapacityBox = targetSlot.findElement(By.id(capacityTextBox));

        try {
            String currentValue = targetCapacityBox.getAttribute("value");

            if (!value.equals(currentValue)) {
                targetCapacityBox.sendKeys(Keys.chord(Keys.CONTROL, "a"), value);

                wait.until(ExpectedConditions.elementToBeClickable(saveCapacityBtn)).click();

                wait.until(ExpectedConditions.invisibilityOf(saveCapacityBtn));
            }
        } catch (Exception e) {
            // Do nothing
        }
    }

    public void setAllCapacityValues(String value) {
        wait.until(ExpectedConditions.invisibilityOf(spinner));

        List<WebElement> capacityTextBoxes = wait.until(ExpectedConditions.visibilityOfAllElementsLocatedBy(
                By.id(capacityTextBox)));

        for (WebElement capacityBox : capacityTextBoxes) {
            try {
                String currentValue = capacityBox.getAttribute("value");

                if (!value.equals(currentValue)) {
                    capacityBox.sendKeys(Keys.chord(Keys.CONTROL, "a"), value);

                     wait.until(ExpectedConditions.elementToBeClickable(saveCapacityBtn))
                     .click();

                    wait.until(ExpectedConditions.invisibilityOf(saveCapacityBtn));

                    wait.until(ExpectedConditions.invisibilityOf(spinner));
                }
            } catch (Exception e) {
                // Do nothing
            }
        }
    }

    public void openDaTab(){
        wait.until(ExpectedConditions.elementToBeClickable(daTab)).click();
    }

    public boolean isDaAssigned(String userName) {
        wait.until(ExpectedConditions.visibilityOf(unassignBtn));
        try {
            wait.until(ExpectedConditions.visibilityOfElementLocated(
                    By.xpath(String.format(daRow, userName))));
            return true;
        } catch (NoSuchElementException | TimeoutException e) {
            return false;
        }
    }

    public void assignDafromList(String userName) {

        wait.until(ExpectedConditions.elementToBeClickable(assignModalBtn)).click();
        wait.until(ExpectedConditions.visibilityOf(searchDaBox)).sendKeys(userName);
        webDriver.findElement(By.xpath(String.format(assignDaList, userName))).click();
        wait.until(ExpectedConditions.elementToBeClickable(confirmAssignBtn)).click();
    }

    public String enable24hrtoggle () {

        wait.until(ExpectedConditions.invisibilityOf(spinner));
        wait.until(ExpectedConditions.visibilityOf(workinghrsTimeSet));

        if (workinghrsTimeSet.getText().equals("24 Hours")) {
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn)).click();
            wait.until(ExpectedConditions.elementToBeClickable(edit24hrtoggle)).click();
            wait.until(ExpectedConditions.elementToBeClickable(workinghrsFromTextbox)).sendKeys("12 a", Keys.ENTER);
            wait.until(ExpectedConditions.elementToBeClickable(workinghrsToTextbox)).sendKeys("4 a", Keys.ENTER);
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn)).click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn))
                    .click();
            wait.until(ExpectedConditions.elementToBeClickable(edit24hrtoggle))
                    .click();
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn))
                    .click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
        } else {
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn))
                    .click();
            wait.until(ExpectedConditions.elementToBeClickable(edit24hrtoggle))
                    .click();
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn))
                    .click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
        }
        wait.until(ExpectedConditions.invisibilityOf(spinner));
        wait.until(ExpectedConditions.visibilityOf(extendedhrsSet)).click();
        return workinghrsTimeSet.getText();
    }

    public String disable24hrtoggle() {
        wait.until(ExpectedConditions.invisibilityOf(spinner));
        wait.until(ExpectedConditions.visibilityOf(workinghrsTimeSet));

        if (workinghrsTimeSet.getText().equals("24 Hours")) {
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn)).click();
            wait.until(ExpectedConditions.elementToBeClickable(edit24hrtoggle)).click();
            wait.until(ExpectedConditions.elementToBeClickable(workinghrsFromTextbox)).sendKeys("12 a", Keys.ENTER);
            wait.until(ExpectedConditions.elementToBeClickable(workinghrsToTextbox)).sendKeys("4 a", Keys.ENTER);
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn)).click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
        } else {
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn))
                    .click();
            wait.until(ExpectedConditions.elementToBeClickable(edit24hrtoggle))
                    .click();
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn))
                    .click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn)).click();
            wait.until(ExpectedConditions.elementToBeClickable(edit24hrtoggle)).click();
            wait.until(ExpectedConditions.elementToBeClickable(workinghrsFromTextbox)).sendKeys("12 a", Keys.ENTER);
            wait.until(ExpectedConditions.elementToBeClickable(workinghrsToTextbox)).sendKeys("7 a", Keys.ENTER);
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn)).click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
        }
        wait.until(ExpectedConditions.invisibilityOf(spinner));
        wait.until(ExpectedConditions.visibilityOf(extendedhrsSet)).click();
        return workinghrsTimeSet.getText();
    }

    public String enableExtendedHrsToggle() {
        wait.until(ExpectedConditions.invisibilityOf(spinner));
        wait.until(ExpectedConditions.visibilityOf(extendedhrsSet));

        if (extendedhrsSet.getText().equals("DISABLED")) {
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn)).click();
            wait.until(ExpectedConditions.elementToBeClickable(editExtendedhrsToggle)).click();
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn)).click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
        } else {
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn))
                    .click();
            wait.until(ExpectedConditions.elementToBeClickable(editExtendedhrsToggle))
                    .click();
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn))
                    .click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn)).click();
            wait.until(ExpectedConditions.elementToBeClickable(editExtendedhrsToggle)).click();
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn)).click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
        }
        wait.until(ExpectedConditions.invisibilityOf(spinner));
        wait.until(ExpectedConditions.visibilityOf(extendedhrsSet)).click();
        return extendedhrsSet.getText();
    }

    public String disableExtendedHrsToggle() {
        wait.until(ExpectedConditions.invisibilityOf(spinner));
        wait.until(ExpectedConditions.visibilityOf(extendedhrsSet));

        if (extendedhrsSet.getText().equals("ENABLED")) {
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn)).click();
            wait.until(ExpectedConditions.elementToBeClickable(editExtendedhrsToggle)).click();
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn)).click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
        } else {
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn))
                    .click();
            wait.until(ExpectedConditions.elementToBeClickable(editExtendedhrsToggle))
                    .click();
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn))
                    .click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
            wait.until(ExpectedConditions.elementToBeClickable(editSettingsBtn)).click();
            wait.until(ExpectedConditions.elementToBeClickable(editExtendedhrsToggle)).click();
            wait.until(ExpectedConditions.elementToBeClickable(saveSettingsBtn)).click();
            wait.until(ExpectedConditions.invisibilityOf(saveSettingsBtn));
        }
        wait.until(ExpectedConditions.invisibilityOf(spinner));
        wait.until(ExpectedConditions.visibilityOf(extendedhrsSet)).click();
        return extendedhrsSet.getText();
    }

    public String getNextTimeSlot24Hours() {
        ZoneId egyptZone = ZoneId.of("Africa/Cairo");
        ZonedDateTime currentTimeInEgypt = ZonedDateTime.now(egyptZone);

        LocalTime nextStartTime = currentTimeInEgypt.toLocalTime().plusHours(1);
        LocalTime nextEndTime = nextStartTime.plusHours(1);

        return nextStartTime.format(DateTimeFormatter.ofPattern("HH:00")) + "-" +
                nextEndTime.format(DateTimeFormatter.ofPattern("HH:00"));
    }
    public String getNextTimeSlot12Hours() {
        LocalTime currentTime = LocalTime.now();
        LocalTime nextStartTime = currentTime.plusHours(1).withMinute(0);
        LocalTime nextEndTime = nextStartTime.plusHours(1).withMinute(0);
        String startTime12Hours = nextStartTime.format(DateTimeFormatter.ofPattern("hh:00 a"));
        String endTime12Hours = nextEndTime.format(DateTimeFormatter.ofPattern("hh:00 a"));
        return startTime12Hours + " - " + endTime12Hours;
    }

    public String getScheduledTimeSlot24Hours() {
        ZoneId egyptZone = ZoneId.of("Africa/Cairo"); 
        ZonedDateTime currentTimeInEgypt = ZonedDateTime.now(egyptZone);

        LocalTime nextStartTime = currentTimeInEgypt.toLocalTime().plusHours(2);
        LocalTime nextEndTime = nextStartTime.plusHours(1);

        return nextStartTime.format(DateTimeFormatter.ofPattern("HH:00")) + "-" +
                nextEndTime.format(DateTimeFormatter.ofPattern("HH:00"));
    }
    public String getScheduledTimeSlot24HoursCalculated(int hours) {
        LocalTime currentTime = LocalTime.now();
        LocalTime nextStartTime = currentTime.plusHours(hours);
        LocalTime nextEndTime = nextStartTime.plusHours(1);
        return nextStartTime.format(DateTimeFormatter.ofPattern("HH:00")) + "-" +
                nextEndTime.format(DateTimeFormatter.ofPattern("HH:00"));
    }

    public String getSelectedFpName () {

        wait.until(ExpectedConditions.visibilityOf(selectedFp)).getText();
        return selectedFp.getText();
    }
}

