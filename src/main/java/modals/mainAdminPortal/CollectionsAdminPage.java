package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class CollectionsAdminPage extends BaseWebPage {
    public CollectionsAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "dashboard/collections";

    @FindBy(xpath = "//h3[text()='Collections']")
    WebElement pageTitle;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-primary']/span[text()='Add category collection']")
    WebElement addNewCollectionBtn;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-primary']/span[text()='Add homescreen collection']")
    WebElement addHomeScreenCollectionBtn;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-primary']/span[text()='SORT']")
    WebElement sortBtn;

    @FindBy(xpath = "//tr[contains(@class, 'ant-table-row')]")
    List<WebElement> collectionsList;

    @FindBy(xpath = "//span[@class='ant-spin-dot ant-spin-dot-spin']")
    WebElement loadingSpinner;

    @FindBy(xpath = "//ul[@class='ant-pagination ant-table-pagination ant-table-pagination-right']")
    WebElement collectionsListPaginationFooter;

    String editBtnXpath = "./td[last()]/div/div/div/button/span[@aria-label='edit']";

    String deleteBtnXpath = "./td[last()]/div/div/div/button/span[@aria-label='delete']";

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public void waitUntilCollectionsAreLoaded(){
        wait.until(ExpectedConditions.invisibilityOf(loadingSpinner));
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle) && isElementDisplayed(collectionsListPaginationFooter);
    }

    public boolean isAddNewCollectionBtnDisplayed(){
        return isElementDisplayed(addNewCollectionBtn);
    }

    public boolean isAddHomeScreenCollectionBtnDisplayed(){
        return isElementDisplayed(addHomeScreenCollectionBtn);
    }

    public boolean isAddButtonsDisplayed(){
        return isAddNewCollectionBtnDisplayed() && isAddHomeScreenCollectionBtnDisplayed();
    }

    public boolean isEditBtnDisplayed(){
        if (isPageDisplayed()){
            try{
                wait.until(ExpectedConditions.visibilityOf(collectionsList.get(0).findElement(By.xpath(editBtnXpath))));
                return true;
            } catch (Exception e){
                return false;
            }
        } else {
            return false;
        }
    }

    public boolean isDeleteBtnDisplayed(){
        if (isPageDisplayed()){
            try{
                wait.until(ExpectedConditions.visibilityOf(collectionsList.get(0).findElement(By.xpath(deleteBtnXpath))));
                return true;
            } catch (Exception e){
                return false;
            }
        } else {
            return false;
        }
    }

    public boolean isSortCollectionsBtnDisplayed(){
        return isElementDisplayed(sortBtn);
    }
}
