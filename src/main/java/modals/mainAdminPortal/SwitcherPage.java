package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;
import java.util.Objects;

public class SwitcherPage extends BaseWebPage {
    public SwitcherPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String path = "switcher/";

    @FindBy(id = "select2-search_inpt-container")
    WebElement selectUserDropdown;

    @FindBy(className = "select2-search__field")
    WebElement searchUserTxtField;

    String userSelector = "//li[contains(text(), '%s')]";

    @FindBy(xpath = "//label[contains(text(), \"Roles\")]/following-sibling::div[@class='multiselect']")
    WebElement rolesDropdown;

    @FindBy(xpath = "//label[contains(text(), 'Roles')]/following-sibling::div[@class=" +
            "'multiselect multiselect--disabled']")
    WebElement disabledRolesDropdown;

    @FindBy(xpath = "//p[contains(text(), 'User ID: ')]")
    WebElement userIdTextBox;

    @FindBy(xpath = "//i[@class='multiselect__tag-icon']")
    List<WebElement> deleteRoleBtns;

    String roleSelector = "//li[@class='multiselect__element']//span[contains(text(), '%s')]";

    @FindBy(xpath = "//div[@class='multiselect__tags']//input[@class='multiselect__input']")
    WebElement roleSearchTxtField;

    @FindBy(id = "BlockUser_btn")
    WebElement blockUserBtn;

    @FindBy(id = "DeleteUser_btn")
    WebElement deleteUserBtn;

    @FindBy(id = "Save_btn")
    WebElement saveBtn;

    @FindBy(id = "updateBalance_btn")
    WebElement updateBalanceBtn;

    @FindBy(id = "ChangePassword_btn")
    WebElement changePwdBtn;

    @FindBy(id = "AddNewUser_btnwwww")
    WebElement addNewUserBtn;

    @FindBy(id = "FirstName_txt")
    WebElement firstNameTxtField;

    @FindBy(css = "input.form-control[placeholder='Address']")
    WebElement addressTxtField;

    @FindBy(xpath = "//b[text() = 'Recent Orders']")
    WebElement recentOrdersSectionHeader;

    @FindBy(id = "innerHeaderDdl")
    WebElement moreBtn;

    @FindBy(xpath = "//b[text()='Main Info']")
    WebElement b2bSectionHeader;

    @FindBy(xpath = "//input[@placeholder='New Balance']")
    WebElement updateBalanceTxtField;

    @FindBy(xpath = "//*[@id='updateBalanceModal']/div/div/div[2]/button")
    WebElement updateBtn;

    @FindBy(xpath = "//*[@class='woocommerce-message']")
    WebElement updateBalanceSuccessMessage;

    public void goToSwitcherPage(){
        visitASubPage(path);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(selectUserDropdown);
    }

    public void openSelectUserDropdown(){
        try {
            wait.until(ExpectedConditions.elementToBeClickable(selectUserDropdown))
                    .click();
        } catch (Exception e){
            // Do Nothing
        }
    }

    public void searchForPhoneNumberAndSelectUser(String phoneNumber){
        try {
            wait.until(ExpectedConditions.visibilityOf(searchUserTxtField)).sendKeys(phoneNumber);
            wait.until(ExpectedConditions.elementToBeClickable(By.xpath(String.format(userSelector, phoneNumber))))
                    .click();
            wait.until(ExpectedConditions.visibilityOf(userIdTextBox));
        } catch (Exception e){
            // Do Nothing
        }
    }

    public void removeAllRoles(){
        wait.until(ExpectedConditions.visibilityOfAllElements(deleteRoleBtns));

        for (WebElement button : deleteRoleBtns){
            try {
                moveToElement(b2bSectionHeader);
                wait.until(ExpectedConditions.visibilityOf(button)).click();
            } catch (Exception e){
                openRolesDropdown();
                new Actions(webDriver).sendKeys(roleSearchTxtField, Keys.BACK_SPACE);
                wait.until(ExpectedConditions.elementToBeClickable(b2bSectionHeader)).click();
//                wait.until(ExpectedConditions.elementToBeClickable(button)).click();
            }
            wait.until(ExpectedConditions.invisibilityOf(button));
        }

        wait.until(ExpectedConditions.invisibilityOfAllElements(deleteRoleBtns));
    }

    public void openRolesDropdown(){
        moveToElement(b2bSectionHeader);
        wait.until(ExpectedConditions.elementToBeClickable(rolesDropdown))
                .click();
    }

    public void chooseRole(String role){
        role = capitalizeFirstLetter(role);
        wait.until(ExpectedConditions.visibilityOf(roleSearchTxtField)).sendKeys(role);
        wait.until(ExpectedConditions.elementToBeClickable(By.xpath(String.format(roleSelector, role)))).click();
    }

    public boolean isRolesDropdownDisabled(){
        try {
            wait.until(ExpectedConditions.visibilityOf(disabledRolesDropdown));
            return true;
        } catch (Exception e){
            return false;
        }

    }

    public boolean isBlockUserBtnDisplayed(){
        return isElementDisplayed(blockUserBtn);
    }

    public boolean isDeleteUserBtnDisplayed(){
        return isElementDisplayed(deleteUserBtn);
    }

    public boolean isUpdateBalanceBtnDisplayed(){
        return isElementDisplayed(updateBalanceBtn);
    }

    public boolean isChangePwdBtnDisplayed(){
        return isElementDisplayed(changePwdBtn);
    }

    public boolean isAddNewUserBtnDisplayed(){
        return isElementDisplayed(addNewUserBtn);
    }

    public boolean isFirstNameTxtFieldDisplayed(){
        return isElementDisplayed(firstNameTxtField);
    }

    public boolean isAddressTxtFieldDisplayed(){
        return isElementDisplayed(addressTxtField);
    }

    public boolean isFirstNameTxtFieldDisabled(){
        wait.until(ExpectedConditions.visibilityOf(firstNameTxtField));
        return isTextFieldDisabled(firstNameTxtField);
    }

    public boolean isAddressTxtFieldDisabled(){
        wait.until(ExpectedConditions.visibilityOf(addressTxtField));
        return isTextFieldDisabled(addressTxtField);
    }

    public boolean isTextFieldDisabled(WebElement element){
        String disabled = element.getAttribute("disabled");

        boolean isTxtFieldDisabled;
        isTxtFieldDisabled = Objects.equals(disabled, "disabled");

        return isTxtFieldDisabled;
    }

    public boolean isRolesDropDownDisplayed(){
        return isElementDisplayed(rolesDropdown);
    }

    public void clickSaveBtn(){
        try {
            wait.until(ExpectedConditions.visibilityOf(saveBtn))
                    .click();
        } catch (Exception e){
            moveToElement(saveBtn);
            wait.until(ExpectedConditions.elementToBeClickable(saveBtn))
                    .click();
        }
    }

    public void waitUntilOrdersSectionIsDisplayed(){
        isElementDisplayed(recentOrdersSectionHeader);
        //Forced sleep as the recent orders table renders in view
        //Since we don't know if the user has orders or not, we can't wait for an element and all we can do is wait
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void scrollToRolesDropDown(){
        moveToElement(rolesDropdown);
    }

    public void clickMoreBtn(){
        wait.until(ExpectedConditions.elementToBeClickable(moreBtn))
                .click();
    }

    public Boolean updateUserBalance(String balance) {
        wait.until(ExpectedConditions.elementToBeClickable(updateBalanceBtn)).click();
        wait.until(ExpectedConditions.elementToBeClickable(updateBalanceTxtField)).clear();
        wait.until(ExpectedConditions.elementToBeClickable(updateBalanceTxtField)).sendKeys(balance);
        wait.until(ExpectedConditions.elementToBeClickable(updateBtn)).click();

        try {
            wait.until(ExpectedConditions.visibilityOf(updateBalanceSuccessMessage));
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
