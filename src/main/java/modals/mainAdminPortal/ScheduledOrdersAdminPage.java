package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class ScheduledOrdersAdminPage extends BaseWebPage {
    public ScheduledOrdersAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "scheduled-orders/";

    @FindBy(xpath = "//a[@class='btn btn-sm btn-breadfast btn-stroke' and text()='New Scheduler' " +
            "and @data-bs-target='#scheduledOrdersModal']")
    WebElement newSchedulerBtn;

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(newSchedulerBtn);
    }
}
