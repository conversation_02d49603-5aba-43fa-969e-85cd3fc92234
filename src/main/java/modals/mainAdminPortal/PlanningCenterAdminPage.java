package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;

public class PlanningCenterAdminPage extends BaseWebPage {
    public PlanningCenterAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "dashboard/planning-center";

    @FindBy(xpath = "//span[@class='ant-page-header-heading-title' and @title='Planning Center']")
    WebElement pageHeader;

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageHeader);
    }

    public void goToPage(){
        visitASubPage(pagePath);
    }
}
