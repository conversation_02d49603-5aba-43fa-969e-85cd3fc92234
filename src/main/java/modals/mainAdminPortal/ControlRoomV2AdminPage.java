package modals.mainAdminPortal;

import modals.BaseWebPage;
import models.Order;
import org.openqa.selenium.*;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.Keys;

import java.util.List;

public class ControlRoomV2AdminPage extends BaseWebPage {

    public ControlRoomV2AdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String path = "dashboard/controlRoom";

    @FindBy(xpath = "//span[@class='ant-page-header-heading-title' and @title='Control Room']")
    WebElement pageTitle;

    @FindBy(xpath = "//div[@class='ant-modal-title' and text()='Select FP']")
    WebElement fpModuleTitle;

    @FindBy(id = "fps-ddl")
    WebElement fpDropDown;

    @FindBy(xpath = "//body/div[3]/div/div[2]/div/div[1]/div/div[2]/div[2]/div/div/div/div/div/div[2]/div/div[2]/div[1]/div/div")
    WebElement fpDropDownWait;

    @FindBy(xpath = "//input[@type='search' and @class='ant-select-selection-search-input']")
    WebElement fpDropDownSearch;

    String fpDropdownListItem = "//div[@class='rc-virtual-list-holder-inner']//div[contains(@title, '%s')]";

    @FindBy(id = "date-ddl")
    WebElement dateCalender;

    @FindBy(id = "show-fp-btn")
    WebElement showButton;

    @FindBy(id = "fp-date-btn")
    WebElement displayedFpAndDate;

    @FindBy(xpath = "//button[@type= 'button' and @class = 'ant-btn ant-btn-default ant-btn-sm']")
    WebElement displayedFpAndDate2;

    @FindBy(id = "search-cell")
    WebElement searchTxtField;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-default' and @style='color: rgb(170, 0, 130);']/span[text()='Assign Da']")
    WebElement assignDaModalInDetails;

    @FindBy(xpath = "//*[@class='ant-radio-wrapper _assignDriverRadioBtnContainer_u4mjn_30']")
    WebElement selectDaFromList;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-primary ant-btn-lg']")
    WebElement confirmAssignDaBtn;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-default _addOrderToTripBtn_1b662_365']")
    WebElement addToTripBtn;

    @FindBy(xpath = "//div[@class='ant-drawer-header-title']")
    WebElement orderDetailsDrawer;

    @FindBy(xpath = "//div[@class='ant-drawer-header-title']/div/div/div[1]")
    WebElement orderDetailsDrawerTitle;

    @FindBy(xpath = "//*[@id='single-spa-application:mainApp']/section/section/main/div/div[1]/div[1]/div[1]/div/div/span/div/span")
    WebElement orderNumberInOrderDetails;

    @FindBy(xpath = "//div[@class='ant-select-item-option-content']")
    List<WebElement> fpOptionsList;

    @FindBy(xpath = "//button[@id='show-fp-btn'][@disabled]")
    WebElement disabledShowBtn;

    @FindBy(xpath = "//div[@class='rc-virtual-list-holder-inner']//h3")
    WebElement searchResult;

    @FindBy(id = "search-cell_list")
    WebElement emptySearchResult;

    @FindBy(id = "customer-name-link")
    WebElement customerNameInOrderDetails;

    @FindBy(xpath = "//body[1]/div[2]/section[1]/section[1]/main[1]/div[1]/div[1]/div[3]/div[2]/button[2]")
    WebElement filtersBtn;

    @FindBy(xpath = "//div[@class='ant-col']//span[@class='ant-typography']//span[@class='ant-tag ant-tag-has-color']")
    WebElement timeslotInOrderDetails;

    @FindBy(xpath = "//button[@id='filters-btn']")
    WebElement showFiltersBtn;

    @FindBy(xpath = "//button[@id='group-by-btn']")
    WebElement showGroupByBtn;

    @FindBy(xpath = "//div[@class='ant-drawer-title' and text()='Group by']")
    WebElement groupByDrawerTitle;

    @FindBy(xpath = "//div[@class='ant-drawer-title' and text()='Filters']")
    WebElement filtersDrawerTitle;

    @FindBy(id = "order-details-container")
    WebElement orderDetailsContainer;

    @FindBy(xpath = "//button[contains(@class, 'ant-btn') and contains(., 'Done')]")
    WebElement packageBtnInDetails;

    @FindBy(css = "#picked-up-btn")
    WebElement pickupBtnInDetails;

    @FindBy(css = "#in-route-btn")
    WebElement inrouteBtnInDetails;

    @FindBy(id = "delivering-btn")
    WebElement deliveringBtnInDetails;

    @FindBy(id = "delivered-btn")
    WebElement deliveredBtnInDetails;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-primary _confirmBtn_1lfdj_1']/span")
    WebElement confirmCompleteOrderBtnInDetails;

    @FindBy(xpath = "//div[@class='ant-select-selector']")
    WebElement shelfNumberList;

    @FindBy(xpath = "//div[@class='ant-select-item ant-select-item-option' and @title='2']")
    WebElement shelfNumber;

    @FindBy(xpath = "//button[@class= 'ant-btn ant-btn-link'][2]")
    WebElement bagNumberBtn;

    @FindBy(xpath = "//div[@class='_bagsCountDetails_dsqqv_50']")
    WebElement bagContainer;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-primary _confirmBtn_dsqqv_92']")
    WebElement confirmShelfAndBagsBtn;

    @FindBy(xpath = "//h3[@class='_stepActionDetailsText_1cd5f_72' and text()='Packed']")
    WebElement packedStatus;

    @FindBy(xpath = "//h3[@class='_stepActionDetailsText_1cd5f_72' and text()='Picked up']")
    WebElement pickupStatus;

    @FindBy(xpath = "//div[@class='ant-timeline-item-content' and contains(., 'Starting')]")
    WebElement inrouteStatus;

    @FindBy(xpath = "//div[@class='ant-timeline-item-content' and contains(., 'Arriving')]")
    WebElement deliveringStatus;

    @FindBy(xpath = "//div[@class='ant-timeline-item-content' and contains(., 'Order completed')]")
    WebElement deliveredStatus;

    @FindBy(id = "timeslot-rad")
    WebElement groupByTimeslotBtn;

    @FindBy(xpath = "//tr[@class='ant-table-row ant-table-row-level-0']/td[1]")
    WebElement productInDetails;

    @FindBy(id = "apply-group-by-btn")
    WebElement applyGroupByBtn;

    String orderNumberRow = "//table//tr[contains(., '%s')]";

    @FindBy(xpath = "//button[@title='Open order actions list']")
    List<WebElement> editOrderBtn;

    @FindBy(id = "assign-da-btn")
    WebElement assignDaBtn;

    @FindBy(xpath = "//label[@class='ant-radio-wrapper']")
    List<WebElement> allDeliveryAgents;

    @FindBy(xpath = "//button[span[text()='Assign']]")
    WebElement assignBtn;

    @FindBy(xpath = "//div[@class='ant-notification-notice-message']")
    WebElement alertToaster;

    @FindBy(id = "assign-picker-order-details-btn")
    WebElement assignPickerBtn;

    @FindBy(xpath = "//div[@class='ant-drawer-title' and text()='Assign Picker']")
    WebElement assignPickerModuleTitle;

    @FindBy(xpath = "//input[@class='ant-input ant-input-lg' and @placeholder='Search by picker name or HR ID']")
    WebElement pickerSearchField;

    @FindBy(id = "show-fp-btn")
    WebElement showFpBtn;

    @FindBy(xpath = "//label[contains(@class,'ant-radio-wrapper')]")
    WebElement pickerRadioBtn;

    String datePickerTargetDateSelector = "//td[@title='%s']";

    String searchResultItemSelector = "//div[@id='%s']";

    String orderRowSelector = "//tr[@data-row-key='%s']";

    String actionsBtnSelector = "//button[@title='Open order actions list' and @data-type='actions-btn-%s']";

    public boolean isPageDisplayed() {
        return isElementDisplayed(pageTitle);
    }

    public void goToPage() {
        visitASubPage(path);
    }

    public boolean isFpModuleOpen() {
        try {
            wait.until(ExpectedConditions.visibilityOf(fpModuleTitle));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void selectFpFromList(String fpName) {
        fpDropDown.click();
        wait.until(ExpectedConditions.elementToBeClickable(fpDropDownWait));
        fpDropDown.click();
        fpDropDownSearch.sendKeys(fpName.toLowerCase());
        wait.until(ExpectedConditions
                .visibilityOfElementLocated(By.xpath(String.format(fpDropdownListItem, fpName)))).click();
        fpModuleTitle.click();
    }

    public void selectMultipleFPs(String fpName, String fpName2) {
        wait.until(ExpectedConditions.elementToBeClickable(fpDropDownWait));
        fpDropDown.click();
        fpDropDown.sendKeys(fpName);
        fpDropDown.sendKeys(Keys.ENTER);
        fpDropDown.sendKeys(fpName2);
        fpDropDown.sendKeys(Keys.ENTER);
        fpDropDown.sendKeys(Keys.ESCAPE);
    }

    public boolean isFPDisabled() {
        return !fpDropDown.isEnabled();
    }

    public void clickShowButton() {
        wait.until(ExpectedConditions.elementToBeClickable(showButton)).click();
        wait.until(ExpectedConditions.invisibilityOf(fpModuleTitle));
    }

    public void clickAssignPickerButton() {
        wait.until(ExpectedConditions.elementToBeClickable(assignPickerBtn)).click();
    }

    public boolean isSelectedFPAndDateDisplayed() {
        try {
            wait.until(ExpectedConditions.visibilityOf(displayedFpAndDate));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public void clickDisplayedFPAndDate() {
        wait.until(ExpectedConditions.visibilityOf(displayedFpAndDate2)).click();
    }

    public String getDisplayedFPAndDateText() {
        return displayedFpAndDate.getText();
    }

    public void pressTargetDayFromDatePicker(String targetDate) {
        String targetDateSelector = String.format(datePickerTargetDateSelector, targetDate);
        wait.until(ExpectedConditions.elementToBeClickable(dateCalender)).click();
        wait.until(ExpectedConditions.elementToBeClickable(By.xpath(targetDateSelector))).click();
    }

    public void chooseTargetDate(String targetDate, String dateFormat) {
        wait.until(ExpectedConditions.visibilityOf(fpModuleTitle));
        enterDateIntoTextField(changeDateFormatToTargetFormat(targetDate, dateFormat, "dd-MM-yyyy"));
        pressTargetDayFromDatePicker(changeDateFormatToTargetFormat(targetDate, dateFormat, "yyyy-MM-dd"));
    }

    public void enterDateIntoTextField(String testFpDate) {
        while (!dateCalender.getAttribute("value").equalsIgnoreCase(testFpDate)) {
            wait.until(ExpectedConditions.elementToBeClickable(dateCalender)).click();

            // Determine the appropriate key based on the platform
            Keys selectAllKey =
                    System.getProperty("os.name").toLowerCase().contains("mac") ? Keys.COMMAND : Keys.CONTROL;

            // Send the appropriate key to the text field
            dateCalender.sendKeys(selectAllKey + "a");
            dateCalender.sendKeys(Keys.DELETE);

            wait.until(ExpectedConditions.elementToBeClickable(dateCalender)).sendKeys(testFpDate);
            dateCalender.sendKeys(Keys.ENTER);
        }
    }

    public void searchForOrder(String searchString) {
        wait.until(ExpectedConditions.elementToBeClickable(searchTxtField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(searchTxtField))
                .sendKeys(searchString);
    }

    public void searchForPicker(String searchString) {
        wait.until(ExpectedConditions.elementToBeClickable(pickerSearchField)).click();
        wait.until(ExpectedConditions.elementToBeClickable(pickerSearchField))
                .sendKeys(searchString);
    }

    public void submitSelectedPickerToOrderBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(assignBtn)).click();
    }

    public boolean isTargetOrderDisplayedInSearchResults(String orderId) {
        try {
            WebElement targetOrderSearchItem = wait.until(ExpectedConditions
                    .visibilityOfElementLocated(By.xpath(String.format(searchResultItemSelector, orderId))));
            return isElementDisplayed(targetOrderSearchItem);
        } catch (Exception e) {
            return false;
        }
    }

    public String getSearchResult() {
        wait.until(ExpectedConditions.visibilityOf(searchResult));
        return searchResult.getText();
    }

    public String wrongSearchResult() {
        wait.until(ExpectedConditions.visibilityOf(emptySearchResult));
        return emptySearchResult.getText();
    }

    public String getOrderTimeslotFromDetails() {
        wait.until(ExpectedConditions.elementToBeClickable(searchResult)).click();
        switchToWindow(1);
        wait.until(ExpectedConditions.visibilityOf(timeslotInOrderDetails));
        return timeslotInOrderDetails.getText();
    }

    public String getOrderProductsFromDetails() {
        wait.until(ExpectedConditions.elementToBeClickable(searchResult)).click();
        switchToWindow(1);
        wait.until(ExpectedConditions.visibilityOf(productInDetails));
        return productInDetails.getText();
    }

    public void openThePlacedOrder() {
        wait.until(ExpectedConditions.elementToBeClickable(searchResult)).click();

    }

    public String getRowTextWithOrderNumber(String orderNumber) {
        WebElement rowElement = wait.until(ExpectedConditions.visibilityOfElementLocated(
                By.xpath(String.format(orderNumberRow, orderNumber))));
        return rowElement.getText();
    }

    public void clickOrderFromSearchResults(String orderId) {
        WebElement targetOrderSearchItem = wait.until(ExpectedConditions
                .visibilityOfElementLocated(By.xpath(String.format(searchResultItemSelector, orderId))));
        wait.until(ExpectedConditions.elementToBeClickable(targetOrderSearchItem)).click();
    }

    public boolean isOrderDetailsDrawerDisplayed() {
        return isElementDisplayed(orderDetailsDrawer);
    }

    public boolean isOrderDetailsDrawerDisplayedForCorrectOrder(String orderNumber) {
        try {
            return wait.until(ExpectedConditions.visibilityOf(orderDetailsDrawerTitle)).getText().contains(orderNumber);
        } catch (Exception e) {
            return false;
        }
    }

    public String getOrderNumberFromDetails() {
        wait.until(ExpectedConditions.elementToBeClickable(searchResult)).click();
        switchToWindow(1);
        wait.until(ExpectedConditions.visibilityOf(timeslotInOrderDetails));
        wait.until(ExpectedConditions.visibilityOf(orderNumberInOrderDetails));
        return orderNumberInOrderDetails.getText();
    }

    public String getCustomerNameFromDetails() {
        wait.until(ExpectedConditions.elementToBeClickable(searchResult)).click();
        switchToWindow(1);
        wait.until(ExpectedConditions.visibilityOf(timeslotInOrderDetails));
        wait.until(ExpectedConditions.visibilityOf(customerNameInOrderDetails));
        return customerNameInOrderDetails.getText();
    }

    public void selectFirstFpFromFpSelectionModal() {
        wait.until(ExpectedConditions.visibilityOf(fpDropDown)).click();
        wait.until(ExpectedConditions.visibilityOf(fpOptionsList.getFirst())).click();
    }

    public boolean isShowBtnDisabled() {
        return isElementDisplayed(disabledShowBtn);
    }

    public boolean isAllOrdersDisplayed(List<Order> ordersList) {
        boolean allOrdersDisplayed = true;
        for (Order order : ordersList) {
            try {
                WebElement orderRow = wait.until(ExpectedConditions.visibilityOfElementLocated(
                        By.xpath(String.format(orderRowSelector, order.getOrderId()))));
                if (!orderRow.isDisplayed())
                    allOrdersDisplayed = false;
            } catch (Exception e) {
                return false;
            }
        }
        return allOrdersDisplayed;
    }

    public void clickFiltersBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(showFiltersBtn)).click();
    }

    public boolean isFiltersDrawerDisplayed() {
        return isElementDisplayed(filtersDrawerTitle);
    }

    public void clickGroupByBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(showGroupByBtn)).click();
    }

    public boolean isGroupByDrawerDisplayed() {
        return isElementDisplayed(groupByDrawerTitle);
    }

    public boolean selectGroupByTimeSlot() {
        return wait.until(ExpectedConditions.visibilityOf(groupByTimeslotBtn)).isSelected();
    }

    public void clickApplyGroupByBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(applyGroupByBtn))
                .click();
    }

    public void assignDaFromDetails() {
        // Assign DA from order details
        wait.until(ExpectedConditions.elementToBeClickable(assignDaModalInDetails))
                .click();
        wait.until(ExpectedConditions.elementToBeClickable(selectDaFromList))
                .click();
        wait.until(ExpectedConditions.elementToBeClickable(addToTripBtn))
                .click();
        wait.until(ExpectedConditions.elementToBeClickable(confirmAssignDaBtn))
                        .click();
        wait.until(ExpectedConditions.invisibilityOf(confirmAssignDaBtn));
    }

    public String packageOrderStatus(){

        //Package order with shelf number
        moveToElement(orderDetailsContainer);
        moveToElement(packageBtnInDetails);
        wait.until(ExpectedConditions.elementToBeClickable(packageBtnInDetails))
                .click();
        wait.until(ExpectedConditions.elementToBeClickable(shelfNumberList))
                .click();
        wait.until(ExpectedConditions.elementToBeClickable(shelfNumber))
                .click();
        wait.until(ExpectedConditions.elementToBeClickable(bagNumberBtn))
                .click();
        wait.until(ExpectedConditions.elementToBeClickable(confirmShelfAndBagsBtn))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(packageBtnInDetails));
        wait.until((ExpectedConditions.visibilityOf(packedStatus)));
        return packedStatus.getText();
    }

    public String pickupOrderStatus() {

        // Pick up order
        moveToElement(orderDetailsContainer);
        moveToElement(packageBtnInDetails);
        wait.until(ExpectedConditions.elementToBeClickable(pickupBtnInDetails))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(pickupBtnInDetails));
        wait.until(ExpectedConditions.visibilityOf(pickupStatus));
        return pickupStatus.getText();
    }

    public String inrouteOrderStatus() {

        // Start delivering order with status in route
        moveToElement(orderDetailsContainer);
        moveToElement(packageBtnInDetails);
        wait.until(ExpectedConditions.elementToBeClickable(inrouteBtnInDetails))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(inrouteBtnInDetails));
        wait.until(ExpectedConditions.visibilityOf(inrouteStatus));
        return inrouteStatus.getText();
    }

    public String deliveringOrderStatus() {

        // Change status of order to delivering
        wait.until(ExpectedConditions.elementToBeClickable(deliveringBtnInDetails))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(deliveringBtnInDetails));
        wait.until(ExpectedConditions.visibilityOf(deliveringStatus));
        return deliveringStatus.getText();
    }

    public String deliveredOrderStatus() {

        // Complete order
        wait.until(ExpectedConditions.elementToBeClickable(deliveredBtnInDetails))
                .click();
        wait.until(ExpectedConditions.elementToBeClickable(confirmCompleteOrderBtnInDetails))
                .click();
        wait.until(ExpectedConditions.invisibilityOf(confirmCompleteOrderBtnInDetails));
        wait.until(ExpectedConditions.visibilityOf(deliveredStatus));
        return deliveredStatus.getText();
    }

    public void clickOrderActionsBtn(String orderId) {
        getOrderActionsBtnUiElement(orderId).click();
    }

    public void clickAssignDaBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(assignDaBtn)).click();
    }

    public void selectFirstAvailableDa() {
        wait.until(ExpectedConditions.elementToBeClickable(allDeliveryAgents.getFirst())).click();
    }

    public void clickAssignBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(assignBtn)).click();
    }

    public String getSuccessToasterText() {
        return getElementText(alertToaster);
    }

    public WebElement getOrderActionsBtnUiElement(String orderId) {
        return wait.until(ExpectedConditions.visibilityOfElementLocated(By.xpath(String.format(actionsBtnSelector, orderId))));
    }

    public void clickFirstOrderActionsBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(editOrderBtn.getFirst())).click();
    }

    public void clickShowFiltersBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(showFpBtn))
                .click();
    }

    public void clickPickerRadioBtn() {
        wait.until(ExpectedConditions.elementToBeClickable(pickerRadioBtn))
                .click();
    }

}
