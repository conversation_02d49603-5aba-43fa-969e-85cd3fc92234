package modals.mainAdminPortal;

import modals.BaseWebPage;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.util.List;

public class PopUpsAdminPage extends BaseWebPage {
    public PopUpsAdminPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    String pagePath = "dashboard/popups";

    @FindBy(xpath = "//h3[@class='_title_ewico_7' and text()='Popups']")
    WebElement pageTitle;

    @FindBy(xpath = "//button[@class='ant-btn ant-btn-primary']/span[text()='ADD NEW POPUP']")
    WebElement addNewPopupBtn;

    @FindBy(xpath = "//tr[@class='ant-table-row ant-table-row-level-0']")
    List<WebElement> popupsList;

    String editBtnXpath = "./td[last()]/div/div/button/span[@aria-label='edit']";

    String deleteBtnXpath = "./td[last()]/div/div[last()]/button/span[@aria-label='delete']";

    public void goToPage(){
        visitASubPage(pagePath);
    }

    public boolean isPageDisplayed(){
        return isElementDisplayed(pageTitle) && isElementDisplayed(popupsList.get(0));
    }

    public boolean isAddNewPopupBtnDisplayed(){
        return isElementDisplayed(addNewPopupBtn);
    }

    public boolean isEditBtnDisplayed(){
        if (isPageDisplayed()){
            try{
                wait.until(ExpectedConditions.visibilityOf(popupsList.get(0).findElement(By.xpath(editBtnXpath))));
                return true;
            } catch (Exception e){
                return false;
            }
        } else {
            return false;
        }
    }

    public boolean isDeleteBtnDisplayed(){
        if (isPageDisplayed()){
            try{
                wait.until(ExpectedConditions.visibilityOf(popupsList.get(0).findElement(By.xpath(deleteBtnXpath))));
                return true;
            } catch (Exception e){
                return false;
            }
        } else {
            return false;
        }
    }
}
