package modals.mainAdminPortal;

import modals.BaseWebPage;
import models.TestData;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.ExpectedConditions;

public class GoogleLoginPage extends BaseWebPage {
    public GoogleLoginPage(WebDriver webDriver) {
        super(webDriver);
        PageFactory.initElements(webDriver, this);
    }

    @FindBy(id = "identifierId")
    WebElement emailField;

    @FindBy(xpath = "//input[@name=\"Passwd\"]")
    WebElement passwordField;

    @FindBy(id = "identifierNext")
    WebElement emailNextBtn;

    @FindBy(id = "passwordNext")
    WebElement passwordNextBtn;

    public void enterEmail(String email){
        wait.until(ExpectedConditions.elementToBeClickable(emailField))
                .sendKeys(email);
        wait.until(ExpectedConditions.attributeToBe(emailField, "data-initial-value", email));
    }

    public void pressNextFromEmailField(){
        wait.until(ExpectedConditions.elementToBeClickable(emailNextBtn))
                .click();
    }

    public void enterPassword(String password){
        wait.until(ExpectedConditions.elementToBeClickable(passwordField))
                .sendKeys(password);
        wait.until(ExpectedConditions.attributeToBe(passwordField, "data-initial-value", password));
    }

    public void pressNextFromPasswordField(){
        wait.until(ExpectedConditions.elementToBeClickable(passwordNextBtn))
                .click();
    }

    public void loginWithGmail(TestData testData){
        enterEmail(testData.getAdminUser().getEmailAddress());
        pressNextFromEmailField();
        enterPassword(testData.getAdminUser().getEmailPassword());
        pressNextFromPasswordField();
    }
}
