<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="All Test Suite" parallel="methods" skipfailedinvocationcounts="true" thread-count="3" data-provider-thread-count="10">
    <listeners>
        <listener class-name="io.qameta.allure.testng.AllureTestNg"/>
        <!--        <listener class-name="helpers.factories.AllureLogListener"/>-->
    </listeners>
    <test name="Fleet App Tests">
        <classes>
            <class name="fleetApp.api.LoginFlowTests">
                <methods>
                    <exclude name="fleetApp.api.LoginFlowTests.checkValidPasswordLoginSuccessfully"/>
                    <exclude name="fleetApp.api.LoginFlowTests.checkWrongPassword"/>
                </methods>
            </class>
            <class name="fleetApp.api.SideMenuTests">
                <methods>
                    <exclude name="fleetApp.api.SideMenuTests.CheckDaFullName"/>
                    <exclude name="fleetApp.api.SideMenuTests.CheckDaHRID"/>
                    <exclude name="fleetApp.api.SideMenuTests.CheckDaRatingScore"/>
                </methods>
            </class>
            <class name="fleetApp.api.PickupTasksTests"/>
            <class name="fleetApp.api.DeliveryTasksTests"/>
            <class name="fleetApp.api.CashCollectTests"/>
            <class name="fleetApp.api.EndTripTests"/>
        </classes>
    </test>

</suite>