userName=
accessKey=
local=false
debug=true
networkLogs=false
selfHeal=true
appiumVersionForAndroidTests=2.18.0
appiumVersionForIosTests=2.15.0
#Android Device options
bStackAndroidCustomerAppNativeApp=
bStackAndroidCustomerAppNativeAppBuildNumber=
bStackAndroidCustomerAppReactNativeApp=
bStackAndroidCustomerAppReactNativeAppBuildNumber=
bStackAndroidMidMileApp=
bStackAndroidMidMileAppBuildNumber=
bStackAndroidFleetApp=
bStackAndroidFleetAppBuildNumber=
androidPlatformVersion=
androidDeviceName=
androidPlatformVersionDevice2=
androidDeviceName2=
androidPlatformVersionDevice3=
androidDeviceName3=
androidPlatformVersionDevice4=
androidDeviceName4=
#Ios Device options
bStackIosCustomerAppNativeApp=
bStackIosCustomerAppNativeAppBuildNumber=
bStackIosCustomerAppReactNativeApp=
bStackIosCustomerAppReactNativeAppBuildNumber=
bStackIosMidMileApp=
bStackIosMidMileAppBuildNumber=
bStackIosFleetApp=
bStackIosFleetAppBuildNumber=
iosPlatformVersion=
iosDeviceName=
iosPlatformVersionDevice2=
iosDeviceName2=
iosPlatformVersionDevice3=
iosDeviceName3=
iosPlatformVersionDevice4=
iosDeviceName4=