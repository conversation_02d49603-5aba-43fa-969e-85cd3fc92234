#!/bin/bash

while read -r local_ref local_sha remote_ref remote_sha; do
    # Extract the branch name from the local_ref
    branch_name="${local_ref#refs/heads/}"
    echo "Current branch name is: $branch_name"
    # Skip validation if the branch is "main"
    if [ "$branch_name" = "main" ]; then
        continue
    fi

    # Get the current year
    current_year=$(date +'%Y')

    # Define the regex pattern for the branch naming convention
    branch_pattern="^$current_year/sprintQ[0-9]+\.[0-9]+(/[-a-z0-9B]+)*$"

    # Check if the branch name matches the pattern
    if [[ ! $branch_name =~ $branch_pattern ]]; then
        echo "Error: Branch name '$branch_name' does not match the required naming convention."
        echo "Branch names should start with '$current_year/sprintQ<number>.<number>' and contain only lowercase letters, numbers, Capital letter 'B', Capital letters 'PHPO' and hyphens."
        exit 1
    fi
done

exit 0