testEmail=<EMAIL>
testPassword=@Admin123
testCountryCode=EG
#+20, +966
testMobileNumber=+20
#For EG: Etisalat, Vodafone, Orange
#For KSA: Zain, STC, Mobily, Lebara, Virgin
testMobileCompany=Etisalat
testCreditCard=4005 5500 0000 0001
testExpDate=09 / 30
testCVC=123
secondaryTestCreditCard=5123 4567 8901 2346
secondaryTestExpDate=09 / 30
secondaryTestCVC=123
declinedCreditCard=4916 7833 9176 0242
#local,remote, localMobile, remoteMobile, localHeadless
webRunMode=localHeadless
#local, remote, localHeadless
mobileRunMode=remote
gridURL=http://localhost:4444/wd/hub/
internalAPIToken=xyz
baseURL=
controlRoomBaseURL=
billingServicesBaseURL=
midMileBaseURL=
transitBaseURL=
cardServicesBaseURL=
cardServicesAdminPanelBaseURL=
pickerServicesBaseURL=
logisticsBaseURL=
fleetAppBaseURL=
fleetServiceBaseURL=
paymentServiceBaseURL=
internalOrdersBaseURL=
inaiBaseURL=
stockTakeBaseURL=
freshChatApiBaseURL=
orderServiceBaseURL=
browserstackBaseURL=
foodAggregatorServiceBaseURL=
catalogBaseURL=
yeloBaseURL=
yeloMarketplaceUserId=
ngrokAuthToken=
#Slack API info
slackApiBaseURL=https://slack.com/api
slackApiToken=
#Determines if code should build the web/mobile drivers or not
webBuildEnabled=false
mobileBuildEnabled=false
#Determines if code should create a local server and a ngrok tunnel or not
webhookBuildEnabled=false
#Determines if code should build the mobile drivers or not
androidBuildEnabled=false
iosBuildEnabled=false
##Device names should match the names of the device config files
androidDeviceName=Pixel_2
iosDeviceName=iPhone 14
#Admin Credentials
##sample phoneNumber: 1111222911
adminLocalPhoneNumber=
adminPhoneCountryCode=+20
adminPhoneCountry=EG
adminGmailAddress=
adminGmailPassword=
adminBypassScriptPassword=
adminReferralCode=
#Roles Data Sets Reader flag (If enabled, CSV files will be read)
rolesTestsEnabled=false
#Emulator or simulator location
testLatitude=29.9658208110915
testLongitude=31.603054486513585
#FP Fixed location and Date For presetting test orders
testFpName=
testFpDate=
#OrderID in DB/Order Number/Customer's Full Name
testOrderInfo=
#SupplyChain Static Auth Token
supplyChainStaticAuthToken=
#Cookies hash names
wpSecCookieName=wordpress_sec_84b0126a58d0bd66a9525bbd35ce5ae4
wpLoggedInCookieName=wordpress_logged_in_84b0126a58d0bd66a9525bbd35ce5ae4
wpNodeAuthorizationCookieName=wp_node_secure_key
#Test data handling
buildMobileCategoriesAndProductsTestData=false
buildMobileShopsTestData=false
buildWebControlRoomWarehousesAndOrders=false
registerUserUsingApi=false
buildRestaurantsTestData=false
#Building Mobile TestData requires the user to be authorized otherwise, it will be ignored
buildMobileBillingTestData=false
#CardService feature flag, if true, we will read the config file of the cardServices
cardServicesTestsEnabled=false
#Enable video recordings of the test execution for iOS and android
testExecutionVideoRecordingEnabled=false
#pickerTestData, phone format should be without the country code
#ex: 1111222911
pickerPhoneNumber=
pickerPhoneCountryCode=+20
pickerPassword=
#fp_manager TestData, phone format should be without the country code
#ex: 1111222911
fpManagerPhoneNumber=
fpManagerPhoneCountryCode=+20
fpManagerPassword=
#midMileTestData, phone format should be without the country code
#ex: 1111222911
midMilePhoneNumber=
midMilePhoneCountryCode=+20
midMilePassword=
#dATestData, phone format should be without the country code
#ex: 1111222911
dAPhoneNumber=
dAPhoneCountryCode=+20
dAPassword=
#chefTestData, phone format should be without the country code
chefPhoneNumber=
chefCountryCode=+20
chefPassword=
#targetApp which can be any of the following (customerAppReactNative, customerAppNative or midMileApp)
targetApp=
#Credentials for payment service - Secret , keys for different order types & inai token
paymentServiceSecret=
paymentShoppingKey=
paymentTopUpKey=
paymentBillingKey=
paymentGratuityKey=
inaiToken=
#DB connection initiation flag
connectToDB=false
mysqlHost=
mysqlUserName=
mysqlUserPassword=
mysqlDatabaseName=
mysqlServerPort=
sshConnectionRequired=
sshHost=
sshUserName=
sshPort=0
isSshKeyProtected=
sshPassphrase=
sshKeyPath=
#stockTakerTestData, phone format should be without the country code
#ex: 1111222911
stockTakerPhoneNumber=
stockTakerPhoneCountryCode=+20
stockTakerPassword=
#Credentials for payment panel
paymentPanelEmail=
paymentPanelPassword=
#Flag to control the foreign Country Data building
setupForeignCountryData=false
applePayInaiCallBackToken=
#Fresh Chat API Info
freshChatApiKey=
freshChatChannelId=
#Flag to skip initializing the Admin Authorization step from setup process
skipAdminAuthorizationStep=false
#A flag to control which Api version to use to create order (old vs V2)
useOrderApiV2=false
#remoteProviderName, it can be either 'browserstack' or 'lambdaTest'
remotePlatformName=browserstack
#FallBack product IDs, their stock will be updated with each test run to be the below value, Products count must be > 1
fallBackProductIds=
targetQtyForEachFallBackProduct=
#Categories that will get excluded from dynamic data parsing logic
excludedCategoryIds=
#MidMileOrders Date
testMidMileOrdersDate=
#Categories that are part of the shared facility categories
sharedFacilityCategoryIds=