#!/bin/bash

# Ensure the correct number of arguments are provided
if [ "$#" -ne 1 ]; then
    echo "Usage: $0 <threads_count>"
    exit 1
fi

THREADS_COUNT=$1
CSV_DIR="resources/dataSets"
CSV_FILE="${CSV_DIR}/threads_counter.csv"

# Create the directory if it does not exist
mkdir -p $CSV_DIR

# Generate the CSV file
echo "threads_count" > $CSV_FILE
for ((i=1; i<=THREADS_COUNT; i++))
do
    echo $i >> $CSV_FILE
done

echo "CSV file generated at $CSV_FILE"
