"autocomplete","Reqs Sent","autocomplete | 220reqs/s | 0reqs/s | 472reqs/s | 880reqs/s | 0reqs/s | "
"autocomplete","Total vs Failed","url:https://search-integration.breadfast.tech/v3/autocomplete_query_eggs_fp_5e2c373d6b874d02cdc83203_serve_now | sum:k6.http_req_failed{testcase:autocomplete}.as_count() | 0 | 0 | 0 | 0 | 0 | status:200
url:https://search-integration.breadfast.tech/v3/autocomplete_query_eggs_fp_5e2c373d6b874d02cdc83203_serve_now | sum:k6.http_reqs{testcase:autocomplete}.as_count() | 66.0kreqs | 0reqs | 142kreqs | 264kreqs | 0reqs | "
"autocomplete","Virtual Users","autocomplete | 121users/s | 37.7users/s | 186users/s | 364users/s | 140users/s | "
"autocomplete","Req Duration P95","autocomplete | 351ms | 167ms | 458ms | 1.05s | 428ms | "
"autocomplete","Resp Status","status:200
testcase:autocomplete | 220reqs/s | 0reqs/s | 472reqs/s | 880reqs/s | 0reqs/s | "
"autocomplete","Time Waiting for Response","testcase:autocomplete
url:https://search-integration.breadfast.tech/v3/autocomplete_query_eggs_fp_5e2c373d6b874d02cdc83203_serve_now | max | 2,033ms | 367ms | 5,040ms | 6,098ms | 691ms | testcase:autocomplete
url:https://search-integration.breadfast.tech/v3/autocomplete_query_eggs_fp_5e2c373d6b874d02cdc83203_serve_now | median | 268ms | 138ms | 340ms | 805ms | 326ms | testcase:autocomplete
url:https://search-integration.breadfast.tech/v3/autocomplete_query_eggs_fp_5e2c373d6b874d02cdc83203_serve_now | 95P | 351ms | 167ms | 458ms | 1,052ms | 428ms | "
"autocomplete","Req Blocked","autocomplete | 293units/s | 195units/s | 472units/s | 879units/s | 213units/s | "
"autocomplete","Req Failed","https://search-integration.breadfast.tech/v3/autocomplete_query_eggs_fp_5e2c373d6b874d02cdc83203_serve_now | 0 | 0 | 0 | 0 | 0 | "
"autocomplete","Latency","https://search-integration.breadfast.tech/v3/autocomplete_query_eggs_fp_5e2c373d6b874d02cdc83203_serve_now | 286ms | 142ms | 388ms | 857ms | 326ms | "
"autocomplete","Response Status from Response Body","No matching results found | "
"dealssmartlist","Reqs Sent","dealssmartlist | 37.7reqs/s | 0reqs/s | 66.8reqs/s | 189reqs/s | 0reqs/s | "
"dealssmartlist","Total vs Failed","url:https://integration.breadfast.tech/smartlist/63ff45f2dad1af000d703b00/themes_warehouseid_658d63b7fc8ee2000d9bca3f_serve_now | sum:k6.http_req_failed{testcase:dealssmartlist}.as_count() | 0 | 0 | 0 | 0 | 0 | status:200
url:https://integration.breadfast.tech/smartlist/63ff45f2dad1af000d703b00/themes_warehouseid_658d63b7fc8ee2000d9bca3f_serve_now | sum:k6.http_reqs{testcase:dealssmartlist}.as_count() | 11.3kreqs | 0reqs | 20.1kreqs | 56.6kreqs | 0reqs | "
"dealssmartlist","Virtual Users","dealssmartlist | 115users/s | 7.56users/s | 219users/s | 459users/s | 147users/s | "
"dealssmartlist","Req Duration P95","dealssmartlist | 5.36s | 269ms | 9.35s | 21.5s | 9.35s | "
"dealssmartlist","Resp Status","status:200
testcase:dealssmartlist | 37.7reqs/s | 0reqs/s | 66.8reqs/s | 189reqs/s | 0reqs/s | "
"dealssmartlist","Time Waiting for Response","testcase:dealssmartlist
url:https://integration.breadfast.tech/smartlist/63ff45f2dad1af000d703b00/themes_warehouseid_658d63b7fc8ee2000d9bca3f_serve_now | max | 8.08s | 457ms | 12.21s | 32.3s | 12.21s | testcase:dealssmartlist
url:https://integration.breadfast.tech/smartlist/63ff45f2dad1af000d703b00/themes_warehouseid_658d63b7fc8ee2000d9bca3f_serve_now | median | 1.86s | 113ms | 3.77s | 7.4s | 3.77s | testcase:dealssmartlist
url:https://integration.breadfast.tech/smartlist/63ff45f2dad1af000d703b00/themes_warehouseid_658d63b7fc8ee2000d9bca3f_serve_now | 95P | 5.36s | 269ms | 9.35s | 21.4s | 9.35s | "
"dealssmartlist","Req Blocked","dealssmartlist | 47.1units/s | 15.3units/s | 66.8units/s | 188units/s | 15.3units/s | "
"dealssmartlist","Req Failed","https://integration.breadfast.tech/smartlist/63ff45f2dad1af000d703b00/themes_warehouseid_658d63b7fc8ee2000d9bca3f_serve_now | 0 | 0 | 0 | 0 | 0 | "
"dealssmartlist","Latency","https://integration.breadfast.tech/smartlist/63ff45f2dad1af000d703b00/themes_warehouseid_658d63b7fc8ee2000d9bca3f_serve_now | 2.19s | 121ms | 4.19s | 8.78s | 4.19s | "
"dealssmartlist","Response Status from Response Body","No matching results found | "
"dealssmartlistwarehousecategories","Reqs Sent","dealssmartlistwarehousecategories | 74.5reqs/s | 0reqs/s | 134reqs/s | 447reqs/s | 0reqs/s | "
"dealssmartlistwarehousecategories","Total vs Failed","url:https://integration.breadfast.tech/warehouses-service/warehouses/categories/2582_serve_now_shiftid_5fc84b891758b02853efc357_warehouseid_658d63b7fc8ee2000d9bca3f_type_deals | sum:k6.http_req_failed{testcase:dealssmartlistwarehousecategories}.as_count() | 0 | 0 | 0 | 0 | 0 | status:200
+1
url:https://integration.breadfast.tech/warehouses-service/warehouses/categories/2582_serve_now_shiftid_5fc84b891758b02853efc357_warehouseid_658d63b7fc8ee2000d9bca3f_type_deals | sum:k6.http_reqs{testcase:dealssmartlistwarehousecategories}.as_count() | 22.4kreqs | 0reqs | 40.1kreqs | 134kreqs | 0reqs | "
"dealssmartlistwarehousecategories","Virtual Users","dealssmartlistwarehousecategories | 197users/s | 23.6users/s | 424users/s | 986users/s | 158users/s | "
"dealssmartlistwarehousecategories","Req Duration P95","dealssmartlistwarehousecategories | 4.92s | 269ms | 8.76s | 24.6s | 3.82s | "
"dealssmartlistwarehousecategories","Resp Status","status:200
testcase:dealssmartlistwarehousecategories | 74.5reqs/s | 0reqs/s | 134reqs/s | 447reqs/s | 0reqs/s | "
"dealssmartlistwarehousecategories","Time Waiting for Response","testcase:dealssmartlistwarehousecategories
+1
url:https://integration.breadfast.tech/warehouses-service/warehouses/categories/2582_serve_now_shiftid_5fc84b891758b02853efc357_warehouseid_658d63b7fc8ee2000d9bca3f_type_deals | max | 9.36s | 511ms | 13.88s | 46.8s | 10.45s | testcase:dealssmartlistwarehousecategories
+1
url:https://integration.breadfast.tech/warehouses-service/warehouses/categories/2582_serve_now_shiftid_5fc84b891758b02853efc357_warehouseid_658d63b7fc8ee2000d9bca3f_type_deals | median | 1.41s | 127ms | 2.81s | 7.1s | 0.73s | testcase:dealssmartlistwarehousecategories
+1
url:https://integration.breadfast.tech/warehouses-service/warehouses/categories/2582_serve_now_shiftid_5fc84b891758b02853efc357_warehouseid_658d63b7fc8ee2000d9bca3f_type_deals | 95P | 4.92s | 268ms | 8.76s | 24.6s | 3.81s | "
"dealssmartlistwarehousecategories","Req Blocked","dealssmartlistwarehousecategories | 89.4units/s | 49.9units/s | 134units/s | 447units/s | 49.9units/s | "
"dealssmartlistwarehousecategories","Req Failed","https://integration.breadfast.tech/warehouses-service/warehouses/categories/2582_serve_now_shiftid_5fc84b891758b02853efc357_warehouseid_658d63b7fc8ee2000d9bca3f_type_deals | 0 | 0 | 0 | 0 | 0 | "
"dealssmartlistwarehousecategories","Latency","https://integration.breadfast.tech/warehouses-service/warehouses/categories/2582_serve_now_shiftid_5fc84b891758b02853efc357_warehouseid_658d63b7fc8ee2000d9bca3f_type_deals | 1.84s | 133ms | 3.30s | 9.19s | 1.21s | "
"dealssmartlistwarehousecategories","Response Status from Response Body","No matching results found | "
"defaultaddress","Reqs Sent","defaultaddress | 7.43reqs/s | 0reqs/s | 12.2reqs/s | 29.7reqs/s | 0reqs/s | "
"defaultaddress","Total vs Failed","url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/set-default-address | sum:k6.http_req_failed{testcase:defaultaddress}.as_count() | 215 | 0 | 483 | 859 | 0 | status:0
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/set-default-address | sum:k6.http_reqs{testcase:defaultaddress}.as_count() | 11reqs | 0reqs | 22reqs | 22reqs | 0reqs | status:200
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/set-default-address | sum:k6.http_reqs{testcase:defaultaddress}.as_count() | 2,015reqs | 0reqs | 3,167reqs | 8,058reqs | 0reqs | status:502
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/set-default-address | sum:k6.http_reqs{testcase:defaultaddress}.as_count() | 279reqs | 0reqs | 461reqs | 837reqs | 0reqs | "
"defaultaddress","Virtual Users","defaultaddress | 133users/s | 36.7users/s | 213users/s | 400users/s | 213users/s | "
"defaultaddress","Req Duration P95","defaultaddress | 25.0s | 4.70s | 41.2s | 1.25min | 41.2s | "
"defaultaddress","Resp Status","status:0
testcase:defaultaddress | 0.037reqs/s | 0reqs/s | 0.073reqs/s | 0.073reqs/s | 0reqs/s | status:200
testcase:defaultaddress | 6.72reqs/s | 0reqs/s | 10.56reqs/s | 26.86reqs/s | 0reqs/s | status:502
testcase:defaultaddress | 0.93reqs/s | 0reqs/s | 1.54reqs/s | 2.79reqs/s | 0reqs/s | "
"defaultaddress","Time Waiting for Response","testcase:defaultaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/set-default-address | max | 65.4s | 5.01s | 120.0s | 3.27min | 120.0s | testcase:defaultaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/set-default-address | median | 10.1s | 2.99s | 16.5s | 30.3s | 16.5s | testcase:defaultaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/set-default-address | 95P | 25.0s | 4.70s | 41.2s | 75.1s | 41.2s | "
"defaultaddress","Req Blocked","defaultaddress | 9.91units/s | 7.02units/s | 12.2units/s | 29.7units/s | 12.17units/s | "
"defaultaddress","Req Failed","https://integration.breadfast.tech/wp-json/breadfast/v3/user/set-default-address | 215 | 0 | 483 | 859 | 0 | "
"defaultaddress","Latency","https://integration.breadfast.tech/wp-json/breadfast/v3/user/set-default-address | 12.1s | 3.02s | 19.8s | 36.3s | 19.8s | "
"defaultaddress","Response Status from Response Body","No matching results found | "
"deleteaddress","Reqs Sent","deleteaddress | 9.49reqs/s | 0reqs/s | 21.1reqs/s | 47.5reqs/s | 0reqs/s | "
"deleteaddress","Total vs Failed","url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/delete-address | sum:k6.http_req_failed{testcase:deleteaddress}.as_count() | 39 | 0 | 195 | 195 | 0 | status:200
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/delete-address | sum:k6.http_reqs{testcase:deleteaddress}.as_count() | 2,808reqs | 0reqs | 6.13kreqs | 14.04kreqs | 0reqs | status:502
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/delete-address | sum:k6.http_reqs{testcase:deleteaddress}.as_count() | 98reqs | 0reqs | 0.20kreqs | 0.20kreqs | — | "
"deleteaddress","Virtual Users","deleteaddress | 104users/s | 11.9users/s | 238users/s | 418users/s | 63users/s | "
"deleteaddress","Req Duration P95","deleteaddress | 11.7s | 1.37s | 23.3s | 46.6s | 12.9s | "
"deleteaddress","Resp Status","status:200
testcase:deleteaddress | 9.36reqs/s | 0reqs/s | 20.42reqs/s | 46.8reqs/s | 0reqs/s | status:502
testcase:deleteaddress | 0.32reqs/s | 0reqs/s | 0.65reqs/s | 0.65reqs/s | — | "
"deleteaddress","Time Waiting for Response","testcase:deleteaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/delete-address | max | 18.81s | 1.67s | 31.8s | 75.3s | 31.79s | testcase:deleteaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/delete-address | median | 5.38s | 0.79s | 9.6s | 21.5s | 6.19s | testcase:deleteaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/delete-address | 95P | 11.66s | 1.37s | 23.3s | 46.6s | 12.88s | "
"deleteaddress","Req Blocked","deleteaddress | 11.9units/s | 2.86units/s | 21.1units/s | 47.5units/s | 3.6units/s | "
"deleteaddress","Req Failed","https://integration.breadfast.tech/wp-json/breadfast/v3/user/delete-address | 39 | 0 | 195 | 195 | 0 | "
"deleteaddress","Latency","https://integration.breadfast.tech/wp-json/breadfast/v3/user/delete-address | 5.89s | 838ms | 11.1s | 23.6s | 6.62s | "
"deleteaddress","Response Status from Response Body","No matching results found | "
"getordershistory","Reqs Sent","getordershistory | 1.47reqs/s | 0reqs/s | 3.66reqs/s | 5.89reqs/s | 0reqs/s | "
"getordershistory","Total vs Failed","url:https://integration.breadfast.tech/wp-json/breadfast/v3/orders/get | sum:k6.http_req_failed{testcase:getordershistory}.as_count() | 32.3 | 0 | 125 | 129 | 0 | status:0
url:https://integration.breadfast.tech/wp-json/breadfast/v3/orders/get | sum:k6.http_reqs{testcase:getordershistory}.as_count() | 14.7reqs | 0reqs | 40reqs | 44reqs | 0reqs | status:200
url:https://integration.breadfast.tech/wp-json/breadfast/v3/orders/get | sum:k6.http_reqs{testcase:getordershistory}.as_count() | 409.5reqs | 0reqs | 1,094reqs | 1,638reqs | 0reqs | status:502
url:https://integration.breadfast.tech/wp-json/breadfast/v3/orders/get | sum:k6.http_reqs{testcase:getordershistory}.as_count() | 42.5reqs | 0reqs | 85reqs | 85reqs | 0reqs | "
"getordershistory","Virtual Users","getordershistory | 67.4users/s | 26.6users/s | 89.3users/s | 202users/s | 86.3users/s | "
"getordershistory","Req Duration P95","getordershistory | 47.0s | 11.5s | 1.40min | 2.35min | 84.1s | "
"getordershistory","Resp Status","status:0
testcase:getordershistory | 0.049reqs/s | 0reqs/s | 0.13reqs/s | 0.15reqs/s | 0reqs/s | status:200
testcase:getordershistory | 1.37reqs/s | 0reqs/s | 3.65reqs/s | 5.46reqs/s | 0reqs/s | status:502
testcase:getordershistory | 0.14reqs/s | 0reqs/s | 0.28reqs/s | 0.28reqs/s | 0reqs/s | "
"getordershistory","Time Waiting for Response","testcase:getordershistory
url:https://integration.breadfast.tech/wp-json/breadfast/v3/orders/get | max | 83.9s | 11.77s | 2.00min | 4.20min | 120.0s | testcase:getordershistory
url:https://integration.breadfast.tech/wp-json/breadfast/v3/orders/get | median | 31.6s | 7.58s | 1.05min | 1.58min | 62.8s | testcase:getordershistory
url:https://integration.breadfast.tech/wp-json/breadfast/v3/orders/get | 95P | 47.0s | 11.53s | 1.40min | 2.35min | 84.1s | "
"getordershistory","Req Blocked","getordershistory | 1.96units/s | 0.97units/s | 3.66units/s | 5.89units/s | 1.26units/s | "
"getordershistory","Req Failed","https://integration.breadfast.tech/wp-json/breadfast/v3/orders/get | 32.3 | 0 | 125 | 129 | 0 | "
"getordershistory","Latency","https://integration.breadfast.tech/wp-json/breadfast/v3/orders/get | 33.3s | 7.53s | 1.09min | 1.66min | 65.6s | "
"getordershistory","Response Status from Response Body","No matching results found | "
"getuserdata","Reqs Sent","getuserdata | 5.97reqs/s | 0reqs/s | 10.2reqs/s | 23.9reqs/s | 0reqs/s | "
"getuserdata","Total vs Failed","url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/get-user-data | sum:k6.http_req_failed{testcase:getuserdata}.as_count() | 9.5 | 0 | 38 | 38 | 0 | status:0
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/get-user-data | sum:k6.http_reqs{testcase:getuserdata}.as_count() | 7.5reqs | 0reqs | 15reqs | 15reqs | 0reqs | status:200
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/get-user-data | sum:k6.http_reqs{testcase:getuserdata}.as_count() | 1,782.0reqs | 0reqs | 3,066reqs | 7,128reqs | 0reqs | status:502
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/get-user-data | sum:k6.http_reqs{testcase:getuserdata}.as_count() | 11.5reqs | 0reqs | 23reqs | 23reqs | 0reqs | "
"getuserdata","Virtual Users","getuserdata | 103users/s | 0users/s | 221users/s | 411users/s | 221users/s | "
"getuserdata","Req Duration P95","getuserdata | 27.4s | 5.08s | 56.4s | 1.37min | 56.4s | "
"getuserdata","Resp Status","status:0
testcase:getuserdata | 0.025reqs/s | 0reqs/s | 0.05reqs/s | 0.05reqs/s | 0reqs/s | status:200
testcase:getuserdata | 5.94reqs/s | 0reqs/s | 10.22reqs/s | 23.76reqs/s | 0reqs/s | status:502
testcase:getuserdata | 0.038reqs/s | 0reqs/s | 0.077reqs/s | 0.077reqs/s | 0reqs/s | "
"getuserdata","Time Waiting for Response","testcase:getuserdata
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/get-user-data | max | 49.2s | 5.64s | 120.2s | 2.46min | 120.2s | testcase:getuserdata
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/get-user-data | median | 18.3s | 3.09s | 35.4s | 0.92min | 35.4s | testcase:getuserdata
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/get-user-data | 95P | 27.3s | 5.04s | 56.3s | 1.37min | 56.3s | "
"getuserdata","Req Blocked","getuserdata | 7.96units/s | 5.19units/s | 10.2units/s | 23.9units/s | 5.19units/s | "
"getuserdata","Req Failed","https://integration.breadfast.tech/wp-json/breadfast/v3/user/get-user-data | 9.5 | 0 | 38 | 38 | 0 | "
"getuserdata","Latency","https://integration.breadfast.tech/wp-json/breadfast/v3/user/get-user-data | 19.7s | 3.35s | 38.7s | 59.2s | 38.7s | "
"getuserdata","Response Status from Response Body","No matching results found | "
"homepageconfig","Reqs Sent","homepageconfig | 5.53reqs/s | 0reqs/s | 9.75reqs/s | 33.2reqs/s | 0reqs/s | "
"homepageconfig","Total vs Failed","url:https://integration.breadfast.tech/wp-json/breadfast/v3/main/config | sum:k6.http_req_failed{testcase:homepageconfig}.as_count() | 8.83 | 0 | 48 | 53 | 0 | status:0
url:https://integration.breadfast.tech/wp-json/breadfast/v3/main/config | sum:k6.http_reqs{testcase:homepageconfig}.as_count() | 2.0reqs | 0reqs | 4reqs | 6reqs | 0reqs | status:200
url:https://integration.breadfast.tech/wp-json/breadfast/v3/main/config | sum:k6.http_reqs{testcase:homepageconfig}.as_count() | 1,649.0reqs | 0reqs | 2,925reqs | 9,894reqs | 0reqs | status:500
url:https://integration.breadfast.tech/wp-json/breadfast/v3/main/config | sum:k6.http_reqs{testcase:homepageconfig}.as_count() | 1.5reqs | 0reqs | 3reqs | 3reqs | — | status:502
url:https://integration.breadfast.tech/wp-json/breadfast/v3/main/config | sum:k6.http_reqs{testcase:homepageconfig}.as_count() | 22.0reqs | 0reqs | 44reqs | 44reqs | — | "
"homepageconfig","Virtual Users","homepageconfig | 61.0users/s | 0.20users/s | 158users/s | 305users/s | 39.2users/s | "
"homepageconfig","Req Duration P95","homepageconfig | 16.3s | 717ms | 41.3s | 1.36min | 18.7s | "
"homepageconfig","Resp Status","status:0
testcase:homepageconfig | 6.7e-3reqs/s | 0reqs/s | 0.013reqs/s | 0.02reqs/s | 0reqs/s | status:200
testcase:homepageconfig | 5.50reqs/s | 0reqs/s | 9.75reqs/s | 32.98reqs/s | 0reqs/s | status:500
testcase:homepageconfig | 5e-3reqs/s | 0reqs/s | 1e-2reqs/s | 1e-2reqs/s | — | status:502
testcase:homepageconfig | 0.073reqs/s | 0reqs/s | 0.15reqs/s | 0.15reqs/s | — | "
"homepageconfig","Time Waiting for Response","testcase:homepageconfig
url:https://integration.breadfast.tech/wp-json/breadfast/v3/main/config | max | 52.6s | 716ms | 120.0s | 4.38min | 120.0s | testcase:homepageconfig
url:https://integration.breadfast.tech/wp-json/breadfast/v3/main/config | median | 7.6s | 386ms | 16.8s | 0.63min | 9.9s | testcase:homepageconfig
url:https://integration.breadfast.tech/wp-json/breadfast/v3/main/config | 95P | 16.3s | 716ms | 41.3s | 1.36min | 18.7s | "
"homepageconfig","Req Blocked","homepageconfig | 6.63units/s | 0.033units/s | 9.75units/s | 33.2units/s | 6.11units/s | "
"homepageconfig","Req Failed","https://integration.breadfast.tech/wp-json/breadfast/v3/main/config | 8.83 | 0 | 48 | 53 | 0 | "
"homepageconfig","Latency","https://integration.breadfast.tech/wp-json/breadfast/v3/main/config | 8.48s | 434ms | 19.8s | 42.4s | 10.87s | "
"homepageconfig","Response Status from Response Body","No matching results found | "
"homepagenearestaddress","Reqs Sent","homepagenearestaddress | 6.09reqs/s | 0reqs/s | 10.5reqs/s | 36.6reqs/s | 0reqs/s | "
"homepagenearestaddress","Total vs Failed","url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/nearest-address | sum:k6.http_req_failed{testcase:homepagenearestaddress}.as_count() | 66.3 | 0 | 388 | 398 | 0 | status:200
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/nearest-address | sum:k6.http_reqs{testcase:homepagenearestaddress}.as_count() | 1,762reqs | 0reqs | 3,060reqs | 10,571reqs | 0reqs | status:500
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/nearest-address | sum:k6.http_reqs{testcase:homepagenearestaddress}.as_count() | 5reqs | 0reqs | 10reqs | 10reqs | — | status:502
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/nearest-address | sum:k6.http_reqs{testcase:homepagenearestaddress}.as_count() | 194reqs | 0reqs | 388reqs | 388reqs | — | "
"homepagenearestaddress","Virtual Users","homepagenearestaddress | 60.9users/s | 0.25users/s | 158users/s | 305users/s | 39.1users/s | "
"homepagenearestaddress","Req Duration P95","homepagenearestaddress | 12.9s | 550ms | 30.5s | 1.07min | 13.3s | "
"homepagenearestaddress","Resp Status","status:200
testcase:homepagenearestaddress | 5.87reqs/s | 0reqs/s | 10.20reqs/s | 35.24reqs/s | 0reqs/s | status:500
testcase:homepagenearestaddress | 0.017reqs/s | 0reqs/s | 0.033reqs/s | 0.033reqs/s | — | status:502
testcase:homepagenearestaddress | 0.65reqs/s | 0reqs/s | 1.29reqs/s | 1.29reqs/s | — | "
"homepagenearestaddress","Time Waiting for Response","testcase:homepagenearestaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/nearest-address | max | 15.6s | 549ms | 33.1s | 78.1s | 22.7s | testcase:homepagenearestaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/nearest-address | median | 5.8s | 314ms | 12.7s | 29.0s | 6.0s | testcase:homepagenearestaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/nearest-address | 95P | 12.9s | 549ms | 30.5s | 64.3s | 13.3s | "
"homepagenearestaddress","Req Blocked","homepagenearestaddress | 7.31units/s | 0.053units/s | 10.5units/s | 36.6units/s | 6.54units/s | "
"homepagenearestaddress","Req Failed","https://integration.breadfast.tech/wp-json/breadfast/v3/user/nearest-address | 66.3 | 0 | 388 | 398 | 0 | "
"homepagenearestaddress","Latency","https://integration.breadfast.tech/wp-json/breadfast/v3/user/nearest-address | 6.42s | 336ms | 14.5s | 32.1s | 6.63s | "
"homepagenearestaddress","Response Status from Response Body","No matching results found | "
"homepagereferral","Reqs Sent","homepagereferral | 10.9reqs/s | 0reqs/s | 19.1reqs/s | 65.6reqs/s | 0reqs/s | "
"homepagereferral","Total vs Failed","url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/referral | sum:k6.http_req_failed{testcase:homepagereferral}.as_count() | 0 | 0 | 0 | 0 | 0 | status:200
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/referral | sum:k6.http_reqs{testcase:homepagereferral}.as_count() | 3.28kreqs | 0reqs | 5.74kreqs | 19.7kreqs | 0reqs | "
"homepagereferral","Virtual Users","homepagereferral | 60.5users/s | 9.51users/s | 121users/s | 302users/s | 11.3users/s | "
"homepagereferral","Req Duration P95","homepagereferral | 6.21s | 1.24s | 11.0s | 31.0s | 1.31s | "
"homepagereferral","Resp Status","status:200
testcase:homepagereferral | 10.9reqs/s | 0reqs/s | 19.1reqs/s | 65.6reqs/s | 0reqs/s | "
"homepagereferral","Time Waiting for Response","testcase:homepagereferral
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/referral | max | 6.89s | 1.42s | 12.69s | 34.5s | 1.58s | testcase:homepagereferral
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/referral | median | 3.21s | 0.76s | 6.26s | 16.0s | 0.77s | testcase:homepagereferral
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/referral | 95P | 6.21s | 1.24s | 11.01s | 31.0s | 1.30s | "
"homepagereferral","Req Blocked","homepagereferral | 13.1units/s | 1.92units/s | 19.1units/s | 65.6units/s | 1.9units/s | "
"homepagereferral","Req Failed","https://integration.breadfast.tech/wp-json/breadfast/v3/user/referral | 0 | 0 | 0 | 0 | 0 | "
"homepagereferral","Latency","https://integration.breadfast.tech/wp-json/breadfast/v3/user/referral | 3.37s | 815ms | 6.21s | 16.8s | 0.84s | "
"homepagereferral","Response Status from Response Body","No matching results found | "
"listcoupons","Reqs Sent","listcoupons | 0.69reqs/s | 0reqs/s | 1.97reqs/s | 3.45reqs/s | 0reqs/s | "
"listcoupons","Total vs Failed","url:https://integration.breadfast.tech/wp-json/coupons-api/v1/coupons_sort_col_id_sort_type_desc_limit_10_coupon_types_multi_codes_offset_20_0 | sum:k6.http_req_failed{testcase:listcoupons}.as_count() | 106 | 0 | 499 | 528 | 0 | status:0
url:https://integration.breadfast.tech/wp-json/coupons-api/v1/coupons_sort_col_id_sort_type_desc_limit_10_coupon_types_multi_codes_offset_20_0 | sum:k6.http_reqs{testcase:listcoupons}.as_count() | 121.5reqs | 0reqs | 457reqs | 486reqs | 0reqs | status:200
url:https://integration.breadfast.tech/wp-json/coupons-api/v1/coupons_sort_col_id_sort_type_desc_limit_10_coupon_types_multi_codes_offset_20_0 | sum:k6.http_reqs{testcase:listcoupons}.as_count() | 101.6reqs | 0reqs | 290reqs | 508reqs | 0reqs | status:502
url:https://integration.breadfast.tech/wp-json/coupons-api/v1/coupons_sort_col_id_sort_type_desc_limit_10_coupon_types_multi_codes_offset_20_0 | sum:k6.http_reqs{testcase:listcoupons}.as_count() | 21.0reqs | 0reqs | 42reqs | 42reqs | — | "
"listcoupons","Virtual Users","listcoupons | 104users/s | 25.3users/s | 233users/s | 416users/s | 25users/s | "
"listcoupons","Req Duration P95","listcoupons | 1.42min | 20.4s | 1.98min | 5.68min | 1.98min | "
"listcoupons","Resp Status","status:0
testcase:listcoupons | 0.40reqs/s | 0reqs/s | 1.52reqs/s | 1.62reqs/s | 0reqs/s | status:200
testcase:listcoupons | 0.34reqs/s | 0reqs/s | 0.97reqs/s | 1.69reqs/s | 0reqs/s | status:502
testcase:listcoupons | 0.07reqs/s | 0reqs/s | 0.14reqs/s | 0.14reqs/s | — | "
"listcoupons","Time Waiting for Response","testcase:listcoupons
+1
url:https://integration.breadfast.tech/wp-json/coupons-api/v1/coupons_sort_col_id_sort_type_desc_limit_10_coupon_types_multi_codes_offset_20_0 | max | 1.59min | 20.9s | 2.00min | 6.35min | 2.00min | testcase:listcoupons
+1
url:https://integration.breadfast.tech/wp-json/coupons-api/v1/coupons_sort_col_id_sort_type_desc_limit_10_coupon_types_multi_codes_offset_20_0 | median | 1.39min | 19.1s | 1.96min | 5.57min | 1.96min | testcase:listcoupons
+1
url:https://integration.breadfast.tech/wp-json/coupons-api/v1/coupons_sort_col_id_sort_type_desc_limit_10_coupon_types_multi_codes_offset_20_0 | 95P | 1.42min | 20.4s | 1.98min | 5.68min | 1.98min | "
"listcoupons","Req Blocked","listcoupons | 0.86units/s | 0.037units/s | 1.97units/s | 3.45units/s | 0.037units/s | "
"listcoupons","Req Failed","https://integration.breadfast.tech/wp-json/coupons-api/v1/coupons_sort_col_id_sort_type_desc_limit_10_coupon_types_multi_codes_offset_20_0 | 106 | 0 | 499 | 528 | 0 | "
"listcoupons","Latency","https://integration.breadfast.tech/wp-json/coupons-api/v1/coupons_sort_col_id_sort_type_desc_limit_10_coupon_types_multi_codes_offset_20_0 | 1.40min | 19.6s | 1.96min | 5.60min | 1.96min | "
"listcoupons","Response Status from Response Body","No matching results found | "
"transit","Reqs Sent","transit | 1.62kreqs/s | 0reqs/s | 2.59kreqs/s | 6.46kreqs/s | 0reqs/s | "
"transit","Total vs Failed","url:https://integration.breadfast.tech/supply-chain/internal-order/transfers/search | sum:k6.http_req_failed{testcase:transit}.as_count() | 194 | 0 | 417 | 775 | 0 | status:0
url:https://integration.breadfast.tech/supply-chain/internal-order/transfers/search | sum:k6.http_reqs{testcase:transit}.as_count() | 71reqs | 0reqs | 142reqs | 142reqs | — | status:200
url:https://integration.breadfast.tech/supply-chain/internal-order/transfers/search | sum:k6.http_reqs{testcase:transit}.as_count() | 484,492reqs | 0reqs | 775,237reqs | 1.94Mreqs | 0reqs | status:502
url:https://integration.breadfast.tech/supply-chain/internal-order/transfers/search | sum:k6.http_reqs{testcase:transit}.as_count() | 158reqs | 0reqs | 304reqs | 632reqs | 0reqs | "
"transit","Virtual Users","transit | 139users/s | 46.6users/s | 202users/s | 418users/s | 202users/s | "
"transit","Req Duration P95","transit | 2.16s | 63.3ms | 3.51s | 6.48s | 3.51s | "
"transit","Resp Status","status:0
testcase:transit | 0.24reqs/s | 0reqs/s | 0.47reqs/s | 0.47reqs/s | — | status:200
testcase:transit | 1,614.97reqs/s | 0reqs/s | 2,584.12reqs/s | 6,459.89reqs/s | 0reqs/s | status:502
testcase:transit | 0.53reqs/s | 0reqs/s | 1.01reqs/s | 2.11reqs/s | 0reqs/s | "
"transit","Time Waiting for Response","testcase:transit
url:https://integration.breadfast.tech/supply-chain/internal-order/transfers/search | max | 80.11s | 324.9ms | 120.01s | 4.01min | 120.01s | testcase:transit
url:https://integration.breadfast.tech/supply-chain/internal-order/transfers/search | median | 2.08s | 21.0ms | 3.40s | 6.24s | 3.40s | testcase:transit
url:https://integration.breadfast.tech/supply-chain/internal-order/transfers/search | 95P | 2.16s | 62.4ms | 3.51s | 6.47s | 3.51s | "
"transit","Req Blocked","transit | 2.15kunits/s | 1.57kunits/s | 2.59kunits/s | 6.46kunits/s | 2.31kunits/s | "
"transit","Req Failed","https://integration.breadfast.tech/supply-chain/internal-order/transfers/search | 194 | 0 | 417 | 775 | 0 | "
"transit","Latency","https://integration.breadfast.tech/supply-chain/internal-order/transfers/search | 2.09s | 28.5ms | 3.42s | 6.28s | 3.42s | "
"transit","Response Status from Response Body","No matching results found | "
"updateaddress","Reqs Sent","updateaddress | 0.34reqs/s | 0reqs/s | 0.94reqs/s | 1.35reqs/s | 0reqs/s | "
"updateaddress","Total vs Failed","url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/update-address_xdebug_session_start_phpstorm | sum:k6.http_req_failed{testcase:updateaddress}.as_count() | 97 | 0 | 277 | 388 | 0 | status:0
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/update-address_xdebug_session_start_phpstorm | sum:k6.http_reqs{testcase:updateaddress}.as_count() | 68.3reqs | 0reqs | 136reqs | 205reqs | 0reqs | status:200
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/update-address_xdebug_session_start_phpstorm | sum:k6.http_reqs{testcase:updateaddress}.as_count() | 4.5reqs | 0reqs | 11reqs | 18reqs | 0reqs | status:502
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/update-address_xdebug_session_start_phpstorm | sum:k6.http_reqs{testcase:updateaddress}.as_count() | 61.0reqs | 0reqs | 141reqs | 183reqs | 0reqs | "
"updateaddress","Virtual Users","updateaddress | 55.0users/s | 19.1users/s | 78.8users/s | 165users/s | 67.2users/s | "
"updateaddress","Req Duration P95","updateaddress | 1.41min | 50.2s | 1.85min | 4.24min | 1.55min | "
"updateaddress","Resp Status","status:0
testcase:updateaddress | 0.23reqs/s | 0reqs/s | 0.45reqs/s | 0.68reqs/s | 0reqs/s | status:200
testcase:updateaddress | 0.015reqs/s | 0reqs/s | 0.037reqs/s | 0.06reqs/s | 0reqs/s | status:502
testcase:updateaddress | 0.20reqs/s | 0reqs/s | 0.47reqs/s | 0.61reqs/s | 0reqs/s | "
"updateaddress","Time Waiting for Response","testcase:updateaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/update-address_xdebug_session_start_phpstorm | max | 1.83min | 89.6s | 2.00min | 5.49min | 2.00min | testcase:updateaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/update-address_xdebug_session_start_phpstorm | median | 1.36min | 49.8s | 1.74min | 4.08min | 1.51min | testcase:updateaddress
url:https://integration.breadfast.tech/wp-json/breadfast/v3/user/update-address_xdebug_session_start_phpstorm | 95P | 1.41min | 49.8s | 1.85min | 4.23min | 1.55min | "
"updateaddress","Req Blocked","updateaddress | 0.45units/s | 0.037units/s | 0.94units/s | 1.35units/s | 0.38units/s | "
"updateaddress","Req Failed","https://integration.breadfast.tech/wp-json/breadfast/v3/user/update-address_xdebug_session_start_phpstorm | 97 | 0 | 277 | 388 | 0 | "
"updateaddress","Latency","https://integration.breadfast.tech/wp-json/breadfast/v3/user/update-address_xdebug_session_start_phpstorm | 1.37min | 50.2s | 1.75min | 4.12min | 1.53min | "
"updateaddress","Response Status from Response Body","No matching results found | "
