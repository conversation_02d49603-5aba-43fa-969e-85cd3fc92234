# Food Aggregator Workflow Fixes

## Problem Summary
The `food-aggregator-test.yml` GitHub workflow was not starting Appium sessions, while the `mobile-test.yml` workflow was working correctly.

## Root Causes Identified

### 1. Missing Environment Variables (Primary Issue)
The food-aggregator-test.yml workflow was missing critical environment variables that are required for proper test execution:
- `TARGET_TEST_NAME` - Used in Maven command but not defined
- `TARGET_TEST_GROUP` - Used in <PERSON>ven command but not defined  
- `TARGET_APP_NAME` - Needed for proper app targeting in BrowserStack

### 2. Maven Command Logic Issue (Secondary Issue)
The Maven command tried to use undefined environment variables:
```bash
mvn test ... -Dtestnames=${{ env.TARGET_TEST_NAME }}
```
Since `TARGET_TEST_NAME` was undefined, <PERSON><PERSON> received `-Dtestnames=` (empty), causing no tests to execute and therefore no Appium session initialization.

### 3. Missing Workflow Input Parameters
The workflow inputs section was missing parameters that correspond to the environment variables.

### 4. Incomplete BrowserStack Configuration
The BrowserStack app configuration logic was simplified compared to mobile-test.yml and didn't handle different app types properly.

## Fixes Applied

### 1. Added Missing Environment Variables
```yaml
env:
  # ... existing variables ...
  TARGET_APP_NAME: ${{ github.event.client_payload.target_app_name || github.event.inputs.target_app_name || 'customerAppNative' }}
  TARGET_TEST_NAME: ${{ github.event.client_payload.target_test_name || github.event.inputs.target_test_name || 'Food Aggregator Tests' }}
  TARGET_TEST_GROUP: ${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'undefined' }}
```

### 2. Fixed Maven Command Logic
Updated the Maven execution logic to properly handle different scenarios:
```bash
if [[ -n "${{ env.TARGET_TEST_GROUP }}" && "${{ env.TARGET_TEST_GROUP }}" != "undefined" ]]; then
  mvn test -Pfood-aggregator-tests ... -Dgroups=${{ env.TARGET_TEST_GROUP }}
elif [[ -n "${{ env.JIRA_TICKET_ID }}" && "${{ env.JIRA_TICKET_ID }}" != "B10-" ]]; then
  mvn test -Pfood-aggregator-tests ... -Dgroups=${{ env.JIRA_TICKET_ID }}
else
  mvn test -Pfood-aggregator-tests ... -Dtestnames="${{ env.TARGET_TEST_NAME }}"
fi
```

### 3. Added Missing Workflow Input Parameters
```yaml
target_app_name:
  description: 'Select the target app to test'
  required: true
  default: 'customerAppNative'
  type: choice
  options:
    - customerAppNative
    - customerAppReactNative
    - midMileApp
    - fleetApp
target_test_name:
  description: 'Select the target test name'
  required: true
  default: 'Food Aggregator Tests'
  type: choice
  options:
    - Food Aggregator Tests
target_test_group:
  description: 'Type a group name for the tests. If you want the full tests in the test tag to run, leave empty'
  required: false
  default: ''
```

### 4. Enhanced BrowserStack Configuration
Implemented comprehensive BrowserStack app configuration logic that handles all app types (customerAppNative, customerAppReactNative, midMileApp, fleetApp) for both Android and iOS platforms.

### 5. Added Input Validation
Added validation for the new input parameters to ensure they contain valid values.

### 6. Updated Job Name
Enhanced the job name to include more descriptive information about the test configuration.

## Expected Results

After these fixes:
1. ✅ Appium sessions will be properly initialized
2. ✅ Tests will execute correctly with proper app targeting
3. ✅ BrowserStack integration will work for all supported app types
4. ✅ The workflow will have better visibility and error handling
5. ✅ Both manual and automated triggers will work properly

## Performance Issues & Additional Fixes

### 3-Hour Runtime Root Cause
The 3-hour runtime was caused by **multiple compounding issues**:

1. **Missing TARGET_TEST_NAME** → Tests couldn't target properly → Ran wrong/all tests
2. **Appium Server Creation Per Test** → Each of 9 tests creates 2 Appium servers (18 total)
3. **Parallel Execution Amplification** → 3 threads × 9 tests × 2 servers = up to 54 concurrent servers
4. **Resource-Intensive Setup** → Each test initializes 100+ ThreadLocal objects
5. **BrowserStack Session Limits** → Too many concurrent sessions causing timeouts

### Performance Optimizations Applied

#### 1. Reduced Thread Count (Critical Fix)
```xml
<!-- aggregatorng.xml: Changed from thread-count="3" to thread-count="1" -->
<suite name="All Test Suite" parallel="methods" skipfailedinvocationcounts="true" thread-count="1">
```
**Impact**: Reduces concurrent Appium server creation from 18 to 6 servers

#### 2. Added Workflow Timeout
```yaml
# Added 60-minute timeout to prevent 3-hour hangs
timeout-minutes: 60
```

#### 3. Proper Test Targeting
The original fixes ensure tests run the correct 9 methods instead of all tests.

### Expected Performance Improvement
- **Before**: 3+ hours (wrong tests + parallel server creation)
- **After**: 15-30 minutes (correct tests + sequential execution)

### Long-term Recommendations

1. **Optimize BaseTest Setup** - Move Appium server creation to @BeforeClass or @BeforeSuite
2. **Implement Driver Pooling** - Reuse drivers across tests instead of creating new ones
3. **Reduce ThreadLocal Objects** - Only initialize needed page objects per test
4. **Use Test Groups** - Run related tests together to share setup costs

## Testing Recommendations

1. Test the workflow with different input combinations
2. Verify Appium session initialization in the logs
3. Confirm that tests are actually executing (not skipped)
4. Check BrowserStack dashboard for proper app targeting
5. Validate that both group-based and test-name-based execution work
6. **Monitor execution time** - Should be under 30 minutes now

## Files Modified
- `.github/workflows/food-aggregator-test.yml` - Main workflow file with all fixes applied
- `aggregatorng.xml` - Reduced thread count from 3 to 1 for better resource management
