# GitHub Copilot Instructions for QA Automation Framework

This document provides GitHub Copilot with context and guidelines for working with the Breadfast QA Automation Framework. Follow these instructions when generating code suggestions, writing tests, or refactoring existing code.

## Framework Overview

This is a comprehensive QA automation framework supporting:
- **Web Testing**: Selenium WebDriver with Page Object Model
- **Android Mobile Testing**: Appium with UiAutomator2
- **iOS Mobile Testing**: Appium with XCUITest
- **API Testing**: REST Assured for API validation
- **Multi-Environment Support**: Testing, Integration, Production
- **Multi-Region Support**: Egypt (EG), Saudi Arabia (KSA)

## Project Structure and Patterns

### Package Organization
```
src/
├── main/java/
│   ├── helpers/           # Core framework utilities
│   ├── modals/            # Page Object Models (POMs)
│   └── models/            # Data models and DTOs
└── test/java/
    ├── base/              # Base test classes
    └── [module]/          # Test classes organized by module
```

### Naming Conventions

#### Test Classes
- **Pattern**: `[Feature]Tests.java`
- **Examples**: `LoginTests.java`, `OrderTests.java`, `PaymentTests.java`
- **Location**: Organized by module (e.g., `mainAdminPortal/authentication/`)

#### Test Methods
- **Pattern**: `[action][Condition][ExpectedResult]`
- **Examples**: 
  - `loginWithValidLocalPhoneNumber()`
  - `createOrderWithValidPaymentMethod()`
  - `validateErrorMessageForInvalidInput()`

#### Page Object Classes
- **Web**: `[PageName]Page.java` (e.g., `WebLoginPage.java`)
- **Android**: `Android[ScreenName]Screen.java` (e.g., `AndroidHomeScreen.java`)
- **iOS**: `Ios[ScreenName]Screen.java` (e.g., `IosHomeScreen.java`)

#### Helper Classes
- **Pattern**: `[Purpose]Helper.java`
- **Examples**: `DataHelper.java`, `EncryptionHelper.java`, `SetUpHelper.java`

## Code Generation Guidelines

### Test Class Structure
When creating new test classes, follow this template:

```java
package [module].[submodule];

import base.BaseTest;
import io.qameta.allure.testng.Tag;
import io.qameta.allure.testng.Tags;
import org.testng.Assert;
import org.testng.Reporter;
import org.testng.annotations.Test;

@Test
public class [Feature]Tests extends BaseTest {

    @Test(groups = {"smoke"})
    @Tags({@Tag("web")})  // Use appropriate tags: "web", "android", "ios", "api"
    public void [testMethodName]() {
        Reporter.log("Starting [testMethodName] test");

        // Arrange
        Reporter.log("Setting up test data", 3, true);

        // Act
        Reporter.log("Performing test actions", 3, true);

        // Assert
        Reporter.log("Validating test results", 3, true);
        Assert.assertTrue(condition, "Validation message");
    }
}
```

### Page Object Model Pattern
Follow the existing POM structure:

```java
public class [PageName]Page {
    private final Logger logger = LoggerFactory.getLogger([PageName]Page.class);
    
    // Locators
    private final By elementLocator = By.id("element-id");
    
    // Actions
    public void performAction() {
        logger.info("Performing action on [PageName]");
        // Implementation
    }
    
    // Validations
    public boolean isElementDisplayed() {
        logger.info("Checking if element is displayed");
        // Implementation
        return true;
    }
}
```

### API Test Pattern
For API tests, use REST Assured with the existing validation helpers:

```java
@Test
@Tags({@Tag("api")})
public void validateApiEndpoint() {
    // Use existing API clients
    JsonPath response = [apiClient].performApiCall();
    
    // Use existing validators
    [apiValidator].validateResponse(response);
}
```

## Framework-Specific Guidelines

### Configuration Management
- Use `defaultTestData.get()` for accessing test data and always refer to existing tests and TestData model to know it's structure.
- Environment configs are in `resources/environments/`
- Device configs: `Pixel_2.properties`, `iPhone_14.properties`

### Platform-Specific Considerations

#### Web Testing
- Always extend `BaseTest`
- Use thread-safe page objects with `ThreadLocal` that should be initialized in `BaseTest`
- Include `@Tags({@Tag("web")})` annotation
- This Tag acts as config management trigger for environment setup and driver initialization.

#### Mobile Testing
- Android: Use `AndroidDriver` and UiAutomator2 selectors
- iOS: Use `IOSDriver` and XCUITest selectors
- Include appropriate tags: `@Tags({@Tag("android")})` or `@Tags({@Tag("ios")})`
- These Tags act as config management triggers for environment setup and driver initialization.

#### Cross-Platform Testing
- Implement platform-specific page objects
- Use factory pattern for driver initialization which is already implemented in Mobile/Web drivers factories classes
- Maintain consistent test logic across platforms

### Logging and Reporting
- Use `Reporter.log()` for TestNG reporting
- Use SLF4J logger for detailed logging
- Include descriptive log messages with severity levels

### Test Data Management
- Use existing data factories in `helpers/factories/dataFactories/`
- Implement data providers for parameterized tests
- Use `DataHelper` for data manipulation

### Error Handling
- Rely on robust error handling and logging in case code within src/main is not working as expected.
```java
try {
    // Test actions
} catch (Exception e) {
    logger.error("Test failed with error: " + e.getMessage());
}
```

## Testing Best Practices

### Test Independence
- Each test should be independent and atomic
- Avoid test dependencies

### Assertions
- Use meaningful assertion messages
- Prefer specific assertions over generic ones
- Include context in assertion messages

### Test Groups
- Use appropriate TestNG groups: `"smoke"`, `"regression"`, `"sanity"`
- Tag tests by platform: `"web"`, `"android"`, `"ios"`, `"api"`

### Environment Handling
- Tests should work across all environments without code changes
- Use configuration-driven approach for environment-specific values
- Handle regional differences (EG vs KSA) through configuration

### Driver Management
- Access drivers through POM constructors which are already implemented in existing page objects
```java
webDriver.get().findElement(locator);
androidDriver.get().findElement(locator);
iosDriver.get().findElement(locator);
```

### API Calls
```java
// Use existing API clients
JsonPath response = customerApiClient.get().makeApiCall();
apiValidator.validateStatusCode(response, 200);
```

### Page Navigation
```java
// Web navigation
webHomePage.get().navigateToOrdersPage();

// Mobile navigation  
androidHomeScreen.get().navigateToMoreScreen();
iosHomeScreen.get().navigateToMoreScreen();
```

## Anti-Patterns to Avoid

- **Hard-coded values**: Use configuration files and data factories
- **Direct driver usage**: Use page objects and helper methods
- **Platform-specific code in tests**: Abstract platform differences in page objects
- **Synchronous waits**: Use explicit waits and helper methods
- **Copy-paste code**: Create reusable helper methods
- **Missing error handling**: Always include appropriate exception handling

## Integration with CI/CD

### TestNG Suite Files
- `testng.xml`: Default test suite
- `sanityng.xml`: Sanity tests
- `mobileng.xml`: Mobile-specific tests
- `masterng.xml`: Full regression suite
- `fintechng.xml`: Fintech tests
- `fleetappng.xml`: Fleet app tests
- `referralsng.xml`: referrals tests
- `supplychainng.xml`: supplychain tests
- `aggregatorng.xml`: food aggregator tests

### Maven Profiles
- `default-tests`: Standard test execution
- `sanity-tests`: Quick validation test suite
- `mobile-tests`: Mobile platform test suite
- `stress-tests`: Performance testing
- `full-tests`: The full regression suite test suite
- `food-aggregator-tests`: Food aggregator test suite
- `fintech-tests`: Fintech test suite
- `supplychain-tests`: Supplychain test suite
- `fleetApp-tests`: Fleet app test suite
- `referrals-tests`: Referrarls test suite

## When Generating Code

1. **Always check existing patterns** before creating new implementations
2. **Use existing helper classes** rather than creating new utilities
3. **Follow the established package structure** for organization
4. **Include appropriate logging and reporting** statements
5. **Use configuration-driven approaches** for environment-specific values
6. **Maintain consistency** with existing naming conventions
7. **Include proper error handling** and validation
8. **Add appropriate TestNG annotations** and Allure tags
9. **In the test methods you must generate clear, deterministic assertions without branching logic** as a best practice.
10. **In test methods do not assume there is a need for mobile app restart** no need for that step as long as it is not mentioned to you exceplicitly.
11. **The est data should not be treated as volatile**.
12. **Try to use the already-initialized API client/threads** to avoid duplication and potential issues when tests run in parallel.
13. **Do not generate methods that have only placeholders** your methods must be functioning
14. **After you finish generting your code review it against the rules mentioned above, the best practices and redundency** to ensures better code qaulity and less human review time and efforts

Remember: This framework emphasizes maintainability, scalability, and cross-platform consistency. Always consider these principles when generating suggestions or writing code.
