name: Group Runner Test

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the target environment'
        required: true
        default: 'testing'
        type: choice
        options:
          - testing
          - integration
      test_country_code:
        description: 'Select the test country code'
        required: true
        default: 'EG'
        type: choice
        options:
          - EG
          - KSA
      use_order_api_v2:
        description: 'Flag to use Order API V2'
        required: true
        default: true
        type: boolean
      target_app_platform:
        description: 'Select the target app platform'
        required: true
        default: 'android'
        type: choice
        options:
          - android
          - ios
      target_app_name:
        description: 'Select the target app to test'
        required: true
        default: 'customerAppNative'
        type: choice
        options:
          - customerAppNative
          - customerAppReactNative
          - midMileApp
          - fleetApp
      target_app_id:
        description: 'Enter the target-App ID from browserStack or leave as is if you want to use stored value'
        required: false
        type: string
        default: 'bs://'
      target_app_build_number:
        description: 'Enter the target-App build version and number leave empty if no app ID is provided. Accepted values are digits, dot, () and hyphen'
        required: false
        type: string
        default: ''
      jira_ticket_id:
        description: 'Enter the Jira Ticket ID (digits, B, and - only)'
        required: true
        type: string
        default: 'B10-'
  repository_dispatch:
    types: [ group-runner-test ]

jobs:
  sanity-test-check:
    name: Group Runner Test - ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }} - Order API V2 (${{ github.event.client_payload.use_order_api_v2 || github.event.inputs.use_order_api_v2 || 'true' }}) - Jira Tikcet ID (${{ github.event.client_payload.jira_ticket_id || github.event.inputs.jira_ticket_id || 'B10-' }})
    runs-on: ubuntu-latest
    environment: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }}
    env:
      SELECTED_ENV: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }}
      USE_ORDER_API_V2: ${{ github.event.client_payload.use_order_api_v2 || github.event.inputs.use_order_api_v2 || 'true' }}
      TEST_COUNTRY_CODE: ${{ github.event.client_payload.test_country_code || github.event.inputs.test_country_code || 'EG' }}
      TRIGGERING_REPO_OWNER: ${{ github.event.client_payload.triggering_repo_owner || 'undefined' }}
      TRIGGERING_REPO: ${{ github.event.client_payload.triggering_repo || 'undefined' }}
      TRIGGERING_SHA_ID: ${{ github.event.client_payload.sha_id || 'undefined' }}
      JIRA_TICKET_ID: ${{ github.event.client_payload.jira_ticket_id || github.event.inputs.jira_ticket_id || 'B10-' }}
      TARGET_APP_PLATFORM: ${{ github.event.client_payload.target_app_platform || github.event.inputs.target_app_platform || 'android' }}
      TARGET_APP_NAME: ${{ github.event.client_payload.target_app_name || github.event.inputs.target_app_name || 'customerAppNative' }}
      TARGET_APP_ID: ${{ github.event.client_payload.target_app_id || github.event.inputs.target_app_id || 'undefined' }}
      TARGET_APP_BUILD_NUMBER: ${{ github.event.client_payload.target_app_build_number || github.event.inputs.target_app_build_number || 'undefined' }}
      TESTMO_URL: ${{ secrets.TESTMO_URL }}
      TESTMO_TOKEN: ${{ secrets.TESTMO_TOKEN }}
    steps:
      - name: Validate Inputs and Values
        run: |
          # Validate presence of required fields for repository_dispatch
          if [ "${{ github.event_name }}" == "repository_dispatch" ]; then
            if [ -z "${{ github.event.client_payload.triggering_repo_owner }}" ] || \
               [ -z "${{ github.event.client_payload.triggering_repo }}" ] || \
               [ -z "${{ github.event.client_payload.sha_id }}" ] || \
               [ -z "${{ github.event.client_payload.jira_ticket_id }}" ]; then
              echo "Error: Required fields in client_payload are missing for repository_dispatch!"
              echo "Triggering Repo Owner: '${{ github.event.client_payload.triggering_repo_owner }}'"
              echo "Triggering Repo: '${{ github.event.client_payload.triggering_repo }}'"
              echo "Triggering SHA ID: '${{ github.event.client_payload.sha_id }}'"
              echo "Jira Ticket ID: '${{ github.event.client_payload.jira_ticket_id }}'"
              exit 1
            fi
          fi
          # Validate environment
          if [[ "${{ env.SELECTED_ENV }}" != "testing" && \
                "${{ env.SELECTED_ENV }}" != "integration" ]]; then
            echo "Error: Invalid environment '${{ env.SELECTED_ENV }}'. Must be one of: testing, integration"
            exit 1
          fi
          # Validate use_order_api_v2
          if [[ "${{ env.USE_ORDER_API_V2 }}" != "true" && \
                "${{ env.USE_ORDER_API_V2 }}" != "false" ]]; then
            echo "Error: Invalid use_order_api_v2 '${{ env.USE_ORDER_API_V2 }}'. Must be true or false."
            exit 1
          fi
          # Validate test_country_code
          if [[ "${{ env.TEST_COUNTRY_CODE }}" != "EG" && \
                "${{ env.TEST_COUNTRY_CODE }}" != "KSA" ]]; then
            echo "Error: Invalid test_country_code '${{ env.TEST_COUNTRY_CODE }}'. Must be one of: EG, KSA."
            exit 1
          fi
          # Validate JIRA_TICKET_ID format
          if [[ ! "${{ env.JIRA_TICKET_ID }}" =~ ^B10-[0-9]+$ ]]; then
            echo "Error: Invalid JIRA_TICKET_ID format. Must be 'B10-<digits>'."
            exit 1
          fi
          # Validate TARGET_APP_PLATFORM
          if [[ "${{ env.TARGET_APP_PLATFORM }}" != "android" && \
                "${{ env.TARGET_APP_PLATFORM }}" != "ios" ]]; then
            echo "Error: Invalid target_app_platform '${{ env.TARGET_APP_PLATFORM }}'. Must be one of: android, ios"
            exit 1
          fi
          # Validate TARGET_APP_NAME
          if [[ "${{ env.TARGET_APP_NAME }}" != "customerAppNative" && \
                "${{ env.TARGET_APP_NAME }}" != "customerAppReactNative" && \
                "${{ env.TARGET_APP_NAME }}" != "midMileApp" && \
                "${{ env.TARGET_APP_NAME }}" != "fleetApp" ]]; then
            echo "Error: Invalid target_app_name '${{ env.TARGET_APP_NAME }}'. Must be one of: customerAppNative, customerAppReactNative, midMileApp, fleetApp"
            exit 1
          fi
          # Validate TARGET_APP_ID
          if [[ -n "${{ env.TARGET_APP_ID }}" && "${{ env.TARGET_APP_ID }}" != "bs://" && ! "${{ env.TARGET_APP_ID }}" =~ ^bs://[a-zA-Z0-9]{10,}$ ]]; then
            echo "Error: Invalid target_app_id value. Must start with 'bs://' and be followed by at least 10 alphanumeric characters."
            exit 1
          fi
          # Validate Target app build number
          if [[ -n "${{ env.TARGET_APP_ID }}" && "${{ env.TARGET_APP_ID }}" != "bs://" ]]; then
            if [[ -z "${{ env.TARGET_APP_BUILD_NUMBER }}" || "${{ env.TARGET_APP_BUILD_NUMBER }}" == "undefined" ]]; then
              echo "Error: target_app_build_number is required when target_app_id is provided and is not 'bs://'."
              exit 1
            fi
            if [[ ! "${{ env.TARGET_APP_BUILD_NUMBER }}" =~ ^[0-9.\(\)\-]+$ ]]; then
              echo "Error: Invalid target_app_build_number. Only digits, dots, parentheses and hyphen are allowed."
              exit 1
            fi
          fi
          echo "All validations passed successfully!"
        shell: bash

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create Pending Status Check (composite)
        if: ${{ github.event_name == 'repository_dispatch' }}
        uses: ./.github/actions/create-pending-check-on-pr
        with:
          repo-owner: ${{ env.TRIGGERING_REPO_OWNER }}
          repo-name: ${{ env.TRIGGERING_REPO }}
          run-id: ${{ github.run_id }}
          triggering-sha-id: ${{ env.TRIGGERING_SHA_ID }}
          gh-pat: ${{ secrets.GH_PAT }}
          description: "Group Runner Tests are in progress."
          title: "Group Runner Tests Result"

      - name: Setup Runtime (composite)
        uses: ./.github/actions/setup-machine

      - name: Configs Setup (composite)
        uses: ./.github/actions/configs-setup
        with:
          target-environment: ${{ env.SELECTED_ENV }}
          test-country-code: ${{ env.TEST_COUNTRY_CODE }}
        env:
          GHA_VARS_JSON: ${{ toJson(vars) }}
          GHA_SECRETS_JSON: ${{ toJson(secrets) }}

      - name: Testmo Setup
        uses: ./.github/actions/testmo-setup
        with:
          working_directory: .
          version: "latest"

      - name: Add the github Run Link to testmo Run Details
        run: |
          npx testmo automation:resources:add-field --name git --type string \
            --value ${GITHUB_SHA:0:7} --resources resources.json
          RUN_URL="$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"
          npx testmo automation:resources:add-link --name build \
            --url $RUN_URL --resources resources.json
          
      - name: Check and Set TARGET_APP_ID to 'undefined' if it's on default values
        run: |
          if [[ -z "${{ env.TARGET_APP_ID }}" || "${{ env.TARGET_APP_ID }}" == "bs://" ]]; then
            echo "TARGET_APP_ID=" >> $GITHUB_ENV
          fi

      - name: Override BrowserStack App ID (composite)
        uses: ./.github/actions/target-app-definition
        with:
          target-app-platform: ${{ env.TARGET_APP_PLATFORM }}
          target-app-name: ${{ env.TARGET_APP_NAME }}
          target-app-id: ${{ env.TARGET_APP_ID }}
          target-app-build-number: ${{ env.TARGET_APP_BUILD_NUMBER }}

      - name: Build with Maven and Execute Tests
        id: build_and_test
        run: |
          npx testmo automation:run:submit \
            --instance "$TESTMO_URL" \
            --project-id 9 \
            --name "${{ env.JIRA_TICKET_ID }} - Tests Run" \
            --source "${{ env.JIRA_TICKET_ID }}-TestsRunner" \
            --results ${{ github.workspace }}/target/surefire-reports/*.xml \
            -- mvn test -q -Pfull-tests -Dmaven.test.failure.ignore=true -Dbrowserstack.enabled=true -Dconnect.to.db=true -Denv=${{ env.SELECTED_ENV }} -Duse.order.api.v2=${{ env.USE_ORDER_API_V2 }} -Dgroups=${{ env.JIRA_TICKET_ID }} \

      - name: Publish test artifacts (composite)
        if: ${{ always() }}
        uses: ./.github/actions/generate-reports
        with:
          working-directory: .
          allure_artifact_name: allure-reports
          allure_path: target/site/allure-maven-plugin
          upload_screenshots: "true"
          screenshots_artifact_name: screenshots
          screenshots_path: resources/screenshots/**
          screenshots_if_no_files_found: ignore
          screenshots_retention_days: "14"
          testng_artifact_name: testng-reports
          testng_path: target/surefire-reports

      - name: Check Test Results
        if: always()
        id: check_test_results
        run: |
          TEST_RESULTS_FILE="target/surefire-reports/testng-results.xml"
          if [ -f "$TEST_RESULTS_FILE" ]; then
            EXECUTED_TESTS=$(xmllint --xpath "string(//testng-results/@total)" "$TEST_RESULTS_FILE")
            echo "executed_tests=$EXECUTED_TESTS" >> $GITHUB_ENV
          fi
          if [ "${{ steps.build_and_test.outcome }}" == "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
          fi

      - name: Report Status Back to Triggering Repository (composite)
        if: ${{ always() && github.event_name == 'repository_dispatch' }}
        uses: ./.github/actions/update-existing-check-on-pr
        with:
          repo-owner: ${{ env.TRIGGERING_REPO_OWNER }}
          repo-name: ${{ env.TRIGGERING_REPO }}
          run-id: ${{ github.run_id }}
          triggering-sha-id: ${{ env.TRIGGERING_SHA_ID }}
          gh-pat: ${{ secrets.GH_PAT }}
          title: "Group Runner Tests Result"
          build-outcome: ${{ steps.build_and_test.outcome }}
          success-description: "Group Runner Tests passed successfully."
          failure-description: "Group Runner Tests failed."
          error-description: "Group Runner Tests encountered an error or was cancelled."
