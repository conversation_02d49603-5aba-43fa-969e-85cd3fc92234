name: Referral Test

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the target environment'
        required: true
        default: 'integration'
        type: choice
        options:
          - testing
          - integration
          - production
      test_country_code:
        description: 'Select the test country code'
        required: true
        default: 'EG'
        type: choice
        options:
          - EG
          - KSA
      use_order_api_v2:
        description: 'Flag to use Order API V2'
        required: true
        default: true
        type: boolean

  schedule:
    - cron: '0 6 * * *' # Runs every day at 6 a.m. UTC 9 a.m. Cairo Local Time

jobs:
  sanity-test-check:
    name: Referral Test - ${{ github.event.inputs.environment || 'integration' }} - Order API V2 (${{ github.event.inputs.use_order_api_v2 || 'true' }})
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'integration' }}
    env:
      SELECTED_ENV: ${{ github.event.inputs.environment || 'integration' }}
      USE_ORDER_API_V2: ${{ github.event.inputs.use_order_api_v2 || 'true' }}
      TEST_COUNTRY_CODE: ${{ github.event.inputs.test_country_code || 'EG' }}
      TESTMO_URL: ${{ secrets.TESTMO_URL }}
      TESTMO_TOKEN: ${{ secrets.TESTMO_TOKEN }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Runtime (composite)
        uses: ./.github/actions/setup-machine

      - name: Configs Setup (composite)
        uses: ./.github/actions/configs-setup
        with:
          target-environment: ${{ env.SELECTED_ENV }}
          test-country-code: ${{ env.TEST_COUNTRY_CODE }}
        env:
          GHA_VARS_JSON: ${{ toJson(vars) }}
          GHA_SECRETS_JSON: ${{ toJson(secrets) }}

      - name: Testmo Setup
        uses: ./.github/actions/testmo-setup
        with:
          working_directory: .
          version: "latest"

      - name: Add the github Run Link to testmo Run Details
        run: |
          npx testmo automation:resources:add-field --name git --type string \
            --value ${GITHUB_SHA:0:7} --resources resources.json
          RUN_URL="$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"
          npx testmo automation:resources:add-link --name build \
            --url $RUN_URL --resources resources.json

      - name: Build with Maven and Execute Tests
        run: |
          npx testmo automation:run:submit \
            --instance "$TESTMO_URL" \
            --project-id 9 \
            --name "GitHub Actions Sanity Test Run" \
            --source "OrderSanityTests" \
            --results ${{ github.workspace }}/target/surefire-reports/*.xml \
            -- mvn test -q -Preferrals-tests -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }} -Duse.order.api.v2=${{ env.USE_ORDER_API_V2 }} \

      - name: Publish test artifacts (composite)
        if: ${{ always() }}
        uses: ./.github/actions/generate-reports
        with:
          working-directory: .
          allure_artifact_name: allure-reports
          allure_path: target/site/allure-maven-plugin
          upload_screenshots: "true"
          screenshots_artifact_name: screenshots
          screenshots_path: resources/screenshots/**
          screenshots_if_no_files_found: ignore
          screenshots_retention_days: "14"
          testng_artifact_name: testng-reports
          testng_path: target/surefire-reports

      - name: Upload Allure Test Results to Browserstack
        if: always()
        run: |
          curl -u "${{ secrets.BROWSERSTACK_TEST_OBSERVABILITY_KEY }}" -vvv \
          -X POST \
          -F "data=@allure-results.zip" \
          -F "projectName=Referral" \
          -F "buildName=Default Referral Build" \
          -F "tags=Smoke" \
          -F "format=allure" \
          https://upload-observability.browserstack.com/upload
     
      - name: Check Test Results
        id: check_test_results
        run: |
          echo "status=$(grep -E 'Tests run: [0-9]+, Failures: [1-9]+' target/surefire-reports/*.txt | wc -l)" >> $GITHUB_OUTPUT    

      - name: Notify Slack Channel (Composite)
        if: always()
        uses: ./.github/actions/send-slack-message-to-internal-channels
        with:
          scope: Referral
          build-outcome: ${{ steps.build_and_test.outcome }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
        env:
          GHA_VARS_JSON: ${{ toJson(vars) }}