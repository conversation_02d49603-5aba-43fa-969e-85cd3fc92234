name: Mobile Tests

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the target environment'
        required: true
        default: 'testing'
        type: choice
        options:
          - testing
          - integration
      test_country_code:
        description: 'Select the test country code'
        required: true
        default: 'EG'
        type: choice
        options:
          - EG
      target_app_platform:
        description: 'Select the target app platform'
        required: true
        default: 'android'
        type: choice
        options:
          - android
          - ios
      target_app_name:
        description: 'Select the target app to test'
        required: true
        default: 'customerAppNative'
        type: choice
        options:
          - customerAppNative
          - customerAppReactNative
          - midMileApp
          - fleetApp
      target_test_name:
        description: 'Select the target test name'
        required: true
        default: 'CustomerAppNative_Android'
        type: choice
        options:
          - CustomerAppNative_Android
          - CustomerAppNative_iOS
      target_test_group:
        description: 'Type a group name for the tests. If you want the full tests in the test tag to run, leave empty'
        required: false
        default: ''
      target_app_id:
        description: 'Enter the target-App ID from BrowserStack or leave as is if you want to use stored value'
        required: false
        type: string
        default: 'bs://'
      target_app_build_number:
        description: 'Enter the target-App build version and number leave empty if no app ID is provided. Accepted values are digits, dot, () and hyphen'
        required: false
        type: string
        default: ''
  repository_dispatch:
    types: [mobile-test]

concurrency:
  group: mobile-tests-workflow
  cancel-in-progress: false

jobs:
  build-and-test:
    name: Mobile Tests - ${{ github.event.client_payload.environment || github.event.inputs.environment || 'testing' }} - ${{ github.event.client_payload.target_app_platform || github.event.inputs.target_app_platform || 'android' }} - ${{ github.event.client_payload.target_app_name || github.event.inputs.target_app_name || 'customerAppNative' }} - ${{ github.event.client_payload.target_test_name || github.event.inputs.target_test_name || 'CustomerAppNative_Android' }} - Groups(${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'undefined' }})
    runs-on: ubuntu-latest
    environment: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'testing' }}
    env:
      SELECTED_ENV: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'testing' }}
      TEST_COUNTRY_CODE: ${{ github.event.client_payload.test_country_code || github.event.inputs.test_country_code || 'EG' }}
      TRIGGERING_REPO_OWNER: ${{ github.event.client_payload.triggering_repo_owner || 'undefined' }}
      TRIGGERING_REPO: ${{ github.event.client_payload.triggering_repo || 'undefined' }}
      TRIGGERING_SHA_ID: ${{ github.event.client_payload.sha_id || 'undefined' }}
      TARGET_APP_PLATFORM: ${{ github.event.client_payload.target_app_platform || github.event.inputs.target_app_platform || 'android' }}
      TARGET_APP_NAME: ${{ github.event.client_payload.target_app_name || github.event.inputs.target_app_name || 'customerAppNative' }}
      TARGET_APP_ID: ${{ github.event.client_payload.target_app_id || github.event.inputs.target_app_id || 'undefined' }}
      TARGET_APP_BUILD_NUMBER: ${{ github.event.client_payload.target_app_build_number || github.event.inputs.target_app_build_number || 'undefined' }}
      TARGET_TEST_NAME: ${{ github.event.client_payload.target_test_name || github.event.inputs.target_test_name || 'CustomerAppNative_Android' }}
      TARGET_TEST_GROUP: ${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'undefined' }}

    steps:
      - name: Validate Inputs and Values
        shell: bash
        run: |
          # Validate presence of required fields for repository_dispatch
          if [ "${{ github.event_name }}" == "repository_dispatch" ]; then
            if [ -z "${{ github.event.client_payload.triggering_repo_owner }}" ] || \
               [ -z "${{ github.event.client_payload.triggering_repo }}" ] || \
               [ -z "${{ github.event.client_payload.sha_id }}" ]; then
              echo "Error: Required fields in client_payload are missing for repository_dispatch!"
              echo "Triggering Repo Owner: '${{ github.event.client_payload.triggering_repo_owner }}'"
              echo "Triggering Repo: '${{ github.event.client_payload.triggering_repo }}'"
              echo "Triggering SHA ID: '${{ github.event.client_payload.sha_id }}'"
              exit 1
            fi
          fi
          # Validate environment
          if [[ "${{ env.SELECTED_ENV }}" != "testing" && \
                "${{ env.SELECTED_ENV }}" != "integration" ]]; then
            echo "Error: Invalid environment '${{ env.SELECTED_ENV }}'. Must be one of: testing, integration"
            exit 1
          fi
          # Validate test_country_code
          if [[ "${{ env.TEST_COUNTRY_CODE }}" != "EG" ]]; then
            echo "Error: Invalid test_country_code '${{ env.TEST_COUNTRY_CODE }}'. Must be: EG"
            exit 1
          fi
          # Validate TARGET_APP_PLATFORM
          if [[ "${{ env.TARGET_APP_PLATFORM }}" != "android" && \
                "${{ env.TARGET_APP_PLATFORM }}" != "ios" ]]; then
            echo "Error: Invalid target_app_platform '${{ env.TARGET_APP_PLATFORM }}'. Must be one of: android, ios"
            exit 1
          fi
          # Validate TARGET_APP_NAME
          if [[ "${{ env.TARGET_APP_NAME }}" != "customerAppNative" && \
                "${{ env.TARGET_APP_NAME }}" != "customerAppReactNative" && \
                "${{ env.TARGET_APP_NAME }}" != "midMileApp" && \
                "${{ env.TARGET_APP_NAME }}" != "fleetApp" ]]; then
            echo "Error: Invalid target_app_name '${{ env.TARGET_APP_NAME }}'. Must be one of: customerAppNative, customerAppReactNative, midMileApp, fleetApp"
            exit 1
          fi
          # Validate Target app ID format
          if [[ -n "${{ env.TARGET_APP_ID }}" && "${{ env.TARGET_APP_ID }}" != "bs://" && "${{ env.TARGET_APP_ID }}" != "undefined" && ! "${{ env.TARGET_APP_ID }}" =~ ^bs://[a-zA-Z0-9]{10,}$ ]]; then
            echo "Error: Invalid target_app_id value. Must start with 'bs://' and be followed by at least 10 alphanumeric characters."
            exit 1
          fi
          # Validate Target app build number
          if [[ -n "${{ env.TARGET_APP_ID }}" && "${{ env.TARGET_APP_ID }}" != "bs://" ]]; then
            if [[ -z "${{ env.TARGET_APP_BUILD_NUMBER }}" || "${{ env.TARGET_APP_BUILD_NUMBER }}" == "undefined" ]]; then
              echo "Error: target_app_build_number is required when target_app_id is provided and is not 'bs://'."
              exit 1
            fi
            if [[ ! "$(echo "${{ env.TARGET_APP_BUILD_NUMBER }}" | xargs)" =~ ^[0-9.\(\)\-]+$ ]]; then
              echo "Error: Invalid target_app_build_number. Only digits, dots, parentheses and hyphen are allowed."
              exit 1
            fi
          fi
          # Validate TARGET_TEST_NAME
          if [[ "${{ env.TARGET_TEST_NAME }}" != "CustomerAppNative_Android" && \
                "${{ env.TARGET_TEST_NAME }}" != "CustomerAppNative_iOS" ]]; then
            echo "Error: Invalid target_test_name '${{ env.TARGET_TEST_NAME }}'. Must be one of: CustomerAppNative_Android, CustomerAppNative_iOS"
            exit 1
          fi
          echo "All validations passed successfully!"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create Pending Status Check (composite)
        if: ${{ always() && github.event_name == 'repository_dispatch' }}
        uses: ./.github/actions/create-pending-check-on-pr
        with:
          repo-owner: ${{ env.TRIGGERING_REPO_OWNER }}
          repo-name: ${{ env.TRIGGERING_REPO }}
          run-id: ${{ github.run_id }}
          triggering-sha-id: ${{ env.TRIGGERING_SHA_ID }}
          gh-pat: ${{ secrets.GH_PAT }}
          description: "Mobile Tests are in progress."
          title: "Mobile Tests Result"

      - name: Setup Runtime (composite)
        uses: ./.github/actions/setup-machine

      - name: Configs Setup (composite)
        uses: ./.github/actions/configs-setup
        with:
          target-environment: ${{ env.SELECTED_ENV }}
          test-country-code:  ${{ env.TEST_COUNTRY_CODE }}
        env:
          GHA_VARS_JSON: ${{ toJson(vars) }}
          GHA_SECRETS_JSON: ${{ toJson(secrets) }}

      - name: Check and Set TARGET_APP_ID to 'undefined' if it's on default values
        run: |
          if [[ -z "${{ env.TARGET_APP_ID }}" || "${{ env.TARGET_APP_ID }}" == "bs://" ]]; then
            echo "TARGET_APP_ID=" >> $GITHUB_ENV
          fi

      - name: Override BrowserStack App ID (composite)
        uses: ./.github/actions/target-app-definition
        with:
          target-app-platform:     ${{ env.TARGET_APP_PLATFORM }}
          target-app-name:         ${{ env.TARGET_APP_NAME }}
          target-app-id:           ${{ env.TARGET_APP_ID }}
          target-app-build-number: ${{ env.TARGET_APP_BUILD_NUMBER }}

      - name: Build with Maven and Execute Tests
        id: build_and_test
        run: |
          if [[ -n "${{ env.TARGET_TEST_GROUP }}" && "${{ env.TARGET_TEST_GROUP }}" != "undefined" ]]; then
            mvn test -q -Pmobile-tests \
              -Dmaven.test.failure.ignore=true \
              -Dbrowserstack.enabled=true \
              -Dconnect.to.db=true \
              -Denv=${{ env.SELECTED_ENV }} \
              -Dtestnames=${{ env.TARGET_TEST_NAME }} \
              -Dgroups=${{ env.TARGET_TEST_GROUP }}
          else
            mvn test -q -Pmobile-tests \
              -Dmaven.test.failure.ignore=true \
              -Dbrowserstack.enabled=true \
              -Dconnect.to.db=true \
              -Denv=${{ env.SELECTED_ENV }} \
              -Dtestnames=${{ env.TARGET_TEST_NAME }}
          fi

      - name: Publish test artifacts (composite)
        if: ${{ always() }}
        uses: ./.github/actions/generate-reports
        with:
          working-directory: .
          allure_artifact_name: allure-reports
          allure_path: target/site/allure-maven-plugin
          upload_screenshots: "true"
          screenshots_artifact_name: screenshots
          screenshots_path: resources/screenshots/**
          screenshots_if_no_files_found: ignore
          screenshots_retention_days: "14"
          testng_artifact_name: testng-reports
          testng_path: target/surefire-reports

      - name: Upload Allure Test Results to Browserstack
        if: always()
        run: |
          curl -u "${{ secrets.BROWSERSTACK_TEST_OBSERVABILITY_KEY }}" -vvv \
          -X POST \
          -F "data=@allure-results.zip" \
          -F "projectName=$(case "${{ env.TARGET_APP_NAME }}" in
              customerAppNative)
                case "${{ env.TARGET_APP_PLATFORM }}" in
                  android) echo "CustomerApp - Native Android" ;;
                  ios) echo "CustomerApp - Native IOS" ;;
                esac ;;
              customerAppReactNative)
                case "${{ env.TARGET_APP_PLATFORM }}" in
                  android) echo "CustomerApp - ReactNative Android" ;;
                  ios) echo "CustomerApp - ReactNative IOS" ;;
                esac ;;
              midMileApp)
                case "${{ env.TARGET_APP_PLATFORM }}" in
                  android) echo "MidMileApp - Android" ;;
                  ios) echo "MidMileApp - IOS" ;;
                esac ;;
              fleetApp)
                case "${{ env.TARGET_APP_PLATFORM }}" in
                  android) echo "FleetApp - Android" ;;
                  ios) echo "FleetApp - IOS" ;;
                esac ;;
            esac)" \
          -F "buildName=$(case "${{ env.TARGET_APP_NAME }}" in
              customerAppNative)
                case "${{ env.TARGET_APP_PLATFORM }}" in
                  android) echo "CustomerApp - Native Android Build" ;;
                  ios) echo "CustomerApp - Native IOS Build" ;;
                esac ;;
              customerAppReactNative)
                case "${{ env.TARGET_APP_PLATFORM }}" in
                  android) echo "CustomerApp - ReactNative Android Build" ;;
                  ios) echo "CustomerApp - ReactNative IOS Build" ;;
                esac ;;
              midMileApp)
                case "${{ env.TARGET_APP_PLATFORM }}" in
                  android) echo "MidMileApp - Android Build" ;;
                  ios) echo "MidMileApp - IOS Build" ;;
                esac ;;
              fleetApp)
                case "${{ env.TARGET_APP_PLATFORM }}" in
                  android) echo "FleetApp - Android Build" ;;
                  ios) echo "FleetApp - IOS Build" ;;
                esac ;;
            esac)" \
          -F "tags=$(if [[ "${{ env.TARGET_APP_ID }}" != "bs://" && "${{ env.TARGET_APP_ID }}" != "" && "${{ env.TARGET_APP_ID }}" != "undefined" ]]; then echo "Smoke_${{ env.TARGET_APP_BUILD_NUMBER }}"; else echo "Smoke"; fi)" \
          -F "format=allure" \
          https://upload-observability.browserstack.com/upload

      - name: Check Test Results
        if: ${{ always() }}
        id: check_test_results
        run: |
          if [ "${{ steps.build_and_test.outcome }}" == "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
          fi

      - name: Report Status Back to Triggering Repository (composite)
        if: ${{ always() && github.event_name == 'repository_dispatch' }}
        uses: ./.github/actions/update-existing-check-on-pr
        with:
          repo-owner: ${{ env.TRIGGERING_REPO_OWNER }}
          repo-name: ${{ env.TRIGGERING_REPO }}
          run-id: ${{ github.run_id }}
          triggering-sha-id: ${{ env.TRIGGERING_SHA_ID }}
          gh-pat: ${{ secrets.GH_PAT }}
          title: "Mobile Tests Result"
          build-outcome: ${{ steps.build_and_test.outcome }}
          success-description: "Mobile Test passed successfully."
          failure-description: "Mobile Tests failed."
          error-description: "Mobile Tests encountered an error or was cancelled."

      - name: Notify Slack Channel (Composite)
        if: always()
        uses: ./.github/actions/send-slack-message-to-internal-channels
        with:
          scope: Mobile
          build-outcome: ${{ steps.build_and_test.outcome }}
          target-test-group: ${{ env.TARGET_TEST_GROUP }}
          target-app-platform: ${{ env.TARGET_APP_PLATFORM }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
        env:
          GHA_VARS_JSON: ${{ toJson(vars) }}