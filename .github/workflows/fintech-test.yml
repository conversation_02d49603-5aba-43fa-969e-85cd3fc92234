name: FinTech Test

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the target environment'
        required: true
        default: 'testing'
        type: choice
        options:
          - testing
          - integration
      test_country_code:
        description: 'Select the test country code'
        required: true
        default: 'EG'
        type: choice
        options:
          - EG
          - KSA
      target_test_group:
        description: 'Type a group name for the tests.'
        required: true
        default: 'payment'
        type: choice
        options:
          - payment
          - card
          - fintech-test
          - payment-api-smoke
  repository_dispatch:
    types: [ fintech-test ]

jobs:
  fintech-test-check:
    name: Fintech Test - ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }} - Target Test group (${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'payment' }})
    runs-on: ubuntu-latest
    environment: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }}
    env:
      SELECTED_ENV: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }}
      TEST_COUNTRY_CODE: ${{ github.event.client_payload.test_country_code || github.event.inputs.test_country_code || 'EG' }}
      TESTMO_URL: ${{ secrets.TESTMO_URL }}
      TESTMO_TOKEN: ${{ secrets.TESTMO_TOKEN }}
      TRIGGERING_REPO_OWNER: ${{ github.event.client_payload.triggering_repo_owner || 'undefined' }}
      TRIGGERING_REPO: ${{ github.event.client_payload.triggering_repo || 'undefined' }}
      TRIGGERING_SHA_ID: ${{ github.event.client_payload.sha_id || 'undefined' }}
      TARGET_TEST_GROUP: ${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'payment' }}

    steps:
      - name: Validate Inputs and Values
        run: |
          # Validate presence of required fields for repository_dispatch
          if [ "${{ github.event_name }}" == "repository_dispatch" ]; then
            if [ -z "${{ github.event.client_payload.triggering_repo_owner }}" ] || \
               [ -z "${{ github.event.client_payload.triggering_repo }}" ] || \
               [ -z "${{ github.event.client_payload.sha_id }}" ]; then
              echo "Error: Required fields in client_payload are missing for repository_dispatch!"
              echo "Triggering Repo Owner: '${{ github.event.client_payload.triggering_repo_owner }}'"
              echo "Triggering Repo: '${{ github.event.client_payload.triggering_repo }}'"
              echo "Triggering SHA ID: '${{ github.event.client_payload.sha_id }}'"
              exit 1
            fi
          fi
          # Validate environment
          if [[ "${{ env.SELECTED_ENV }}" != "testing" && \
                "${{ env.SELECTED_ENV }}" != "integration" ]]; then
            echo "Error: Invalid environment '${{ env.SELECTED_ENV }}'. Must be one of: testing, integration"
            exit 1
          fi
          # Validate test_country_code
          if [[ "${{ env.TEST_COUNTRY_CODE }}" != "EG" && \
                "${{ env.TEST_COUNTRY_CODE }}" != "KSA" ]]; then
            echo "Error: Invalid test_country_code '${{ env.TEST_COUNTRY_CODE }}'. Must be one of: EG, KSA."
            exit 1
          fi
          # Validate TARGET_TEST_GROUP       
          if [[ "${{ env.TARGET_TEST_GROUP }}" != "payment" && \
                "${{ env.TARGET_TEST_GROUP }}" != "card" && \
                "${{ env.TARGET_TEST_GROUP }}" != "payment-api-smoke" && \
                "${{ env.TARGET_TEST_GROUP }}" != "fintech-test" ]]; then
            echo "Error: Invalid target_test_group '${{ env.TARGET_TEST_GROUP }}'. Must be one of: payment, card, payment-api-smoke and fintech-test."
            exit 1
          fi
          echo "All validations passed successfully!"
        shell: bash

      - name: Checkout code
        if: ${{ always() }}
        uses: actions/checkout@v4

      - name: Create Pending Status Check (composite)
        if: ${{ github.event_name == 'repository_dispatch' && env.TRIGGERING_SHA_ID != '' && env.TRIGGERING_SHA_ID != 'undefined' }}
        uses: ./.github/actions/create-pending-check-on-pr
        with:
          repo-owner: ${{ env.TRIGGERING_REPO_OWNER }}
          repo-name: ${{ env.TRIGGERING_REPO }}
          run-id: ${{ github.run_id }}
          triggering-sha-id: ${{ env.TRIGGERING_SHA_ID }}
          gh-pat: ${{ secrets.GH_PAT }}
          description: "FinTech Tests are in progress."
          title: "Fintech Tests Result"

      - name: Setup Runtime (composite)
        uses: ./.github/actions/setup-machine

      - name: Configs Setup (composite)
        uses: ./.github/actions/configs-setup
        with:
          target-environment: ${{ env.SELECTED_ENV }}
          test-country-code: ${{ env.TEST_COUNTRY_CODE }}
        env:
          GHA_VARS_JSON: ${{ toJson(vars) }}
          GHA_SECRETS_JSON: ${{ toJson(secrets) }}

      - name: Install testmo CLI
        uses: ./.github/actions/testmo-setup
        with:
          working_directory: .
          version: "latest"

      - name: Build with Maven and Execute Tests
        id: build_and_test
        run: |
          if [[ -n "${{ env.TARGET_TEST_GROUP }}" && "${{ env.TARGET_TEST_GROUP }}" != "undefined" ]]; then
            npx testmo automation:run:submit \
              --instance "$TESTMO_URL" \
              --project-id 9 \
              --name "GitHub Actions Fintech Test Run" \
              --source "CardActivationTests" \
              --results ${{ github.workspace }}/target/surefire-reports/*.xml \
              -- mvn test -q -Pfintech-tests -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }} -Dgroups=${{ env.TARGET_TEST_GROUP }}
          else
            npx testmo automation:run:submit \
              --instance "$TESTMO_URL" \
              --project-id 9 \
              --name "GitHub Actions Fintech Test Run" \
              --source "CardActivationTests" \
              --results ${{ github.workspace }}/target/surefire-reports/*.xml \
              -- mvn test -q -Pfintech-tests -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }}
          fi  

      - name: Publish test artifacts (composite)
        if: ${{ always() }}
        uses: ./.github/actions/generate-reports
        with:
          working-directory: .
          allure_artifact_name: allure-reports
          allure_path: target/site/allure-maven-plugin
          upload_screenshots: "true"
          screenshots_artifact_name: screenshots
          screenshots_path: resources/screenshots/**
          screenshots_if_no_files_found: ignore
          screenshots_retention_days: "14"
          testng_artifact_name: testng-reports
          testng_path: target/surefire-reports

      - name: Upload Allure Test Results to Browserstack
        if: always()
        run: |
          curl -u "${{ secrets.BROWSERSTACK_TEST_OBSERVABILITY_KEY }}" -vvv \
          -X POST \
          -F "data=@allure-results.zip" \
          -F "projectName=Fintech" \
          -F "buildName=Default Fintech Build" \
          -F "tags=Smoke" \
          -F "format=allure" \
          https://upload-observability.browserstack.com/upload

      - name: Check Test Results
        if: always()
        id: check_test_results
        run: |
          if [ "${{ steps.build_and_test.outcome }}" == "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
          fi

      - name: Report Status Back to Triggering Repository (Composite)
        if: ${{ github.event_name == 'repository_dispatch' && env.TRIGGERING_SHA_ID != '' && env.TRIGGERING_SHA_ID != 'undefined' }}
        uses: ./.github/actions/update-existing-check-on-pr
        with:
          repo-owner: ${{ env.TRIGGERING_REPO_OWNER }}
          repo-name: ${{ env.TRIGGERING_REPO }}
          run-id: ${{ github.run_id }}
          triggering-sha-id: ${{ env.TRIGGERING_SHA_ID }}
          gh-pat: ${{ secrets.GH_PAT }}
          title: "Fintech Tests Result"
          build-outcome: ${{ steps.build_and_test.outcome }}
          success-description: "Fintech Test passed successfully."
          failure-description: "Fintech Test failed."
          error-description: "Fintech Test encountered an error or was cancelled."

      - name: Notify Slack Channel (Composite)
        if: always()
        uses: ./.github/actions/send-slack-message-to-internal-channels
        with:
          scope: Fintech
          build-outcome: ${{ steps.build_and_test.outcome }}
          target-test-group: ${{ env.TARGET_TEST_GROUP }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
        env:
          GHA_VARS_JSON: ${{ toJson(vars) }}
