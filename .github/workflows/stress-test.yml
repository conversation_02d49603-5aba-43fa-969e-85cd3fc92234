name: Stress test Workflow

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the target environment'
        required: true
        default: 'testing'
        type: choice
        options:
          - testing
          - integration
          - production
      test_country_code:
        description: 'Select the test country code'
        required: true
        default: 'EG'
        type: choice
        options:
          - EG
          - KSA
      threads_count:
        description: 'Type the number of threads to run in parallel'
        required: true
        default: 35
        type: number
      test_type:
        description: 'Select the test to run'
        required: true
        default: 'create_order'
        type: choice
        options:
          - create_order
          - chatbot
          - chatbot_with_predefined_tokens
          - freshchat_api_performance_test
          - freshchat_api_performance_test_with_predefined_tokens

jobs:
  build-and-test:
    name: Stress Test - ${{ github.event.inputs.environment || 'integration' }} - ${{ github.event.inputs.test_type || 'create_order' }}
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'testing' }}
    env:
      SELECTED_ENV: ${{ github.event.inputs.environment || 'testing' }}
      TEST_COUNTRY_CODE: ${{ github.event.inputs.test_country_code || 'EG' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Runtime (composite)
        uses: ./.github/actions/setup-machine

      - name: Configs Setup (composite)
        uses: ./.github/actions/configs-setup
        with:
          target-environment: ${{ env.SELECTED_ENV }}
          test-country-code: ${{ env.TEST_COUNTRY_CODE }}
        env:
          GHA_VARS_JSON: ${{ toJson(vars) }}
          GHA_SECRETS_JSON: ${{ toJson(secrets) }}

      - name: Set execute permission for the script
        run: chmod +x ./resources/scripts/generate_csv.sh

      - name: Generate CSV file
        run: ./resources/scripts/generate_csv.sh ${{ github.event.inputs.threads_count || 35 }}

      - name: Build with Maven and Execute Tests
        run: |
          if [ "${{ github.event.inputs.test_type }}" == "create_order" ]; then
            mvn test -q -Pstress-test -DtestType=create_order -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }}
          elif [ "${{ github.event.inputs.test_type }}" == "chatbot" ]; then
            mvn test -q -Pstress-tests -DtestType=chatbot -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }}
          elif [ "${{ github.event.inputs.test_type }}" == "chatbot_with_predefined_tokens" ]; then
            mvn test -q -Pstress-tests -DtestType=chatbot_with_predefined_tokens -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }}
          elif [ "${{ github.event.inputs.test_type }}" == "freshchat_api_performance_test" ]; then
            mvn test -q -Pstress-tests -DtestType=freshchat_api_performance_test -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }}
          elif [ "${{ github.event.inputs.test_type }}" == "freshchat_api_performance_test_with_predefined_tokens" ]; then
            mvn test -q -Pstress-tests -DtestType=freshchat_api_performance_test_with_predefined_tokens -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }}
          fi

      - name: Publish test artifacts (composite)
        if: ${{ always() }}
        uses: ./.github/actions/generate-reports
        with:
          working-directory: .
          allure_artifact_name: allure-reports
          allure_path: target/site/allure-maven-plugin
          upload_screenshots: "true"
          screenshots_artifact_name: screenshots
          screenshots_path: resources/screenshots/**
          screenshots_if_no_files_found: ignore
          screenshots_retention_days: "14"
          testng_artifact_name: testng-reports
          testng_path: target/surefire-reports

      - name: Check Test Results
        id: check_test_results
        run: |
          echo "status=$(grep -E 'Tests run: [0-9]+, Failures: [1-9]+' target/surefire-reports/*.txt | wc -l)" >> $GITHUB_OUTPUT
