name: Close stale pull requests

on:
  workflow_dispatch:

  schedule:
    # run once per day at midnight UTC
    - cron:  '0 0 * * *'

jobs:
  close-stale-prs:
    runs-on: ubuntu-latest
    steps:
      - name: Close PRs with no activity for >30 days
        uses: actions/stale@v5
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          only-pr: true
          days-before-stale: 30
          days-before-close: 0
          delete-branch: true
          close-pr-message: |
            This pull request has been automatically closed because there has
            been no activity for over 30 days. If you’d still like to pursue
            this change, please feel free to reopen or open a new PR.
          stale-pr-label: 'stale'
          operations-per-run: 50
