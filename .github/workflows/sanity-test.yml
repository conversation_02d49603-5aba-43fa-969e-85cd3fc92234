name: Sanity Test

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the target environment'
        required: true
        default: 'integration'
        type: choice
        options:
          - testing
          - integration
          - huawei-testing
      test_country_code:
        description: 'Select the test country code'
        required: true
        default: 'EG'
        type: choice
        options:
          - EG
          - KSA
      target_test_group:
        description: 'Type a group name for the tests. If you want the full tests in the test tag to run, leave empty'
        required: false
        default: ''
      use_order_api_v2:
        description: 'Flag to use Order API V2'
        required: true
        default: true
        type: boolean
      is_wind_tunnel_enabled:
        description: 'Flag to enable Wind Tunnel mode'
        required: true
        default: false
        type: boolean
  schedule:
    - cron: '0 5 * * *' # Runs every day at 5 a.m. UTC 8 a.m. Cairo Local Time
  repository_dispatch:
    types: [ sanity-test ]

#  schedule:
#    # Runs every 1 hour from 7 AM to 8 PM - UTC Timezone
#    - cron: '0 8-20 * * 0-4'

jobs:
  sanity-test-check:
    name: Sanity Test - ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }} - Order API V2 (${{ github.event.client_payload.use_order_api_v2 || github.event.inputs.use_order_api_v2 || 'true' }}) - Target Test group (${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'undefined' }}) - Wind Tunnel Mode (${{ github.event.client_payload.is_wind_tunnel_enabled || github.event.inputs.is_wind_tunnel_enabled || 'false' }})
    runs-on: ubuntu-latest
    environment: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }}
    env:
      SELECTED_ENV: ${{ github.event.client_payload.environment || github.event.inputs.environment || 'integration' }}
      USE_ORDER_API_V2: ${{ github.event.client_payload.use_order_api_v2 || github.event.inputs.use_order_api_v2 || 'true' }}
      TEST_COUNTRY_CODE: ${{ github.event.client_payload.test_country_code || github.event.inputs.test_country_code || 'EG' }}
      TESTMO_URL: ${{ secrets.TESTMO_URL }}
      TESTMO_TOKEN: ${{ secrets.TESTMO_TOKEN }}
      TRIGGERING_REPO_OWNER: ${{ github.event.client_payload.triggering_repo_owner || 'undefined' }}
      TRIGGERING_REPO: ${{ github.event.client_payload.triggering_repo || 'undefined' }}
      TRIGGERING_SHA_ID: ${{ github.event.client_payload.sha_id || 'undefined' }}
      TARGET_TEST_GROUP: ${{ github.event.client_payload.target_test_group || github.event.inputs.target_test_group || 'undefined' }}
      IS_WIND_TUNNEL_ENABLED: ${{ github.event.client_payload.is_wind_tunnel_enabled || github.event.inputs.is_wind_tunnel_enabled || 'false' }}

    steps:
      - name: Validate Inputs and Values
        shell: bash
        run: |
          # Validate presence of required fields for repository_dispatch
          if [ "${{ github.event_name }}" == "repository_dispatch" ]; then
            if [ -z "${{ github.event.client_payload.triggering_repo_owner }}" ] || \
               [ -z "${{ github.event.client_payload.triggering_repo }}" ]; then
              echo "Error: Required fields in client_payload are missing for repository_dispatch!"
              echo "Triggering Repo Owner: '${{ github.event.client_payload.triggering_repo_owner }}'"
              echo "Triggering Repo: '${{ github.event.client_payload.triggering_repo }}'"
              echo "Triggering SHA ID: '${{ github.event.client_payload.sha_id }}'"
              exit 1
            fi
          fi
          # Validate environment
          if [[ "${{ env.SELECTED_ENV }}" != "testing" && \
                "${{ env.SELECTED_ENV }}" != "huawei-testing" && \
                "${{ env.SELECTED_ENV }}" != "integration" ]]; then
            echo "Error: Invalid environment '${{ env.SELECTED_ENV }}'. Must be one of: testing, integration"
            exit 1
          fi
          # Validate use_order_api_v2
          if [[ "${{ env.USE_ORDER_API_V2 }}" != "true" && \
                "${{ env.USE_ORDER_API_V2 }}" != "false" ]]; then
            echo "Error: Invalid use_order_api_v2 '${{ env.USE_ORDER_API_V2 }}'. Must be true or false."
            exit 1
          fi
          # Validate test_country_code
          if [[ "${{ env.TEST_COUNTRY_CODE }}" != "EG" && \
                "${{ env.TEST_COUNTRY_CODE }}" != "KSA" ]]; then
            echo "Error: Invalid test_country_code '${{ env.TEST_COUNTRY_CODE }}'. Must be one of: EG, KSA."
            exit 1
          fi
          # Validate is_wind_tunnel_enabled
          if [[ "${{ env.IS_WIND_TUNNEL_ENABLED }}" != "true" && \
                "${{ env.IS_WIND_TUNNEL_ENABLED }}" != "false" ]]; then
            echo "Error: Invalid is_wind_tunnel_enabled '${{ env.IS_WIND_TUNNEL_ENABLED }}'. Must be true or false."
            exit 1
          fi
          echo "All validations passed successfully!"
          
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create Pending Status Check (composite)
        if: ${{ github.event_name == 'repository_dispatch' && env.TRIGGERING_SHA_ID != '' && env.TRIGGERING_SHA_ID != 'undefined' }}
        uses: ./.github/actions/create-pending-check-on-pr
        with:
          repo-owner: ${{ env.TRIGGERING_REPO_OWNER }}
          repo-name: ${{ env.TRIGGERING_REPO }}
          run-id: ${{ github.run_id }}
          triggering-sha-id: ${{ env.TRIGGERING_SHA_ID }}
          gh-pat: ${{ secrets.GH_PAT }}
          description: "Sanity Tests are in progress."
          title: "Sanity Tests Result"

      - name: Send Slack message for Wind Tunnel Start
        if: ${{ env.IS_WIND_TUNNEL_ENABLED == 'true' }}
        uses: slackapi/slack-github-action@v2.0.0
        with:
          method: chat.postMessage
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          payload: |
            channel: ${{ vars.WINDTUNNEL_AUTOMATION_UPDATES_PIPELINE_CHANNEL_ID }}
            text: "Automation run has started with windTunnel mode set to: ${{ env.IS_WIND_TUNNEL_ENABLED }}. <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|URL>"

      - name: Setup Runtime (composite)
        uses: ./.github/actions/setup-machine

      - name: Configs Setup (composite)
        uses: ./.github/actions/configs-setup
        with:
          target-environment: ${{ env.SELECTED_ENV }}
          test-country-code: ${{ env.TEST_COUNTRY_CODE }}
        env:
          GHA_VARS_JSON:    ${{ toJson(vars) }}
          GHA_SECRETS_JSON: ${{ toJson(secrets) }}

      - name: Datadog Setup
        uses: ./.github/actions/datadog-setup
        with:
          datadog_api_key: ${{ secrets.DATADOG_API_KEY }}
          dd_site: datadoghq.com
          dd_agent_major: "7"
          enable_log_collection: "true"
          wait_after_start_seconds: "10"
          service: "test-automation-sanity-suite-service"
          tags: "env:githubciSanity,team:qa"

      - name: Testmo Setup
        uses: ./.github/actions/testmo-setup
        with:
          working_directory: .
          version: "latest"

      - name: Build with Maven and Execute Tests
        id: build_and_test
        run: |
          export DD_ENV=githubciSanity
          export DD_SERVICE=test-automation-sanity-suite-service
          export DD_LOGS_INJECTION=true
          export DD_TRACE_SAMPLE_RATE=1.0
          export DD_TRACE_ENABLED=true
          export DD_AGENT_HOST=localhost
          export DD_TRACE_DEBUG=false
          export DD_JAVA_AGENT_JAR=dd-java-agent.jar
          curl -L -o $DD_JAVA_AGENT_JAR 'https://dtdg.co/latest-java-tracer'
          if [[ -n "${{ env.TARGET_TEST_GROUP }}" && "${{ env.TARGET_TEST_GROUP }}" != "undefined" ]]; then
            MAVEN_OPTS="-javaagent:./$DD_JAVA_AGENT_JAR" mvn test -q -Psanity-tests -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }} -Duse.order.api.v2=${{ env.USE_ORDER_API_V2 }} -Dgroups=${{ env.TARGET_TEST_GROUP }} -Dlogback.configurationFile=src/main/resources/logback.xml
          else
            MAVEN_OPTS="-javaagent:./$DD_JAVA_AGENT_JAR" mvn test -q -Psanity-tests -Dmaven.test.failure.ignore=true -Denv=${{ env.SELECTED_ENV }} -Duse.order.api.v2=${{ env.USE_ORDER_API_V2 }} -Dlogback.configurationFile=src/main/resources/logback.xml
          fi
          npx testmo automation:run:submit \
            --instance "$TESTMO_URL" \
            --project-id 9 \
            --name "GitHub Actions Sanity Test Run" \
            --source "OrderSanityTests" \
            --results ${{ github.workspace }}/target/surefire-reports/*.xml

      - name: Print test.log after Build and Tests
        if: always()
        run: |
          echo "===== Content of test.log after Build and Tests ====="
          cat /home/<USER>/work/QA_Automation_Framework/QA_Automation_Framework/logs/test.log || echo "test.log not found"
          echo "===================================================="

      - name: Publish test artifacts (composite)
        if: ${{ always() }}
        uses: ./.github/actions/generate-reports
        with:
          working-directory: .
          allure_artifact_name: allure-reports
          allure_path: target/site/allure-maven-plugin
          upload_screenshots: "true"
          screenshots_artifact_name: screenshots
          screenshots_path: resources/screenshots/**
          screenshots_if_no_files_found: ignore
          screenshots_retention_days: "14"
          testng_artifact_name: testng-reports
          testng_path: target/surefire-reports

      - name: Upload Allure Test Results to Browserstack
        if: always()
        run: |
          curl -u "${{ secrets.BROWSERSTACK_TEST_OBSERVABILITY_KEY }}" -vvv \
          -X POST \
          -F "data=@allure-results.zip" \
          -F "projectName=Sanity" \
          -F "buildName=Default Sanity Build" \
          -F "tags=Smoke" \
          -F "format=allure" \
          https://upload-observability.browserstack.com/upload

      - name: Check Test Results
        if: always()
        id: check_test_results
        run: |
          if [ "${{ steps.build_and_test.outcome }}" == "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
          fi

      - name: Report Status Back to Triggering Repository (composite)
        if: ${{ github.event_name == 'repository_dispatch' && env.TRIGGERING_SHA_ID != '' && env.TRIGGERING_SHA_ID != 'undefined' }}
        uses: ./.github/actions/update-existing-check-on-pr
        with:
          repo-owner: ${{ env.TRIGGERING_REPO_OWNER }}
          repo-name: ${{ env.TRIGGERING_REPO }}
          run-id: ${{ github.run_id }}
          triggering-sha-id: ${{ env.TRIGGERING_SHA_ID }}
          gh-pat: ${{ secrets.GH_PAT }}
          title: "Sanity Tests Result"
          build-outcome: ${{ steps.build_and_test.outcome }}
          success-description: "Sanity Tests passed successfully."
          failure-description: "Sanity Tests failed."
          error-description: "Sanity Tests encountered an error or was cancelled."

      - name: Notify Slack Channel (Composite)
        if: always()
        uses: ./.github/actions/send-slack-message-to-internal-channels
        with:
          scope: Sanity
          build-outcome: ${{ steps.build_and_test.outcome }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
        env:
          GHA_VARS_JSON: ${{ toJson(vars) }}
