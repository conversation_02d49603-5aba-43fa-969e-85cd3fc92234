name: Setup Machine
description: Install toolchains

inputs:
  java-version:      { required: false, default: "25" }
  java-distribution: { required: false, default: "oracle" }
  maven-version:     { required: false, default: "3.9.9" }
  node-version:      { required: false, default: "22.18.0" }

runs:
  using: "composite"
  steps:
    - name: Set up JDK 25
      uses: actions/setup-java@v4
      with:
        java-version: ${{ inputs.java-version }}
        distribution: ${{ inputs.java-distribution }}
        cache: maven

    - name: Set up Maven
      uses: stCarolas/setup-maven@v5
      with:
        maven-version: ${{ inputs.maven-version }}

    - name: Install Node.js and npm
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node-version }}

    - name: Add Google Chrome APT repo
      shell: bash
      run: |
        wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
        echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list

    - name: Update applications index on Ubuntu
      shell: bash
      run: |
        export DEBIAN_FRONTEND=noninteractive
        sudo apt-get update -yq

    - name: Install libxml2-utils
      shell: bash
      run: |
        sudo apt-get install -yq --no-install-recommends libxml2-utils

    - name: Install Google Chrome
      shell: bash
      run: |
        sudo apt-get install -yq --no-install-recommends google-chrome-stable

    - name: Install unzip tool
      shell: bash
      run: |
        sudo apt-get install -yq --no-install-recommends unzip

    - name: Install OpenSSH client
      shell: bash
      run: |
        sudo apt-get install -yq --no-install-recommends openssh-client

    - name: Install MySQL client
      shell: bash
      run: |
        sudo apt-get install -yq --no-install-recommends mysql-client

    - name: Install ffmpeg
      shell: bash
      run: |
        sudo apt-get install -yq --no-install-recommends ffmpeg

    - name: Fetch Latest Stable Chrome Version
      shell: bash
      run: |
        LATEST_STABLE_CHROME_VERSION=$(curl -s https://googlechromelabs.github.io/chrome-for-testing/LATEST_RELEASE_STABLE)
        echo "LATEST_STABLE_CHROME_VERSION=$LATEST_STABLE_CHROME_VERSION" >> $GITHUB_ENV

    - name: Download ChromeDriver Matching Latest Stable Chrome
      shell: bash
      run: |
        mkdir -p resources/webDrivers
        CHROMEDRIVER_URL="https://storage.googleapis.com/chrome-for-testing-public/${{ env.LATEST_STABLE_CHROME_VERSION }}/linux64/chromedriver-linux64.zip"
        curl -s -o chromedriver.zip "$CHROMEDRIVER_URL"
        unzip -q chromedriver.zip -d resources/webDrivers
        mv resources/webDrivers/chromedriver-linux64/chromedriver resources/webDrivers/
        chmod +x resources/webDrivers/chromedriver
        ls -la resources/webDrivers/

    - name: Install Appium
      shell: bash
      run: npm install -g appium@latest

    - name: Install Appium drivers
      shell: bash
      run: |
        appium driver install uiautomator2
        appium driver install xcuitest

    - name: Install Allure CLI
      shell: bash
      run: |
        wget -q -O /tmp/allure.zip https://github.com/allure-framework/allure2/releases/download/2.13.8/allure-2.13.8.zip
        sudo unzip -q /tmp/allure.zip -d /opt/
        sudo ln -sf /opt/allure-2.13.8/bin/allure /usr/bin/allure
