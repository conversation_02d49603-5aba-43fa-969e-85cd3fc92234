name: BrowserStack App ID Override
description: Update BrowserStack app id/build number in browserStackConfigs.properties

inputs:
  target-app-platform:
    description: "android | ios"
    required: true
  target-app-name:
    description: "customerAppNative | customerAppReactNative | midMileApp | fleetApp"
    required: true
  target-app-id:
    description: "bs://... or a real app id"
    required: false
    default: ""
  target-app-build-number:
    description: "Build string (optional)"
    required: false
    default: ""

runs:
  using: composite
  steps:
    - name: Override BrowserStack App ID in config
      if: ${{ inputs.target-app-id != '' && inputs.target-app-id != 'undefined' && inputs.target-app-id != 'bs://' }}
      shell: bash
      run: |
        PLATFORM="${{ inputs.target-app-platform }}"
        APP_NAME="${{ inputs.target-app-name }}"
        APP_ID="${{ inputs.target-app-id }}"
        BUILD="${{ inputs.target-app-build-number }}"

        case "$PLATFORM" in
          android)
            case "$APP_NAME" in
              customerAppNative)
                sed -i'' \
                  -e "s|^bStackAndroidCustomerAppNativeApp=.*|bStackAndroidCustomerAppNativeApp=${APP_ID}|" \
                  -e "s|^bStackAndroidCustomerAppNativeAppBuildNumber=.*|bStackAndroidCustomerAppNativeAppBuildNumber=${BUILD}|" \
                  resources/environments/browserStackConfigs.properties
                ;;
              customerAppReactNative)
                sed -i'' \
                  -e "s|^bStackAndroidCustomerAppReactNativeApp=.*|bStackAndroidCustomerAppReactNativeApp=${APP_ID}|" \
                  -e "s|^bStackAndroidCustomerAppReactNativeAppBuildNumber=.*|bStackAndroidCustomerAppReactNativeAppBuildNumber=${BUILD}|" \
                  resources/environments/browserStackConfigs.properties
                ;;
              midMileApp)
                sed -i'' \
                  -e "s|^bStackAndroidMidMileApp=.*|bStackAndroidMidMileApp=${APP_ID}|" \
                  -e "s|^bStackAndroidMidMileAppBuildNumber=.*|bStackAndroidMidMileAppBuildNumber=${BUILD}|" \
                  resources/environments/browserStackConfigs.properties
                ;;
              fleetApp)
                sed -i'' \
                  -e "s|^bStackAndroidFleetApp=.*|bStackAndroidFleetApp=${APP_ID}|" \
                  -e "s|^bStackAndroidFleetAppBuildNumber=.*|bStackAndroidFleetAppBuildNumber=${BUILD}|" \
                  resources/environments/browserStackConfigs.properties
                ;;
            esac
            ;;
          ios)
            case "$APP_NAME" in
              customerAppNative)
                sed -i'' \
                  -e "s|^bStackIosCustomerAppNativeApp=.*|bStackIosCustomerAppNativeApp=${APP_ID}|" \
                  -e "s|^bStackIosCustomerAppNativeAppBuildNumber=.*|bStackIosCustomerAppNativeAppBuildNumber=${BUILD}|" \
                  resources/environments/browserStackConfigs.properties
                ;;
              customerAppReactNative)
                sed -i'' \
                  -e "s|^bStackIosCustomerAppReactNativeApp=.*|bStackIosCustomerAppReactNativeApp=${APP_ID}|" \
                  -e "s|^bStackIosCustomerAppReactNativeAppBuildNumber=.*|bStackIosCustomerAppReactNativeAppBuildNumber=${BUILD}|" \
                  resources/environments/browserStackConfigs.properties
                ;;
              midMileApp)
                sed -i'' \
                  -e "s|^bStackIosMidMileApp=.*|bStackIosMidMileApp=${APP_ID}|" \
                  -e "s|^bStackIosMidMileAppBuildNumber=.*|bStackIosMidMileAppBuildNumber=${BUILD}|" \
                  resources/environments/browserStackConfigs.properties
                ;;
              fleetApp)
                sed -i'' \
                  -e "s|^bStackIosFleetApp=.*|bStackIosFleetApp=${APP_ID}|" \
                  -e "s|^bStackIosFleetAppBuildNumber=.*|bStackIosFleetAppBuildNumber=${BUILD}|" \
                  resources/environments/browserStackConfigs.properties
                ;;
            esac
            ;;
        esac

    - name: Display BrowserStack config file (sanitized)
      shell: bash
      run: |
        echo "===== browserStackConfigs.properties ====="
        sed -E 's/(password|secret|token|key)=.*/\1=***REDACTED***/Ig' "resources/environments/browserStackConfigs.properties" | cat
