name: Datadog Setup
description: Install & start Datadog Agent; optionally enable log collection

inputs:
  datadog_api_key:
    description: Pass secrets.DATADOG_API_KEY from the caller workflow
    required: true
  dd_site:
    description: Datadog site
    required: false
    default: datadoghq.com
  dd_agent_major:
    description: Datadog Agent major version
    required: false
    default: "7"
  wait_after_start_seconds:
    description: Seconds to wait after starting the agent
    required: false
    default: "10"
  enable_log_collection:
    description: Append logs_enabled as true to datadog.yaml file
    required: false
    default: "true"
  service:
    description: Datadog service name for the tailed log
    required: true
  tags:
    description: Comma-separated tags (e.g., env:githubciSanity,team:qa)
    required: false
    default: "team:qa"

runs:
  using: composite
  steps:
    - name: Install curl if not present
      shell: bash
      run: |
        if ! command -v curl >/dev/null 2>&1; then
          sudo apt-get update -yq
          sudo apt-get install -yq --no-install-recommends curl
        fi

    - name: Install Datadog Agent
      shell: bash
      run: |
        DD_AGENT_MAJOR_VERSION="${{ inputs.dd_agent_major }}" \
        DD_API_KEY="${{ inputs.datadog_api_key }}" \
        DD_SITE="${{ inputs.dd_site }}" \
        bash -c "$(curl -L https://s3.amazonaws.com/dd-agent/scripts/install_script.sh)"

    - name: Start Datadog Agent (explicit)
      shell: bash
      run: |
        sudo systemctl start datadog-agent || sudo service datadog-agent start
        sleep "${{ inputs.wait_after_start_seconds }}"

    - name: Check Datadog Agent Service Status (after start)
      shell: bash
      run: |
        (sudo systemctl status datadog-agent || sudo service datadog-agent status) || true
        sudo journalctl -u datadog-agent | tail -50 || true

    - name: Enable Datadog Log Collection
      if: ${{ inputs.enable_log_collection == 'true' }}
      shell: bash
      run: |
        sudo sh -c "echo 'logs_enabled: true' >> /etc/datadog-agent/datadog.yaml"

    - name: Ensure log file and directory permissions
      shell: bash
      run: |
        sudo mkdir -p /home/<USER>/work/QA_Automation_Framework/QA_Automation_Framework/logs
        sudo touch /home/<USER>/work/QA_Automation_Framework/QA_Automation_Framework/logs/test.log
        sudo chmod o+rwx /home/<USER>
        sudo chmod o+rwx /home/<USER>/work
        sudo chmod o+rwx /home/<USER>/work/QA_Automation_Framework
        sudo chmod o+rwx /home/<USER>/work/QA_Automation_Framework/QA_Automation_Framework
        sudo chmod -R o+rwx /home/<USER>/work/QA_Automation_Framework/QA_Automation_Framework/logs

    - name: Configure Datadog Agent for Log Collection
      shell: bash
      env:
        DD_SERVICE: ${{ inputs.service }}
        DD_TAGS: ${{ inputs.tags }}
      run: |
        sudo mkdir -p /etc/datadog-agent/conf.d/java.d
        sudo tee /etc/datadog-agent/conf.d/java.d/conf.yaml > /dev/null <<EOF
        logs:
          - type: file
            path: /home/<USER>/work/QA_Automation_Framework/QA_Automation_Framework/logs/test.log
            service: ${DD_SERVICE}
            source: java
            sourcecategory: sourcecode
            tags: ${DD_TAGS}
        EOF
        sudo systemctl restart datadog-agent || sudo service datadog-agent restart
        sleep 10

    - name: Check Datadog Agent Logs (after config)
      shell: bash
      run: |
        sudo cat /var/log/datadog/agent.log | tail -100
        sudo datadog-agent status | head -100
