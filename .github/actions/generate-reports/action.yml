name: Post Test Artifacts
description: Generate Allure report and upload artifacts (Allure, screenshots, TestNG)

inputs:
  working-directory:              { required: false, default: . }
  allure_artifact_name:           { required: false, default: allure-reports }
  allure_path:                    { required: false, default: target/site/allure-maven-plugin }
  upload_screenshots:             { required: false, default: "true" }
  screenshots_artifact_name:      { required: false, default: screenshots }
  screenshots_path:               { required: false, default: resources/screenshots/** }
  screenshots_if_no_files_found:  { required: false, default: ignore }
  screenshots_retention_days:     { required: false, default: "14" }
  testng_artifact_name:           { required: false, default: testng-reports }
  testng_path:                    { required: false, default: target/surefire-reports }

runs:
  using: composite
  steps:
    - name: Generate Allure Reports
      if: ${{ always() }}
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: mvn allure:report -ntp

    - name: Generate Allure Reports (CLI)
      if: ${{ always() }}
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: allure generate allure-results --clean

    - name: Archive Allure Reports
      if: ${{ always() }}
      uses: actions/upload-artifact@v4
      with:
        name: ${{ inputs.allure_artifact_name }}
        path: ${{ inputs.allure_path }}

    - name: Upload screenshots
      if: ${{ always() && inputs.upload_screenshots == 'true' }}
      uses: actions/upload-artifact@v4
      with:
        name: ${{ inputs.screenshots_artifact_name }}
        path: ${{ inputs.screenshots_path }}
        if-no-files-found: ${{ inputs.screenshots_if_no_files_found }}
        retention-days: ${{ inputs.screenshots_retention_days }}

    - name: Archive TestNG Reports
      if: ${{ always() }}
      uses: actions/upload-artifact@v4
      with:
        name: ${{ inputs.testng_artifact_name }}
        path: ${{ inputs.testng_path }}

    - name: Zip the Allure Test Results directory
      if: ${{ always() }}
      shell: bash
      working-directory: ${{ inputs.working-directory }}
      run: zip -r allure-results.zip allure-results
