name: Update Existing Check on PR
description: Update a commit status on the triggering repository based on build outcome
inputs:
  repo-owner: { description: "Repository owner (e.g., org/user)", required: true }
  repo-name:  { description: "Repository name (e.g., mobile-app)", required: true }
  run-id:     { description: "github.run_id from caller (for target URL)", required: true }
  triggering-sha-id: { description: "Commit SHA to update the status on", required: true }
  gh-pat:     { description: "Token with repo:status (and repo read) scope", required: true }
  title:      { description: "Status check context/label", required: false, default: "Tests Result" }
  build-outcome: { description: "Outcome from caller (e.g., steps.<id>.outcome)", required: true }
  success-description: { description: "Description when outcome == success", required: false, default: "Tests passed successfully." }
  failure-description: { description: "Description when outcome == failure", required: false, default: "Tests failed" }
  error-description:   { description: "Description when outcome is neither success nor failure", required: false, default: "Tests encountered an error or was cancelled." }

runs:
  using: "composite"
  steps:
    - name: Validate required inputs
      shell: bash
      run: |
        if [ -z "${{ inputs.triggering-sha-id }}" ] || [ "${{ inputs.triggering-sha-id }}" = "undefined" ] || [ "${{ inputs.triggering-sha-id }}" = "null" ]; then
          echo "Error: triggering-sha-id is empty/undefined/null"
          exit 1
        fi
        if [ -z "${{ inputs.repo-owner }}" ] || [ "${{ inputs.repo-owner }}" = "undefined" ] || [ "${{ inputs.repo-owner }}" = "null" ]; then
          echo "Error: repo-owner is empty/undefined/null"
          exit 1
        fi
        if [ -z "${{ inputs.repo-name }}" ] || [ "${{ inputs.repo-name }}" = "undefined" ] || [ "${{ inputs.repo-name }}" = "null" ]; then
          echo "Error: repo-name is empty/undefined/null"
          exit 1
        fi
        if [ -z "${{ inputs.build-outcome }}" ] || [ "${{ inputs.build-outcome }}" = "undefined" ] || [ "${{ inputs.build-outcome }}" = "null" ]; then
          echo "Error: build-outcome is empty/undefined/null"
          exit 1
        fi
        echo "Inputs validations passed successfully!"

    - name: Update Status
      shell: bash
      env:
        REPO_OWNER:  ${{ inputs.repo-owner }}
        REPO_NAME:   ${{ inputs.repo-name }}
        SHA_ID:      ${{ inputs.triggering-sha-id }}
        GH_PAT:      ${{ inputs.gh-pat }}
        RUN_ID:      ${{ inputs.run-id }}
        TITLE:       ${{ inputs.title }}
        OUTCOME:     ${{ inputs.build-outcome }}
        SUCCESS_DESC: ${{ inputs.success-description }}
        FAILURE_DESC: ${{ inputs.failure-description }}
        ERROR_DESC:   ${{ inputs.error-description }}
      run: |
        case "${OUTCOME}" in
          success) STATE="success"; DESCRIPTION="${SUCCESS_DESC}";;
          failure) STATE="failure"; DESCRIPTION="${FAILURE_DESC}";;
          *)       STATE="error";   DESCRIPTION="${ERROR_DESC}";;
        esac

        TARGET_URL="https://github.com/${REPO_OWNER}/${REPO_NAME}/actions/runs/${RUN_ID}"

        curl -s -X POST \
          -H "Authorization: Bearer ${GH_PAT}" \
          -H "Accept: application/vnd.github+json" \
          -d '{
            "state": "'"${STATE}"'",
            "target_url": "'"${TARGET_URL}"'",
            "description": "'"${DESCRIPTION}"'",
            "context": "'"${TITLE}"'"
          }' \
          "https://api.github.com/repos/${REPO_OWNER}/${REPO_NAME}/statuses/${SHA_ID}"
