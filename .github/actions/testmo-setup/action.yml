name: Testmo Setup
description: Install the Testmo CLI via npm and add run metadata

inputs:
  working_directory:
    description: Directory to run npm/npx in
    required: false
    default: "."
  version:
    description: Version of @testmo/testmo-cli (use "latest" for latest)
    required: false
    default: "latest"

runs:
  using: composite
  steps:
    - name: Install testmo CLI
      shell: bash
      working-directory: ${{ inputs.working_directory }}
      run: |
        if [ "${{ inputs.version }}" = "latest" ]; then
          npm install --no-save @testmo/testmo-cli
        else
          npm install --no-save @testmo/testmo-cli@${{ inputs.version }}
        fi

    - name: Add the GitHub run metadata to Testmo resources
      shell: bash
      working-directory: ${{ inputs.working_directory }}
      run: |
        npx testmo automation:resources:add-field --name git --type string \
          --value "${GITHUB_SHA:0:7}" --resources resources.json
        RUN_URL="$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"
        npx testmo automation:resources:add-link --name build \
          --url "$RUN_URL" --resources resources.json
