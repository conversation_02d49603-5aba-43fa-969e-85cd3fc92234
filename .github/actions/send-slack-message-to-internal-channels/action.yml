name: "Slack Notify"
description: "Resolve Slack channel from repo vars, build status message once, and post"
inputs:
  scope:
    description: "sanity | mobile | supply-chain | fintech | fleet-app | referral"
    required: true
  build-outcome:
    description: "success or failure from caller step outcome"
    required: true
  target-test-group:
    description: "Used by mobile / supply-chain / fintech routing"
    required: false
  target-app-platform:
    description: "android | ios (used by mobile routing)"
    required: false
  slack-bot-token:
    description: "Slack bot token to post the message"
    required: true
  send-enabled:
    description: "Flag to enable/disable sending the Slack message"
    required: false
    default: "true"

runs:
  using: "composite"
  steps:
    - name: Load repo vars into env
      shell: bash
      run: |
        python3 - "$GITHUB_ENV" <<'PY'
        import os, sys, json
        out = sys.argv[1]
        data = {}
        try:
          data.update(json.loads(os.environ.get("GHA_VARS_JSON") or "{}"))
        except Exception:
          pass
        with open(out, "a") as f:
          for k, v in data.items():
            v = "" if v is None else str(v)
            if "\n" in v:
              f.write(f"{k}<<EOF\n{v}\nEOF\n")
            else:
              f.write(f"{k}={v}\n")
        PY

    - name: Determine Slack channel
      id: channel
      shell: bash
      env:
        SCOPE: ${{ inputs.scope }}
        TTG: ${{ inputs.target-test-group }}
        PLATFORM: ${{ inputs.target-app-platform }}
      run: |
        set -euo pipefail

        need() { v="${!1:-}"; [ -n "$v" ] || { echo "Missing required var: $1" >&2; exit 1; }; printf '%s' "$v"; }

        case "$SCOPE" in
          Sanity)
            if [[ "${IS_WIND_TUNNEL_ENABLED,,}" == "true" || "${IS_WIND_TUNNEL_ENABLED}" == "1" ]]; then
              CHANNEL_ID="$(need WINDTUNNEL_AUTOMATION_UPDATES_PIPELINE_CHANNEL_ID)"
            else
              CHANNEL_ID="$(need SANITY_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID)"
            fi
            ;;
          Mobile)
            lcttg="$(printf '%s' "${TTG:-}" | tr '[:upper:]' '[:lower:]')"
            if [[ "$lcttg" == *aggregator* ]]; then
              CHANNEL_ID="$(need AGGREGATORS_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID)"
            else
              case "${PLATFORM:-}" in
                android) CHANNEL_ID="$(need MOBILE_ANDROID_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID)";;
                ios)     CHANNEL_ID="$(need MOBILE_IOS_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID)";;
                *) echo "Invalid target_app_platform '${PLATFORM}'. Must be android or ios" >&2; exit 1;;
              esac
            fi
            ;;
          Supply-chain)
            case "${TTG:-}" in
              ops)           KEY=OPS_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID ;;
              logistics)     KEY=LOGISTICS_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID ;;
              inventory)     KEY=INVENTORY_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID ;;
              supply-demand) KEY=SUPPLY_DEMAND_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID ;;
              *) echo "Invalid target_group_runner '${TTG}'. Must be one of: ops, logistics, inventory, supply-demand" >&2; exit 1 ;;
            esac
            CHANNEL_ID="$(need "$KEY")"
            ;;
          Fintech)
            case "${TTG:-}" in
              card)             KEY=CARD_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID ;;
              payment|payment-api-smoke) KEY=PAYMENT_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID ;;
              fintech-test)     KEY=FINTECH_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID ;;
              *) echo "Invalid target_group_runner '${TTG}'. Must be one of: card, payment, payment-api-smoke, fintech-test" >&2; exit 1 ;;
            esac
            CHANNEL_ID="$(need "$KEY")"
            ;;
          Fleet-app)
            CHANNEL_ID="$(need FLEET_APP_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID)"
            ;;
          Referral)
            CHANNEL_ID="$(need REFERRAL_AUTOMATION_PIPELINE_SLACK_CHANNEL_ID)"
            ;;
          *)
            echo "Unknown scope '${SCOPE}'" >&2; exit 1;;
        esac

        echo "id=$CHANNEL_ID" >> "$GITHUB_OUTPUT"

    - name: Build Slack message
      id: message
      shell: bash
      env:
        OUTCOME: ${{ inputs.build-outcome }}
        SCOPE: ${{ inputs.scope }}
        PR_LINK_TEXT: ${{ env.PR_LINK_TEXT }}
      run: |
        ART="<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}#artifacts|Download the reports>"
        if [ "$OUTCOME" = "success" ]; then
          TEXT="${SCOPE} Tests passed successfully. ${ART} ${PR_LINK_TEXT}"
        else
          TEXT="${SCOPE} Tests failed. ${ART} ${PR_LINK_TEXT}"
        fi
        # trim leading/trailing spaces
        TEXT="$(echo "$TEXT" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')"
        echo "text=$TEXT" >> "$GITHUB_OUTPUT"

    - name: Send Slack message
      if: ${{ inputs.send-enabled == 'true' }}
      uses: slackapi/slack-github-action@v2.0.0
      with:
        method: chat.postMessage
        token: ${{ inputs.slack-bot-token }}
        payload: |
          channel: ${{ steps.channel.outputs.id }}
          text: "${{ steps.message.outputs.text }}"
