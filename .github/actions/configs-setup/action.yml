name: Configs Setup
description: Copy templates and inject variables into config files
inputs:
  target-environment: { required: true }
  test-country-code:  { required: true, default: "EG" }

runs:
  using: "composite"
  steps:
    - name: Copy config files
      shell: bash
      run: |
        SELECTED_ENV="${{ inputs.target-environment }}"
        mkdir -p resources/webDrivers resources/builds
        cp resources/environments/config.properties.example "resources/environments/config_${SELECTED_ENV}.properties"
        cp resources/environments/webConfig.properties.example resources/environments/webConfig.properties
        cp resources/environments/cardServiceConfigs.properties.example "resources/environments/cardServiceConfigs_${SELECTED_ENV}.properties"
        cp resources/environments/browserStackConfigs.properties.example resources/environments/browserStackConfigs.properties
        cp resources/environments/iPhone_14.properties.example resources/environments/iPhone_14.properties
        cp resources/environments/Pixel_2.properties.example resources/environments/Pixel_2.properties
        cp resources/environments/cardServiceEncryptionPublicKey.pub.example resources/environments/cardServiceEncryptionPublicKey.pub
        cp resources/environments/dbSshKey.example resources/environments/dbSshKey

    - name: Derive test mobile & company
      shell: bash
      run: |
        TEST_COUNTRY_CODE="${{ inputs.test-country-code }}"
        if [ "$TEST_COUNTRY_CODE" = "EG" ]; then
          echo "test_mobile_number=+20" >> $GITHUB_ENV
          echo "mobile_company=Etisalat" >> $GITHUB_ENV
        else
          echo "test_mobile_number=+966" >> $GITHUB_ENV
          echo "mobile_company=Zain" >> $GITHUB_ENV
        fi

    - name: Load all repo vars & secrets into env
      shell: bash
      run: |
        python3 - "$GITHUB_ENV" <<'PY'
        import os, sys, json
        out = sys.argv[1]
        
        def load(name):
          s = os.environ.get(name)
          if not s:
            return {}
          try:
            return json.loads(s)
          except Exception:
            return {}
        
        merged = {}
        merged.update(load("GHA_VARS_JSON"))
        merged.update(load("GHA_SECRETS_JSON"))
        
        with open(out, "a") as f:
          for k, v in merged.items():
            v = "" if v is None else str(v)
            if "\n" in v:
              f.write(f"{k}<<EOF\n{v}\nEOF\n")
            else:
              f.write(f"{k}={v}\n")
        PY

    - name: Modify config.properties
      shell: bash
      run: |
        SELECTED_ENV="${{ inputs.target-environment }}"
        TEST_COUNTRY_CODE="${{ inputs.test-country-code }}"
        sed -i'' \
          -e "s/^testCountryCode=.*/testCountryCode=$TEST_COUNTRY_CODE/" \
          -e "s/^testMobileNumber=.*/testMobileNumber=${test_mobile_number}/" \
          -e "s/^testMobileCompany=.*/testMobileCompany=${mobile_company}/" \
          -e "s/^testFpName=.*/testFpName=${FP_NAME}/" \
          -e "s/^testFpDate=.*/testFpDate=${FP_DATE}/" \
          -e "s/^testLatitude=.*/testLatitude=${WAREHOUSE_LATITUDE}/" \
          -e "s/^testLongitude=.*/testLongitude=${WAREHOUSE_LONGITUDE}/" \
          -e "s|^testOrderInfo=.*|testOrderInfo=${ORDER_INFO}|" \
          -e "s/^adminBypassScriptPassword=.*/adminBypassScriptPassword=${ADMIN_BYPASS_SCRIPT_PASSWORD}/" \
          -e "s/^adminGmailAddress=.*/adminGmailAddress=${ADMIN_GMAIL_ADDRESS}/" \
          -e "s/^adminGmailPassword=.*/adminGmailPassword=${ADMIN_GMAIL_PASSWORD}/" \
          -e "s/^midMilePassword=.*/midMilePassword=${MID_MILE_PASSWORD}/" \
          -e "s/^ngrokAuthToken=.*/ngrokAuthToken=${NGROK_AUTH_TOKEN}/" \
          -e "s/^pickerPassword=.*/pickerPassword=${PICKER_PASSWORD}/" \
          -e "s/^slackApiToken=.*/slackApiToken=${SLACK_API_TOKEN}/" \
          -e "s/^wpLoggedInCookieName=.*/wpLoggedInCookieName=${WP_LOGGED_IN_COOKIE_NAME}/" \
          -e "s/^wpNodeAuthorizationCookieName=.*/wpNodeAuthorizationCookieName=${WP_NODE_AUTHORIZATION_COOKIE_NAME}/" \
          -e "s/^wpSecCookieName=.*/wpSecCookieName=${WP_SEC_COOKIE_NAME}/" \
          -e "s/^adminLocalPhoneNumber=.*/adminLocalPhoneNumber=${ADMIN_LOCAL_PHONE_NUMBER}/" \
          -e "s/^adminReferralCode=.*/adminReferralCode=${ADMIN_REFERRAL_CODE}/" \
          -e "s/^midMilePhoneNumber=.*/midMilePhoneNumber=${MID_MILE_PHONE_NUMBER}/" \
          -e "s/^pickerPhoneNumber=.*/pickerPhoneNumber=${PICKER_PHONE_NUMBER}/" \
          -e "s|^paymentServiceSecret=.*|paymentServiceSecret=${PAYMENT_SERVICE_SECRET}|" \
          -e "s|^paymentShoppingKey=.*|paymentShoppingKey=${PAYMENT_SHOPPING_KEY}|" \
          -e "s|^paymentTopUpKey=.*|paymentTopUpKey=${PAYMENT_TOPUP_KEY}|" \
          -e "s|^paymentBillingKey=.*|paymentBillingKey=${PAYMENT_BILLING_KEY}|" \
          -e "s|^paymentGratuityKey=.*|paymentGratuityKey=${PAYMENT_GRATUITY_KEY}|" \
          -e "s/^mysqlUserPassword=.*/mysqlUserPassword=${MYSQL_USER_PASSWORD}/" \
          -e "s/^sshPassphrase=.*/sshPassphrase=${SSH_KEY_PASSPHRASE}/" \
          -e "s/^mysqlHost=.*/mysqlHost=${MYSQL_HOST}/" \
          -e "s/^mysqlUserName=.*/mysqlUserName=${MYSQL_USERNAME}/" \
          -e "s/^mysqlDatabaseName=.*/mysqlDatabaseName=${MYSQL_DATABASE_NAME}/" \
          -e "s/^mysqlServerPort=.*/mysqlServerPort=${MYSQL_SERVER_PORT}/" \
          -e "s/^sshConnectionRequired=.*/sshConnectionRequired=${SSH_CONNECTION_REQUIRED}/" \
          -e "s/^sshHost=.*/sshHost=${SSH_HOST}/" \
          -e "s/^sshUserName=.*/sshUserName=${SSH_USERNAME}/" \
          -e "s/^sshPort=.*/sshPort=${SSH_PORT}/" \
          -e "s/^isSshKeyProtected=.*/isSshKeyProtected=${IS_SSH_KEY_PROTECTED}/" \
          -e "s|^sshKeyPath=.*|sshKeyPath=${SSH_KEY_PATH}|" \
          -e "s|^baseURL=.*|baseURL=${BASE_URL}|" \
          -e "s|^controlRoomBaseURL=.*|controlRoomBaseURL=${CONTORL_ROOM_BASE_URL}|" \
          -e "s|^billingServicesBaseURL=.*|billingServicesBaseURL=${BILLING_SERVICE_BASE_URL}|" \
          -e "s|^midMileBaseURL=.*|midMileBaseURL=${MID_MILE_BASE_URL}|" \
          -e "s|^transitBaseURL=.*|transitBaseURL=${TRANSIT_BASE_URL}|" \
          -e "s|^cardServicesBaseURL=.*|cardServicesBaseURL=${CARD_SERVICES_BASE_URL}|" \
          -e "s|^cardServicesAdminPanelBaseURL=.*|cardServicesAdminPanelBaseURL=${CARD_SERVICES_ADMIN_PANEL_BASE_URL}|" \
          -e "s|^pickerServicesBaseURL=.*|pickerServicesBaseURL=${PICKER_SERVICES_BASE_URL}|" \
          -e "s|^logisticsBaseURL=.*|logisticsBaseURL=${LOGISTICS_BASE_URL}|" \
          -e "s|^fleetAppBaseURL=.*|fleetAppBaseURL=${FLEET_APP_BASE_URL}|" \
          -e "s|^fleetServiceBaseURL=.*|fleetServiceBaseURL=${FLEET_SERVICE_BASE_URL}|" \
          -e "s|^paymentServiceBaseURL=.*|paymentServiceBaseURL=${PAYMENT_SERVICE_BASE_URL}|" \
          -e "s|^internalOrdersBaseURL=.*|internalOrdersBaseURL=${INTERNAL_ORDERS_BASE_URL}|" \
          -e "s|^foodAggregatorServiceBaseURL=.*|foodAggregatorServiceBaseURL=${FOOD_AGGREGATOR_BASE_URL}|" \
          -e "s|^catalogBaseURL=.*|catalogBaseURL=${CATALOG_BASE_URL}|" \
          -e "s|^inaiBaseURL=.*|inaiBaseURL=${INAI_BASE_URL}|" \
          -e "s|^stockTakeBaseURL=.*|stockTakeBaseURL=${STOCK_TAKE_BASE_URL}|" \
          -e "s|^freshChatApiBaseURL=.*|freshChatApiBaseURL=${FRESH_CHAT_API_BASE_URL}|" \
          -e "s|^orderServiceBaseURL=.*|orderServiceBaseURL=${ORDER_SERVICE_BASE_URL}|" \
          -e "s|^dAPhoneNumber=.*|dAPhoneNumber=${DA_PHONE_NUMBER}|" \
          -e "s|^dAPhoneCountryCode=.*|dAPhoneCountryCode=${DA_PHONE_COUNTRY_CODE}|" \
          -e "s|^dAPassword=.*|dAPassword=${DA_PASSWORD}|" \
          -e "s|^fpManagerPhoneNumber=.*|fpManagerPhoneNumber=${FP_MANAGER_PHONE_NUMBER}|" \
          -e "s|^fpManagerPhoneCountryCode=.*|fpManagerPhoneCountryCode=${FP_MANAGER_COUNTRY_CODE}|" \
          -e "s|^fpManagerPassword=.*|fpManagerPassword=${FP_MANAGER_PASSWORD}|" \
          -e "s|^paymentPanelEmail=.*|paymentPanelEmail=${PAYMENT_PANEL_EMAIL}|" \
          -e "s|^paymentPanelPassword=.*|paymentPanelPassword=${PAYMENT_PANEL_PASSWORD}|" \
          -e "s|^freshChatApiKey=.*|freshChatApiKey=${FRESH_CHAT_API_KEY}|" \
          -e "s|^supplyChainStaticAuthToken=.*|supplyChainStaticAuthToken=${SUPPLY_CHAIN_STATIC_AUTH_TOKEN}|" \
          -e "s|^freshChatChannelId=.*|freshChatChannelId=${FRESH_CHAT_CHANNEL_ID}|" \
          -e "s|^fallBackProductIds=.*|fallBackProductIds=${FALLBACK_PRODUCT_IDS}|" \
          -e "s|^targetQtyForEachFallBackProduct=.*|targetQtyForEachFallBackProduct=${TARGET_QTY_FOR_EACH_FALLBACK_PRODUCT}|" \
          -e "s|^excludedCategoryIds=.*|excludedCategoryIds=${EXCLUDED_CATEGORY_IDS}|" \
          -e "s|^sharedFacilityCategoryIds=.*|sharedFacilityCategoryIds=${SHARED_FACILITY_CATEGORY_IDS}|" \
          "resources/environments/config_${SELECTED_ENV}.properties"

    - name: Modify cardServiceConfigs.properties
      shell: bash
      run: |
        SELECTED_ENV="${{ inputs.target-environment }}"
        sed -i'' \
          -e "s|^adminUserName=.*|adminUserName=${TESTING_CARDSERVICE_ADMIN_USERNAME}|" \
          -e "s|^adminPassword=.*|adminPassword=${TESTING_CARDSERVICE_ADMIN_PASSWORD}|" \
          -e "s|^loginMobileSchemeUserName=.*|loginMobileSchemeUserName=${TESTING_CARDSERVICE_LOGIN_MOBILE_SCHEME_USER_NAME}|" \
          -e "s|^loginMobileSchemePassword=.*|loginMobileSchemePassword=${TESTING_CARDSERVICE_LOGIN_MOBILE_SCHEME_PASSWORD}|" \
          -e "s|^defaultCardPasscode=.*|defaultCardPasscode=${CARDSERVICE_DEFAULT_PASSCODE}|" \
          -e "s|^cardUserBackTransactionId=.*|cardUserBackTransactionId=${CARDSERVICE_CARD_USER_ID_BACK_TRANSACTION_ID}|" \
          -e "s|^cardUserFrontTransactionId=.*|cardUserFrontTransactionId=${CARDSERVICE_CARD_USER_ID_FRONT_TRANSACTION_ID}|" \
          -e "s|^cardServiceContractNumber=.*|cardServiceContractNumber=${CARDSERVICE_CONTRACT_NUMBER}|" \
          -e "s|^cardServiceProductNumber=.*|cardServiceProductNumber=${CARDSERVICE_PRODUCT_NUMBER}|" \
          -e "s|^cardServiceTypeId=.*|cardServiceTypeId=${CARDSERVICE_TYPE_ID}|" \
          "resources/environments/cardServiceConfigs_${SELECTED_ENV}.properties"
        
    - name: Modify browserStackConfigs.properties
      shell: bash
      run: |
        sed -i'' \
          -e "s|^userName=.*|userName=${BROWSERSTACK_USERNAME}|" \
          -e "s|^accessKey=.*|accessKey=${BROWSERSTACK_ACCESS_KEY}|" \
          -e "s|^debug=.*|debug=${BROWSERSTACK_DEBUG_FLAG}|" \
          -e "s|^networkLogs=.*|networkLogs=${BROWSERSTACK_NETWORKLOGS_FLAG}|" \
          -e "s|^appiumVersionForAndroidTests=.*|appiumVersionForAndroidTests=${BROWSERSTACK_APPIUM_ANDROID_TESTS_VERSION}|" \
          -e "s|^appiumVersionForIosTests=.*|appiumVersionForIosTests=${BROWSERSTACK_APPIUM_IOS_TESTS_VERSION}|" \
          -e "s|^bStackAndroidCustomerAppNativeApp=.*|bStackAndroidCustomerAppNativeApp=${BROWSERSTACK_ANDROID_CUSTOMERAPP_NATIVE_APP}|" \
          -e "s|^bStackAndroidCustomerAppNativeAppBuildNumber=.*|bStackAndroidCustomerAppNativeAppBuildNumber=${BROWSERSTACK_ANDROID_CUSTOMERAPP_NATIVE_APP_BUILD_NUMBER}|" \
          -e "s|^bStackAndroidCustomerAppReactNativeApp=.*|bStackAndroidCustomerAppReactNativeApp=${BROWSERSTACK_ANDROID_CUSTOMERAPP_REACTNATIVE_APP}|" \
          -e "s|^bStackAndroidCustomerAppReactNativeAppBuildNumber=.*|bStackAndroidCustomerAppReactNativeAppBuildNumber=${BROWSERSTACK_ANDROID_CUSTOMERAPP_REACTNATIVE_APP_BUILD_NUMBER}|" \
          -e "s|^bStackAndroidMidMileApp=.*|bStackAndroidMidMileApp=${BROWSERSTACK_ANDROID_MIDMILE_APP}|" \
          -e "s|^bStackAndroidMidMileAppBuildNumber=.*|bStackAndroidMidMileAppBuildNumber=${BROWSERSTACK_ANDROID_MIDMILE_APP_BUILD_NUMBER}|" \
          -e "s|^bStackAndroidFleetApp=.*|bStackAndroidFleetApp=${BROWSERSTACK_ANDROID_FLEET_APP}|" \
          -e "s|^bStackAndroidFleetAppBuildNumber=.*|bStackAndroidFleetAppBuildNumber=${BROWSERSTACK_ANDROID_FLEET_APP_BUILD_NUMBER}|" \
          -e "s|^androidPlatformVersion=.*|androidPlatformVersion=${BROWSERSTACK_ANDROID_PLATFORM_VERSION}|" \
          -e "s|^androidDeviceName=.*|androidDeviceName=${BROWSERSTACK_ANDROID_DEVICE_NAME}|" \
          -e "s|^androidPlatformVersionDevice2=.*|androidPlatformVersionDevice2=${BROWSERSTACK_ANDROID_PLATFORM_VERSION_DEVICE_2}|" \
          -e "s|^androidDeviceName2=.*|androidDeviceName2=${BROWSERSTACK_ANDROID_DEVICE_NAME_2}|" \
          -e "s|^androidPlatformVersionDevice3=.*|androidPlatformVersionDevice3=${BROWSERSTACK_ANDROID_PLATFORM_VERSION_DEVICE_3}|" \
          -e "s|^androidDeviceName3=.*|androidDeviceName3=${BROWSERSTACK_ANDROID_DEVICE_NAME_3}|" \
          -e "s|^androidPlatformVersionDevice4=.*|androidPlatformVersionDevice4=${BROWSERSTACK_ANDROID_PLATFORM_VERSION_DEVICE_4}|" \
          -e "s|^androidDeviceName4=.*|androidDeviceName4=${BROWSERSTACK_ANDROID_DEVICE_NAME_4}|" \
          -e "s|^bStackIosCustomerAppNativeApp=.*|bStackIosCustomerAppNativeApp=${BROWSERSTACK_IOS_CUSTOMERAPP_NATIVE_APP}|" \
          -e "s|^bStackIosCustomerAppNativeAppBuildNumber=.*|bStackIosCustomerAppNativeAppBuildNumber=${BROWSERSTACK_IOS_CUSTOMERAPP_NATIVE_APP_BUILD_NUMBER}|" \
          -e "s|^bStackIosCustomerAppReactNativeApp=.*|bStackIosCustomerAppReactNativeApp=${BROWSERSTACK_IOS_CUSTOMERAPP_REACTNATIVE_APP}|" \
          -e "s|^bStackIosCustomerAppReactNativeAppBuildNumber=.*|bStackIosCustomerAppReactNativeAppBuildNumber=${BROWSERSTACK_IOS_CUSTOMERAPP_REACTNATIVE_APP_BUILD_NUMBER}|" \
          -e "s|^bStackIosMidMileApp=.*|bStackIosMidMileApp=${BROWSERSTACK_IOS_MIDMILE_APP}|" \
          -e "s|^bStackIosMidMileAppBuildNumber=.*|bStackIosMidMileAppBuildNumber=${BROWSERSTACK_IOS_MIDMILE_APP_BUILD_NUMBER}|" \
          -e "s|^bStackIosFleetApp=.*|bStackIosFleetApp=${BROWSERSTACK_IOS_FLEET_APP}|" \
          -e "s|^bStackIosFleetAppBuildNumber=.*|bStackIosFleetAppBuildNumber=${BROWSERSTACK_IOS_FLEET_APP_BUILD_NUMBER}|" \
          -e "s|^iosPlatformVersion=.*|iosPlatformVersion=${BROWSERSTACK_IOS_PLATFORM_VERSION}|" \
          -e "s|^iosDeviceName=.*|iosDeviceName=${BROWSERSTACK_IOS_DEVICE_NAME}|" \
          -e "s|^iosPlatformVersionDevice2=.*|iosPlatformVersionDevice2=${BROWSERSTACK_IOS_PLATFORM_VERSION_DEVICE_2}|" \
          -e "s|^iosDeviceName2=.*|iosDeviceName2=${BROWSERSTACK_IOS_DEVICE_NAME_2}|" \
          -e "s|^iosPlatformVersionDevice3=.*|iosPlatformVersionDevice3=${BROWSERSTACK_IOS_PLATFORM_VERSION_DEVICE_3}|" \
          -e "s|^iosDeviceName3=.*|iosDeviceName3=${BROWSERSTACK_IOS_DEVICE_NAME_3}|" \
          -e "s|^iosPlatformVersionDevice4=.*|iosPlatformVersionDevice4=${BROWSERSTACK_IOS_PLATFORM_VERSION_DEVICE_4}|" \
          -e "s|^iosDeviceName4=.*|iosDeviceName4=${BROWSERSTACK_IOS_DEVICE_NAME_4}|" \
          resources/environments/browserStackConfigs.properties

    - name: Write keys/files
      shell: bash
      run: |
        echo "${TESTING_CARDSERVICE_MOBILE_USER_PUBLIC_KEY}" > resources/environments/cardServiceEncryptionPublicKey.pub
        echo "${SSH_PRIVATE_KEY}" > resources/environments/dbSshKey
        chmod 600 resources/environments/dbSshKey

    - name: Display config files (sanitized)
      shell: bash
      run: |
        SELECTED_ENV="${{ inputs.target-environment }}"
        echo "===== config_${SELECTED_ENV}.properties ====="
        sed -E 's/(password|secret|token|key)=.*/\1=***REDACTED***/Ig' "resources/environments/config_${SELECTED_ENV}.properties" | cat
        echo "===== cardServiceConfigs_${SELECTED_ENV}.properties ====="
        sed -E 's/(password|secret|token|key)=.*/\1=***REDACTED***/Ig' "resources/environments/cardServiceConfigs_${SELECTED_ENV}.properties" | cat
        echo "===== browserStackConfigs.properties ====="
        sed -E 's/(password|secret|token|key)=.*/\1=***REDACTED***/Ig' "resources/environments/browserStackConfigs.properties" | cat
