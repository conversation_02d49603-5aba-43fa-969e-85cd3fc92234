name: Create Pending Check on PR
description: Fetch PR number for a given commit and create a pending status
inputs:
  repo-owner: { description: "Repository owner (e.g., org/user)", required: true }
  repo-name:  { description: "Repository name (e.g., mobile-app)", required: true }
  run-id:     { description: "github.run_id from caller (used to build target URL)", required: true }
  triggering-sha-id: { description: "Commit SHA to set the status on", required: true }
  gh-pat:     { description: "Token with repo:status (and repo read) scope", required: true }
  description: { description: "Description for the pending status", required: false, default: "Tests are in progress." }
  title:      { description: "Status check context/label", required: false, default: "Tests Result" }

runs:
  using: "composite"
  steps:
    - name: Validate required inputs
      shell: bash
      run: |
        if [ -z "${{ inputs.triggering-sha-id }}" ] || [ "${{ inputs.triggering-sha-id }}" = "undefined" ] || [ "${{ inputs.triggering-sha-id }}" = "null" ]; then
          echo "Error: triggering-sha-id is empty/undefined/null";
          exit 1;
        fi
        if [ -z "${{ inputs.repo-owner }}" ] || [ "${{ inputs.repo-owner }}" = "undefined" ] || [ "${{ inputs.repo-owner }}" = "null" ]; then
          echo "Error: repo-owner is empty/undefined/null";
          exit 1;
        fi
        if [ -z "${{ inputs.repo-name }}" ] || [ "${{ inputs.repo-name }}" = "undefined" ] || [ "${{ inputs.repo-name }}" = "null" ]; then
          echo "Error: repo-name is empty/undefined/null";
          exit 1;
        fi
        echo "Inputs validations passed successfully!"

    - name: Install jq
      shell: bash
      run: sudo apt-get install -yq jq

    - name: Fetch Pull Request Number
      id: fetch_pr_number
      shell: bash
      env:
        REPO_OWNER: ${{ inputs.repo-owner }}
        REPO_NAME:  ${{ inputs.repo-name }}
        SHA_ID:     ${{ inputs.triggering-sha-id }}
        GH_PAT:     ${{ inputs.gh-pat }}
      run: |
        REPO="${REPO_OWNER}/${REPO_NAME}"
        PR_RESPONSE=$(curl --location "https://api.github.com/repos/${REPO}/commits/${SHA_ID}/pulls" \
          --header "Accept: application/vnd.github+json" \
          --header "X-GitHub-Api-Version: 2022-11-28" \
          --header "Authorization: Bearer ${GH_PAT}")

        PR_COUNT=$(echo "$PR_RESPONSE" | jq '. | length')
        if [ "$PR_COUNT" -gt 0 ]; then
          PR_NUMBER=$(echo "$PR_RESPONSE" | jq '.[0].number')
          echo "PR_NUMBER=$PR_NUMBER" >> "$GITHUB_ENV"
        else
          echo "No pull request found for the given commit SHA."
        fi

    - name: Form PR Link Text
      id: form_pr_link_text
      shell: bash
      env:
        REPO_OWNER: ${{ inputs.repo-owner }}
        REPO_NAME:  ${{ inputs.repo-name }}
      run: |
        if [ -n "${PR_NUMBER}" ]; then
          PR_LINK="https://github.com/${REPO_OWNER}/${REPO_NAME}/pull/${PR_NUMBER}"
          PR_LINK_TEXT="\nPR Link: <${PR_LINK}|View Pull Request>"
        else
          PR_LINK_TEXT=""
        fi
        echo "PR_LINK_TEXT=${PR_LINK_TEXT}" >> "$GITHUB_ENV"

    - name: Create Pending Status Check
      shell: bash
      env:
        REPO_OWNER:  ${{ inputs.repo-owner }}
        REPO_NAME:   ${{ inputs.repo-name }}
        SHA_ID:      ${{ inputs.triggering-sha-id }}
        GH_PAT:      ${{ inputs.gh-pat }}
        STATUS_DESC: ${{ inputs.description }}
        STATUS_CTX:  ${{ inputs.title }}
        RUN_ID:      ${{ inputs.run-id }}
      run: |
        STATE="pending"
        TARGET_URL="https://github.com/${REPO_OWNER}/${REPO_NAME}/actions/runs/${RUN_ID}"
        curl -s -X POST \
          -H "Authorization: Bearer ${GH_PAT}" \
          -H "Accept: application/vnd.github+json" \
          -d '{
            "state": "'"${STATE}"'",
            "target_url": "'"${TARGET_URL}"'",
            "description": "'"${STATUS_DESC}"'",
            "context": "'"${STATUS_CTX}"'"
          }' \
          "https://api.github.com/repos/${REPO_OWNER}/${REPO_NAME}/statuses/${SHA_ID}"
